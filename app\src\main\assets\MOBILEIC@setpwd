{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var getTag = amc.fn.getById;\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var rpc = amc.rpcData;\n\n        var tipsSet = '{{spwd_pay}}';\n        var tipsRepeat = '{{confirm_again}}';\n        var actionSet = '';\n        var actionRepeat = '';\n        var protocols = [{\n            'text': '{{alipay_serve_protocol}}',\n            'url': 'https://ds.alipay.com/fd-inhm9zcq/index.html'\n        }, {\n            'text': '{{baoxian_protocol}}',\n            'url': 'https://ds.alipay.com/baoxian/zhxprotocol.htm'\n        }];\n\n        //### 是否给用户保险\n        var canGiveInsurance = false;\n        //### 是否显示支付宝服务协议\n        var showServiceProtocol = true;\n        //### 要显示的协议个数\n        var protocolCount = 0;\n        //### 只显示一个协议时的下标\n        var protocolIndex = 0;\n        //### 小额免密开关\n        var checkbox = null;\n        //### 小额免密开关文案\n        var checkLabel = null;\n\n        //### 密文密码\n        var encryptedPassword = null;\n\n        var pubKey = rpc.pubKey;\n\n        //### 初始化\n        function init() {\n            var nav = amc.fn.getNav(amc.res.navBack, null, null, null, null, onBack, null);\n            getTag('bodyContainer').insertBefore(nav, getTag('mainBody'));\n            //外部商户Q转T,正常Q用户支付(无资产)\n            if (rpc.setSpwdType === 'quser') {\n                actionSet = '/cashier/setSpwd';\n                actionRepeat = '/cashier/saveSpwd';\n            } else if (rpc.setSpwdType === 'complete') {\n                //淘宝Q用户支付(有资产)\n                tipsSet = '{{pay_spwd}}';\n                actionSet = '/complete/spwd';\n                actionRepeat = '/complete/savespwd';\n                showServiceProtocol = false;\n            }\n            initCheckBox();\n\n            getTag('mainLabel').innerText = '{{set_pwd}}';\n            getTag('tips').innerText = tipsSet;\n            initPwdInput('spwd');\n            getTag('spwd').focus();\n\n            if (amc.isAndroid) {\n                show('bottomPlaceholder');\n            }\n        }\n\n        function initPwdInput(tagName) {\n            var pluginInitData = pluginInitData || {};\n            pluginInitData['pubKey'] = pubKey;\n            var pluginInitDataString = JSON.stringify(pluginInitData);\n            getTag(tagName).src = pluginInitDataString;\n        }\n\n        //### 处理第一次请求成功后，服务端返回的数据.\n        function handleResponse(data) {\n\n            //### 是否显示小额免密推荐文案\n            var recommendNoPwd = (data['recommendNoPwd'] === 'true');\n            //### 是否选中小额免密开关\n            checkbox.checked = (data['selectOpenNoPwd'] === 'true');\n            canGiveInsurance = (data['canGiveBaoxian'] === 'true');\n\n            checkLabel.innerText = canGiveInsurance ? '{{can_insuranced}}' : '{{insuranced}}';\n\n            if (recommendNoPwd) {\n                show('checkArea');\n            } else {\n                hide('checkArea');\n            }\n\n            protocolCount = 0;\n            protocolIndex = 0;\n            if (showServiceProtocol) {\n                protocolCount++;\n                protocolIndex = 0;\n            }\n            if (recommendNoPwd && canGiveInsurance) {\n                protocolCount++;\n                protocolIndex = 1;\n            }\n            show('agreeProtocol');\n\n            if (protocolCount === 1) {\n                getTag('protocol').innerText = protocols[protocolIndex]['text'] || '';\n                if (protocolIndex === 1) {\n                    // 只有保险协议则显示 查看:<<保险协议>>\n                    getTag('agree').innerText = '{{look}}';\n                }\n            } else if (protocolCount === 2) {\n                getTag('protocol').innerText = '{{user_agreement}}';\n            } else {\n                hide('agreeProtocol');\n            }\n        }\n\n        //第一遍校验是否成功\n        function isFirstVerifySuccess(data) {\n            return (data['verifyCode'] === 'CONTINUE') ? true : false;\n        }\n\n        function canRedoVerify(data) {\n            return (data['verifyCode'] === 'RETRY') ? true : false;\n        }\n\n        //### 初次提交，服务器进行密码的规范性校验\n        function onFirstInput() {\n            var obj = {};\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['actionName'] = 'VERIFY_CHECK_PWD';\n            obj['params'] = {\n                'encryptPwd': encryptedPassword\n            };\n            obj['showLoading'] = 'true';\n\n            document.asyncSubmit(obj, function(data) {\n                data['pageloading'] = '0';\n                if (isFirstVerifySuccess(data)) { //校验成功，此处的校验成功\n                    handleResponse(data);\n                    hide('spwdBox');\n                    hide('firstPage');\n                    show('spwdRepeatBox');\n                    show('secondPage');\n\n                    initPwdInput('spwdRepeat');\n                    getTag('tips').innerText = tipsRepeat;\n                    getTag('spwdRepeat').value = '';\n                    getTag('nextBtn').disabled = true;\n                    getTag('spwdRepeat').focus();\n                } else { //校验失败\n                    var verifyMessage = data['verifyMessage'] || '人气太旺了，请稍后再试';\n                    if(canRedoVerify(data)){\n                        getTag('spwd').disabled = false;\n                        getTag('spwd').value = '';\n                        resetErrMsg(verifyMessage);\n                        getTag('spwd').focus();\n                    }else{\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                }\n            });\n        }\n\n        //### 提交按钮\n        function onSubmit() {\n            //提交给服务器进行保存。 Notice:出于安全考虑，这里的JS无法得到密码的实际内容。\n            var obj = {};\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['actionName'] = 'VERIFY_COMPLETE_PWD';\n            obj['params'] = {\n                'encryptPwd': encryptedPassword\n            };\n            obj['showLoading'] = 'true';\n\n            document.asyncSubmit(obj, function(data) {\n                data['pageloading'] = '0';\n                var verifyMessage = data['verifyMessage'] || '人气太旺了，请稍后再试';\n                if (!Boolean(data['verifySuccess'])) { //服务端校验失败\n                    if(canRedoVerify(data)){//允许重来\n                        resetErrMsg(verifyMessage);\n                        againOnce();\n                    }else{\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                } else {\n                    mic.fn.onBackWithResponse(data);\n                }\n            });\n        }\n\n        /*\n         * 重新设置一次\n         */\n        function againOnce() {\n            getTag('spwd').value = '';\n\n            show('spwdBox');\n            hide('spwdRepeatBox');\n            hide('secondPage');\n            show('firstPage');\n\n            getTag('tips').innerText = tipsSet;\n            getTag('spwd').focus();\n        }\n\n        // 打开协议\n        function showProtocol() {\n            getTag('spwdRepeat').blur();\n            // 一个协议\n            if (protocolCount === 1) {\n                var obj = {};\n                obj['eventName'] = 'vi_external_action';\n                var openUrl = protocols[protocolIndex]['url'];\n                obj['params'] = {\n                    'url': openUrl,\n                    'action_name': 'openUrl'\n                };\n                document.submit(obj);\n                return;\n            }\n            // 两个协议\n            var protocolArray = new Array();\n            for (var i = 0; i < protocols.length; i++) {\n                protocolArray.push(protocols[i]['text']);\n            }\n            document.actionSheet({\n                'btns': protocolArray,\n                'cancelBtn': '{{cancel}}'\n            }, function(data) {\n                document.submit({\n                    'action': {\n                        'name': \"loc:openweb('\" + protocols[data.index]['url'] + \"','\" + protocols[data.index]['text'] + \"')\"\n                    }\n                });\n            });\n        }\n\n        // 重置错误消息\n        function resetErrMsg(errMsg) {\n            errMsg = errMsg || '';\n            getTag('errMsg').innerText = errMsg || '';\n\n            if (errMsg) {\n                show('errMsgBox');\n            } else {\n                hide('errMsgBox');\n            }\n        }\n\n        // 推荐免密的checkbox\n        function initCheckBox() {\n\n            var checkArea = getTag('checkArea');\n            checkbox = amc.fn.create('input');\n            checkArea.appendChild(checkbox);\n            checkbox.type = 'checkbox';\n            checkbox.className = 'amc-checkbox amc-margin-r-s';\n            checkbox.checked = false;\n\n            checkLabel = amc.fn.create('label');\n            checkLabel.className = 'sub-title-font amc-flex-1';\n\n            // 先撑满右边区域\n            var checkLabelDiv = amc.fn.create('div');\n            checkLabelDiv.style.cssText = 'flex:1.0;';\n            checkLabelDiv.appendChild(checkLabel);\n\n            checkArea.appendChild(checkLabelDiv);\n        }\n\n        //### 后退\n        function onBack() {\n            //如果是在第二页则返回\n            if (!isFirstPage()) {\n                againOnce();\n            } else {\n                //如果是在第一页则退出\n                mic.fn.onBack();\n            }\n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                onBack();\n            }\n        }\n\n        function isFirstPage() {\n            return (getTag('spwdBox').style.display === 'none') ? false : true;\n        }\n\n        function onPwdConfirmed(encryptedPwd) {\n            //更新密码\n            encryptedPassword = encryptedPwd;\n\n            if (isFirstPage()) {\n                //第一页\n                onFirstInput();\n            }\n        }\n\n        function onPwdChanged(pwdLength) {\n            if (Number(pwdLength) >= 6) {\n                resetErrMsg();\n            }\n\n            getTag('nextBtn').disabled = !(Number(pwdLength) >= 6);\n        }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n            background-color: #ffffff;\n            display:flex;\n            flex-direction: column;\n        }\n\n        .main-label {\n            font-size: 22px;\n            color: #333333;\n            margin-top: 73px;\n            line-height: 30px;\n        }\n\n        .tips-label {\n            font-size: 16px;\n            color: #999999;\n            margin-top: 12px;\n            line-height: 22px;\n        }\n\n        .input-container {\n            margin-top: 73px;\n        }\n\n        .input-box {\n            height: 50px;\n            min-height: 50px;\n            max-height: 50px;\n            overflow: hidden;\n            align-items: center;\n        }\n\n        .pwd-input {\n            display: flex;\n            flex: 1.0;\n            border-radius: 4px;\n            padding: 0px;\n            color: #e5e5e5;\n            white-space: nowrap;\n            height: 48px;\n            max-height: 48px;\n            align-self: center;\n        }\n\n        .pwd-notice {\n            margin-top: 18px;\n            font-size: 16px;\n            color: #999999;\n            text-align: center;\n        }\n\n        .second-page {\n            margin-top: 18px;\n            flex-direction: column;\n        }\n\n        .sub-title-font {\n            font-size: 16px;\n        }\n\n        .sub-title-color {\n            color: #999999;\n        }\n\n        .err-msg-box {\n            margin-top: 18px;\n            display:flex;\n            flex-direction:column;\n            justify-content: center;\n            align-items:center;\n            display: none;\n        }\n\n        .err-msg-text {\n            color: #f4333c;\n        }\n\n        .check-area-box {\n            align-items: flex-start;\n        }\n\n        .agree-box {\n            display: flex;\n            flex-direction: row;\n            justify-content: center;\n            align-items: center;\n            margin-top: 18px;\n        }\n\n        .submit-btn {\n            background-color:#1677ff;\n            border: 0;\n            border-radius:4px;\n            color:#fff;\n            font-size:18px;\n            height:49px;\n            max-height:49px;\n            min-height:49px;\n            flex: 1.0;\n        }\n\n        .submit-btn:active {\n            background-color: #1677FF;\n            color: #fff;\n        }\n\n        .submit-btn:disabled {\n            background-color:#f5f5f5;\n            color:#999999;\n        }\n\n        .bottom-placeholder {\n            width: 100%;\n            height: 273px;\n            display: none;\n        }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "amc-pd-lr amc-scroll-flex main-body", "children": [{"css": "amc-flex-center", "children": [{"css": "main-label amc-text-center amc-flex-1", "tag": "label", "id": "mainLabel"}], "tag": "div"}, {"css": "amc-flex-center", "children": [{"css": "amc-text-center amc-flex-1 tips-label", "tag": "label", "id": "tips"}], "tag": "div"}, {"css": "amc-v-box input-container", "children": [{"css": "input-box", "children": [{"css": "pwd-input", "src": "payspwd", "tag": "embed", "id": "spwd", "type": "VIPayPwdView", "value": "", "oninput": ""}], "tag": "div", "id": "spwdBox"}, {"css": "amc-hidden input-box", "children": [{"css": "pwd-input", "src": "payspwd", "tag": "embed", "id": "spwdRepeat", "type": "VIPayPwdView", "value": "", "oninput": ""}], "tag": "div", "id": "spwdRepeatBox"}], "tag": "div"}, {"css": "err-msg-box", "children": [{"css": "err-msg-text sub-title-font", "children": [{"tag": "text", "text": "{{pwd-error-repeat}}"}], "tag": "label", "id": "errMsg"}], "tag": "div", "id": "errMsgBox"}, {"css": "pwd-notice", "children": [{"tag": "text", "text": "{{pwd-notice}}"}], "tag": "label", "id": "firstPage"}, {"css": "second-page amc-hidden", "children": [{"css": "submit-btn", "children": [{"tag": "text", "text": "{{next}}"}], "onclick": "onSubmit()", "disabled": "true", "tag": "button", "id": "nextBtn"}, {"css": "agree-box", "children": [{"css": "check-area-box", "tag": "div", "id": "checkArea"}, {"css": "amc-margin-r-xs sub-title-font sub-title-color", "children": [{"tag": "text", "text": "{{agree}}"}], "tag": "label", "id": "agree"}, {"css": "amc-theme-color sub-title-font", "children": [{"tag": "text", "text": "{{alipay_serve_protocol}}"}], "onclick": "showProtocol()", "tag": "label", "id": "protocol"}], "tag": "div", "id": "agreeProtocol"}], "tag": "div", "id": "secondPage"}, {"css": "bottom-placeholder", "tag": "div", "id": "bottomPlaceholder"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown();", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "setpwd", "format": "JSON", "tag": "MOBILEIC", "time": "0023", "tplId": "MOBILEIC@setpwd", "tplVersion": "5.3.5"}