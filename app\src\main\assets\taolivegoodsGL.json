{"container": {"data": [{"name": "layer", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$layer$0$0"]}, {"name": "linearlayout", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$linearlayout$0$0"]}, {"name": "sub_section", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$sub_section$0$0"]}, {"name": "taolive_gl_categorytop", "containerType": "dinamicx", "version": "8", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_categorytop/1722493587678/taolive_gl_categorytop.zip", "md5": null, "type": ["dinamicx$taolive_gl_categorytop$0$8"]}, {"name": "taolive_gl_header_atmosphere2", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_atmosphere2/1742182214878/taolive_gl_header_atmosphere2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_atmosphere2$0$9"]}, {"name": "taolive_gl_header_common2", "containerType": "dinamicx", "version": "3", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_common2/1738985875689/taolive_gl_header_common2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_common2$0$3"]}, {"name": "taolive_gl_header_shopinfo2", "containerType": "dinamicx", "version": "5", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_shopinfo2/1738985998013/taolive_gl_header_shopinfo2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_shopinfo2$0$5"]}, {"name": "taolive_gl_header_store2", "containerType": "dinamicx", "version": "3", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_store2/1738985947775/taolive_gl_header_store2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_store2$0$3"]}, {"name": "taolive_gl_header_topoperate2", "containerType": "dinamicx", "version": "2", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_topoperate2/1735888858771/taolive_gl_header_topoperate2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_topoperate2$0$2"]}, {"name": "taolive_gl_header_topuserbuy2", "containerType": "dinamicx", "version": "2", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_topuserbuy2/1735887567678/taolive_gl_header_topuserbuy2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_topuserbuy2$0$2"]}, {"name": "taolive_gl_header_urge_receive_privilege2", "containerType": "dinamicx", "version": "1", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_header_urge_receive_privilege2/1735889167141/taolive_gl_header_urge_receive_privilege2.zip", "md5": null, "type": ["dinamicx$taolive_gl_header_urge_receive_privilege2$0$1"]}, {"name": "taolive_gl_normal_bigimg2", "containerType": "dinamicx", "version": "25", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_bigimg2/1740731613521/taolive_gl_normal_bigimg2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_bigimg2$0$25"]}, {"name": "taolive_gl_normal_btn_downtime2", "containerType": "dinamicx", "version": "31", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_downtime2/1741862423097/taolive_gl_normal_btn_downtime2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_downtime2$0$31"]}, {"name": "taolive_gl_normal_btn_groupbuy2", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_groupbuy2/1741785174318/taolive_gl_normal_btn_groupbuy2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_groupbuy2$0$9"]}, {"name": "taolive_gl_normal_btn_hotitempreheat2", "containerType": "dinamicx", "version": "6", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_hotitempreheat2/1741054434613/taolive_gl_normal_btn_hotitempreheat2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_hotitempreheat2$0$6"]}, {"name": "taolive_gl_normal_btn_newuser2", "containerType": "dinamicx", "version": "7", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_newuser2/1741342869546/taolive_gl_normal_btn_newuser2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_newuser2$0$7"]}, {"name": "taolive_gl_normal_btn_normal2", "containerType": "dinamicx", "version": "29", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_normal2/1741785174583/taolive_gl_normal_btn_normal2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_normal2$0$29"]}, {"name": "taolive_gl_normal_btn_presale2", "containerType": "dinamicx", "version": "18", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_presale2/1741862423336/taolive_gl_normal_btn_presale2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_presale2$0$18"]}, {"name": "taolive_gl_normal_btn_seckill2", "containerType": "dinamicx", "version": "13", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_btn_seckill2/1741860555458/taolive_gl_normal_btn_seckill2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_btn_seckill2$0$13"]}, {"name": "taolive_gl_normal_downshelflayer2", "containerType": "dinamicx", "version": "7", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_normal_downshelflayer2/1726824997725/taolive_gl_normal_downshelflayer2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_downshelflayer2$0$7"]}, {"name": "taolive_gl_normal_name2", "containerType": "dinamicx", "version": "61", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_name2/1741696798121/taolive_gl_normal_name2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_name2$0$61"]}, {"name": "taolive_gl_normal_ontaplayer2", "containerType": "dinamicx", "version": "10", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_normal_ontaplayer2/1739347754488/taolive_gl_normal_ontaplayer2.zip", "md5": null, "type": ["dinamicx$taolive_gl_normal_ontaplayer2$0$10"]}, {"name": "taolive_gl_pcg", "containerType": "dinamicx", "version": "4", "url": "https://dinamicx.alibabausercontent.com/pub/taolive_gl_pcg/1726280691403/taolive_gl_pcg.zip", "md5": null, "type": ["dinamicx$taolive_gl_pcg$0$4"]}, {"name": "taolive_gl_rightstopv2", "containerType": "dinamicx", "version": "41", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_rightstopv2/1740483985539/taolive_gl_rightstopv2.zip", "md5": null, "type": ["dinamicx$taolive_gl_rightstopv2$0$41"]}, {"name": "taolive_gl_searchbottom", "containerType": "dinamicx", "version": "7", "url": "https://dinamicx.alibabausercontent.com/l_pub/taolive_gl_searchbottom/1738826308625/taolive_gl_searchbottom.zip", "md5": null, "type": ["dinamicx$taolive_gl_searchbottom$0$7"]}]}, "data": {"GoodsItemList": {"tag": "GoodsItemList", "type": "layout$layer$0$0"}, "normal2": {"tag": "normal2", "type": "layout$layer$0$0"}, "backgroundLayerNormalCard2": {"tag": "backgroundLayerNormalCard2", "type": "layout$layer$0$0", "fields": {"isRoot": "true", "type": "layer"}}, "OnTapLayerNormalCard2": {"tag": "OnTapLayerNormalCard2", "type": "dinamicx$taolive_gl_normal_ontaplayer2$0$10", "events": {"showInvalidClickToast": [{"type": "tlToast", "fields": {"message": "@data{ultronItemData.itemExtData.disableJumpItemDetailToast}"}}]}}, "defaultLayerNormalCard2": {"tag": "defaultLayerNormalCard2", "type": "layout$layer$0$0", "fields": {"type": "layer"}}, "bigImgSectionNormalCard2": {"tag": "bigImgSectionNormalCard2", "type": "layout$sub_section$0$0", "fields": {"exclusion": "true", "h": "116", "w": "116", "x": "0", "y": "0"}}, "BigImgAreaNormalCard2": {"tag": "BigImgAreaNormalCard2", "type": "dinamicx$taolive_gl_normal_bigimg2$0$25"}, "nameSectionNormalCard2": {"tag": "nameSectionNormalCard2", "type": "layout$sub_section$0$0", "fields": {"exclusion": "true", "h": "69", "w": "259", "x": "116", "y": "0"}}, "NameAreaNormalCard2": {"tag": "NameAreaNormalCard2", "type": "dinamicx$taolive_gl_normal_name2$0$61"}, "priceAndButtonSectionNormalCard2": {"tag": "priceAndButtonSectionNormalCard2", "type": "layout$sub_section$0$0", "fields": {"exclusion": "true", "h": "47", "w": "259", "x": "116", "y": "69"}}, "BtnSeckill2": {"tag": "BtnSeckill2", "type": "dinamicx$taolive_gl_normal_btn_seckill2$0$13", "fields": {"dependency": "@triple{@equal{@data{nativeConfigInfos.isSeckill}, '1'},'true','false'}"}}, "BtnHotItemPreheat2": {"tag": "BtnHotItemPreheat2", "type": "dinamicx$taolive_gl_normal_btn_hotitempreheat2$0$6", "fields": {"dependency": "@triple{@and{@data{liveItemStatusData.isHotItemPreheat},@equal{@data{itemExtData.hotItemActivityStatus}, '0'}}, 'true', 'false'}"}}, "BtnGroupBuy2": {"tag": "BtnGroupBuy2", "type": "dinamicx$taolive_gl_normal_btn_groupbuy2$0$9", "fields": {"dependency": "@triple{@and{@equal{@data{native_itemBenefits[0].styleType}, '-1'},@equal{@data{native_itemBenefits[0].type}, 'groupBuyInfo'}}, 'true', 'false'}"}}, "BtnNewUserCardInNormal2": {"tag": "BtnNewUserCardInNormal2", "type": "dinamicx$taolive_gl_normal_btn_newuser2$0$7", "fields": {"dependency": "@triple{@equal{@data{extendVal.itemBizType}, 'newUser'}, 'true', 'false'}"}}, "BtnDowntimeNormalCard2": {"tag": "BtnDowntimeNormalCard2", "type": "dinamicx$taolive_gl_normal_btn_downtime2$0$31", "fields": {"dependency": "@triple{@or{@data{liveItemStatusData.isDownShelf}, @data{liveItemStatusData.isGray}, @equal{@data{itemExtData.timingUpShelfStatus},'0'}, @data{native_canShowVipEntrance},@equal{@data{extendVal.itemBizType},'preHeat'}, @data{liveItemStatusData.isPreHeat}, @data{itemExtData.isCredit}, @data{liveItemStatusData.isInvalidChannelSubItem}}, 'true', 'false'}"}}, "BtnPresaleNormalCard2": {"tag": "BtnPresaleNormalCard2", "type": "dinamicx$taolive_gl_normal_btn_presale2$0$18", "fields": {"dependency": "@triple{@or{@equal{@data{itemExtData.preSaleStatus}, '0'}, @equal{@data{itemExtData.preSaleStatus}, '1'}}, 'true', 'false'}"}}, "BtnNormalNormalCard2": {"tag": "BtnNormalNormalCard2", "type": "dinamicx$taolive_gl_normal_btn_normal2$0$29", "fields": {"dependency": "@const{true}"}}, "downshelfLayerNormalCard2": {"tag": "downshelfLayerNormalCard2", "type": "layout$layer$0$0"}, "DownShelftLayerNormalCard2": {"tag": "DownShelftLayerNormalCard2", "type": "dinamicx$taolive_gl_normal_downshelflayer2$0$7", "fields": {"dependency": "@triple{@or{@data{liveItemStatusData.isGray},@data{nativeConfigInfos.expansionRedPacket.index}}, 'true', 'false'}"}}, "pcg": {"tag": "pcg", "type": "layout$layer$0$0", "fields": {"height": "@triple{@data{itemExtData.rec0}, @triple{@equal{@data{nativeConfigInfos.isFirstRec}, '1'}, '147', '157'}, '121'}"}}, "CardPcg": {"tag": "CardPcg", "type": "dinamicx$taolive_gl_pcg$0$4"}, "categoryTop": {"tag": "categoryTop", "type": "layout$layer$0$0", "fields": {"height": "@const{'44'}"}}, "CardCategoryTop": {"tag": "CardCategoryTop", "type": "dinamicx$taolive_gl_categorytop$0$8"}, "searchBottom": {"tag": "searchBottom", "type": "layout$layer$0$0", "fields": {"height": "@triple{@data{blankMode}, '36', '100'}"}}, "CardSearchBottom": {"tag": "CardSearchBottom", "type": "dinamicx$taolive_gl_searchbottom$0$7"}, "rightsTopV2": {"tag": "rightsTopV2", "type": "layout$layer$0$0", "fields": {"height": "@triple{@or{@equal{@data{separateRight.type},'platformThreeOrders'}, @equal{@data{separateRight.type}, 'returnWelfare'}}, '57', @triple{@equal{@data{separateRight.type}, 'itemZoneV2'}, @triple{@equal{@data{separateRight.extData.version}, '2'}, '57', '73'}, @triple{@data{separateRight}, @triple{@data{rightsList}, '88', '44'}, @triple{@data{rightsList}, '44', '0'}}}}"}}, "CardRightsV2": {"tag": "CardRightsV2", "type": "dinamicx$taolive_gl_rightstopv2$0$41"}, "GLHeader": {"tag": "GLHeader", "type": "layout$layer$0$0", "fields": {"compressList": "@triple{@data{topAtmosphereList[0].type}, '36','0'}"}}, "TaoLiveGoodsHeader": {"tag": "TaoLiveGoodsHeader", "type": "layout$linearlayout$0$0", "position": "header"}, "TaoLiveGoodsUserBuyContainer": {"tag": "TaoLiveGoodsUserBuyContainer", "type": "layout$sub_section$0$0", "fields": {"dependency": "@len{@data{userBuyList}}"}}, "TaoLiveGoodsUserBuy": {"tag": "TaoLiveGoodsUserBuy", "type": "dinamicx$taolive_gl_header_topuserbuy2$0$2"}, "TaoLiveGoodsHongbaoContainer": {"tag": "TaoLiveGoodsHongbaoContainer", "type": "layout$sub_section$0$0", "fields": {"dependency": "@len{@data{carouselListV2}}"}, "position": "header"}, "TaoLiveGoodsHongbao": {"tag": "TaoLiveGoodsHongbao", "type": "dinamicx$taolive_gl_header_topoperate2$0$2", "events": {"openUrl": [{"type": "tlTrack", "fields": {"args": "@subdata{''}", "eventId": "2101", "eventName": "goodsList_light"}}, {"type": "tlOpenUrl", "fields": {"method": "GET", "pageType": "H5", "url": "@subdata{jumpUrl}"}}], "appear": [{"type": "tlTrack", "fields": {"args": "@tlMap{'type', 'packet'}", "eventId": "2201", "eventName": "Show-goodsList_light"}}]}, "position": "header"}, "TaoLiveGoodsUrgeReceivePrivilegeContainer": {"tag": "TaoLiveGoodsUrgeReceivePrivilegeContainer", "type": "layout$sub_section$0$0", "fields": {"dependency": "@len{@data{carouselListV3}}"}}, "TaoLiveGoodsUrgeReceivePrivilege": {"tag": "TaoLiveGoodsUrgeReceivePrivilege", "type": "dinamicx$taolive_gl_header_urge_receive_privilege2$0$1"}, "TaoLiveGoodAtmosphereContainer": {"tag": "TaoLiveGoodAtmosphereContainer", "type": "layout$sub_section$0$0", "fields": {"dependency": "@len{@data{topAtmosphereList}}", "y": "-30"}, "position": "header"}, "atmosphere": {"tag": "atmosphere", "type": "dinamicx$taolive_gl_header_atmosphere2$0$9", "events": {"openUrl": [{"type": "tlTrack", "fields": {"args": "@tlStrMap{@data{ultronItemData.topAtmosphereList[0].utParams}}", "eventId": "2101", "eventName": "GoodslistFloating_Atmosphere"}}, {"type": "tlOpenUrl", "fields": {"pageType": "H5", "url": "@data{ultronItemData.topAtmosphereList[0].jumpUrl}"}}], "appear": [{"type": "tlTrack", "fields": {"args": "@tlStrMap{@data{ultronItemData.topAtmosphereList[0].utParams}}", "eventId": "2201", "eventName": "GoodslistFloating_Atmosphere"}}]}}, "TaoLiveGoodsTitleContainer": {"tag": "TaoLiveGoodsTitleContainer", "type": "layout$sub_section$0$0", "fields": {"exclusion": "true"}, "position": "header"}, "headerCommon": {"tag": "headerCommon", "type": "dinamicx$taolive_gl_header_common2$0$3", "fields": {"dependency": "@triple{@or{@not{@data{topBarList[0]}}, @equal{@data{itemListType}, 'multiTab'}}, 'true', 'false'}"}, "events": {"openUrl": [{"type": "tlTrack", "fields": {"args": "@data{broadCaster}", "eventId": "2101", "eventName": "gl_anchorHead"}}, {"type": "tlOpenUrl", "fields": {"pageType": "H5", "url": "@data{ultronItemData.broadCaster.jumpUrl}"}}]}}, "headerStore": {"tag": "headerStore", "type": "dinamicx$taolive_gl_header_store2$0$3", "fields": {"dependency": "@triple{@and{@equal{@data{topBarList[0].type}, '0'}, @not{@data{extraGoodsTabList[1].type}}}, 'true', 'false'}"}, "events": {"showStoreEntrance": [{"type": "tlTrack", "fields": {"args": "@tlMap{@const{type},@data{ultronItemData.topBarList[0].type},@const{subType},@data{ultronItemData.topBarList[0].subType},@const{iconSuffixType},@data{ultronItemData.topBarList[0].iconSuffix[0].type}}", "eventId": "2201", "eventName": "Show-gostore"}}], "goToStore": [{"type": "tlTrack", "fields": {"args": "@tlMap{@const{type},@data{ultronItemData.topBarList[0].type},@const{subType},@data{ultronItemData.topBarList[0].subType},@const{iconSuffixType},@data{ultronItemData.topBarList[0].iconSuffix[0].type}}", "eventId": "2101", "eventName": "gostore"}}, {"type": "tlOpenUrl", "fields": {"pageType": "H5", "url": "@data{ultronItemData.topBarList[0].jumpUrl}"}}]}}, "headerShopInfo": {"tag": "headerShopInfo", "type": "dinamicx$taolive_gl_header_shopinfo2$0$5", "fields": {"dependency": "@triple{@and{@equal{@data{topBarList[0].type}, '1'}, @not{@data{extraGoodsTabList[1].type}}}, 'true', 'false'}"}, "events": {"openUrl": [{"type": "tlOpenUrl", "fields": {"method": "GET", "pageType": "H5", "url": "@data{ultronItemData.topBarList[0].jumpUrl}"}}]}}}, "linkage": {"common": {"compress": "true"}, "signature": "23760ce7ffe3840b649b882d630655de"}, "hierarchy": {"root": "taolivegoodsGL", "structure": {"taolivegoodsGL": ["GoodsItemList", "GLHeader"], "GoodsItemList": ["normal2", "pcg", "categoryTop", "searchBottom", "rightsTopV2"], "normal2": ["backgroundLayerNormalCard2", "defaultLayerNormalCard2", "downshelfLayerNormalCard2"], "backgroundLayerNormalCard2": ["OnTapLayerNormalCard2"], "defaultLayerNormalCard2": ["bigImgSectionNormalCard2", "nameSectionNormalCard2", "priceAndButtonSectionNormalCard2"], "bigImgSectionNormalCard2": ["BigImgAreaNormalCard2"], "nameSectionNormalCard2": ["NameAreaNormalCard2"], "priceAndButtonSectionNormalCard2": ["BtnSeckill2", "BtnHotItemPreheat2", "BtnGroupBuy2", "BtnNewUserCardInNormal2", "BtnDowntimeNormalCard2", "BtnPresaleNormalCard2", "BtnNormalNormalCard2"], "downshelfLayerNormalCard2": ["DownShelftLayerNormalCard2"], "pcg": ["CardPcg"], "categoryTop": ["CardCategoryTop"], "searchBottom": ["CardSearchBottom"], "rightsTopV2": ["CardRightsV2"], "GLHeader": ["TaoLiveGoodsHeader"], "TaoLiveGoodsHeader": ["TaoLiveGoodsUserBuyContainer", "TaoLiveGoodsHongbaoContainer", "TaoLiveGoodsUrgeReceivePrivilegeContainer", "TaoLiveGoodAtmosphereContainer", "TaoLiveGoodsTitleContainer"], "TaoLiveGoodsUserBuyContainer": ["TaoLiveGoodsUserBuy"], "TaoLiveGoodsHongbaoContainer": ["TaoLiveGoodsHongbao"], "TaoLiveGoodsUrgeReceivePrivilegeContainer": ["TaoLiveGoodsUrgeReceivePrivilege"], "TaoLiveGoodAtmosphereContainer": ["atmosphere"], "TaoLiveGoodsTitleContainer": ["headerCommon", "headerStore", "headerShopInfo"]}}, "endpoint": {"ultronage": "true", "protocolVersion": "3.0", "contextVersion": "taolivegoods_gc_202503171532306984_259138", "domainCode": "taolivegoods", "page": "taolivegoodsGL", "traceIds": ["213e3fad17422676046114868e1361"]}, "reload": "true"}