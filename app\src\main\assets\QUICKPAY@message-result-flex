{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"titlebar": "找朋友幫忙付", "msg_title": "付款邀請已發送到對方支付寶", "msg_subtitle": "請對方打開支付寶進入「朋友」功能找到對話消息", "btn_complete": "完成", "btn_continue": "繼續找人"}, "zh_TW": {"titlebar": "找朋友幫忙付", "msg_title": "付款邀請已發送到對方支付寶", "msg_subtitle": "請對方打開支付寶進入「朋友」功能找到對話消息", "btn_complete": "完成", "btn_continue": "繼續找人"}, "en_US": {"titlebar": "Ping friends a bill", "msg_title": "Payment invitation has been sent to the other party&#39;s <PERSON><PERSON><PERSON>", "msg_subtitle": "Please open <PERSON><PERSON><PERSON> to enter the \"friends\" function to find the conversation message", "btn_complete": "Complete", "btn_continue": "Continue to find friends"}, "zh_CN": {"titlebar": "找朋友帮忙付", "msg_title": "付款邀请已发送到对方支付宝", "msg_subtitle": "请对方打开支付宝进入“朋友”功能找到对话消息", "btn_complete": "完成", "btn_continue": "继续找人"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": "._SharepayMessageResult_0ec4-c-result-box{margin:12px 0px 0px;background-color:#fff;padding-bottom:32px}._SharepayMessageResult_0ec4-c-result-img{margin-top:37.5px;width:64px;height:64px;align-self:center}._SharepayMessageResult_0ec4-c-result-main-label{margin-top:20px;color:#333;font-size:18px;height:25px;text-align:center}._SharepayMessageResult_0ec4-c-result-sub-label{margin-top:8px;color:#999;font-size:13px;height:20px;text-align:center}._SharepayMessageResult_0ec4-c-img-container{margin:0px 12px 0px;padding-bottom:16px}._SharepayMessageResult_0ec4-c-tutorial-img{margin:0px}._SharepayMessageResult_0ec4-c-recommend-box{margin:0px;background-color:#fff;padding-bottom:8px}._SharepayMessageResult_0ec4-c-recommend-bubble-box{align-self:center;margin-top:0px;height:35px;background-color:#FFEDEF;border-radius:12.5px}._SharepayMessageResult_0ec4-c-recommend-bubble-label{text-align:center;font-size:13px;margin-left:20px;margin-right:20px;color:#FF3140}._SharepayMessageResult_0ec4-c-blue-btn{height:49px;background-color:#1677ff;border-radius:8px;margin:6px 12px 6px;font-size:18px;color:#fff}._SharepayMessageResult_0ec4-c-white-btn{height:49px;background-color:#fff;border-radius:8px;margin:6px 12px 6px;border:#e5e5e5 1px solid;font-size:18px;color:#333}._SharepayMessageResult_0ec4-c-btn-list-box{margin:0px;padding:6px 0px}._ButtonGroup_1afd-c-pay-btn-box{flex-direction:column;overflow:hidden;padding:12px 16px;min-height:68px;border-radius:2px;justify-content:flex-end}._DefaultRecommendComponent_okum-i-container{flex:1.0;margin:12px 12px 0px 12px;border-radius:8px}._DefaultRecommendComponent_okum-c-default-img{height:100px;width:100px;margin-top:6px}._DefaultRecommendComponent_okum-c-default-label{color:#999;font-size:15px;margin-top:12px;margin-bottom:27px;text-align:center}._DefaultRecommendComponent_nuo5-i-container{flex:1.0;margin:0px;border-radius:8px;background-color:#fff}._DefaultRecommendComponent_nuo5-c-default-img{height:100px;width:100px;margin-top:40px}._DefaultRecommendComponent_nuo5-c-default-label{color:#999;font-size:15px;margin-top:12px;margin-bottom:45px;text-align:center}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 792d3c2bea8c33cc50cc88828fa88d0debd10b15:C */!function(n){var o={};function r(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,r),t.l=!0,t.exports}r.m=n,r.c=o,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&'object'==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,'a',t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p='',r(r.s=4)}([function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(5);t.BNComponent=o.BNComponent;var r=n(3);t.ComponentRegistry=r.ComponentRegistry;var i=n(7);t.Logger=i.Logger,t.logger=i.logger},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},t.startsWith=function(e,t){return!!e&&0===e.indexOf(t)}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(0),r=function(){function n(){}return n.registerComponent=function(e,t){t?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(o.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(e){return n.jsons[e]},n.putComponentJson=function(e,t){t||o.logger.e('CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i('CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},n.createComponent=function(e){o.logger.i('CmpReg#crtCmp','I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e('CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}();t.ComponentRegistry=r},function(e,s,t){'use strict';var o,n=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(s,'__esModule',{value:!0});var r=t(0),c=t(1),i=t(8),a=t(9);s.Spm={PAGE:'a283.b44109',BOX:'a283.b44109.c109150',DONEBTN:'a283.b44109.c109150.d226031',BACKBTN:'a283.b44109.c109150.d226030',RECBOX:'a283.b44109.c109150.d226032'};var u=c.amc.rpcData.spmObj,l=function(t){function e(e){var i=t.call(this)||this;return i.onDone=function(){c.amc.fn.setResult({resultStatus:'6006'})},i.onReturn=function(){c.amc.fn.back()},i.onMounted=function(){var e=i.rpcData,t=(e.imageUrl,e.guidedImgUrl);i.configSubtitle(),i.configRecBox(),i.configButton();var n=i.getViewInComponentById('resultImg');n.src='https://mdn.alipayobjects.com/portal_pkjy6x/afts/img/A*lAsnTpoyu2wAAAAAAAAAAAAAAQAAAQ/original',n.contentmode='ScaleAspectFill';var o=i.getViewInComponentById('tutorialImg'),r=window.innerWidth-12-12;o.src=t||'https://mdn.alipayobjects.com/portal_pkjy6x/afts/img/A*jQWwSb4xjb8AAAAAAAAAAAAAAQAAAQ/original',o.contentmode='ScaleAspectFit',o.style.cssText+='width:'+r+'px;',!c.amc.isAndroid||c.amc.fn.sdkGreaterThanOrEqual('10.8.50')&&!c.amc.isSDK||(o.style.cssText+='margin-bottom:40px;'),f('exposure',s.Spm.PAGE),f('exposure',s.Spm.BOX),f('exposure',s.Spm.DONEBTN)},i.rpcData=e,i}return n(e,t),e.prototype.configSubtitle=function(){var e=this.getViewInComponentById('subTitle');this.rpcData.subResultMsg&&0<this.rpcData.subResultMsg.length&&(e.style.display=c.amc.VISIBLE,e.innerText=this.rpcData.subResultMsg)},e.prototype.configRecBox=function(){if(this.rpcData.recTxt&&0!=this.rpcData.recTxt.length){var e=this.rpcData.recTxt,t=this.getViewInComponentById('recommendBox');t.style.display=c.amc.VISIBLE,this.createStyledElement('div','','amc-v-box');var n=this.createStyledElement('div','','recommend-bubble-box'),o=this.createStyledElement('label','','recommend-bubble-label');o.innerText=e,n.appendChild(o),t.appendChild(n),f('exposure',s.Spm.RECBOX)}},e.prototype.configButton=function(){if(this.rpcData.btns&&!(this.rpcData.btns.length<=0)){var e=this.rpcData.blueBtnIndexes||[];this.btnsArr=this.rpcData.btns;for(var t=0;t<e.length;t++){var n=e[t];this.btnsArr[n].isPrimary=!0}var o=function(e){var t=void 0,n=void 0,o=a.btnsArr[e];n=a.getViewInComponentById('btnListBox');var r=o.isPrimary?'blue-btn':'white-btn';if(0==e&&a.rpcData.recTxt&&0<a.rpcData.recTxt.length)(t=a.createStyledElement('button','',r)).onclick=function(){if(o.act){var e={type:'json',operationType:c.amc.isSDK?'alipay.msp.cashier.dispatch.json.tb':'alipay.msp.cashier.dispatch.json',action:o.act};f('clicked',s.Spm.RECBOX),document.invoke('rpc',e,function(e){var t;e&&e.success?t='支付宝已通知Ta给你赠卡～':(t='网络开小差, 请稍后再试',e&&e.errorTip&&(t=e.errorTip)),document.toast({text:t,type:'none'},function(){})})}},t.innerText=o.txt||'求赠亲情卡';else{var i=a.createStyledElement('button','',r);i.innerText=o.txt||'',o.act&&o.act.name&&o.act.name.indexOf('back')&&f('exposure',s.Spm.BACKBTN),i.onclick=function(){o.act&&o.act.name&&o.act.name.indexOf('back')&&f('clicked',s.Spm.BACKBTN),document.submit({action:o.act})},t=i}n.style.display=c.amc.VISIBLE,n.appendChild(t)},a=this;for(t=0;t<this.btnsArr.length;t++)o(t)}},e.getComponentCSSRules=function(){return{'.result-box':'_SharepayMessageResult_0ec4-c-result-box','.result-img':'_SharepayMessageResult_0ec4-c-result-img','.result-main-label':'_SharepayMessageResult_0ec4-c-result-main-label','.result-sub-label':'_SharepayMessageResult_0ec4-c-result-sub-label','.img-container':'_SharepayMessageResult_0ec4-c-img-container','.tutorial-img':'_SharepayMessageResult_0ec4-c-tutorial-img','.recommend-box':'_SharepayMessageResult_0ec4-c-recommend-box','.recommend-bubble-box':'_SharepayMessageResult_0ec4-c-recommend-bubble-box','.recommend-bubble-label':'_SharepayMessageResult_0ec4-c-recommend-bubble-label','.blue-btn':'_SharepayMessageResult_0ec4-c-blue-btn','.white-btn':'_SharepayMessageResult_0ec4-c-white-btn','.btn-list-box':'_SharepayMessageResult_0ec4-c-btn-list-box'}},e.getComponentJson=function(){return{_c:'amc-scroll-flex',_t:'div',_cd:[{_c:'_SharepayMessageResult_0ec4-c-result-box amc-v-box',_t:'div',_cd:[{'bn-view-id':'resultImg',_c:'_SharepayMessageResult_0ec4-c-result-img',_t:'img'},{_c:'_SharepayMessageResult_0ec4-c-result-main-label',_t:'label',_x:'发送成功'},{'bn-view-id':'subTitle',_c:'_SharepayMessageResult_0ec4-c-result-sub-label amc-hidden',_t:'label',_x:'资金将转入余额, 余额足够后将自动支付订单'}]},{'bn-view-id':'recommendBox',_c:'_SharepayMessageResult_0ec4-c-recommend-box amc-v-box amc-hidden',_t:'div'},{'bn-view-id':'btnListBox',_c:'_SharepayMessageResult_0ec4-c-btn-list-box amc-v-box amc-hidden',_t:'div'},{'bn-view-id':'imgContainer',_c:'_SharepayMessageResult_0ec4-c-img-container',_t:'div',_cd:[{'bn-view-id':'tutorialImg',_c:'_SharepayMessageResult_0ec4-c-tutorial-img',_t:'img'}]},{_c:'amc-iphone-x-pd-b',_t:'div'}]}},e.componentName='SharepayMessageResult',e.componentHashName='SharepayMessageResult_0ec4',e}(r.BNComponent),p=!1;function f(e,t,n){t&&0!==t.length&&('exposure'===e?c.amc.fn.spmExposure(t,a.mergeObject(u,n)):'clicked'===e&&c.amc.fn.spmClick(t,a.mergeObject(u,n)))}document.viewDidAppear=function(){p?c.amc.fn.spmExposureResume():p=!0},window.onload=function(){c.amc.fn.spmPageCreate(s.Spm.PAGE,{}),!c.amc.isSDK&&c.amc.fn.sdkGreaterThanOrEqual('10.8.50')?document.body.style.height=c.amc.isAndroid?'100%':c.amc.specs.bodyHeight:document.body.style.height=c.amc.isAndroid?window.innerHeight:c.amc.specs.bodyHeight;var e=c.amc.rpcData.pageTitle;!c.amc.rpcData||c.amc.rpcData.pageTitle||c.amc.rpcData.productScene==i.ProductScene.FamilyPay&&c.amc.rpcData.productScene==i.ProductScene.MoneyTurnOver||(e='找朋友帮忙付'),document.body.appendChild(c.amc.fn.getNav(c.amc.res.navBack,c.amc.isAndroid?'':'{{return}}',e,'完成',void 0,function(){t.onReturn()},function(){f('clicked',s.Spm.DONEBTN),c.amc.fn.setResult&&c.amc.fn.setResult({resultStatus:'6006'})},{backMode:'back'}));var t=new l(c.amc.rpcData);t.mountTo(document.body),window.page=t,window.onKeyDown=function(){4===window.event.which&&t.onReturn()}},document.onDestroy=function(){c.amc.fn.spmPageDestroy(s.Spm.PAGE,{})}},function(e,t,n){'use strict';var a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};Object.defineProperty(t,'__esModule',{value:!0});var x=n(0),C=n(2),S=n(3),w=n(6),o=function(){function e(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=C.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return e.getComponentCSSRules=function(){throw new Error('E0100')},e.getComponentJson=function(){throw new Error('E0101')},e.prototype.mountTo=function(e,t){if(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t):e.appendChild(n),this._triggerOnMounted()):x.logger.e('Cmp#mT','E0103 '+n)}else x.logger.e('Cmp#mT','E0102 '+e)},Object.defineProperty(e.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),e.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},e.prototype.getMountedParentView=function(){var e=this.getMountedRootView();return e?e.parentNode:null},e.prototype.getSubComponentById=function(e,t){var n=this.debugName+'#SCById',o=this._subComponentsMap[e];if(!o)return null;var r='',i='';try{r=o.constructor.componentName,i=t.componentName}catch(e){x.logger.e(n,'E0104 '+e)}return r&&r===i?o:(x.logger.e(n,'E0105 '+r+', '+i),null)},e.prototype.getViewInComponentById=function(e){return this._viewsIdMap[e]},e.prototype.getComponentId=function(){return this._componentId},e.prototype.createStyledElement=function(e,t,n){var o=document.createElement(e);if(o)return e&&(o.className+=' '+this._css(e,2)),n&&(o.className+=' '+this._csses(n,1)),t&&(o.className+=' '+this._css('#'+t,2)),o},e.prototype.applyStyleTo=function(e,t){e&&(e.className+=' '+this._csses(t,1))},e.prototype.css=function(e){return this._css(e,0)},e.prototype.csses=function(e){return this._csses(e,0)},e.prototype._csses=function(e,t){var n=this;return e.split(' ').map(function(e){return n._css(e,t)}).join(' ')},e.prototype._css=function(e,t){if(!e)return'';var n=this._componentCSSRules;if(!n)return e;switch(e.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e;case 2:default:return e}}},e.prototype._triggerOnMounted=function(){new w.Observer(this.vueModel.data),x.logger.i('','I0106 '+this.debugName);for(var e=0,t=this._subComponents;e<t.length;e++){var n=t[e];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},e.prototype._getMethod=function(e){var t=this[e];return t instanceof Function?t:null},e.prototype._acquireComponentJson=function(){var e=this.debugName+'#acCJ',t=S.ComponentRegistry.getComponentJson(this._componentName);return t?(x.logger.i(e,'I0107'),t):void 0!==this._componentJson?(x.logger.i(e,'I0108'),S.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(x.logger.e(e,'E0109'),null)},e.prototype._acquireRootView=function(){var e=this.debugName+'#acRV';if(this._hasRootViewBuilt)return x.logger.i(e,'I0110'),this._rootView;var t=this._acquireComponentJson();return t?(this._rootView=this._convertJsonToBNNode(t,this.vueModel.data||{}),this._hasRootViewBuilt=!0,x.logger.i(e,'I0112'),this._rootView):(x.logger.e(e,'E0111'),null)},e.prototype._genArrayChildNode=function(e,t,n,o,r){var i=this._convertJsonToBNNode(e,a({},t,{item:n,index:o,arrayName:r}));return i?(i.setAttribute('index',o),i.setAttribute('for_name',r),i):null},e.prototype._convertJsonToBNNode=function(e,u){var l=this,t=this.debugName+'#cJTB';if(void 0===e._t)return null;var p=document.createElement(e._t),f=[];if(void 0!==e._cd)for(var n=function(a){if(a['v-for']||a['v-for-cal']){var e=!a['v-for']&&!!a['v-for-cal'],t=(e?m.vueModel.compute:u)||{},n=w.vueUtils.getObject(e?a['v-for-cal']:a['v-for'],t,e),s=e?w.vueUtils.rmSymbol(a['v-for-cal']):w.vueUtils.rmSymbol(a['v-for']);if(!s||!n)return'continue';for(var o in a['v-for']='',a['v-for-cal']='',n)if(n.hasOwnProperty(o)){var r=m._genArrayChildNode(a,u,n[o],o,s);r&&f.push(r)}var c=document.createElement('div');c&&(c.style.display='none',c.setAttribute('for_end',s),f.push(c),new w.Watcher(s,t,function(e){if(p){w.rmWatchers(s);for(var t=0,n=p.childNodes;t<n.length;t++){var o=n[t];o.getAttribute('for_name')===s&&p.removeChild(o)}if(e)for(var r in e)if(e.hasOwnProperty(r)){var i=l._genArrayChildNode(a,u,e[r],r,s);i&&p.insertBefore(i,c)}}},e).id=u.arrayName)}else{var i=m._convertJsonToBNNode(a,u);if(!i)return'continue';f.push(i)}},m=this,o=0,r=e._cd;o<r.length;o++)n(r[o]);if(!p)return null;u&&u.index&&p.setAttribute('index',u.index);var i=e['bn-component']||e['sp-component'];if(i){x.logger.i(t,'I0113 '+i);var a=S.ComponentRegistry.createComponent(i);if(!a)return x.logger.e(t,'E0114 '+i+', '+a),null;var s=e['bn-component-id']||e['sp-component-id'];return s&&(a._componentId=s),a.onCreated&&a.onCreated(),x.logger.i(t,'I0115 '+a.debugName+', '+s),this._subComponents.push(a),s&&!this._subComponentsMap[s]&&(this._subComponentsMap[s]=a),a._acquireRootView()}var c=e['bn-view-id']||e['sp-view-id'];for(var d in c&&(x.logger.i(t,'I0116 '+c),this._viewsIdMap[c]||(this._viewsIdMap[c]=p)),e._i&&(p.id=e._i),e._c&&(p.className=e._c),e._s&&(p.style.cssText=e._s),e._x&&(p.innerText=e._x),e._y&&(p.type=e._y),e)if(e.hasOwnProperty(d))if(0===d.indexOf('on')){var h=this._getMethod(e[d]);h&&(p[d]=h.bind(this,p))}else if(0===d.indexOf('_'));else if(0===d.indexOf('bn-')||0===d.indexOf('sp-'));else if(C.startsWith(d,'v-')){var g=d.split('-');if(2===g.length||3===g.length){var b=g[1];2===g.length?new w.NodeCompile(u).compile(b,p,e[d],e._t):'cal'===g[2]&&new w.NodeCompile(this.vueModel.compute,!0).compile(b,p,e[d],e._t)}else p[d]=e[d]}else p[d]=e[d];for(var v=0,y=f;v<y.length;v++){var _=y[v];p.appendChild(_)}return p},e.componentName='',e.componentHTML='',e.componentCSS='',e.componentHashName='',e}();t.BNComponent=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var i,a=n(2),s=n(1);t.rmWatchers=function(t){i=i.filter(function(e){return e.id!==t})};var c=function(){function e(e,t,n,o){if(this.id='',this.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,this.callback=n,a.startsWith(e,'item')&&t.arrayName&&t.index){var r=e.replace('item','');this.expression=t.arrayName+'.'+t.index,r&&(this.expression+=r)}else this.expression=e;this.data=t,this.value=l.getVal(e,t,this.lazy),i||(i=[]),i.push(this)}}return e.prototype.update=function(){if(this.data&&this.expression&&this.callback){var e=l.getVal(this.expression,this.data,this.lazy),t=this.value;l.equals(e,t)||(this.value=e,this.callback(e))}},e}();t.Watcher=c;var o=function(){function e(e){this.observe(e)}return e.prototype.observe=function(t){var n=this;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){try{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})},e.prototype.defineReactive=function(e,t,n){var o=this;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){l.equals(e,n)||(n=e,o.observe(e),i&&i.forEach(function(e){e.update()}))}})},e}();t.Observer=o;var r=function(){function e(e,t){void 0===t&&(t=!1),this.data=e||{},this.lazy=t}return e.prototype.compile=function(n,e,t,o){var r=this;if(e)switch(n){case'text':this.labelProcess(e,t,function(e,t){e.innerText=void 0===t?'':t});break;case'html':this.labelProcess(e,t,function(e,t){e.innerHtml=void 0===t?'':t});break;case'class':this.labelProcess(e,t,function(e,t){var n=e.className,o=(n=n.replace(t,'').replace(/\\s$/,''))&&String(t)?' ':'';e.className=n+o+t});break;case'model':this.eventProcess(e,t,function(e,t){e.value=t}),'input'===o?e.oninput=function(){l.setTextVal(t,e.value,r.data)}:'switch'===o&&(e.onchange=function(e){l.setTextVal(t,e||'off',r.data)});break;case'if':this.eventProcess(e,t,function(e,t){!0===t?(e.style.display='flex',u.process(e,function(e){s.amc.fn.spmExposure(e.spmId,e.param4Map,e.doNotResume)})):e.style.display='none'});break;case'spm':this.labelProcess(e,t,function(e,t){e.setAttribute('spm',void 0===t?'':t)});break;case'click':this.eventProcess(e,t,function(e,t){l.isFunction(t)?e.onclick=function(){t(e),u.process(e,function(e){s.amc.fn.spmClick(e.spmId,e.param4Map)})}:e.onclick=function(){}});break;default:this.labelProcess(e,t,function(e,t){e[n]=void 0===t?'':t})}},e.prototype.labelProcess=function(n,o,r){var i=this,e=o.match(/@\\{([^}]+)\\}/g),t=l.getTextVal(o,this.data,this.lazy);e&&e.forEach(function(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(new c(t[1],i.data,function(e){r(n,l.getTextVal(o,i.data,i.lazy))},i.lazy).id=i.data.arrayName)}),r(n,t)},e.prototype.eventProcess=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(e),r=l.getObject(e,this.data,this.lazy);o&&1<o.length&&(new c(o[1],this.data,function(e){n(t,e)},this.lazy).id=this.data.arrayName),n(t,r)},e}();t.NodeCompile=r;var u=function(){function e(){}return e.process=function(e,t){var n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&t(o)}catch(e){}},e}(),l=function(){function s(){}return s.item2ArrayIndex=function(e,t){var n=e;if(a.startsWith(e,'item')&&t.arrayName&&t.index){var o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(n+=o)}return n},s.getVal=function(e,t,n){if(e){var o=e.split('.').reduce(function(e,t){return e[t]},t);return n?s.isFunction(o)?o():void 0:o}},s.getTextVal=function(e,r,i){var a=this;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(i)e=a.getVal(t[1],r,i);else{var o=s.item2ArrayIndex(t[1],r);e=a.getVal(o,r,!1)}return void 0===e?'':e})},s.getObject=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length)return this.getVal(o[1],t,n)},s.rmSymbol=function(e){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''},s.setVal=function(e,o,t){var r=e.split('.');return r.reduce(function(e,t,n){return n===r.length-1?e[t]=o:e[t]},t)},s.setTextVal=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(o[1],t,n)},s.equals=function(e,t){return this.eq(e,t,void 0,void 0)},s.eq=function(e,t,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var r=toString.call(e);if(r!==toString.call(t))return!1;switch(r){case'[object RegExp]':case'[object String]':return''+e==''+t;case'[object Number]':return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case'[object Date]':case'[object Boolean]':return+e==+t}var i='[object Array]'===r;if(!i){if('object'!=typeof e||'object'!=typeof t)return!1;var a=e.constructor,s=t.constructor;if(a!==s&&!(this.isFunction(a)&&a instanceof a&&this.isFunction(s)&&s instanceof s)&&'constructor'in e&&'constructor'in t)return!1}o=o||[];for(var c=(n=n||[]).length;c--;)if(n[c]===e)return o[c]===t;if(n.push(e),o.push(t),i){if((c=e.length)!==t.length)return!1;for(;c--;)if(!this.eq(e[c],t[c],n,o))return!1}else{var u=Object.keys(e),l=void 0;if(c=u.length,Object.keys(t).length!==c)return!1;for(;c--;)if(l=u[c],!t.hasOwnProperty(l)||!this.eq(e[l],t[l],n,o))return!1}return n.pop(),o.pop(),!0},s.isFunction=function(e){return'function'==typeof e||!1},s}();t.vueUtils=l},function(e,t,n){'use strict';var o,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var i=function(){function i(){this.enabled=!0}return i.fmtLine=function(e,t,n,o){var r='';return o&&(r=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+e+']['+i.fmtTime()+']['+t+']'+n+' '+r},i.fmtTime=function(){var e=new Date;return e.getHours()+':'+e.getMinutes()+':'+e.getSeconds()+'.'+e.getMilliseconds()},i.prototype.enable=function(){this.enabled=!0},i.prototype.disable=function(){this.enabled=!1},i}();t.Logger=i,t.logger=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.e=function(e,t,n){},t.prototype.i=function(e,t,n){},t}(i))},function(e,t,n){'use strict';var o,r,i;Object.defineProperty(t,'__esModule',{value:!0}),t.VI_CHANNEL_MODE_FROM_TEMPLATE='1',(i=t.SCALE_FACTOR||(t.SCALE_FACTOR={})).LEVEL_0='0',i.LEVEL_1='1',i.LEVEL_2='2',i.LEVEL_3='3',i.LEVEL_4='4',(r=t.ProductScene||(t.ProductScene={})).FamilyPay='familyPay',r.MoneyTurnOver='MONEY_TURNOVER',(o=t.ChannelType||(t.ChannelType={})).Scan='code',o.WeiXin='shareToken',o.CopyToken='copyToken',o.Recommend='recommend',o.Search='search'},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var a=n(1);t.mergeObject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(e&&e.length)for(var o=0;o<e.length;o++){var r=e[o];if(a.amc.fn.isObject(r))for(var i in r)r.hasOwnProperty(i)&&(n[i]=r[i])}return n},t.isPreRender=function(e){return e&&(e.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender)},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},t.doNothing=function(){},t.tryJSONParse=function(e){if(a.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}},t.checkEmptyObj=function(e){return a.amc.fn.isString(e)?0===e.length:!(e&&0!==Object.keys(e).length)},t.substrWithFontWidth=function(e,t,n){if(!e)return e;for(var o='',r=0,i=e.length,a=0;a<i;a++){var s=n?e[i-a-1]:e[a];if(/^[A-Za-z0-9\\(\\)]*$/.test(s)?r+=.45:r++,o+=s,t-1<r)break}return o},t.calculateFontWidth=function(e){if(!e)return 0;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++)n.test(e[o])?t+=.45:t++;return Math.round(t)},t.deepCopy=function e(t){if(null==t||'object'!=typeof t)return t;var n;if(t instanceof Date)return(n=new Date).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(var o=0,r=t.length;o<r;o++)n[o]=e(t[o]);return n}if(t instanceof Object){for(var i in n={},t)t.hasOwnProperty(i)&&(n[i]=e(t[i]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},t.getConfig=function(e,t){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:e},function(e){t(e.available)})},20)},t.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},t.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)},t.locLogin=function(t){!a.amc.isSDK&&a.amc.fn.sdkGreaterThanOrEqual('10.8.44')?document.invoke('login',{noretry:!0,loginState:0,moveToBack:!1},function(e){t&&t(e)}):t&&t({isLogin:!0})}}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body", "tag": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "message-result-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0106", "tplId": "QUICKPAY@message-result-flex", "tplVersion": "5.5.1"}