{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var rpc = amc.rpcData;\n            var getTag = amc.fn.getById;\n            var gModuleName = \"BANK_RISK_CARD\";\n            var gWindowHeight = window.innerHeight;\n\n            function mockData() {\n                rpc = {\n                    index : 1,\n                    body_title : '{{input_bank_info}}',\n                    form_cardno :  '{{card_no}}',\n                    form_cardno_placeholder : '{{card_no_placeholder}}',\n                    form_certno : '{{id_no}}',\n                    form_certno_placeholder : '{{id_no_placeholder}}', //需要$1替换\n                    form_mobileno : '{{mobile_no}}',\n                    form_mobileno_placeholder : '{{mobile_no_placeholder}}',\n                    form_tip : '{{input_bank_tip}}',\n                    next_button : '{{next}}',\n                    form_title_m : \"通过$1银行卡验证\",\n\n                    userName : \"**马\",\n                    HAS_OTHERS : true,\n                    form_title : '{{verify_bank_info}}',\n                    fast_top_tip : '{{card_binded_already}}',\n                    bind_top_tip : '{{bind_new_card}}',\n                    bind_new_card_content : '{{bind_new_card_content}}',\n                    riskLevel : \"BANK_RISK_TWO\",\n                    isSupportBindCard : true,\n                    verifyAction : \"VERIFY_BANK_RISK_ONE\",\n                    cardInfoList : [\n                        {\n                            bankCardType : \"Debit Card\", //银行卡类型\n                            bankName : \"China merchants bank\", //银行卡名称\n                            bankCardLast4Num : \"8839\", //银行卡后四位\n                            bankCardLen : 16, //银行卡长度\n                            index: 1, //回传给服务端\n                            bankCardHolderCertNoType : 1, //绑定银行卡的证件类型\n                        },\n                        {\n                            bankCardType : \"储蓄卡\", //银行卡类型\n                            bankName : \"招商银行\", //银行卡名称\n                            bankCardLast4Num : \"8839\", //银行卡后四位\n                            bankCardLen : 19, //银行卡长度\n                            index: 2, //回传给服务端\n                            bankCardHolderCertNoType : 1, //绑定银行卡的证件类型\n                        }\n                    ],\n                    risk_button2 : \"其他核身方式\"\n                }\n            }\n            \n            function init() {\n                try {\n                    // mockData();\n                    \n                    initUI();\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit(true);\n                    }\n                } \n                catch(e) {\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit();\n                    }\n                }\n            }\n\n            function initUI() {\n                var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', null, null, null, onBack, null);\n                getTag(\"bodyContainer\").insertBefore(nav, getTag(\"mainBody\"));\n              \n                //高度固定\n                var naviAndStatusBarHeight = amc.specs.navHeight;\n                if (amc.isIPhoneX) {\n                    naviAndStatusBarHeight = 88;\n                }\n\n                var mainTitle = rpc.form_title_m || '{{verify_bank_info}}';\n                if (mainTitle.indexOf(\"$1\") != -1) {\n                    mainTitle = mainTitle.replace(\"$1\",rpc.userName);\n                }  \n                getTag(\"mainTitle\").innerText = mainTitle;\n\n                var footerContainerHeight = 110;\n                \n                if (amc.isIPhoneX) {\n                    footerContainerHeight += 4;\n                }\n                //如果没有下发其他核身方式，银行卡内容高度剔除footer \n                if (!Boolean(rpc.HAS_OTHERS)) {\n                    footerContainerHeight = 0;\n                }\n                //控制内容高度\n                var bankContent = getTag(\"bankContent\");\n                bankContent.style.height = (gWindowHeight - naviAndStatusBarHeight - 65 - footerContainerHeight) + \"px\";\n\n                //创建卡列表\n                createBankList();\n\n                var unbindedTip = rpc.bind_top_tip || '{{bind_new_card}}';\n                getTag(\"unbindedTip\").innerText = unbindedTip;\n\n                //创建添加新卡模块\n                createAddNewCardContainer();\n\n                //其他核身方式\n                getTag(\"changeModule\").innerText = rpc.risk_button2 || '{{change_other_way}}';\n                if (Boolean(rpc.HAS_OTHERS)) {\n                    getTag(\"changeModule\").style.visibility = \"visible\";\n                }\n\n                //判断加新卡模块是否要显示\n                getTag(\"unbindedTip\").style.visibility = \"hidden\";\n                if (Boolean(rpc.isSupportBindCard)) {\n                    //只有帮新卡与已绑卡同时显示时，才要unbindedTip\n                    if (rpc.cardInfoList && rpc.cardInfoList.length > 0) {\n                        getTag(\"unbindedTip\").style.visibility = \"visible\";\n                    }\n                    getTag(\"unbindedBankContainer\").style.visibility = \"visible\";\n                }\n                else {\n                    getTag(\"unbindedBankContainer\").style.visibility = \"hidden\";\n                }\n            }\n\n            function createBankList() {\n                if (!rpc.cardInfoList || rpc.cardInfoList.length == 0) {\n                    getTag(\"bindedTip\").style.display = \"none\";\n                    getTag(\"bindedBankContainer\").style.display = \"none\";\n                    return;\n                }\n\n                //已绑定title\n                var bindedTip = rpc.fast_top_tip || '{{card_binded_already}}';\n                getTag(\"bindedTip\").innerText = bindedTip;\n\n                var bindedBankContainer = getTag(\"bindedBankContainer\"); \n                for (var i = 0; i < rpc.cardInfoList.length; i++) {\n                    var bankInfo = rpc.cardInfoList[i];\n                    var container = amc.fn.create(\"div\", \"bank-card-container\", bindedBankContainer);\n                    //label\n                    var label = amc.fn.create(\"label\", \"bank-info-label\", container);\n                    label.style.width = window.innerWidth - 2 * 12 - 8.5 - 15;\n                    label.innerText = bankInfo.bankName + \" \" + bankInfo.bankCardType + \" \" + \"(\" + bankInfo.bankCardLast4Num + \")\";\n                    //icon\n                    var icon = amc.fn.create(\"img\", \"bank-info-detail-img\", container);\n                    icon.src = mic.path + \"alipay_vi_more\";\n                    //插入一条分割线\n                    amc.fn.create(\"div\", \"bank-card-sep-line\", bindedBankContainer);\n\n                    (function(index){\n                        container.onclick = function() {\n                            verifyBankCardHandler(index);\n                        }\n                    }(i));\n                }\n            }\n\n            function createAddNewCardContainer() {\n                var unbindedBankContainer = getTag(\"unbindedBankContainer\"); \n                var container = amc.fn.create(\"div\", \"bank-card-container\", unbindedBankContainer);\n                var label = amc.fn.create(\"label\", \"bank-info-label\", container);\n                label.style.width = window.innerWidth - 2 * 12 - 8.5 - 15;\n                var text = rpc.bind_new_card_content || '{{bind_new_card_content}}';\n                if (text.indexOf(\"$1\") != -1) {\n                    text = text.replace(\"$1\",rpc.userName);\n                }  \n                label.innerText = text;\n                //icon\n                var icon = amc.fn.create(\"img\", \"bank-info-detail-img\", container);\n                icon.src = mic.path + \"alipay_vi_more\";\n                //插入一条分割线\n                amc.fn.create(\"div\", \"bank-card-sep-line\", unbindedBankContainer);\n                container.onclick = bindNewBankCardHandler;\n            }\n\n            function bindNewBankCardHandler() {\n                //提交到native，然后通过native去启动绑新卡\n                var obj = {\n                    \"eventName\": \"vi_go_bank_card_bind\",\n                    \"moduleName\": gModuleName,\n                };\n                document.asyncSubmit(obj, function(data) {\n                    \n                });\n            }\n\n            function verifyBankCardHandler(index) {\n                //提交到native，然后通过native去验证卡\n                var obj = {\n                    \"eventName\": \"vi_go_bank_card_verify\",\n                    \"moduleName\": gModuleName,\n                    \"index\": index\n                };\n                document.asyncSubmit(obj, function(data) {\n                    \n                });\n            }\n\n            function onKeyDown() {\n                if (event.which == 4) {\n                    onBack();\n                }\n            }\n            \n            function onBack() {\n                obj = {\n                    \"eventName\" : \"vi_quit_module_with_retrieve\",\n                    \"exitType\" :\"bank_exit\"\n                };\n                document.submit(obj);\n            }\n\n            function changeModule() {\n                mic.fn.changeModule();\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n                background-color: #f5f5f5;\n                flex-direction: column;\n                display: flex;\n            }\n\n            .top-container {\n                background-color: #ffffff;\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n            }\n        \n            .main-title-label {\n                text-align: center;\n                font-size: 20px;\n                font-weight: bold;\n                color: #333333;\n                line-height: 28px;\n                margin-top: 19px;\n                margin-left: 30px;\n                margin-right: 30px;\n                margin-bottom: 19px;\n            }\n\n            .bank-content {\n                display: flex;\n                flex-direction: column;\n                overflow: scroll;\n            }\n\n            .binded-bank-container {\n                display: flex;\n                background-color: #ffffff;\n                flex-direction: column;\n            }\n\n            .bank-info-label {\n                height: 27px;\n                font-size: 19px;\n                color: #333333;\n                overflow: hidden;\n                text-overflow: ellipsis;\n                white-space: nowrap;\n            }\n            \n            .tip-label {\n                text-align: left;\n                font-size: 15px;\n                color: #999999;\n                line-height: 22px;\n                margin-top: 24px;\n                margin-left: 12px;\n                margin-right: 12px;\n                margin-bottom: 8px;\n            }\n\n            .bank-card-container {\n                background-color: #ffffff;\n                height: 60px;\n                display: flex;\n                flex-direction: row;\n                align-items: center;\n                padding-left: 12px;\n                padding-right: 12px;\n            }\n\n            .bank-info-detail-img {\n                width: 8.5px;\n                height: 15px;\n                margin-left: 15px;\n            }\n\n            .bank-card-sep-line {\n                height: 0.5px;\n                background-color: #eeeeee;\n                margin-left: 12px;\n            }\n\n            .unbinded-bank-container {\n                display: flex;\n                background-color: #ffffff;\n                flex-direction: column;\n            }\n\n            .change-module-label {\n                text-align: center;\n                font-size: 18px;\n                color: #1677FF;\n                line-height: 22px;\n                margin-top: 10px;\n                margin-bottom: 19px;\n                width: 180px;\n                align-self: center;\n                visibility: hidden; \n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "mic-fullscreen main-body", "children": [{"css": "top-container", "children": [{"css": "main-title-label", "tag": "label", "id": "mainTitle"}], "tag": "div", "id": "topContainer"}, {"css": "bank-content", "children": [{"css": "tip-label", "tag": "label", "id": "bindedTip"}, {"css": "binded-bank-container", "tag": "div", "id": "bindedBankContainer"}, {"css": "tip-label", "tag": "label", "id": "unbindedTip"}, {"css": "unbinded-bank-container", "tag": "div", "id": "unbindedBankContainer"}], "tag": "div", "id": "bankContent"}, {"css": "change-module-label", "onclick": "changeModule()", "tag": "label", "id": "changeModule"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "bankRiskCard", "format": "JSON", "tag": "MOBILEIC", "time": "0028", "tplId": "MOBILEIC@bankRiskCard", "tplVersion": "5.5.3"}