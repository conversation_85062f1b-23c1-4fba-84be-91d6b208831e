<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mtrl_calendar_text_input_frame"
    android:paddingLeft="@dimen/mtrl_calendar_content_padding"
    android:paddingTop="@dimen/mtrl_calendar_text_input_padding_top"
    android:paddingRight="@dimen/mtrl_calendar_content_padding"
    android:paddingBottom="16dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/mtrl_picker_text_input_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <com.google.android.material.textfield.TextInputEditText
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/mtrl_picker_text_input_date_hint"
            android:inputType="date"
            android:imeOptions="flagNoExtractUi"/>
    </com.google.android.material.textfield.TextInputLayout>
</FrameLayout>
