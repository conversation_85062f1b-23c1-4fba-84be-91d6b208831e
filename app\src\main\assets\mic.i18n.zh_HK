{"zh_HK": {"got_it": "我知道了", "changeOtherMethod": "選擇其他驗證方式", "alipay": "支付寶", "deduct_dlg_order": "扣款順序：請查看「支付寶-我的-頭像-設置-支付設置-扣款順序」", "code": "校驗碼", "protocol": "協定", "help": "幫助", "confirm_open": "確定開通", "pay_ok": "付款成功", "user_name_title": "持卡人姓名", "sms_help_title": "收不到校驗碼", "last_name": "姓", "open_pcredit_service": "正在開通螞蟻花唄服務", "deduct_dlg_title": "信用免密支付說明", "auth_login": "授權登入", "pcredit_user_protocol": "螞蟻花唄使用者服務合同", "card_info_tip": "請填寫銀行卡資料", "opening_text": "正在開通...", "please_wait": "請稍等", "dotspot": "、", "phone_info": "銀行預留的手機號碼是辦理該銀行卡時所填寫的手機號碼。沒有預留、手機號忘記或者已停用，請聯繫銀行客服更新處理。大陸手機號為11位數字，非大陸手機號為「國家代碼-手機號碼」形式。", "repeat_confirm": "請再次填寫以確認", "activity_share": "分享", "pcredit_serve_protocol": "《花唄服務協定》", "and_so_on": "等", "phone_validate": "驗證手機號碼", "retry_after_full": "#val#秒後重試", "alipay_serve_protocol": "支付寶服務協定", "write_card_info": "填寫銀行卡資訊", "phone_sms_full": "本操作需要短訊確認，請輸入 <font color='#000'>#val#</font> 收到的短訊校驗碼", "skip": "跳過", "deduct_dlg_inform": "扣款成功後，支付寶會以客戶端消息或短訊方式通知你", "mail": "郵編", "select_account": "選擇賬戶", "save": "保存", "tb": "淘寶", "card_no": "卡號", "use_alipay_account": "使用支付寶帳戶", "paying": "正在付款...", "id_validate": "身份驗證", "return": "返回", "pay_spwd": "本次支付成功\n設置支付密碼，用於支付寶付款\n建議勿與銀行卡密碼相同", "use_new_card": "使用新卡付款", "order_info": "訂單資訊", "auth": "授權", "open_pcredit_ad": "開通花唄，搶5元紅包", "name_tip_2": "若更改過姓名，可聯絡銀行處理。", "personal_info": "訪問個人信息", "email_f_error": "電子郵箱格式不正確", "store_card": "儲蓄卡", "full_name_format_error": "姓名格式錯誤，需在名和姓之間添加空格", "state_province": "州/省", "alipay_loginpwd": "支付寶登入密碼", "xx_currency_unit": "#val#元", "name": "姓名", "add_card": "添加銀行卡", "no_pwd_full": "支付金額少於#val#，無需支付密碼。", "sel_dis": "更換優惠", "forexprod_notice": "支付寶需要對此筆交易的用戶進行實名驗證，請填寫你的真實姓名及身份證號碼。", "name_tip_3": "獲取更多幫助，請致電支付寶客戶服務熱線<font color='#1677ff'>95188</font>。", "read_agree": "我已閱讀并同意", "card_type": "卡類型", "cashier_no_tip_5": "瞭解詳情，請致電支付寶客戶服務熱線<font color='#1677ff'>95188</font>。", "phone_no": "手機號", "set_pwd": "設置支付密碼", "write_phone_no": "填寫手機號碼", "cashier_no_tip_1": "安全保障：賬戶保護、實時監控、緊急凍結等多重金融安全保障。", "cashier_no_tip_2": "雙重驗證：每次付款均驗證支付密碼，大額支付還需短訊驗證。", "cashier_no_tip_3": "私隱保護：高強度數據加密，保護用戶私隱資料。", "auth_sth": "使用支付寶帳戶#val#", "country_area": "國家/地區", "money_limit": "充值金額超限", "cashier_no_dlg_title": "安全保障", "spwd_for_pay": "該6位元數位密碼將用於支付", "payment_text": "資金安全由眾安保險承擔，極速全額賠付", "sms_tip_1": "請確認該預留手機號碼是否為目前使用的手機號碼。", "confirm_agree": "確認即表示同意", "sel_state_province": "選擇 州（省）", "card_three_num": "卡背面末三位元數字", "input_id_no": "請輸入身份證號碼", "sms_tip_3": "由於營運商網絡原因，可能存在短訊延遲，請耐心等待或稍後再試。", "use_alipay_auth": "使用支付寶帳戶授權", "input_valid_cvv2": "請輸入正確的安全碼", "phone_tip_4": "獲取更多幫助，請致電支付寶客戶服務熱線<font color='#1677ff'>95188</font>。", "address_info": "訪問收貨信息", "auth_fingering": "正在驗證指紋...", "safepwd_verify_complete": "校驗成功", "pay_tool": "付款方式", "choose_dis": "選擇優惠", "auth_finger_or": "請驗證指紋或者", "account_info_tip": "包含您的支付寶賬戶名等信息，但不包含密碼信息。", "sel": "更換", "validity_info": "有效期是列印在信用卡正面卡號下方，標準格式為月份在前，年份在後的一串數字。", "last_three_num": "卡背面后三位数字", "charge_fail": "無法完成付款，此銀行卡餘額不足。", "confirm_auth": "確定授權", "first_name": "名", "cvv_text": "安全碼", "input_pay_pwd": "輸入支付密碼", "system_error": "系統出錯", "agree_protocol": "同意協議", "other_login_way": "其他登錄方式", "wait_retry": "等待重試", "new_card_deposit": "使用新卡充值", "limit_info_local": "余額的支付限額由賬戶認證等級、收款卡是否為同名卡等因素共同確定，轉賬信息提交後會確定是否超限。\n\n銀行卡的支付限額是由銀行設定，購物支付、轉賬、還款繳費等場景限額共享，<font color=\"#ff8208\">無法提升</font>。若提示超限，建議更換其他付款方式。", "select_account_full": "手機號#val#已關聯以下支付寶賬戶，請選擇", "exit": "登出", "finger": "指紋", "bill_address": "帳單地址", "deposit_info": "充值提示資訊", "paypwd": "支付密碼", "confirm_cancel_pay": "確定要放棄付款？", "email": "郵箱", "use_phone_login": "使用手機號登錄", "bank_no": "銀行卡號", "forget_pwd_text": "忘記密碼", "confirm_again": "請再次填寫以確認", "input_name_on_card": "卡片上的姓名", "card_max_deposit_full": "該卡本次最多可充值#val#元", "input_answer": "輸入答案", "sms_tip_4": "若銀行預留手機號碼已停用，請聯絡銀行客戶服務部處理。", "open_pcredit": "開通螞蟻花唄", "input_email": "請輸入郵箱", "paypwd_nologin": "是支付密碼，不是登錄密碼", "confirm_upgrade": "確定升級", "confirm_fqg": "確認分期", "email_format_error": "郵箱格式錯誤，請重新輸入", "phone_tip_1": "銀行預留手機號碼是你在辦理該銀行卡時所填寫的手機號碼。", "insuranced": "付款金額小於200元，無需輸入支付密碼。", "confirm_exit": "確定要退出嗎?", "pwd-notice": "密碼不能為連續、重複數字或者你的生日", "open_pcredit_agree": "開通花唄即同意", "input_pwd_s": "輸入密碼", "address": "地址", "line_alipay_select": "已關聯以下支付寶賬戶，請選擇", "bank_info_tip": "請填寫銀行預留資料", "some_money": "一定數額", "alipay_finger": "支付寶指紋支付", "alipay_auth": "支付寶授權", "cashier_no_dlg_btn": "知道了", "tb_finger": "淘寶指紋支付", "sel_country_area": "選擇國家和地區", "promotion_sorry": "耽誤您的時間，我們深表歉意", "input_alipay_account": "請輸入支付寶賬戶名", "input_bank_phone": "請輸入銀行預留手機號", "country": "國家", "pwd-error-repeat": "兩次密碼輸入不一致，請重新輸入", "account_deposit": "帳戶充值", "pay_agree": "付款即同意", "validity_text": "有效期", "pay_success": "支付成功", "pcredit_ad": "本訂單支援花唄付款，先消費，後還款，0利息。", "month_year": "月份/年份", "login": "登錄", "use_other_account": "使用其他賬戶", "login_alipay": "登錄支付寶帳號", "info": "提示", "no_method": "無可用付款方式", "pay_right_now": "立即付款", "id_info_tip": "請輸入證件號碼", "open_pcredit_protocol": "《螞蟻花唄用戶服務合同》、《芝麻信用服務協議及授權》", "forget_pwd": "忘記密碼?", "alipay_agree": "《支付寶賬戶互通協議》", "alipay_with_sth": "支付寶#val#", "webbank": "網商銀行", "cancel": "取消", "base_info_tip": "包含您的姓名、性別等信息", "input_deposit_money": "請輸入充值金額", "sel_id_no": "選擇證件號", "discount_detail": "優惠明細", "city": "城市", "pcredit_pay_m": "，下月", "id_card_tail": "證件的最後6位", "id_card": "身份證", "switch_account": "切換帳戶", "next": "下一步", "auth_bracelet": "驗證手環...", "pcredit_protocol": "芝麻信用服務協定", "phone_text": "電話", "sel_pay_tool": "選擇付款方式", "input_valid": "輸入有效期", "info_sec": "資訊加密處理，僅用於銀行驗證", "finger_pay_notice": "你同時開通以下指紋支付功能：", "bind_webbank_account": "請關聯與網商銀行身份資訊一致的帳戶", "open_pcredit_pay": "開通並使用花唄付款", "pay_n_loginpwd": "支付寶支付密碼，不是登入密碼", "spwd_pay": "設置支付密碼，用於支付寶付款\n建議勿與銀行卡密碼相同", "complete": "完成", "pwd_add_card": "請輸入支付密碼，以添加新的銀行卡", "please_input": "請輸入", "finger_pay_text": "開通指紋支付，讓付款更安全便捷", "validity": "有效期說明", "spot": "。", "promotion_crowd": "顧客太多，客官請稍候", "phone": "手機號說明", "pwd": "密碼", "input_name": "請輸入持卡人姓名", "resend_sms": "重發驗證碼", "alipay_account": "支付寶帳號", "alipaypwd": "支付寶支付密碼", "use_alipay_account_full": "使用支付寶帳戶#val#授權", "confirm_pay": "確認付款", "phone_tip_2": "沒有預留、忘記了或者已停用手機號碼，可聯絡銀行客戶服務部更新處理。", "account": "帳戶", "auth_qrcode_info": "付款碼、交易信息", "input_pwd": "請輸入支付密碼", "look": "查看", "cvv2_format_error": "安全碼格式錯誤，請重新輸入", "account_bind": "賬戶綁定", "cvv": "安全碼說明", "finger_process": "指紋支付處理中", "id_no": "證件號", "cvv_info": "安全碼是列印在信用卡背面簽名區域的一組數位，一般是後3位元或者4位元數字。", "wait_text": "稍等一下，馬上輪到你", "phone_sms": "請輸入短信校驗碼", "retry_finger": "指紋校驗失敗，請重試", "input_right_phone": "請輸入正確的手機號碼", "finger_protocol": "《指紋支付相關協議》", "user_agreement": "用戶協議", "safe_question": "回答安全保護問題，進行身份驗證。", "agree": "同意", "please_input_full": "請輸入#val#號碼", "base_info": "訪問基本信息", "authing": "正在授權...", "personal_info_tip": "包含手機號碼、證件號碼等信息。", "money": "金額", "ad": "廣告", "app_auth": "該應用將獲得以下許可權", "bank_name": "銀行名字", "input_right_id_card": "你的證件類型或證件號有誤", "pwdupgrade_text": "升級6位元支付密碼，付款更便捷", "address_info_tip": "包含您的收貨人姓名、地址、郵編等信息。", "limit_info": "限額說明", "money_unit": "元", "pcredit_text_full": "先消費，下月#val#用支付寶還款，0費用", "activity_activity": "活動", "safe_bank_no": "填寫以下銀行卡卡號，進行身份驗證。", "can_insuranced": "免費獲贈帳戶安全險，最高保額100萬！付款金額小於200元，無需輸入支付密碼。", "sms_tip_2": "請查看短訊是否被安全軟件攔截。", "waika_cvv": "安全碼  CVV", "confirm_btn": "確定", "confirming_upgrade": "正在確定...", "paypwd_bind": "請輸入支付密碼，已完成賬戶綁定", "input_expire_ccr": "請輸入付款信用卡的有效期", "sms_tip_5": "獲取更多幫助，請致電支付寶客戶服務熱線<font color='#1677ff'>95188</font>。", "no": "號碼", "pay": "付款", "upgrading": "正在升級…", "new_card_placeholder": "無需網上銀行/免手續費", "phone_tip_3": "若你是海外用戶，請按照區號-手機號碼的形式輸入。", "papers_type": "證件類型", "payment_info": "付款信息", "setting": "設置", "auth_bic": "驗證智慧設備...", "serve_protocol": "服務協定", "serve_protocol_full": "《服務協定》", "credit_quota_full": "信用額度#val#元", "pwdupgrade": "升級支付密碼", "account_info": "訪問賬戶信息", "retry_again": "再試一次", "open_npwd": "開通小額免密", "resend_after_full": "#val#秒後重發", "use_pcredit_agree": "使用花唄即同意", "id_card_only": "提醒：該銀行僅支持大陸身份證驗證", "need_pay": "需付款", "open_finger_pay": "開通指紋支付", "baoxian_protocol": "保險條款", "sms_help": "收不到校驗碼？", "pay_detail": "確認付款", "other_charge_way": "其他方式付款", "credit_card": "信用卡", "waika_validity": "有效期  MM/YY", "sms_code": "填寫校驗碼", "activity_bonus_points": "積分獎勵", "pcredit_pay": "正在用螞蟻花唄付款", "tail_no": "尾號#val#", "confirm_sth": "確定#val#", "safepwd_verifying": "正在校驗...", "alipay_login_id": "支付寶帳號#val#", "open_account_fingerpay": "開通以下支付寶帳戶的指紋支付", "bind_alipay_account": "關聯以下支付寶賬戶", "i_know_it": "知道了", "email_address": "電子郵箱地址", "alipay_sec": "支付寶智能加密，保障你的用卡安全，<font color='#1677ff'>瞭解安全保障</font>", "open_pcredit_and_pay": "開通花唄并付款", "name_tip_1": "持卡人姓名是你在辦理該銀行卡時所填寫的姓名。", "cashier_no_tip_4": "支付理賠：支付安全由螞蟻保險承保。", "access_account_info": "#val#請求訪問以下支付寶信息：", "phone_notice": "電話號碼是6 - 20個字元", "invalid_word": "輸入字元不符合規則。", "credit_amount_full": "本訂單支持螞蟻花唄付款，先消費，下月#val#還款，0利息。因您信用良好，獲得花唄透支額度#val#元。", "deposit": "充值成功", "id_card_tip": "僅第18位數可以輸入字母X", "activity_comment": "评价", "safe_id_card": "填寫你實名認證時的證件資訊，進行身份驗證。", "pwd_id_validate": "請輸入支付密碼，完成身份驗證", "phone_card_deposit": "使用話費卡充值", "finger_pay": "指紋支付", "dot": "，", "activity_score_full": "獲得#acquire#會員積分，累計#total#分", "sms_alert_message": "1，請確認當前手機號是否仍在使用，若不再使用，請至【我的】-【右上角設定】-【手機號】 - 【更換手機號】。\n2，請檢視短訊是否被安全軟體攔截，若是雙卡雙待手機，請檢查副卡短訊情況。\n3，由於運營商網路原因，可能存在短訊延遲，請耐心等待或稍後再試。\n4，若您最近操作過攜號轉台，請等待1-2天後再試。", "safe_remind": "安全提醒", "input_email_code": "輸入電郵認證碼", "email_code_has_sent": "我們已把郵件認證碼傳到你電子郵件信箱", "email_resend_second": "秒後重發", "email_resend": "重發認證碼", "change_other_way": "換個認證方式", "email_back_msg": "郵件認證碼即將到達，建議耐心等待。若長時間未到達，請查看是否在垃圾郵件中", "give_up": "放棄", "input_bank_info": "請輸入銀行卡訊息", "card_no_placeholder": "請輸入完整卡號", "id_no_placeholder": "持卡人的證件號碼", "mobile_no": "手機號碼", "mobile_no_placeholder": "銀行卡預留手機號", "input_bank_tip": "以上內容僅用於驗證身分，支付寶將嚴格保密", "verify_bank_info": "透過你本人的銀行卡訊息驗證身分", "card_binded_already": "已綁定銀行卡", "bind_new_card": "也可以綁定新卡", "bind_new_card_content": "添加你本人的銀行卡", "bind_card_sms_code": "認證碼", "bind_card_sms_code_placeholder": "請輸入短訊認證碼", "bind_card_sms_resend": "重新發送", "bind_card_sms_tip": "短訊認證碼已發送至銀行預留手機號碼，請查收", "sms_back_msg": "簡訊有可能延遲，請再等一會", "sms_back_wait": "等待", "sms_back_quit": "放棄", "sms_alert_bank_message": "1，請確認該銀行預留手機號是否為當前使用手機號。\n2，請查看簡訊是否被安全軟件攔截，若是雙卡雙待手機，請檢查副卡簡訊情况。\n3，由於運營商網絡原因，可能存在簡訊延遲，請耐心等待或稍後再試。\n4，若銀行預留手機號已停用，請聯系銀行客服處理。\n5，若您最近操作過攜號轉網，請等待1-2天后再試。"}}