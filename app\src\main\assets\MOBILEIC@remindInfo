{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var rpc = amc.rpcData;\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var getTag = amc.fn.getById;\n        var gRemindInfo = {};\n\n        function init() {\n            var remindInfo = {};\n            try {\n                remindInfo = JSON.parse(rpc['remindInfo']);\n            } \n            catch (error) {\n                print(\"fail to get data: \" + error.message);\n            }\n            \n            //包了一层\n            gRemindInfo = remindInfo || {};          \n\n            //初始化弹窗\n            initUI();\n        }\n\n        function initUI() {\n            //icon\n            if (gRemindInfo.icon && gRemindInfo.icon.length > 0) {\n                getTag(\"icon\").src = gRemindInfo.icon;\n            }\n            else {\n                getTag(\"icon\").src = mic.path + \"alipay_vi_warning\";\n            }\n\n            //主文案\n            if (gRemindInfo.header && gRemindInfo.header.length > 0) {\n                var mainText = getTag(\"mainText\");\n                mainText.innerText = gRemindInfo.header;\n            }\n            \n            //次文案\n            if (gRemindInfo.text && gRemindInfo.text.length > 0) {\n                //根据<p>切分，如果只有一段文案的话，居中，如果多段的话每段前面加点，左对齐\n                var subTextContainer = getTag(\"subTextContainer\");\n                var subTextArray = gRemindInfo.text.split(\"<p>\");\n                if (subTextArray.length > 1) {\n                    for (var i = 0; i < subTextArray.length; i++) {\n                        var item = subTextArray[i];\n                        var container = amc.fn.create(\"div\", \"sub-text-item-container\", subTextContainer);\n\n                        //dot\n                        var dot = amc.fn.create(\"div\", \"sub-text-dot\", container);\n\n                        //label\n                        var label = amc.fn.create(\"label\", \"sub-text-left\", container);\n                        label.innerText = item;\n                    }\n                }\n                else {\n                    var label = amc.fn.create(\"label\", \"sub-text-center\", subTextContainer);\n                    label.innerText = gRemindInfo.text;\n                }\n            }\n\n            //按钮\n            getTag(\"btnCancel\").innerText = gRemindInfo.cancel || \"取消付款\";\n            getTag(\"btnConfirm\").innerText = gRemindInfo.confirm || \"继续付款\";\n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                // 屏蔽物理back键\n            }\n        }\n\n        function onBtnCancel() {\n            onAlertAction(\"N\");\n        }\n\n        function onBtnConfirm() {\n            onAlertAction(\"Y\");\n        }\n\n        function onAlertAction(operation) {\n            var obj = {};\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['showLoading'] = 'true';\n            obj['actionName'] = rpc.verifyAction;\n            var operationObj = {'operation': operation};\n            obj['params'] = operationObj;\n\n            document.asyncSubmit(obj, function(data) {\n                var isFinishWithSuccess = Boolean(data['finish']) && data['finishCode'] === '1000';\n                var isNotFinishWithSuccess = !Boolean(data['finish']) && Boolean(data['verifySuccess']);\n\n                if (Boolean(rpc['VISwitchConfig']['remindNext']) && (isFinishWithSuccess || isNotFinishWithSuccess)) {\n                    mic.fn.onBackWithResponse(data);\n                } else {\n                    if ('Y' === operation) {\n                        var result = {};\n                        result['finish'] = true;\n                        result['success'] = true;\n                        result['verifySuccess'] = true;\n                        result['finishCode'] = '1000';\n                        result['bizResponseData'] = data['bizResponseData'];\n                        result['verifyId'] = data['verifyId'];\n                        mic.fn.onBackWithResponse(result);\n                    } else {\n                        var result = {};\n                        result['eventName'] = 'vi_quit_module';\n                        document.submit(result);\n                    }\n                }\n            });\n            hide('mainBody');\n        }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".root {\n            display: flex;\n            align-items: center;\n            background-color:rgba(0, 0, 0, 0.3);\n            flex-direction: column;\n            height: 100%;\n            justify-content: center;\n        }\n\n        .main-body {\n            width: 270px;\n            background-color: #ffffff;\n            border-radius: 7px;\n            display: flex;\n            flex-direction: column;\n            padding-top: 37px;\n        }\n\n        .icon-container {\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            margin-bottom: 20px;\n        }\n\n        .icon {\n            width: 60px;\n            height: 60px;\n        }\n\n        .main-text {\n            font-size: 18px;\n            color: #000000;\n            line-height: 21px;\n            text-align: center;\n            margin-left: 15px;\n            margin-right: 15px;\n            margin-bottom: 16px;\n        }\n\n        .sub-text-container {\n            display: flex;\n            flex-direction: column;\n            padding-left: 15px;\n            padding-right: 15px;\n        }\n\n        .sub-text-center {\n            text-align: center;\n            color: #888888;\n            font-size: 15px;\n            line-height: 20px;\n            margin-bottom: 8px;\n        }\n\n        .sub-text-item-container {\n            display: flex;\n            flex-direction: row;\n        }\n\n        .sub-text-dot {\n            width: 4px;\n            height: 4px;\n            background-color: #888888;\n            border-radius: 2px;\n            margin-top: 8px;\n            margin-right: 6px;\n        }\n\n        .sub-text-left {\n            text-align: left;\n            color: #888888;\n            font-size: 15px;\n            line-height: 20px;\n            margin-bottom: 8px;\n            width: 230px;\n        }\n\n        .horizontal-line {\n            width: 100%;\n            margin-top: 14px;\n            height: 0.5px;\n            background-color: #e5e5e5;\n        }\n\n        .btn-container {\n            width: 100%;\n            height: 50px;\n            display: flex;\n            flex-direction: row;\n        }\n\n        .btn-cancel {\n            font-size: 18px;\n            color: #000000;\n            text-align: center;\n            height: 50px;\n            width: 135px;\n        }\n\n        .veritcal-line {\n            background-color:#e5e5e5;\n            width: 0.5px;\n            height: 100%;\n            align-self: center;\n        }\n\n        .btn-confirm {\n            font-size: 18px;\n            color: #1677ff;\n            text-align: center;\n            height: 50px;\n            width: 135px;\n        }"}], "tag": "style"}], "tag": "head"}, {"css": "root", "children": [{"css": "main-body", "children": [{"css": "icon-container", "children": [{"css": "icon", "tag": "img", "id": "icon"}], "tag": "div", "id": "iconContainer"}, {"css": "main-text", "tag": "label", "id": "mainText"}, {"css": "sub-text-container", "tag": "div", "id": "subTextContainer"}, {"css": "horizontal-line", "tag": "div"}, {"css": "btn-container", "children": [{"css": "btn-cancel", "onclick": "onBtnCancel()", "tag": "label", "id": "btnCancel"}, {"css": "veritcal-line", "tag": "div"}, {"css": "btn-confirm", "onclick": "onBtnConfirm()", "tag": "label", "id": "btnConfirm"}], "tag": "div"}], "tag": "div", "id": "mainBody"}], "tag": "body", "id": "root", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "remindInfo", "format": "JSON", "tag": "MOBILEIC", "time": "0012", "tplId": "MOBILEIC@remindInfo", "tplVersion": "5.3.5"}