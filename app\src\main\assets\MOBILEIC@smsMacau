{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var rpc = amc.rpcData;\n            var getTag = amc.fn.getById;\n            var gShortType = false;\n            var gInputElement = getTag(\"input\");\n            var gShortContainerElement = getTag(\"shortCodeInputContainer\");\n            var gLongContainerElement = getTag(\"longCodeInputContainer\");\n            var gCountdownInitTime = 60;\n            var gCountdownTime = gCountdownInitTime;\n            var gIsCountdowning = false; //正在倒计时\n            var gResendLabel = getTag(\"resend\");\n            var gTimer = null;\n            var gVerifyNumber = 0; //验证次数\n            var gShortCodeItemArray = [];   //4位元素item集合\n            var gShortCodeLabelArray = []; //4位元素label集合\n            var gShortCursorArray = []; //4位的光标集合\n            var gLongCodeItemArray = [];   //6位元素item集合\n            var gLongCodeLabelArray = [];   //6位元素label集合\n            var gLongCursorArray = []; //6位的光标集合\n            var gHighlightStyle = \"border: 1px solid #1677ff;\"\n            var gUnhighlightStyle = \"border: 1px solid #c4c4c4;\"\n            var gIsMalai = rpc.viLanguage && \"en\" === rpc.viLanguage;\n            var gResendText = rpc.form_input_tip_low_1 || (gIsMalai ? \"s to tap and resend\": \"秒後可點此重新發送\");\n            \n            function init() {\n                try {\n                    initUI();\n                    initCodeLabelArray();\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit(true);\n                    }\n                } \n                catch(e) {\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit();\n                    }\n                }\n            }\n\n            function initUI() {\n                //head_title\n                var naviTitle = rpc.head_title || (gIsMalai ? \"\": \"身份驗證\");\n                var nav = amc.fn.getNav(amc.res.navBack, null, \"\", null, null, onBack, null);\n                getTag(\"bodyContainer\").insertBefore(nav, getTag(\"mainBody\"));\n              \n                var bodyTitle = rpc.body_title || (gIsMalai ? \"Enter the OTP code\": \"輸入短信驗證碼\");\n                getTag(\"bodyTitle\").innerText = bodyTitle;\n\n                //subtitle\n                var subTitle = rpc.form_title || (gIsMalai ? \"We have sent the 6-digit code to\": \"我們已發送短信驗證碼到你的手機號\");\n                getTag(\"subTitle\").innerText = subTitle;\n\n                //phoneNumber\n                var phoneNumber = rpc.mobile_no || \"\";\n                getTag(\"phoneNumber\").innerText = phoneNumber;\n\n                //短信长度\n                gShortType = rpc.inputCharCount === \"6\" ? false : true;\n\n                //换一个方式，根据服务下发的标记显示\n                getTag(\"changeModule\").innerText = rpc.foot_tip || (gIsMalai ? \"Use a different way\": \"換個驗證方式\");\n                if (Boolean(rpc.HAS_OTHERS)) {\n                    getTag(\"changeModule\").style.visibility = \"visible\";\n                }\n\n                //倒计时\n                gResendLabel.innerText = rpc.form_input_tip_low || (gIsMalai ? \"Resend\": \"重新發送\");\n                gResendLabel.style.color = \"#1677ff\";\n\n                //最后一个输入框不需要rightmargin，保证居中\n                getTag(\"shortCodeItem3\").style.marginRight = \"0px\";\n                getTag(\"longCodeItem5\").style.marginRight = \"0px\";\n                //input字符最大个数\n                gInputElement.maxlength = gShortType ? 4 : 6;\n\n                if (gShortType) {\n                    gShortContainerElement.style.display = \"flex\";\n                    gLongContainerElement.style.display = \"none\";\n                }\n                else {\n                    gShortContainerElement.style.display = \"none\";\n                    gLongContainerElement.style.display = \"flex\";\n                }\n\n                processResendData(rpc.code, rpc.message);\n                \n                var timeInterval = 100;\n                if (amc.isAndroid) {\n                    timeInterval = 400;\n                }\n                setTimeout(function() {\n                    gInputElement.focus();\n                }, timeInterval);\n            }\n\n            function initCodeLabelArray() {\n                /*------4位-------*/\n                gShortCodeItemArray.push(getTag(\"shortCodeItem0\"));\n                gShortCodeItemArray.push(getTag(\"shortCodeItem1\"));\n                gShortCodeItemArray.push(getTag(\"shortCodeItem2\"));\n                gShortCodeItemArray.push(getTag(\"shortCodeItem3\"));\n\n                //label\n                gShortCodeLabelArray.push(getTag(\"shortCode0\"));\n                gShortCodeLabelArray.push(getTag(\"shortCode1\"));\n                gShortCodeLabelArray.push(getTag(\"shortCode2\"));\n                gShortCodeLabelArray.push(getTag(\"shortCode3\"));\n\n                //光标\n                gShortCursorArray.push(getTag(\"shortCodeCursor0\"));\n                gShortCursorArray.push(getTag(\"shortCodeCursor1\"));\n                gShortCursorArray.push(getTag(\"shortCodeCursor2\"));\n                gShortCursorArray.push(getTag(\"shortCodeCursor3\"));\n\n                /*------6位-------*/\n                gLongCodeItemArray.push(getTag(\"longCodeItem0\"));\n                gLongCodeItemArray.push(getTag(\"longCodeItem1\"));\n                gLongCodeItemArray.push(getTag(\"longCodeItem2\"));\n                gLongCodeItemArray.push(getTag(\"longCodeItem3\"));\n                gLongCodeItemArray.push(getTag(\"longCodeItem4\"));\n                gLongCodeItemArray.push(getTag(\"longCodeItem5\"));\n                \n                //label\n                gLongCodeLabelArray.push(getTag(\"longCode0\"));\n                gLongCodeLabelArray.push(getTag(\"longCode1\"));\n                gLongCodeLabelArray.push(getTag(\"longCode2\"));\n                gLongCodeLabelArray.push(getTag(\"longCode3\"));\n                gLongCodeLabelArray.push(getTag(\"longCode4\"));\n                gLongCodeLabelArray.push(getTag(\"longCode5\"));\n\n                //光标\n                gLongCursorArray.push(getTag(\"longCodeCursor0\"));\n                gLongCursorArray.push(getTag(\"longCodeCursor1\"));\n                gLongCursorArray.push(getTag(\"longCodeCursor2\"));\n                gLongCursorArray.push(getTag(\"longCodeCursor3\"));\n                gLongCursorArray.push(getTag(\"longCodeCursor4\"));\n                gLongCursorArray.push(getTag(\"longCodeCursor5\"));\n                \n                //初始模式，第一个光标闪动\n                resetInput();\n            }\n\n            function startCountdown() {\n                disableTimer();\n                gIsCountdowning = true;\n                gCountdownTime = gCountdownInitTime;\n                gResendLabel.disabled = true;\n                gResendLabel.style.color = \"#777777\";\n                gResendLabel.innerText = gCountdownTime + gResendText;\n                gTimer = setInterval(function() {\n                    gCountdownTime--;\n                    if (gCountdownTime == 0) {\n                        disableTimer();\n                        gCountdownTime = gCountdownInitTime;\n                        gIsCountdowning = false;\n                        gResendLabel.disabled = false;\n                        gResendLabel.innerText = rpc.form_input_tip_low || (gIsMalai ? \"Resend\": \"重新發送\");\n                        gResendLabel.style.color = \"#1677ff\";\n                        return;\n                    }\n                    gResendLabel.innerText = gCountdownTime + gResendText;\n                }, 1000);\n            }\n\n            function disableTimer() {\n                if (gTimer) {\n                    clearInterval(gTimer);\n                    gTimer = null;\n                }\n            }\n\n            function codeItemReset() {\n                if (gShortType) {\n                    //4位外框置灰\n                    for (var i = 0; i < gShortCodeItemArray.length; i++) {\n                        var item = gShortCodeItemArray[i];\n                        item.style.cssText = gUnhighlightStyle;\n                    }\n                    //4位数据清空\n                    for (var i = 0; i < gShortCodeLabelArray.length; i++) {\n                        var label = gShortCodeLabelArray[i];\n                        label.innerText = \"\";\n                    }\n                    //4位光标隐藏\n                    for (var i = 0; i < gShortCursorArray.length; i++) {\n                        var cursor = gShortCursorArray[i];\n                        stopCursorAnimate(cursor);\n                    }\n                }\n                else {\n                    //6位外框置灰\n                    for (var i = 0; i < gLongCodeItemArray.length; i++) {\n                        var item = gLongCodeItemArray[i];\n                        item.style.cssText = gUnhighlightStyle;\n                    }\n                    //6位数据清空\n                    for (var i = 0; i < gLongCodeLabelArray.length; i++) {\n                        var label = gLongCodeLabelArray[i];\n                        label.innerText = \"\";\n                    }\n                    //6位光标隐藏\n                    for (var i = 0; i < gLongCursorArray.length; i++) {\n                        var cursor = gLongCursorArray[i];\n                        stopCursorAnimate(cursor);\n                    }\n                }\n            }\n\n            function onInputChanged() {\n                //重置item\n                codeItemReset();\n                var text = gInputElement.value;\n                if (gShortType) {\n                    //高亮下一个框\n                    if (text.length < 4) {\n                        gShortCodeItemArray[text.length].style.cssText = gHighlightStyle;\n                        var cursor = gShortCursorArray[text.length];\n                        startCursorAnimate(cursor);\n                    }    \n                    //4位验证码模式\n                    for (var i = 0; i < text.length && i < 4; i++) {\n                        gShortCodeLabelArray[i].innerText = text.substr(i, 1);\n                    }\n                    //提交\n                    if (text.length == 4) {\n                        submit(text);\n                    }\n                }\n                else {\n                    //高亮下一个框\n                    if (text.length < 6) {\n                        gLongCodeItemArray[text.length].style.cssText = gHighlightStyle;\n                        var cursor = gLongCursorArray[text.length];\n                        startCursorAnimate(cursor);\n                    }\n                    //6位验证码模式\n                    for (var i = 0; i < text.length && i < 6; i++) {\n                        gLongCodeLabelArray[i].innerText = text.substr(i, 1);\n                    }\n                    //提交\n                    if (text.length == 6) {\n                        submit(text);\n                    }\n                }\n            }\n\n            function startCursorAnimate(cursor) {\n                if (!cursor) {\n                    return;\n                }\n                cursor.animation = [{\n                    \"type\": \"alpha\",\n                    \"fromValue\": 1.0,\n                    \"toValue\": 0.0,\n                    \"duration\": 1000,\n                    \"repeatCount\": 10000,\n                }];\n            }\n\n            function stopCursorAnimate(cursor) {\n                if (!cursor) {\n                    return;\n                }\n                cursor.animation = [{\n                    \"type\": \"alpha\",\n                    \"fromValue\": 0.0,\n                    \"toValue\": 0.0,\n                    \"duration\": 100,\n                }];\n            }\n\n            function flashAnimation() {\n                if (gShortType) {\n                    for (var i = 0; i < gShortCodeItemArray.length; i++) {\n                        var item = gShortCodeItemArray[i];\n                        item.animation = [{\n                            \"type\": \"translate\",\n                            \"fromValue\": {\n                                x:-4.0,\n                                y:0.0\n                            },\n                            \"toValue\": {\n                                x:0.0,\n                                y:0.0\n                            },\n                            \"duration\": 80,\n                            \"repeatCount\" : 3\n                        }];\n                    }\n                }\n                else {\n                    for (var i = 0; i < gLongCodeItemArray.length; i++) {\n                        var item = gLongCodeItemArray[i];\n                        item.animation = [{\n                            \"type\": \"translate\",\n                            \"fromValue\": {\n                                x:-3.0,\n                                y:0.0\n                            },\n                            \"toValue\": {\n                                x:0.0,\n                                y:0.0\n                            },\n                            \"duration\": 80,\n                            \"repeatCount\" : 3\n                        }];\n                    }\n                }\n            }\n\n            //重置的初始模式\n            function resetInput() {\n                //所有的label先清除\n                codeItemReset();\n                //第一个默认高亮 4位的\n                gShortCodeItemArray[0].style.cssText = gHighlightStyle;\n                //第一个默认高亮 6位的\n                gLongCodeItemArray[0].style.cssText = gHighlightStyle;\n                //清空input的值\n                gInputElement.value = \"\";\n                gInputElement.focus();\n                //动画\n                if (gShortType) {\n                    for (var i = 0; i < gShortCursorArray.length; i++) {\n                        var cursor = gShortCursorArray[i];\n                        if (i == 0) {\n                            //第一个光标默认显示\n                            startCursorAnimate(cursor);\n                        }\n                        else {\n                            stopCursorAnimate(cursor);\n                        }\n                    }\n                }\n                else {\n                    for (var i = 0; i < gLongCursorArray.length; i++) {\n                        var cursor = gLongCursorArray[i];\n                        if (i == 0) {\n                            //第一个光标默认显示\n                            startCursorAnimate(cursor);\n                        }\n                        else {\n                            stopCursorAnimate(cursor);\n                        }\n                    }\n                }\n            }\n\n            function submit(text) {\n                gInputElement.blur();\n                var obj = {\n                    \"eventName\": \"vi_rpc_validate\",\n                    \"moduleName\": \"CUSTOMIZED_SMS\",\n                    \"actionName\": \"VERIFY_ACCOUNT_BINDING_SMS\",\n                    \"params\": {\n                        \"ackCode\": text || \"\"\n                    }\n                };\n                document.asyncSubmit(obj, function(data) {\n                    var verifyMessage = data[\"verifyMessage\"] || (gIsMalai ? \"System is busy. Please try again later\": \"人氣太旺了，請稍後再試\");\n                    if (Boolean(data[\"verifySuccess\"])) {\n                        //验证成功\n                        mic.fn.onBackWithResponse(data);\n                        return;\n                    }\n                    //验证失败，出动画\n                    flashAnimation();\n                    //验证失败 验证码过期\n                    if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === \"ACCOUNT_BINDING_SMS\") {\n                        resetInput();\n                        document.toast({\n                            text: verifyMessage,\n                        }, function() {});\n                    } else {\n                        //验证码次数限制 VALIDATECODE_VALIDATE_TIMES_LIMIT\n                        var buttonText = \"\";\n                        if(Boolean(rpc.HAS_OTHERS) ){\n                            if(gIsMalai){\n                                buttonText = \"Use a different way\";\n                            }else{\n                                buttonText = \"換個驗證方式\";\n                            }\n                        }else{\n                            if(gIsMalai){\n                                buttonText = \"Confirm\";\n                            }else{\n                                buttonText = \"確定\";\n                            }\n                        }\n                        amc.fn.viAlert({\n                            \"title\": \"\",\n                            \"message\": verifyMessage,\n                            \"button\": buttonText\n                        }, function() {\n                            if (Boolean(rpc.HAS_OTHERS)) {\n                                changeModule();\n                            }\n                            else {\n                                mic.fn.onBackWithResponse(data);\n                            }\n                        });\n                    }\n                });\n            }\n        \n            function onKeyDown() {\n                if (event.which == 4) {\n                    onBack();\n                }\n            }\n            \n            function onBack() {\n                var dialogObj = {\n                    message: gIsMalai ? \"The message may take a while\":\"短信可能有延遲，請再等一會\",\n                    okButton: gIsMalai ? \"Keep waiting\":\"再等一會兒\",\n                    cancelButton: gIsMalai ? \"Back\":\"返回\"\n                };\n                amc.fn.viConfirm(dialogObj, function(result) {\n                    if (result.ok) {\n                        gInputElement.focus();\n                    }\n                    else {\n                        obj = {\n                            \"eventName\" : \"vi_quit_module\"\n                        };\n                        document.submit(obj);\n                    }\n                });\n            }\n\n            function codeLabelOnClick() {\n                gInputElement.focus();\n            }\n\n            function resendLabelOnClick() {\n                if (gIsCountdowning) {\n                    return;\n                }\n                sendSMS();\n            }\n\n            function sendSMS() {\n                var obj = {\n                    \"eventName\" : \"vi_rpc_validate\",\n                    \"moduleName\" : \"CUSTOMIZED_SMS\",\n                    \"actionName\" : \"VIEW\"\n                };\n                document.asyncSubmit(obj, function(data) {\n                    data = data || {};\n                    var viData = {};\n                    try {\n                        viData = JSON.parse(data[\"data\"]);    \n                    } \n                    catch (error) {\n                        print(\"fail to get viData: \" + error.message);\n                    }\n                    viData = viData || {};\n                    if (true !== Boolean(data[\"success\"])) {\n                        mic.fn.onBackWithResponse(data);\n                    }\n                    else {\n                        var code = viData[\"code\"];\n                        var message = viData[\"message\"];\n                        processResendData(code, message);\n                    }\n                });\n            }\n\n            function processResendData(code, message) {\n                message = message || (gIsMalai ? \"System is busy. Please try again later\": \"人氣太旺了，請稍後再試\");\n                if (code === \"VALIDATECODE_SEND_SUCCESS\") {\n                    //发送成功，倒计时，重置输入框\n                    startCountdown();\n                }\n                else {\n                    //发送失败\n                    amc.fn.viAlert({\n                        \"title\": \"\",\n                        \"message\": message,\n                        \"button\": gIsMalai ? \"Got it\": \"我知道了\"\n                    }, function() {});\n                }\n            }\n\n            function changeModule() {\n                gInputElement.blur();\n                mic.fn.changeModule();\n            }\n\n            function bodyOnClick() {\n                //拦截事件，点击页面空白处不让键盘消失\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n                background-color: #ffffff;\n            }\n        \n            .main-title-label {\n                text-align: center;\n                font-size: 30px;\n                color: #000000;\n                line-height: 42px;\n                margin-top: 32px;\n            }\n            \n            .sub-title-label {\n                text-align: center;\n                font-size: 18px;\n                color: #888888;\n                line-height: 24px;\n                margin-top: 16px;\n                margin-left: 30px;\n                margin-right: 30px;\n            }\n            \n            .phone-number-label {\n                font-weight: bold;\n                text-align: center;\n                font-size: 24px;\n                height: 26px;\n                color: #333333;\n                margin-top: 6px;\n            }\n\n            .short-code-container {\n                margin-top: 38px;\n                height: 58px;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n            }\n\n            .short-code-item {\n                width: 58px;\n                height: 58px;\n                margin-right: 12px;\n                border: 1px solid #c4c4c4;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n            }\n\n            .short-code-label {\n                width: 100%;\n                height: 100%;\n                font-size: 30px;\n                text-align: center;\n                color: #333333;\n            }\n\n            .short-code-cursor {\n                position: absolute;\n                width: 2px;\n                height: 30px;\n                left: 28px;\n                top: 14px;\n                background-color: #1677ff;\n            }\n            \n            .long-code-container {\n                margin-top: 38px;\n                height: 46px;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n            }\n\n            .long-code-item {\n                width: 46px;\n                height: 46px;\n                margin-right: 6px;\n                border: 1px solid #c4c4c4;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n            }\n\n            .long-code-label {\n                width: 100%;\n                height: 100%;\n                font-size: 28px;\n                text-align: center;\n                color: #333333;\n            }\n\n            .long-code-cursor {\n                position: absolute;\n                width: 2px;\n                height: 24px;\n                left: 22px;\n                top: 12px;\n                background-color: #1677ff;\n            }\n\n            .resend-label {\n                height: 24px;\n                width: 220px;\n                text-align: center;\n                font-size: 18px;\n                color: #777777;\n                line-height: 22px;\n                margin-top: 16px;\n                align-self: center;\n            }\n\n            .change-module-label {\n                text-align: center;\n                font-size: 18px;\n                color: #1677ff;\n                line-height: 22px;\n                margin-top: 36px;\n                width: 140px;\n                align-self: center;\n                visibility: hidden; \n            }\n            \n            .code-input-hidden {\n                height: 1px;\n                max-height: 1px;\n                max-width: 1px;\n                width: 1px;\n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "mic-fullscreen main-body", "children": [{"css": "code-input-hidden", "maxlength": "6", "tag": "input", "id": "input", "type": "number", "oninput": "onInputChanged()"}, {"css": "main-title-label", "tag": "label", "id": "bodyTitle"}, {"css": "sub-title-label", "tag": "label", "id": "subTitle"}, {"css": "phone-number-label", "tag": "label", "id": "phoneNumber"}, {"css": "short-code-container", "children": [{"css": "short-code-item", "children": [{"css": "short-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "shortCode0"}, {"css": "short-code-cursor", "tag": "div", "id": "shortCodeCursor0"}], "tag": "div", "id": "shortCodeItem0"}, {"css": "short-code-item", "children": [{"css": "short-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "shortCode1"}, {"css": "short-code-cursor", "tag": "div", "id": "shortCodeCursor1"}], "tag": "div", "id": "shortCodeItem1"}, {"css": "short-code-item", "children": [{"css": "short-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "shortCode2"}, {"css": "short-code-cursor", "tag": "div", "id": "shortCodeCursor2"}], "tag": "div", "id": "shortCodeItem2"}, {"css": "short-code-item", "children": [{"css": "short-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "shortCode3"}, {"css": "short-code-cursor", "tag": "div", "id": "shortCodeCursor3"}], "tag": "div", "id": "shortCodeItem3"}], "tag": "div", "id": "shortCodeInputContainer"}, {"css": "long-code-container", "children": [{"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode0"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor0"}], "tag": "div", "id": "longCodeItem0"}, {"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode1"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor1"}], "tag": "div", "id": "longCodeItem1"}, {"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode2"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor2"}], "tag": "div", "id": "longCodeItem2"}, {"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode3"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor3"}], "tag": "div", "id": "longCodeItem3"}, {"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode4"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor4"}], "tag": "div", "id": "longCodeItem4"}, {"css": "long-code-item", "children": [{"css": "long-code-label", "onclick": "codeLabelOnClick()", "tag": "label", "id": "longCode5"}, {"css": "long-code-cursor", "tag": "div", "id": "longCodeCursor5"}], "tag": "div", "id": "longCodeItem5"}], "tag": "div", "id": "longCodeInputContainer"}, {"css": "resend-label", "onclick": "resendLabelOnClick()", "tag": "label", "id": "resend"}, {"css": "change-module-label", "onclick": "changeModule()", "tag": "label", "id": "changeModule"}], "onclick": "bodyOnClick()", "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "smsMacau", "format": "JSON", "tag": "MOBILEIC", "time": "0013", "tplId": "MOBILEIC@smsMacau", "tplVersion": "5.4.4"}