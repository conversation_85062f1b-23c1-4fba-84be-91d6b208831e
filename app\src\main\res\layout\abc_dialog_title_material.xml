<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.FitWindowsLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:ellipsize="end"
        android:layout_gravity="start"
        android:id="@+id/title"
        android:paddingLeft="?attr/dialogPreferredPadding"
        android:paddingTop="@dimen/abc_dialog_padding_top_material"
        android:paddingRight="?attr/dialogPreferredPadding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:textAlignment="viewStart"
        style="?android:attr/windowTitleStyle"/>
    <include
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        layout="@layout/abc_screen_content_include"/>
</androidx.appcompat.widget.FitWindowsLinearLayout>
