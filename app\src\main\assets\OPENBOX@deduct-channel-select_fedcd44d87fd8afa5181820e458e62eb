{"data": {"children": [{"children": [{"charset": "UTF-8", "tag": "meta"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"src": "AlipaySDK.bundle/amc-meta.js", "tag": "script"}, {"tag": "style"}, {"children": [{"tag": "text", "text": "!function(t){var i={};function o(n){if(i[n])return i[n].exports;var e=i[n]={i:n,l:!1,exports:{}};return t[n].call(e.exports,e,e.exports,o),e.l=!0,e.exports}o.m=t,o.c=i,o.d=function(n,e,t){o.o(n,e)||Object.defineProperty(n,e,{enumerable:!0,get:t})},o.r=function(n){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(n,\"__esModule\",{value:!0})},o.t=function(e,n){if(1&n&&(e=o(e)),8&n)return e;if(4&n&&\"object\"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(o.r(t),Object.defineProperty(t,\"default\",{enumerable:!0,value:e}),2&n&&\"string\"!=typeof e)for(var i in e)o.d(t,i,function(n){return e[n]}.bind(null,i));return t},o.n=function(n){var e=n&&n.__esModule?function(){return n[\"default\"]}:function(){return n};return o.d(e,\"a\",e),e},o.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},o.p=\"/\",o(o.s=59)}({0:function(n,e,t){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0}),e.amc=window.amc},10:function(n,e,t){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});var o=t(0),f=t(9),r=t(6);var u=(i.onFramePluginEventLifetimeNotifier=function(n){var t=\"string\"==typeof n?JSON.parse(n):n;if(function c(n){return\"onEvent\"===n.type}(t)){var e=t.key;if(!e)return;var i=this.EventTypeInnerHandlers[e];i&&i.forEach(function(n){try{n(t.args)}catch(e){}});var o=this.EventTypeOuterHandlers[e];if(o&&t.mqpToken&&t.id)try{var r=o(t.args,f.BNFramePlugin.find(t.mqpToken));d.sendEventResultToPlugin(e,t.id,r||{},t.mqpToken)}catch(l){}}else if(function s(n){return\"onEventResult\"===n.type}(t)){var u=t.id;if(!u)return;var a=this.EventResultCallbacks[u];if(a)try{a(!0,t.result)}catch(l){}}},i.addEventHandler=function(n,e,t){n?(this.EventTypeInnerHandlers[e]||(this.EventTypeInnerHandlers[e]=[]),this.EventTypeInnerHandlers[e].push(t)):this.EventTypeOuterHandlers[e]=t},i.setEventResultCallback=function(n,e){e&&(this.EventResultCallbacks[n]=e)},i.listKnownEventTypes=function(){var n=[];for(var e in this.EventTypeOuterHandlers)this.EventTypeOuterHandlers.hasOwnProperty(e)&&n.push(e);return n},i.EventResultCallbacks={},i.EventTypeInnerHandlers={},i.EventTypeOuterHandlers={},i);function i(){}var d=(a.onInnerEvent=function(n,e){u.addEventHandler(!1,n,e)},a.sendEventToInner=function(e,t,n){var i;n?(i=f.BNFramePlugin.find(n))&&a.sendEventToOnePlugin(e,t||{},i):f.BNFramePlugin.listAll().forEach(function(n){a.sendEventToOnePlugin(e,t||{},n.plugin)})},a.sendEventResultToPlugin=function(n,e,t,i){var o;!i||(o=f.BNFramePlugin.find(i))&&this.sendEventResultToOnePlugin(n,e,t,o)},a.sendEventToOnePlugin=function(n,e,t){var i={type:\"onEvent\",id:this.generateEvId(n),mqpToken:t.mqpToken,key:n,args:e};this._setDomEvent(t,i)},a.sendEventResultToOnePlugin=function(n,e,t,i){var o={type:\"onEventResult\",id:e,mqpToken:i.mqpToken,key:n,result:t};this._setDomEvent(i,o)},a._setDomEvent=function(n,e){n.pluginNode&&window.setTimeout(function(){n.pluginNode.setAttribute(\"event\",JSON.stringify(e))},1)},a.generateEvId=function(n){return\"ev_o_\"+(n||\"\")+\"_\"+r.Utils.randomStr(6)},a);function a(){}e.BNFrameChannelOuter=d;var c=(l.sendEventToOuter=function(n,e,t){var i={type:\"onEvent\",id:this.generateEvId(n),mqpToken:window.flybird&&window.flybird.rpcData&&window.flybird.rpcData.mqpToken,key:n,args:e||{}};u.setEventResultCallback(i.id,t),document.submit({action:{name:\"onBnFrameEvent\"},param:i})},l.onOuterEvent=function(n,e){u.addEventHandler(!0,n,e)},l.generateEvId=function(n){return\"ev_i_\"+(n||\"\")+\"_\"+r.Utils.randomStr(6)},l);function l(){}e.BNFrameChannelInner=c;var s=(p.listApis=function(t){c.sendEventToOuter(m,{},function(n,e){n&&e&&e.apis?t(e.apis):t([])})},p.renderFinished=function(n,t){var e=window.flybird&&window.flybird.rpcData||{},i=e.mqpToken||\"\";r.Utils.niceTry(function(){o.amc.fn.logAction(\"ok|\"+Date.now()+\"|\"+n+\"|\"+(e?e.mqpToken:\"-\"),\"BNPlugin\")}),c.sendEventToOuter(\"MQPBNFRAME_RENDER_SUCCESS\",{mqpToken:i,height:n},function(n,e){t(n)})},p);function p(){}e.BNFrameCommonInnerEvents=s,window.onFramePluginEvent=function(n){u.onFramePluginEventLifetimeNotifier(n)};var m=\"LIST_APIS\";d.onInnerEvent(m,function(n,e){return{apis:u.listKnownEventTypes()}})},11:function(n,e,t){\"use strict\";var u=this&&this.__rest||function(n,e){var t={};for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&e.indexOf(i)<0&&(t[i]=n[i]);if(null!=n&&\"function\"==typeof Object.getOwnPropertySymbols)for(var o=0,i=Object.getOwnPropertySymbols(n);o<i.length;o++)e.indexOf(i[o])<0&&(t[i[o]]=n[i[o]]);return t};Object.defineProperty(e,\"__esModule\",{value:!0});var i,o,r=t(13);(o=i=i||{})[o.INITIAL=0]=\"INITIAL\",o[o.ON_RESUME=1]=\"ON_RESUME\",o[o.ON_PAUSE=2]=\"ON_PAUSE\";var a={value:(e.OnResumeStatusEnum=i).INITIAL};e.onResumeStatus=a;var c,l=null,s=0,f=null,d={rpcFresh:!0,rpcTimeFresh:1e4,luoshuThrottFlag:!0,separateLog:!1,selfHandleLifeCycle:!1,rpcVisibleFresh:!1},p=(c=d,function(n,e){c[n]=e});e.setLifeConfig=p;var m={onPause:[function O(){a.value=i.ON_PAUSE}],onResume:[function b(){a.value=i.ON_RESUME,l&&\"android\"===document.platform&&d.rpcFresh&&Date.now()-s>d.rpcTimeFresh&&(l(),s=Date.now())}],onUnVisible:[],onVisible:[function T(){d.rpcVisibleFresh&&l&&l()}],onload:[function C(){\"android\"===document.platform&&(document.body.style.backgroundColor=\"rgba(0, 0, 0, 0)\")}],onreload:[function N(n){n&&n.bnExt&&n.bnExt.cardWidth&&(document.body.style.width=n.bnExt.cardWidth)}],onLogUpdate:[]},v=Object.keys(m),g=function(n,e){n&&e&&m[n]&&m[n].push(e)};e.registerLifeCycle=g;var h=[];e.registerComponentLifeCycle=function(t){v.forEach(function(n){var e;\"function\"==typeof t[n]&&(e=t[n].bind(t),h.push(e),g(n,e))})};var y=function(){Object.keys(m).forEach(function(t){m[t].forEach(function(n){var e=h.indexOf(n);-1<e&&(\"onreload\"===t&&n(),E(t,n),h.splice(e,1))})})};e.unRegisterComponentLifeCycle=y;var E=function(n,e){var t;n&&e&&m[n]&&(-1<(t=m[n].indexOf(e))&&m[n].splice(t,1))};e.unRegisterLifeCycle=E;e.runAllLifeCycle=function(){Object.keys(m).forEach(function(r){document[r]=function(e){var n,t,i,o;d.separateLog?\"onreload\"===r?(i=void 0===(t=(n=e||{}).bnLogModel)?{}:t,o=u(n,[\"bnLogModel\"]),window.bnLogModel=i,m.onLogUpdate.forEach(function(n){return n(i)}),e&&JSON.stringify(o)!==JSON.stringify(f)&&(l||d.selfHandleLifeCycle||y(),f=o,m[r].forEach(function(n){return n(o)})),l&&l()):m[r].forEach(function(n){return n(e)}):\"onreload\"===r?((!d.luoshuThrottFlag||e&&JSON.stringify(e)!==JSON.stringify(f))&&(l||d.selfHandleLifeCycle||y(),f=e,m[r].forEach(function(n){return n(e)})),l&&l()):m[r].forEach(function(n){return n(e)})},-1<[\"onPause\",\"onResume\",\"onVisible\",\"onUnVisible\"].indexOf(r)&&(window[r]=document[r])})};e.registerRpc=function(n,e,t){l=function(){t&&t.filterRpc&&t.filterRpc()||(s=Date.now(),r[\"default\"](n,function(n){d.selfHandleLifeCycle||y(),e(n)},t))}};e.unRegisterRpc=function(){l=null}},13:function(n,e,t){\"use strict\";var C,i;Object.defineProperty(e,\"__esModule\",{value:!0}),(i=C=C||{})[i.SUCCESS=0]=\"SUCCESS\",i[i.TIMEOUT=1]=\"TIMEOUT\";var N=!(i[i.SERVICE_ERROR=2]=\"SERVICE_ERROR\"),S=0,P=null,R=!1,_=null;e[\"default\"]=function(n,e,t){var i,o,r,u,a,c,l,s,f=t||{},d=f.lock,p=f.lockTime,m=void 0===p?1e3:p,v=f.timeOut,g=void 0===v?1e4:v,h=f.throttled,y=void 0===h?function(){}:h,E=f.useOriginRsp,O=void 0!==E&&E,b=f.clipOriginRspAop,T=void 0===b?null:b;(void 0===d||d)&&N&&Date.now()-S<m?y&&y():(R=!function(n){T&&(n=T(n)),R||(clearTimeout(_),N=!0,m&&setTimeout(function(){N=!1},m),S=Date.now(),P&&JSON.stringify(P)===JSON.stringify(n)?y&&y():(P=n,O?n.success?e(n.result):e(n):n&&n.success&&n.result&&n.result.success&&n.result.result?e({code:C.SUCCESS,success:!0,result:n.result.result}):e({code:C.SERVICE_ERROR,success:!1,result:n})))},_&&clearTimeout(_),_=setTimeout(function(){R=!0,e({code:C.TIMEOUT,success:!1})},g),o=void 0===(i=n.data)?{}:i,u=void 0===(r=n.loading)?{required:!1,text:\"\"}:r,a=o.operationType,l=void 0===(c=o.requestData)?[]:c,s=o.handleRpcExceptionFirst,a&&(Array.isArray(l)||(l=[l]),document.asyncSubmit({alert_event_name:\"workbench_rpc\",params:{data:{operationType:a,requestData:l},loading:u}},function(n){T&&(n=T(n)),R||(clearTimeout(_),N=!0,m&&setTimeout(function(){N=!1},m),S=Date.now(),P&&JSON.stringify(P)===JSON.stringify(n)?y&&y():(P=n,O?n.success?e(n.result):e(n):n&&n.success&&n.result&&n.result.success&&n.result.result?e({code:C.SUCCESS,success:!0,result:n.result.result}):e({code:C.SERVICE_ERROR,success:!1,result:n})))})))}},14:function(n,e,t){\"use strict\";t.r(e),t.d(e,\"registerLifeCycle\",function(){return a}),t.d(e,\"unRegisterLifeCycle\",function(){return s}),t.d(e,\"runAllLifeCycle\",function(){return f}),t.d(e,\"registerRpc\",function(){return d}),t.d(e,\"unRegisterRpc\",function(){return p}),t.d(e,\"setLifeConfig\",function(){return o}),t.d(e,\"registerComponentLifeCycle\",function(){return l});var i,o=(i={},function(n,e){i[n]=e});var r={onPause:[],onResume:[function m(){}],onUnVisible:[],onVisible:[],onload:[function v(){\"android\"===document.platform&&(document.body.style.backgroundColor=\"rgba(0, 0, 0, 0)\")}],onreload:[function g(n){n&&n.bnExt&&n.bnExt.cardWidth&&(document.body.style.width=n.bnExt.cardWidth)}],viewDidAppear:[]},u=Object.keys(r),a=function(n,e){\"onreload\"===n&&(n=\"onload\"),\"onResume\"!==n&&\"onVisible\"!==n||(n=\"viewDidAppear\"),n&&e&&r[n]&&r[n].push(e)},c=[],l=function(t){u.forEach(function(n){var e;\"function\"==typeof t[n]&&(e=t[n].bind(t),c.push(e),a(n,e))})},s=function(n,e){var t;n&&e&&r[n]&&(-1<(t=r[n].indexOf(e))&&r[n].splice(t,1))},f=function(){Object.keys(r).forEach(function(n){document[n]=function(e){r[n].forEach(function(n){return n(e)})},-1<[\"onPause\",\"onResume\",\"onVisible\",\"onUnVisible\"].indexOf(n)&&(window[n]=document[n]),-1<[\"onload\",\"viewDidAppear\"].indexOf(n)&&(\"iOS\"===document.platform?document[n]():window.setTimeout(function(){document[n]()},30))})},d=function(){},p=function(){}},16:function(n,e,t){\"use strict\";function i(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return e===undefined?0===n.length:n.every(function(n){return 0<=e.indexOf(n)})}t.d(e,\"a\",function(){return i})},19:function(n,e,t){},2:function(n,t,e){\"use strict\";function i(n){for(var e in n)t.hasOwnProperty(e)||(t[e]=n[e])}Object.defineProperty(t,\"__esModule\",{value:!0}),i(e(9)),i(e(10))},4:function(n,e,t){\"use strict\";t.d(e,\"a\",function(){return o}),t.d(e,\"b\",function(){return r});t(11);var i=t(14),o=i.registerLifeCycle,r=(i.unRegisterLifeCycle,i.runAllLifeCycle);i.registerRpc,i.unRegisterRpc,i.setLifeConfig,i.registerComponentLifeCycle},59:function(n,e,t){\"use strict\";t.r(e),t.d(e,\"gPageId\",function(){return l});var i=t(4),o=t(6),r=t(2),u=t(16),a=t(0),c=window.mp,l=o.Utils.randomStr(6);t(19);Object(i.a)(\"onreload\",function(n){var t=a.amc.rpcData&&a.amc.rpcData.bizData;!function e(n){return\"object\"==typeof n}(t)?a.amc.fn.logError(\"QR-rec-unknown\",\"-\"):(r.BNFrameCommonInnerEvents.listApis(function(n){Object(u.a)(n,s.API_ON_SELECT_CHANNEL)||a.amc.fn.logError(\"QR-rec-missing-apis\",JSON.stringify(n))}),r.BNFrameChannelInner.onOuterEvent(\"SWITCH_CHANNEL\",function(){a.amc.fn.logAction(\"act|switchChannel\",\"BNPlugin\"),setTimeout(function(){var n=document.platform;\"iOS\"===n&&(n=\"iphone\");var e={channelContextId:t.channelContextId,curChannelIndexList:t.curChannelIndexList,birdParams:JSON.stringify({tplVersion:window.flybird.local.birdNestVer,platform:n})};setTimeout(function(){!function n(){setTimeout(function(){document.invoke(\"showLoading\",{})},20)}()},20),document.invoke(\"rpc\",{operationType:\"alipay.mdeduct.channel.switch\",requestData:e},function(i){setTimeout(function(){var n,o,e;!function t(){setTimeout(function(){document.invoke(\"hideLoading\",{})},20)}(),a.amc.fn.logAction(\"act|onRPCResponse\",\"BNPlugin\"),i&&i.success&&i.data&&i.data.payments?(o=!0,null!==(n=i.data.payments)&&void 0!==n&&n.forEach(function(n,e,t){var i=n;return i.value=n.channelIndex,i.disable=!n.enable,i.name=n.fullName,i.checked=n.choosed,n.choosed&&(o=!1),i}),e={scene:\"front\",title:i.data.title,params:{},bizType:\"deduct\",payments:i.data.payments||[],selectChannelMode:i.data.selectChannelMode,setting:!0,pageToken:l,isSelectGlobalPayOrderChannel:o},document.invoke(\"loc\",{action:{name:\"loc:bnvb\",params:{tplid:i.tplid,tpl:i.tpl,data:e}}},function(){})):document.toast({text:\"网络开小差，请稍后重试\",type:\"none\"},function(){})},1)})},20)}),a.amc.fn.logAction(\"act|onRenderSuccess\",\"BNPlugin\"),c.onNotification(\"CLICK_CHANNEL\",function(n){a.amc.fn.logAction(\"act|NOTIFICATION_CLICK_CHANNEL\",\"BNPlugin\"),n&&n.pageToken===l&&n&&s.apiOnSelectChannel([n],t.channelContextId)}),setTimeout(function(){r.BNFrameCommonInnerEvents.renderFinished(1,function(n){})},20))}),Object(i.b)();var s=(f.apiOnSelectChannel=function(n,e){a.amc.fn.logAction(\"act|OnSelectChannel\",\"BNPlugin\"),r.BNFrameChannelInner.sendEventToOuter(f.API_ON_SELECT_CHANNEL,{selectedChannels:n,channelContextId:e})},f.API_ON_SELECT_CHANNEL=\"ON_SELECT_CHANNEL\",f);function f(){}},6:function(n,e,t){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});var i=(o.randomStr=function(n){for(var e=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\",t=e.length,i=isNaN(n)?1:n,o=\"\",r=0;r<i;r++)o+=e[Math.floor(Math.random()*t)];return o},o.alert=function(n,e){document.alert({title:n||\"-\",message:e||\"-\",button:\"OK\"},function(){})},o.niceTry=function(n){try{return n()}catch(e){}},o.getPlatform=function(){switch(document.platform){case\"android\":return\"android\";case\"iOS\":return\"iOS\";default:return\"\"}},o);function o(){}e.Utils=i},9:function(n,e,t){\"use strict\";var u=this&&this.__assign||function(){return(u=Object.assign||function(n){for(var e,t=1,i=arguments.length;t<i;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};Object.defineProperty(e,\"__esModule\",{value:!0});var a=t(0),i=t(10),c=t(6),o=(r.nativeRenderSucceed=function(n,e){e&&n.parentNode&&n.pluginNode&&(n.parentNode.style.height=e,n.pluginNode.style.height=e),n.renderFinishCallback&&n.renderFinishCallback(n)},r.allPlugins={},r);function r(){}var l=(s.listAll=function(){var n=[];for(var e in o.allPlugins)o.allPlugins.hasOwnProperty(e)&&n.push({mqpToken:e,plugin:o.allPlugins[e]});return n},s.find=function(n){return o.allPlugins[n]},s.create=function(n,e,t){if(!n)return undefined;if(\"PluginBN\"!==n.type)return undefined;if(!n.tplInfo)return undefined;if(!n.data)return undefined;var i=new s(c.Utils.randomStr(6)),o=n.tplInfo,r=o.tplHash?o.tplId+\"_\"+o.tplHash:o.tplId;c.Utils.niceTry(function(){a.amc.fn.logAction(\"new|\"+Date.now()+\"|\"+r+\"|\"+i.mqpToken,\"BNPlugin\")}),i.init({mqpToken:i.mqpToken,tpl:{tplid:r,tpl:JSON.stringify({time:\"0001\",tplId:r,tplVersion:\"5.4.9\",publishVersion:\"150924\",tplUrl:o.tplUrl,tplHash:o.tplHash}),data:u({},n.data,{mqpToken:i.mqpToken})}},e,t)},s.prototype.init=function(n,e,t){var i;(o.allPlugins[this.mqpToken]=this).parentNode=e,this.renderFinishCallback=t,this.initData=n,\"android\"===c.Utils.getPlatform()?((i=document.createElement(\"embed\",{type:\"MQPBNFrame\",src:JSON.stringify(n)},function(){})).type=\"MQPBNFrame\",this.pluginNode=i):(i=document.createElement(\"embed\"),(this.pluginNode=i).type=\"MQPBNFrame\",i.src=JSON.stringify(n)),e.appendChild(i),e.style.height=0},s);function s(n){this.mqpToken=n}e.BNFramePlugin=l,i.BNFrameChannelOuter.onInnerEvent(\"MQPBNFRAME_RENDER_SUCCESS\",function(n,e){if(c.Utils.niceTry(function(){a.amc.fn.logAction(\"ack|\"+Date.now()+\"|\"+(n?n.height:\"-\")+\"|\"+(e?e.mqpToken:\"-\"),\"BNPlugin\")}),!(e&&n&&n.mqpToken&&n.height))return{result:!1};var t=n.height;return o.nativeRenderSucceed(e,t),{result:!0}})}});"}], "tag": "script"}], "tag": "head"}, {"css": "amc-v-box", "children": [{"css": "amc-v-box", "tag": "div", "id": "msp-container"}], "tag": "body", "id": "body", "onload": "window.init && window.init();"}], "tag": "html", "lang": "zh-CN"}, "publishVersion": "150603", "name": "deduct-channel-select", "format": "JSON", "tag": "OPENBOX", "time": "0010", "tplId": "OPENBOX@deduct-channel-select", "tplVersion": "5.4.9"}