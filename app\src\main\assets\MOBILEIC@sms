{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "js/cashier-safeprotect-sms-flex.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var gBankSmsHelpText4Ios = \"—— 1 ——<br>请确认该银行预留手机号是否为当前使用手机号。<br>—— 2 ——<br>请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。<br>—— 3 ——<br>由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。<br>—— 4 ——<br>若银行预留手机号已停用，请联系银行客服处理。<br>—— 5 ——<br>若您最近操作过携号转网，请等待1-2天后再试。\";\n        var gBankSmsHelpText4Android = \"1，请确认该银行预留手机号是否为当前使用手机号。\\n2，请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。\\n3，由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。\\n4，若银行预留手机号已停用，请联系银行客服处理。\\n5，若您最近操作过携号转网，请等待1-2天后再试。\";\n\n       //别名\n        var rpc = amc.rpcData;\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var getTag = amc.fn.getById;\n\n        //### 这个页面与cashier-sms视觉类似，但是参数差异较大，且为安全服务化页面。因此单独列出，省得兼容时的麻烦。\n        var countDown = 60;\n        var codeCount = 6;\n        var countDownNum = countDown;\n        var timer;\n\n        var smsread = {\n            'template': '{{code}}(\\\\d{4,6})',\n            'tempGroup': '1',\n            'rules': ['95188', '106575258188', '106980095188', '106575170001', '106575552766', '106575552788', '10655057868', '10655059666', '106590571868', '106590202028', '106575000086', '1069099999', '95559', '95580', '95528', '95508',\n                '95561'\n            ],\n            'readTime': '180'\n        };\n\n        //### 密码框输入响应\n        function onPwdInput() {\n            var input = getTag('pwd');\n            input.style.cssText = 'color:#000';\n            var submit = getTag('submit');\n            submit.disabled = !(input.value.length == codeCount);\n        }\n\n        function init() {\n            var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', '{{id_validate}}', null, null, onBack, null);\n            getTag('bodyContainer').style.height = window.innerHeight + 80;\n            getTag('bodyContainer').insertBefore(nav, getTag('commonFullTitle'));\n\n            if (rpc.mobile_no) {\n                getTag('mainTitle').innerText = amc.fn.i18nPlaceholderReplace('{{phone_sms_full}}', rpc.mobile_no);\n            } else { // 如果服务端拿不到手机号则返回空字符串@东隅(安全服务化服务端),此情况下显示如下文案,@因梦(安全服务化PD)\n                getTag('mainTitle').innerText = '{{phone_sms}}';\n            }\n\n            // Android平台没有白色菊花，需要特殊处理\n            if (amc.isAndroid) {\n                getTag('loading').src = amc.path + 'alipay_msp_indicator_white_loading';\n            }\n\n            //### 短信倒计时\n            getTag('resendBtn').disabled = true;\n            if (rpc.countDown > 0) {\n                countDown = rpc.countDown;\n            }\n            if (rpc.ackCodeLength > 0) {\n                codeCount = rpc.ackCodeLength;\n            }\n            reCountDown();\n            countDownFuc();\n\n            var parentNode = document.getElementById('mainBody');\n            var othersDiv = document.getElementById('others');\n            parentNode.removeChild(othersDiv);\n\n            processResendData(rpc.code, rpc.message);\n            getTag('pwd').focus();\n        }\n\n        function updateSubmitBtnStatus() {\n            var input = getTag('pwd');\n            input.value = '';\n            var submit = getTag('submit');\n            submit.disabled = !(input.value.length == codeCount);\n        }\n\n        //### 重发短信验证码\n        function onResendClick() {\n            //### 倒计    触发提交操作\n            if (countDownNum > 0) {\n                return;\n            }\n\n            var obj = {};\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['moduleName'] = 'CUSTOMIZED_SMS';\n            obj['actionName'] = 'RESEND_SMS';\n\n            document.asyncSubmit(obj, function(data) {\n                data = data || {};\n                data['pageloading'] = '0';\n                amc.fn.shouldShowBtnIndicator(data, 'submit', 'loading');\n                var viData = {};\n                try {\n                    viData = JSON.parse(data['data']);\n                } catch (error) {\n                    print(\"fail to get viData: \" + error.message);\n                }\n                viData = viData || {};\n                if (true !== Boolean(data['success'])) {\n                    mic.fn.onBackWithResponse(data);\n                } else {\n                    processResendData(viData['code'], viData['message']);\n                }\n            });\n\n            getTag('resendBtn').disabled = true;\n            reCountDown();\n\n            updateSubmitBtnStatus();\n            // getTag('pwd').value = '';\n            // getTag('submit').disabled = true;\n        }\n\n        function reCountDown() {\n            countDownNum = countDown;\n\n            // 防止之前的timer还没被清掉\n            if (timer) {\n                clearInterval(timer);\n                timer = 0;\n            }\n            timer = setInterval(countDownFuc, 1000);\n        }\n\n        function processResendData(code, message) {\n            print(\"processResendData code: \" + code + \", message: \" + message);\n            message = message || '人气太旺了，请稍后再试';\n            if ('VALIDATECODE_SEND_SUCCESS' === code) {\n                //nothing to do\n            } else {\n                enableResend();\n                if ('VALIDATECODE_SEND_TIMES_LIMIT' === code) {\n                    amc.fn.viAlert({\n                        \"title\": '',\n                        \"message\": message,\n                        \"button\": '{{got_it}}'\n                    }, function() {});\n                } else {\n                    amc.fn.viAlert({\n                        \"title\": '',\n                        \"message\": message,\n                        \"button\": '{{got_it}}'\n                    }, function() {});\n                }\n            }\n        }\n\n        //### 倒计时1\n        function countDownFuc() {\n            if (countDownNum > 0) {\n                getTag('resendBtn').innerText = amc.fn.i18nPlaceholderReplace('{{resend_after_full}}', countDownNum);\n                countDownNum -= 1;\n            } else {\n                enableResend();\n            }\n        }\n\n        function enableResend() {\n            countDownNum = 0;\n            getTag('resendBtn').innerText = '{{resend_sms}}';\n            getTag('resendBtn').disabled = false;\n            if (timer) {\n                clearInterval(timer);\n                timer = 0;\n            }\n        }\n\n\n        //### 确认按钮,提交业务数据\n        function submitInfo() {\n            //### 提交前隐藏输入框\n            getTag('pwd').blur();\n\n            var obj = {};\n\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['moduleName'] = 'CUSTOMIZED_SMS';\n            obj['actionName'] = rpc.verifyAction;\n            //### 设置用户提交的参数\n            var submitObj = {\n                'ackCode': getTag('pwd').value\n            };\n            obj['params'] = submitObj;\n\n            document.asyncSubmit(obj, function(data) {\n                data['pageloading'] = '0';\n                amc.fn.shouldShowBtnIndicator(data, 'submit', 'loading');\n\n                var verifyMessage = data['verifyMessage'] || '人气太旺了，请稍后再试';\n                if (Boolean(data['verifySuccess'])) {\n                    mic.fn.onBackWithResponse(data);\n                } else {\n                    //verify failed\n                    updateSubmitBtnStatus();\n\n                    if ('VALIDATECODE_FAILURE' === data['verifyCode']) {\n                        getTag('pwd').focus();\n                        document.toast({\n                            text: verifyMessage,\n                        }, function() {});\n                    } else if ('VALIDATECODE_EXPIRED' === data['verifyCode']) {\n                        getTag('pwd').focus();\n                        document.toast({\n                            text: verifyMessage,\n                        }, function() {});\n                        enableResend();\n                    } else if ('VALIDATECODE_VALIDATE_TIMES_LIMIT' === data['verifyCode']) {\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    } else {\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                }\n            });\n\n            amc.fn.shouldShowBtnIndicator({\n                'pageloading': '1'\n            }, 'submit', 'loading');\n        }\n\n        function gotoHelp() {\n            getTag('pwd').blur();\n            \n            if (Boolean(rpc.HAS_OTHERS)) {\n                var buttons = ['{{changeOtherMethod}}','{{sms_help_title}}'];\n                document.actionSheet({\n                    btns : buttons,\n                    cancelBtn : '{{cancel}}'\n                }, function(data) {\n                    if (data.index == 0) {\n                        // 选择其他验证方式\n                        mic.fn.changeModule();\n                    } else if (data.index == 1) {\n                        // 跳转帮助\n                        showHelpMessage();\n                    }\n                });\n            } else {\n                showHelpMessage();\n            }\n        }\n\n        //展示帮助页面\n        function showHelpMessage() {\n            var msg = null;\n            if (Boolean(rpc.showBankHelpText)) {\n                // msg = '{{sms_alert_bank_message}}';\n                if (amc.isAndroid) {\n                    msg = gBankSmsHelpText4Android;\n                }\n                else {\n                    msg = gBankSmsHelpText4Ios;\n                }\n            }\n            else {\n                msg = '{{sms_alert_message}}';\n            }\n            amc.fn.viAlert({\n                \"title\" : '{{sms_help_title}}',\n                \"message\" : msg,\n                \"button\" : '{{got_it}}'\n            }, function() {\n                \n            });       \n        }\n\n        //### 退出\n        function onBack() {\n            mic.fn.onBack();\n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                onBack();\n            }\n        }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".phone-tip-box {\n            padding: 30px 50px;\n            background-color: #fff;\n        }\n\n        .tip-text {\n            font-size: 17px;\n            color: #777;\n            text-align: center;\n        }\n\n        .help-box {\n            margin-top: 15px;\n        }\n\n        .help-text {\n            font-size: 14px;\n            color: #1677ff;\n            text-align: center;\n            flex: 1.0;\n        }\n\n        .dlg-title-box {\n            margin-top: 21px;\n            margin-bottom: 6px;\n            padding: 0 15px;\n        }\n\n        .dlg-title {\n            font-size: 18px;\n            font-weight: bold;\n            text-align: center;\n            flex: 1;\n        }\n\n        .dlg-dot {\n            margin-top: 7px;\n            width: 4px;\n            height: 4px;\n            background-color: #777;\n            border-radius: 2px;\n        }\n\n        .dlg-list {\n            font-size: 15px;\n            margin-left: 6px;\n        }\n\n        .tip-box {\n            padding: 9px 15px 0 15px;\n        }\n\n        .pd-lr {\n            padding-left: 15px;\n            padding-right: 15px;\n        }\n\n        .pd-l {\n            padding-left: 15px;\n        }\n\n        .dlg-btn-text {\n            font-size: 17px;\n            color: #1677ff;\n            text-align: center;\n            margin: 11.5px 15px;\n        }\n\n        .dlg-line {\n            margin-top: 18px;\n        }\n\n\n        .margin-lr {\n            margin-left: 15px;\n            margin-right: 15px;\n        }\n\n        .cell-l-box {\n            width: 76px;\n        }\n\n        .cell-l-text {\n            font-size: 16px;\n        }\n\n        .input {\n            flex: 1.0;\n            border: 0;\n            color: #000;\n            font-size: 16px;\n            padding: 0px;\n            white-space: nowrap;\n        }\n\n        .resend-btn-w {\n            width: 103px;\n        }\n\n        .btn-primary {\n            background-color: #1677ff;\n            border: 0;\n            border-radius: 5px;\n            color: #fff;\n            font-size: 18px;\n            height: 42px;\n            max-height: 42px;\n            min-height: 42px;\n            flex: 1.0;\n        }\n\n        .btn-primary:active {\n            background-color: #1284D6;\n            color: #fff;\n        }\n\n        .btn-primary:disabled {\n            background-color: #ebebf0;\n            color: #fff;\n        }\n\n        .amc-dlg-width {\n            width: 270px;\n            max-width: 270px;\n            min-width: 270px;\n        }\n\n        .amc-dlg-box {\n            border: 0;\n            background-color: #fff;\n            border-radius: 8px;\n            width: 270px;\n            align-items: center;\n        }\n        .help-box {\n        margin-top: 15px;\n        }\n        .help-text {\n            font-size: 14px;\n            color: #1677ff;\n            text-align: center;\n            flex: 1.0;\n        }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{}, {"css": "pd-lr phone-tip-box amc-v-box", "children": [{"css": "tip-text amc-flex-1", "tag": "label", "id": "mainTitle"}], "tag": "div", "id": "commonFullTitle"}, {"css": "amc-main amc-scroll-flex", "children": [{"css": "amc-1px-line", "tag": "div"}, {}, {"css": "amc-cell pd-l", "children": [{"css": "cell-l-box", "children": [{"css": "cell-l-text amc-flex-1 amc-ellipsis-2-line", "children": [{"tag": "text", "text": "{{code}}"}], "tag": "label"}], "tag": "div"}, {"css": "amc-cell-m-box", "children": [{"css": "input", "sysnumber": "true", "tag": "input", "id": "pwd", "placeholder": "{{sms_code}}", "autofocus": "true", "type": "number", "value": "", "oninput": "onPwdInput()"}], "tag": "div"}, {"css": "amc-cell-v-line", "tag": "div"}, {"css": "amc-resend-btn-w", "children": [{"css": "amc-inline-btn amc-theme-color", "children": [{"tag": "text", "text": "{{resend_sms}}"}], "onclick": "onResendClick()", "disabled": "true", "tag": "button", "id": "resendBtn"}], "tag": "div"}], "tag": "div"}, {"css": "amc-1px-line", "tag": "div"}, {"css": "amc-abs-space-v-l", "tag": "div"}, {}, {"css": "margin-lr btn-primary amc-align-center amc-justify-center", "children": [{"css": "amc-loading-img amc-text-white-clolor amc-hidden", "src": "indicatior", "tag": "img", "id": "loading"}, {"css": "btn-primary", "children": [{"tag": "text", "text": "{{next}}"}], "onclick": "submitInfo()", "disabled": "true", "tag": "button", "id": "submit"}], "tag": "div"}, {}, {"css": "amc-pd-lr amc-margin-t amc-justify-end mic-other-touch-area-height", "children": [{}, {"children": [{"css": "amc-text-color-blue amc-font-m", "children": [{"tag": "text", "text": "{{changeOther<PERSON>ethod}}"}], "tag": "label", "id": "btnText"}], "onclick": "mic.fn.changeModule();", "tag": "div", "id": "othersBtn"}], "tag": "div", "id": "others"}, {"css": "help-box", "children": [{"css": "help-text", "children": [{"tag": "text", "text": "{{sms_help}}"}], "onclick": "gotoHelp();", "tag": "label"}], "tag": "div", "id": "help"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown();", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "sms", "format": "JSON", "tag": "MOBILEIC", "time": "0029", "tplId": "MOBILEIC@sms", "tplVersion": "5.2.8"}