{"template": {"version": "1", "bizId": "dressUpDetail", "priority": 0, "root": {"page": {"type": "defaultTemplate", "config": {"globalUtParams": "{{module[0].utParams}}"}, "utParams": {"pageName": "Page_OutfitChannelSoloDetail", "spmCnt": "a21zgw.29958414"}, "customConfig": [{"key": "bottomContainerLinkable", "value": "true"}]}, "components": [{"type": "dress_content_navbar", "dx": {"name": "dress_content_navbar", "url": "https://dinamicx.alibabausercontent.com/pub/dress_content_navbar/1749608521731/dress_content_navbar.zip", "version": 24, "columnType": "one"}, "renderMode": "dx"}, {"type": "bottom_comment_frame", "dx": {"name": "bottom_comment_frame", "url": "https://dinamicx.alibabausercontent.com/pub/bottom_comment_frame/1749538758046/bottom_comment_frame.zip", "version": 17, "columnType": "one"}, "renderMode": "dx"}], "layout": {"body": [{"data": "{{module[0]}}", "refId": "dressup_detail", "isTemplate": true}], "bottom": [{"type": "bottom_comment_frame", "data": "{{module[0]}}"}], "topBar": [{"type": "dress_content_navbar", "data": "{{module[0]}}"}]}}, "subTemplates": [{"name": "dressup_detail", "_skylineRecordId": "215072004", "page": {"type": "infoflowList", "customConfig": [{"key": "forbidFlowCache", "value": "true"}]}, "components": [{"type": "dressingContentBannerComponent", "renderMode": "native"}, {"type": "dress_content_title", "renderMode": "dx", "dx": {"name": "dress_content_title", "version": 37, "url": "https://dinamicx.alibabausercontent.com/pub/dress_content_title/1749472038933/dress_content_title.zip", "columnType": "one"}}, {"type": "comment_scrolbar_card", "renderMode": "dx", "dx": {"name": "comment_scrolbar_card", "version": 77, "url": "https://dinamicx.alibabausercontent.com/pub/comment_scrolbar_card/1750751358988/comment_scrolbar_card.zip", "columnType": "one"}}, {"type": "product_mount_card", "renderMode": "dx", "dx": {"name": "product_mount_card", "version": 59, "url": "https://dinamicx.alibabausercontent.com/pub/product_mount_card/1750751327126/product_mount_card.zip", "columnType": "one"}}], "layout": {"list": [{"type": "dressingContentBannerComponent", "data": "{{$}}"}, {"type": "dress_content_title", "data": "{{$}}"}, {"type": "comment_scrolbar_card", "data": "{{$}}"}, {"type": "product_mount_card", "data": "{{$}}"}]}}]}}