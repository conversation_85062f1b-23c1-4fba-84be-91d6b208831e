{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var gBankSmsHelpText4Ios = \"—— 1 ——<br>请确认该银行预留手机号是否为当前使用手机号。<br>—— 2 ——<br>请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。<br>—— 3 ——<br>由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。<br>—— 4 ——<br>若银行预留手机号已停用，请联系银行客服处理。<br>—— 5 ——<br>若您最近操作过携号转网，请等待1-2天后再试。\";\n            var gBankSmsHelpText4Android = \"1，请确认该银行预留手机号是否为当前使用手机号。\\n2，请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。\\n3，由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。\\n4，若银行预留手机号已停用，请联系银行客服处理。\\n5，若您最近操作过携号转网，请等待1-2天后再试。\";\n        \n            var rpc = amc.rpcData;\n            var getTag = amc.fn.getById;\n            var gModuleName = \"BANK_RISK_CARD\";\n            var gWindowHeight = window.innerHeight;\n            var gCardInfo = {\n                bankName : \"\",\n                bankCardLast4Num : \"\",\n                bankCardLen : 19\n            };\n            var gCardInputContainer = getTag(\"cardInputContainer\");\n            var gCardInputBox = getTag(\"cardInputBox\");\n            var gCardInput = getTag(\"cardInput\");\n            var gIdInputContainer = getTag(\"idInputContainer\");\n            var gIdInputBox = getTag(\"idInputBox\");\n            var gIdInput = getTag(\"idInput\");\n            var gPhoneInputContainer = getTag(\"phoneInputContainer\");\n            var gPhoneInputBox = getTag(\"phoneInputBox\");\n            var gPhoneInput = getTag(\"phoneInput\");\n            var gSmsInputContainer = getTag(\"smsInputContainer\");\n            var gSmsInputBox = getTag(\"smsInputBox\");\n            var gSmsInput = getTag(\"smsInput\");\n            var gSmsBottomContainer = getTag(\"smsBottomContainer\");\n            var gResendLabelContainer = getTag(\"resendLabelContainer\");\n            var gResendLabel = getTag(\"resendLabel\");\n            var gSubmitBtn = getTag(\"submitBtn\");\n            var gHighlightStyle = \"border: 1px solid #1677FF;\"\n            var gUnhighlightStyle = \"border: 1px solid #e5e5e5;\"\n            var gRiskLevel = 1; //风险级别\n            var gRiskLevelFourStep = 1; //风险级别4第几阶段\n            var gRiskLevelFourSmsLen = 4; //风险级别4短信验证码长度\n            var gTimer = null;\n            var gIsCountdowning = false; //正在倒计时\n            var gCountdownInitTime = 60;\n            var gCountdownTime = gCountdownInitTime;\n            var gResendText = '{{bind_card_sms_resend}}';\n\n            function mockData() {\n                rpc = {\n                    index : 1,\n                    body_title : '{{input_bank_info}}',\n                    form_cardno :  '{{card_no}}',\n                    form_cardno_placeholder : '{{card_no_placeholder}}',\n                    form_certno : '{{id_no}}',\n                    form_certno_placeholder : '{{id_no_placeholder}}', //需要$1替换\n                    form_mobileno : '{{mobile_no}}',\n                    form_mobileno_placeholder : '{{mobile_no_placeholder}}',\n                    form_sms : '{{bind_card_sms_code}}',\n                    form_sms_placeholder : '{{phone_sms}}',\n                    form_tip : '{{input_bank_tip}}',\n                    next_button : '{{next}}',\n\n                    userName : \"**马\",\n                    HAS_OTHERS : true,\n                    form_title : '{{verify_bank_info}}',\n                    fast_top_tip : '{{card_binded_already}}',\n                    bind_top_tip : '{{bind_new_card}}',\n                    bind_new_card_content : '{{bind_new_card_content}}',\n                    riskLevel : \"BANK_RISK_FOUR\",\n                    isSupportBindCard : true,\n                    verifyAction : \"VERIFY_BANK_RISK_FOUR\",\n                    cardInfoList : [\n                        {\n                            bankCardType : \"Debit Card\", //银行卡类型\n                            bankName : \"China merchants bank\", //银行卡名称\n                            bankCardLast4Num : \"8839\", //银行卡后四位\n                            bankCardLen : 16, //银行卡长度\n                            index: 1, //回传给服务端\n                            bankCardHolderCertNoType : 1, //绑定银行卡的证件类型\n                        },\n                        {\n                            bankCardType : \"储蓄卡\", //银行卡类型\n                            bankName : \"招商银行\", //银行卡名称\n                            bankCardLast4Num : \"8839\", //银行卡后四位\n                            bankCardLen : 19, //银行卡长度\n                            index: 2, //回传给服务端\n                            bankCardHolderCertNoType : 1, //绑定银行卡的证件类型\n                        },\n                        {\n                            bankCardType : \"储蓄卡\", //银行卡类型\n                            bankName : \"招商银行\", //银行卡名称\n                            bankCardLast4Num : \"8839\", //银行卡后四位\n                            bankCardLen : 16, //银行卡长度\n                            index: 3, //回传给服务端\n                            bankCardHolderCertNoType : 1, //绑定银行卡的证件类型\n                        }\n                    ],\n                    risk_button2 : \"其他核身方式\"\n                }\n            }\n            \n            function init() {\n                try {\n                    configData();\n                    initUI();\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit(true);\n                    }\n                } \n                catch(e) {\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit();\n                    }\n                }\n            }\n\n            function configData() {\n                // mockData();\n                //通过index，拿到当前的银行卡信息\n                if (rpc.cardInfoList && rpc.cardInfoList.length > 0) {\n                    gCardInfo = rpc.cardInfoList[rpc.index];\n                }\n                \n                //1级别只展示银行卡、2级别展示银行卡+身份证、3级别银行卡+手机号\n                if (rpc.riskLevel === \"BANK_RISK_ONE\") {\n                    gRiskLevel = 1;\n                }\n                else if (rpc.riskLevel === \"BANK_RISK_TWO\") {\n                    gRiskLevel = 2;\n                }\n                else if (rpc.riskLevel === \"BANK_RISK_THREE\") {\n                    gRiskLevel = 3;\n                }\n                else if (rpc.riskLevel === \"BANK_RISK_FOUR\") {\n                    gRiskLevel = 4;\n                }\n            }\n\n            function initUI() {\n                var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', null, null, null, onBack, null);\n                getTag(\"bodyContainer\").insertBefore(nav, getTag(\"mainBody\"));\n              \n                var mainTitle = rpc.body_title || '{{input_bank_info}}'; \n                getTag(\"mainTitle\").innerText = mainTitle;\n\n                var subTitle = gCardInfo.bankName + gCardInfo.bankCardType + \"(\" + gCardInfo.bankCardLast4Num + \")\";\n                getTag(\"subTitle\").innerText = subTitle;\n\n                //卡号输入\n                var cardInputTitle = rpc.form_cardno || '{{card_no}}'; \n                getTag(\"cardInputTitle\").innerText = cardInputTitle;\n                gCardInput.placeholder = rpc.form_cardno_placeholder || '{{card_no_placeholder}}';\n                //需要指定input的宽度\n                gCardInput.style.width = window.innerWidth - 2 * 12 - 2 * 12 + \"px\";\n                //需要算出空格有多少个\n                var realMaxLength = parseInt(gCardInfo.bankCardLen / 4) - 1 + gCardInfo.bankCardLen;\n                if (parseInt(gCardInfo.bankCardLen % 4) > 0) {\n                    realMaxLength += 1;\n                }\n                gCardInput.maxlength = realMaxLength;\n                //19位卡号与16位字体大小不一样\n                if (gCardInfo.bankCardLen <= 16) {\n                    gCardInput.style.fontSize = \"22px;\";\n                }\n                else {\n                    gCardInput.style.fontSize = \"18px;\";\n                }\n\n                //身份证输入\n                var idInputTitle = rpc.form_certno || '{{id_no}}'; \n                getTag(\"idInputTitle\").innerText = idInputTitle;\n                var certNoPlaceholder = rpc.form_certno_placeholder || '';\n                if (certNoPlaceholder.indexOf(\"$1\") != -1 && rpc.userName) {\n                    certNoPlaceholder = certNoPlaceholder.replace(\"$1\",rpc.userName);\n                } \n                gIdInput.placeholder = certNoPlaceholder || '{{id_no_placeholder}}';\n                //需要指定input的宽度\n                gIdInput.style.width = window.innerWidth - 2 * 12 - 2 * 12 + \"px\";\n                gIdInputContainer.style.marginTop = \"12px\";\n\n                //手机号输入\n                var phoneInputTitle = rpc.form_mobileno || '{{mobile_no}}'; \n                getTag(\"phoneInputTitle\").innerText = phoneInputTitle;\n                gPhoneInput.placeholder = rpc.form_mobileno_placeholder || '{{mobile_no_placeholder}}';\n                //需要指定input的宽度\n                gPhoneInput.style.width = window.innerWidth - 2 * 12 - 2 * 12 + \"px\";\n                gPhoneInputContainer.style.marginTop = \"12px\";\n\n                //验证码输入\n                var smsInputTitle = rpc.form_sms || '{{bind_card_sms_code}}'; \n                getTag(\"smsInputTitle\").innerText = smsInputTitle;\n                gSmsInput.placeholder = rpc.form_sms_placeholder || '{{bind_card_sms_code_placeholder}}';\n                //需要指定input的宽度\n                gSmsInput.style.width = window.innerWidth - 2 * 12 - 2 * 12 - 100 + \"px\";\n                gSmsInputContainer.style.marginTop = \"12px\";\n\n                //验证码下方提示\n                gSmsBottomContainer.style.width = window.innerWidth - 2 * 12 + \"px\";\n                getTag(\"smsTipIcon\").src = mic.path + \"alipay_vi_tips\";\n                getTag(\"smsTipIcon\").onclick = function() {\n                    showHelpMessage();\n                };\n                getTag(\"smsTipLabel\").style.width = window.innerWidth - 2 * 12 - 25 + \"px\";\n                getTag(\"smsTipLabel\").innerText = '{{bind_card_sms_tip}}';\n\n                //tip\n                var tipTitle = rpc.form_tip || '{{input_bank_tip}}'; \n                getTag(\"tipTitle\").innerText = tipTitle;\n\n                //按钮\n                gSubmitBtn.innerText = rpc.next_button || '{{next}}';\n\n                //其他核身方式\n                getTag(\"changeModule\").innerText = rpc.risk_button2 || '{{change_other_way}}';\n                if (Boolean(rpc.HAS_OTHERS)) {\n                    getTag(\"changeModule\").style.visibility = \"visible\";\n                }\n                \n                onCardInputFocus();\n                onIdInputFocus();\n                onPhoneInputFocus();\n                onSmsInputFocus();\n\n                var timeInterval = 100;\n                if (amc.isAndroid) {\n                    timeInterval = 400;\n                }\n                setTimeout(function() {\n                    //卡号输入先聚焦\n                    gCardInput.focus();\n                }, timeInterval);\n            }\n\n            function checkSubmitState() {\n                var cardNo = gCardInput.value.replace(/\\s/g, \"\");\n                if (gRiskLevel == 1) {\n                    //级别1，只验证卡号\n                    if (cardNo.length === gCardInfo.bankCardLen) {\n                        gSubmitBtn.disabled = false;\n                    }\n                    else {\n                        gSubmitBtn.disabled = true;\n                    }\n                }\n                else if (gRiskLevel == 2) {\n                    if (gIdInput.value.length > 0 && cardNo.length === gCardInfo.bankCardLen) {\n                        gSubmitBtn.disabled = false;\n                    }\n                    else {\n                        gSubmitBtn.disabled = true;\n                    }\n                }\n                else if (gRiskLevel == 3) {\n                    if (gPhoneInput.value.length > 0 && cardNo.length === gCardInfo.bankCardLen) {\n                        gSubmitBtn.disabled = false;\n                    }\n                    else {\n                        gSubmitBtn.disabled = true;\n                    }\n                }\n                else if (gRiskLevel == 4) {\n                    if (gRiskLevelFourStep == 1) {\n                        //第一阶段时\n                        if (gPhoneInput.value.length > 0 && cardNo.length === gCardInfo.bankCardLen) {\n                            gSubmitBtn.disabled = false;\n                        }\n                        else {\n                            gSubmitBtn.disabled = true;\n                        }\n                    }\n                    else {\n                        //第二阶段\n                        if (gSmsInput.value.length == gRiskLevelFourSmsLen) {\n                            gSubmitBtn.disabled = false;\n                        }\n                        else {\n                            gSubmitBtn.disabled = true;\n                        }\n                    }\n                }\n            }\n\n            function onCardInputChanged() {\n                var text = gCardInput.value || \"\";\n                var trimedText = text.replace(/\\s/g, \"\");\n                \n                var cardNo = gCardInput.value.replace(/\\s/g, \"\");\n                if(cardNo.length > 0){\n                    onCardInputBlur();\n                }else{\n                    onCardInputFocus();\n                }\n                \n                //按钮是否可以点击\n                checkSubmitState();\n                \n                if (!trimedText) {\n                    return;\n                }\n                gCardInput.value = (trimedText.replace(/([\\d|\\*]{4})(?=[\\d|\\*])/g, \"$1\" + \" \")).trim() || \"\";\n\n                //判断是否输入字符最大\n                var cardNo = gCardInput.value.replace(/\\s/g, \"\");\n\n                if (gRiskLevel == 2) {\n                    //显示身份证输入框\n                    if (cardNo.length === gCardInfo.bankCardLen) {\n                        if (gIdInputContainer.style.display != \"flex\") {\n                            gIdInputContainer.style.display = \"flex\";\n                            //从无到有的动画\n                            gIdInputContainer.animation = [{\n                                \"type\": \"alpha\",\n                                \"fromValue\": 0.0,\n                                \"toValue\": 1,\n                                \"duration\": 600\n                            }];\n                            gIdInput.focus();\n                        }\n                    }\n                }\n                else if (gRiskLevel == 3 || gRiskLevel == 4) {\n                    //显示手机号输入框\n                    if (cardNo.length === gCardInfo.bankCardLen) {\n                        if (gPhoneInputContainer.style.display != \"flex\") {\n                            gPhoneInputContainer.style.display = \"flex\";\n                            //从无到有的动画\n                            gPhoneInputContainer.animation = [{\n                                \"type\": \"alpha\",\n                                \"fromValue\": 0.0,\n                                \"toValue\": 1,\n                                \"duration\": 600\n                            }];\n                            gCardInput.blur();\n                            setTimeout(function() {\n                                gPhoneInput.focus();\n                            }, 200);\n                        }\n                    }\n                }\n            }\n\n            function onCardInputBlur() {\n                gCardInputBox.style.cssText = gUnhighlightStyle;\n            }\n\n            function onCardInputFocus() {\n                gCardInputBox.style.cssText = gHighlightStyle;\n            }\n\n            function onIdInputChanged() {\n                checkSubmitState();\n                if (gIdInput.value.length > 0) {\n                    onIdInputBlur();\n                } \n                else {\n                    onIdInputFocus();\n                }\n            }\n\n            function onIdInputBlur() {\n                gIdInputBox.style.cssText = gUnhighlightStyle;\n            }\n\n            function onIdInputFocus() {\n                gIdInputBox.style.cssText = gHighlightStyle;\n            }\n\n            function onPhoneInputChanged() {\n                checkSubmitState();\n                if (gPhoneInput.value.length > 0) {\n                    onPhoneInputBlur();\n                }\n                else {\n                    onPhoneInputFocus();\n                }\n            }\n\n            function onPhoneInputBlur() {\n                gPhoneInputBox.style.cssText = gUnhighlightStyle;\n            }\n\n            function onPhoneInputFocus() {\n                gPhoneInputBox.style.cssText = gHighlightStyle;\n            }\n\n            function onSmsInputBlur() {\n                gSmsInputBox.style.cssText = gUnhighlightStyle;\n            }\n\n            function onSmsInputFocus() {\n                gSmsInputBox.style.cssText = gHighlightStyle;\n            }\n\n            function onSmsInputChanged() {\n                checkSubmitState();\n                if (gSmsInput.value.length > 0) {\n                    onSmsInputBlur();\n                }\n                else {\n                    onSmsInputFocus();\n                }\n            }   \n\n            function resendClick() {\n                if (gIsCountdowning) {\n                    return;\n                }\n                var obj = {\n                    \"eventName\": \"vi_rpc_validate\",\n                    \"moduleName\": gModuleName,\n                    \"actionName\": \"RESEND_SMS_ACTION\",\n                    \"showLoading\": \"true\"\n                };\n                document.asyncSubmit(obj, function(data) {\n                    var verifyMessage = data[\"verifyMessage\"] || \"人气太旺了，请稍后再试\";\n                    var renderData = data[\"data\"];\n                    try {\n                        renderData = JSON.parse(renderData);\n                    } catch (res) {\n                        renderData = {};\n                    }\n                    var code = renderData[\"code\"];\n                    if (code === \"VALIDATECODE_SEND_SUCCESS\") {\n                        //重发短信成功，开始倒计时，同时输入框聚焦\n                        startCountdown();\n                        gSmsInput.value = \"\";\n                        gSmsInput.focus();\n                        gSubmitBtn.disabled = true;\n                        return;\n                    }\n\n                    //失败\n                    if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === gModuleName) {\n                        document.toast({\n                            text: verifyMessage\n                        }, function() {});\n                    } else {\n                        //次数限制\n                        amc.fn.viAlert({\n                            \"title\": \"\",\n                            \"message\": verifyMessage,\n                            \"button\": '{{got_it}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                });\n            }\n            \n            function onBack() {\n                hideKeyboard();\n                setTimeout(function() {\n                    obj = {\n                       \"eventName\" : \"vi_quit_module_with_retrieve\",\n                       \"exitType\" :\"bank_exit\"\n                    };\n                document.submit(obj);\n                }, 200);\n            }\n\n            function hideKeyboard() {\n                gCardInput.blurForceLostFocus();\n                gIdInput.blurForceLostFocus();\n                gPhoneInput.blurForceLostFocus();\n                gSmsInput.blurForceLostFocus();\n            }\n\n            function submit() {\n                hideKeyboard();\n                var cardNo = gCardInput.value.replace(/\\s/g, \"\");\n                var certNo = gIdInput.value;\n                var mobileNo = gPhoneInput.value;\n                var index = gCardInfo.index;\n                var riskLevelFourAction = \"\";\n                var params = {\n                    \"cardNo\": cardNo || \"\",\n                    \"certNo\": certNo || \"\",\n                    \"mobileNo\": mobileNo || \"\",\n                    \"index\": index\n                };\n                //4级银行卡需要加入action字段，标识当前验证第几阶段\n                if (gRiskLevel == 4) {\n                    if (gRiskLevelFourStep == 1) {\n                        params[\"action\"] = \"VERIFY_CARD_ACTION\";\n                    }\n                    else {\n                        var ackCode = gSmsInput.value;\n                        params = {\n                            \"action\": \"VERIFY_SMS_ACTION\",\n                            \"ackCode\": ackCode\n                        };\n                    }\n                }\n                var obj = {\n                    \"eventName\": \"vi_rpc_validate\",\n                    \"moduleName\": gModuleName,\n                    \"actionName\": rpc.verifyAction || \"\",\n                    \"showLoading\": \"true\",\n                    \"params\": params\n                };\n                document.asyncSubmit(obj, function(data) {\n                    if (gRiskLevel == 4) {\n                        //四级验证单独处理，不影响1-3的验证\n                        riskLevelFourRequestHandler(data);\n                        return;\n                    }\n                    var verifyMessage = data[\"verifyMessage\"] || \"人气太旺了，请稍后再试\";\n                    if (Boolean(data[\"verifySuccess\"])) {\n                        //验证成功\n                        mic.fn.onBackWithResponse(data);\n                        return;\n                    }\n                    \n                    //验证失败\n                    if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === gModuleName) {\n                        document.toast({\n                            text: verifyMessage\n                        }, function() {});\n                    } else {\n                        //次数限制\n                        amc.fn.viAlert({\n                            \"title\": \"\",\n                            \"message\": verifyMessage,\n                            \"button\": '{{got_it}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                });\n            }\n\n            function riskLevelFourRequestHandler(data) {\n                if (gRiskLevelFourStep == 1) {\n                    var renderData = data[\"data\"];\n                    try {\n                        renderData = JSON.parse(renderData);\n                    } catch (error) {\n                        renderData = {};\n                    }\n                    //返回短信验证码的长度\n                    if (renderData[\"ackCodeLen\"]) {\n                        gRiskLevelFourSmsLen = parseInt(renderData[\"ackCodeLen\"]);\n                    }\n                    var sendAckCodeSuccess = renderData[\"sendAckCodeSuccess\"];\n\n                    //第一阶段验证成功\n                    if (sendAckCodeSuccess && sendAckCodeSuccess === \"true\") {\n                        riskLevelFourChangeToStepTwo();\n                        return;\n                    }\n                    //验证失败\n                    riskLevelFourVerifyFail(data);\n                }\n                else {\n                    //第二阶段验证成功\n                    if (Boolean(data[\"verifySuccess\"])) {\n                        //验证成功\n                        mic.fn.onBackWithResponse(data);\n                        return;\n                    }\n                    //验证失败\n                    riskLevelFourVerifyFail(data);\n                }\n            }\n\n            function riskLevelFourVerifyFail(data) {\n                var verifyMessage = data[\"verifyMessage\"] || \"人气太旺了，请稍后再试\";\n                //验证失败\n                if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === gModuleName) {\n                    document.toast({\n                        text: verifyMessage\n                    }, function() {});\n                } \n                else {\n                    //次数限制\n                    amc.fn.viAlert({\n                        \"title\": \"\",\n                        \"message\": verifyMessage,\n                        \"button\": '{{got_it}}'\n                    }, function() {\n                        mic.fn.onBackWithResponse(data);\n                    });\n                }\n            }\n\n            //风险级别4切换到二阶段\n            function riskLevelFourChangeToStepTwo() {\n                //验证成功，展示验证码输入框，卡号+手机号不可置灰点击\n                //开始倒计时，置灰提交按钮，输入验证码后且长度一致后方可点击\n                //同时手机号和卡号不允许再点击\n                gRiskLevelFourStep = 2;\n                gSubmitBtn.disabled = true;\n                gCardInput.disabled = true;\n                gPhoneInput.disabled = true;\n                gCardInput.style.color = \"#b4b4b4\";\n                gPhoneInput.style.color = \"#b4b4b4\";\n                gSmsInput.maxlength = gRiskLevelFourSmsLen;\n\n                if (gSmsInputContainer.style.display != \"flex\") {\n                    gSmsInputContainer.style.display = \"flex\";\n                    //从无到有的动画\n                    gSmsInputContainer.animation = [{\n                        \"type\": \"alpha\",\n                        \"fromValue\": 0.0,\n                        \"toValue\": 1,\n                        \"duration\": 600\n                    }];\n                    gSmsBottomContainer.style.display = \"flex\";\n                    gSmsBottomContainer.animation = [{\n                        \"type\": \"alpha\",\n                        \"fromValue\": 0.0,\n                        \"toValue\": 1,\n                        \"duration\": 600\n                    }];\n                    gPhoneInput.blur();\n                    setTimeout(function() {\n                        gSmsInput.focus();\n                    }, 200);\n                }\n\n                //开始倒计时\n                startCountdown();\n            }\n\n            //帮助弹窗\n            function showHelpMessage() {\n                var msg = null;\n                if (Boolean(rpc.showBankHelpText)) {\n                    if (amc.isAndroid) {\n                        msg = gBankSmsHelpText4Android;\n                    }\n                    else {\n                        msg = gBankSmsHelpText4Ios;\n                    }\n                }\n                else {\n                    msg = '{{sms_alert_message}}';\n                }\n                amc.fn.viAlert({\n                    \"title\" : '{{sms_help_title}}',\n                    \"message\" : msg,\n                    \"button\" : '{{i_know_it}}'\n                }, function() {}); \n            }\n\n            function startCountdown() {\n                disableTimer();\n                gIsCountdowning = true;\n                gCountdownTime = gCountdownInitTime;\n                gResendLabel.disabled = true;\n                gResendLabel.innerText = gResendText + \"(\" + gCountdownTime + \")\";\n                gResendLabel.style.color = \"#A3CAFD\";\n                gResendLabelContainer.style.cssText = \"border: 1px solid #A3CAFD;\";\n\n                gTimer = setInterval(function() {\n                    gCountdownTime--;\n                    if (gCountdownTime == 0) {\n                        disableTimer();\n                        gCountdownTime = gCountdownInitTime;\n                        gIsCountdowning = false;\n                        gResendLabel.disabled = false;\n                        gResendLabel.innerText = gResendText;\n                        gResendLabel.style.color = \"#1677ff\";\n                        gResendLabelContainer.style.cssText = \"border: 1px solid #1677ff;\";\n                        return;\n                    }\n                    gResendLabel.innerText = gResendText + \"(\" + gCountdownTime + \")\";\n                }, 1000);\n            }\n\n            function disableTimer() {\n                if (gTimer) {\n                    clearInterval(gTimer);\n                    gTimer = null;\n                }\n            }\n\n            function changeModule() {\n                hideKeyboard();\n                mic.fn.changeModule();\n            }\n            \n            function bodyOnClick() {\n                hideKeyboard();\n            }\n\n            function onKeyDown() {\n                if (event.which == 4) {\n                    onBack();\n                }\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n                background-color: #ffffff;\n                flex-direction: column;\n                display: flex;\n                overflow: scroll;\n            }\n        \n            .main-title-label {\n                text-align: center;\n                font-size: 24px;\n                font-weight: bold;\n                color: #333333;\n                line-height: 38px;\n                margin-top: 12px;\n                margin-left: 30px;\n                margin-right: 30px;\n            }\n\n            .sub-title-label {\n                text-align: center;\n                font-size: 18px;\n                color: #424242;\n                line-height: 25px;\n                margin-top: 12px;\n                margin-left: 30px;\n                margin-right: 30px;\n            }\n\n            .input-box-container {\n                margin-top: 30px;\n                margin-left: 12px;\n                margin-right: 12px;\n                display: flex;\n                flex-direction: column;\n            }\n\n            .input-title-label {\n                text-align: left;\n                font-size: 15px;\n                color: #333333;\n                line-height: 21px;\n                margin-bottom: 8px;\n            }\n\n            .input-box {\n                border: 1px solid #e5e5e5;\n                height: 60px;\n                border-radius: 4px;\n            }\n\n            .card-input {\n                color: #333333;\n                font-size: 22px;\n                font-weight: bold;\n                white-space: nowrap;\n                margin-left: 12px;\n                margin-right: 12px;\n                align-self: center;\n                height: 60px;\n            }\n\n            .id-input {\n                color: #333333;\n                font-size: 18px;\n                font-weight: bold;\n                white-space: nowrap;\n                margin-left: 12px;\n                height: 60px;\n                align-self: center;\n            }\n\n            .phone-input {\n                color: #333333;\n                font-size: 22px;\n                font-weight: bold;\n                white-space: nowrap;\n                margin-left: 12px;\n                height: 60px;\n                align-self: center;\n            }\n\n            .sms-input {\n                color: #333333;\n                font-size: 22px;\n                font-weight: bold;\n                white-space: nowrap;\n                margin-left: 12px;\n                height: 60px;\n                align-self: center;\n            }\n\n            .resend-container {\n                width: 100px;\n                height: 26px;\n                border: 1px solid #1677ff;\n                border-radius: 13px;\n                align-self: center;\n            }\n\n            .resend-title-label {\n                width: 100px;\n                height: 26px;\n                color: #1677ff;\n                font-size: 13px;\n                text-align: center;\n            }\n\n            .sms-bottom-container {\n                display: flex;\n                flex-direction: row;\n                margin-top: 9px;\n                margin-left: 12px;\n            }\n\n            .sms-tip-icon {\n                margin-top: 3px;\n                width: 18px;\n                height: 18px;\n            }\n\n            .sms-tip-label {\n                color: #999999;\n                font-size: 15px;\n                line-height: 21px;\n                margin-left: 5px;\n            }\n\n            .tip-title-label {\n                color: #999999;\n                font-size: 15px;\n                line-height: 21px;\n                margin-top: 45px;\n                margin-left: 12px;\n                margin-right: 12px;\n            }\n\n            .submit-btn {\n                margin-top: 12px;\n                margin-left: 12px;\n                margin-right: 12px;\n                border-radius: 4px;\n                border: 0;\n                color: #fff;\n                font-size: 18px;\n                height: 49px;\n                background-color: #1677FF;\n            }\n            \n            .submit-btn:active {\n                background-color: #1677FF;\n                color: #fff;\n             }\n\n            .submit-btn:disabled {\n                background-color: #A3CAFD;\n                color: #FAFCFF;\n             }\n\n            .change-module-label {\n                text-align: center;\n                font-size: 18px;\n                color: #1677FF;\n                line-height: 22px;\n                margin-top: 15px;\n                margin-left: 12px;\n                margin-right: 12px;\n                align-self: center;\n                visibility: hidden; \n            }\n\n            .node-hidden {\n                display: none; \n            }\n            \n            .block-size {\n                height: 50px;\n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "amc-scroll-flex main-body", "children": [{"css": "main-title-label", "tag": "label", "id": "mainTitle"}, {"css": "sub-title-label", "tag": "label", "id": "subTitle"}, {"css": "input-box-container", "children": [{"css": "input-title-label", "tag": "label", "id": "cardInputTitle"}, {"css": "input-box", "children": [{"css": "card-input", "tag": "input", "id": "cardInput", "type": "number", "oninput": "onCardInputChanged()"}], "tag": "div", "id": "cardInputBox"}], "tag": "div", "id": "cardInputContainer"}, {"css": "input-box-container node-hidden", "children": [{"css": "input-title-label", "tag": "label", "id": "idInputTitle"}, {"css": "input-box", "children": [{"css": "id-input", "tag": "input", "id": "idInput", "type": "idcard", "oninput": "onIdInputChanged()"}], "tag": "div", "id": "idInputBox"}], "tag": "div", "id": "idInputContainer"}, {"css": "input-box-container node-hidden", "children": [{"css": "input-title-label", "tag": "label", "id": "phoneInputTitle"}, {"css": "input-box", "children": [{"keyboard": "safe", "css": "phone-input", "tag": "input", "id": "phoneInput", "type": "phone", "oninput": "onPhoneInputChanged()"}], "tag": "div", "id": "phoneInputBox"}], "tag": "div", "id": "phoneInputContainer"}, {"css": "input-box-container node-hidden", "children": [{"css": "input-title-label", "tag": "label", "id": "smsInputTitle"}, {"css": "input-box", "children": [{"css": "sms-input", "tag": "input", "id": "smsInput", "type": "number", "oninput": "onSmsInputChanged()"}, {"css": "resend-container", "children": [{"css": "resend-title-label", "onclick": "resendClick()", "tag": "label", "id": "resend<PERSON>abel"}], "tag": "div", "id": "resend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "tag": "div", "id": "smsInputBox"}], "tag": "div", "id": "smsInputContainer"}, {"css": "sms-bottom-container node-hidden", "children": [{"css": "sms-tip-icon", "tag": "img", "id": "smsTipIcon"}, {"css": "sms-tip-label", "tag": "label", "id": "smsTipLabel"}], "tag": "div", "id": "smsBottomContainer"}, {"css": "tip-title-label", "tag": "label", "id": "tipTitle"}, {"css": "submit-btn", "onclick": "submit()", "disabled": "true", "tag": "button", "id": "submitBtn"}, {"css": "change-module-label", "onclick": "changeModule()", "tag": "label", "id": "changeModule"}, {"css": "block-size", "tag": "div", "id": "blankpard"}], "onclick": "bodyOnClick()", "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "bankRiskCardVerify", "format": "JSON", "tag": "MOBILEIC", "time": "0100", "tplId": "MOBILEIC@bankRiskCardVerify", "tplVersion": "5.5.3"}