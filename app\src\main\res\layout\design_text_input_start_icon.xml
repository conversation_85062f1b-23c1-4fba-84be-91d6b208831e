<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.internal.CheckableImageButton xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="start|center_vertical"
    android:id="@+id/text_input_start_icon"
    android:background="?attr/actionBarItemBackground"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="48dp"
    android:minHeight="48dp"
    android:layout_marginEnd="@dimen/mtrl_textinput_start_icon_margin_end"/>
