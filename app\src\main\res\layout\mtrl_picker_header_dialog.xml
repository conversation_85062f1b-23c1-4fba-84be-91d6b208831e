<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/mtrl_calendar_header_height">
    <LinearLayout
        android:layout_gravity="top"
        android:orientation="horizontal"
        android:id="@+id/mtrl_picker_header"
        android:paddingLeft="@dimen/mtrl_calendar_header_content_padding"
        android:paddingRight="@dimen/mtrl_calendar_header_content_padding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/mtrl_calendar_header_height"
        android:baselineAligned="false"
        android:paddingStart="@dimen/mtrl_calendar_header_content_padding"
        android:paddingEnd="@dimen/mtrl_calendar_header_content_padding"
        style="?attr/materialCalendarHeaderLayout">
        <FrameLayout
            android:id="@+id/mtrl_picker_header_title_and_selection"
            android:paddingLeft="@dimen/mtrl_calendar_header_text_padding"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingStart="@dimen/mtrl_calendar_header_text_padding">
            <include layout="@layout/mtrl_picker_header_title_text"/>
            <include layout="@layout/mtrl_picker_header_selection_text"/>
        </FrameLayout>
        <include layout="@layout/mtrl_picker_header_toggle"/>
    </LinearLayout>
    <View
        android:layout_gravity="bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/mtrl_calendar_header_divider_thickness"
        style="?attr/materialCalendarHeaderDivider"/>
</merge>
