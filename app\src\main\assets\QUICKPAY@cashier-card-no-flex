{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"cancel": "取消", "phone_no": "手機號", "bind_v2_confirm": "免輸卡號綁卡", "bind_card_description": "已和以下銀行合作，可查詢本人卡號", "bind_card_tip": "免輸卡號添加", "card_placeholder_user": "點擊輸入本人銀行卡號", "security_info": "信息加密處理，僅用於銀行驗證", "select_bank": "選擇銀行和卡類型", "next_step": "提交卡號", "card_placeholder": "點擊輸入 #val# 的銀行卡號", "phone_tip_5": "為了提升您的服務體驗，避免重複填寫信息，將為您智能推薦填寫您名下支付寶賬號近期綁定銀行卡時已通過驗證的手機號。", "bank_card_type": "卡類型", "bank_support": "支持#val#家銀行", "bind_v2_tip": "銀行卡號輸入有誤，建議使用免輸卡號功能，快速完成綁卡。", "input_title": "輸入卡號添加", "quick_bind": "免輸卡號綁卡", "OK": "確定", "valid_bank": "已支持<font weight=\"bold\">中國銀行|交通銀行|平安銀行</font>", "valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安銀行</font>", "recently_used": "最近使用过： ", "auto_input_no": "立即粘貼", "phone_tip": "手機號是你在辦理該銀行卡時所填寫的手機號或該銀行認可的協力廠商機构（如電信運營商等）辦理的本人手機號碼。", "add_new_card": "添加銀行卡", "more": "查看更多", "copy_card_no": "您已復制卡號", "fold": "收起", "propagate_title": "支付寶全力保護你的信息安全", "other_option": "或選擇", "scan_card_text": "點擊掃描銀行卡", "scan_card": "拍照添卡"}, "zh_TW": {"cancel": "取消", "phone_no": "手機號", "bind_v2_confirm": "免輸卡號綁卡", "bind_card_description": "已和以下銀行合作，可查詢本人卡號", "bind_card_tip": "免輸卡號添加", "card_placeholder_user": "點擊輸入本人銀行卡號", "security_info": "信息加密處理，僅用於銀行驗證", "select_bank": "選擇銀行和卡類型", "next_step": "提交卡號", "card_placeholder": "點擊輸入 #val# 的銀行卡號", "phone_tip_5": "為了提升您的服務體驗，避免重複填寫信息，將為您智能推薦填寫您名下支付寶賬號近期綁定銀行卡時已通過驗證的手機號。", "bank_card_type": "卡類型", "bank_support": "支持#val#家銀行", "bind_v2_tip": "銀行卡號輸入有誤，建議使用免輸卡號功能，快速完成綁卡。", "input_title": "輸入卡號添加", "quick_bind": "免輸卡號綁卡", "OK": "確定", "valid_bank": "已支持<font weight=\"bold\">中國銀行|交通銀行|平安銀行</font>", "valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安銀行</font>", "recently_used": "最近使用过： ", "auto_input_no": "立即粘貼", "phone_tip": "手機號是你在辦理該銀行卡時所填寫的手機號或該銀行認可的協力廠商機构（如電信運營商等）辦理的本人手機號碼。", "add_new_card": "添加銀行卡", "more": "查看更多", "copy_card_no": "您已復制卡號", "fold": "收起", "propagate_title": "支付寶全力保護你的信息安全", "other_option": "或選擇", "scan_card_text": "點擊掃描銀行卡", "scan_card": "拍照添卡"}, "en_US": {"cancel": "Cancel", "phone_no": "Mobile", "bind_v2_confirm": "Fast binding", "bind_card_description": "Have cooperated with the following banks, you can check your card number", "bind_card_tip": "No need to add bank card No.", "card_placeholder_user": "Tap to enter bank card No.", "security_info": "Information encryption processing, only for bank verification", "select_bank": "Select Bank and Card Type", "next_step": "Submit", "phone_tip_5": "To improve your service experience and avoid filling in repeated information, you will be intellegetly recommended the phone no. that hads passed the verification when you added the lastest bank card.", "card_placeholder": "Tap to enter bank card No. of #val#", "bank_card_type": "Card Type", "bank_support": "#val# banks supported", "bind_v2_tip": "The input of bank card number is incorrect. It is recommended to use the function of no input card number to bind the card quickly.", "input_title": "Add bank card No.", "quick_bind": "No need to add bank card No.", "OK": "Sure", "valid_bank": "<font weight=\"bold\">BOC, BOCOM, PAB</font> Available", "valid_bank_icbc": "<font weight=\"bold\">ICBC, BOC, BOCOM, PAB</font> Available", "recently_used": "Recently Used: ", "auto_input_no": "Paste now", "add_new_card": "Add Bank Card", "more": "View more", "phone_tip_4": "For further help, call us at +*************.", "phone_tip_3": "If your phone no. is not a Chinese number，please enter “country code-phone no.”.", "phone_tip_2": "If the phone no. is not registered in your bank，forgotten or out of use, please contact the bank for help.", "phone_tip_1": "Phone no. should be the same as the number you left in the bank. It is recommended to fill in the form according to the bank document.", "copy_card_no": "You have copied the card number", "fold": "fold", "phone": "Phone Number Description", "propagate_title": "<PERSON><PERSON><PERSON> makes every effort to protect your information.", "other_option": "Or choose", "scan_card_text": "Tap to scan card", "scan_card": "Photo card"}, "zh_CN": {"cancel": "取消", "bind_v2_confirm": "免输卡号绑卡", "phone_no": "手机号", "bind_card_description": "已和以下银行合作，可查询本人卡号", "bind_card_tip": "免输卡号添加", "card_placeholder_user": "点击输入本人银行卡号", "security_info": "信息加密处理，仅用于银行验证", "select_bank": "选择银行和卡类型", "next_step": "提交卡号", "card_placeholder": "点击输入 #val# 的银行卡号", "phone_tip_5": "为了提升您的服务体验，避免重复填写信息，将为您智能推荐填写您名下支付宝账号近期绑定银行卡时已通过验证的手机号。", "bank_card_type": "卡类型", "bank_support": "支持#val#家银行", "bind_v2_tip": "银行卡号输入有误，建议使用免输卡号功能，快速完成绑卡。", "input_title": "输入卡号添加", "quick_bind": "免输卡号绑卡", "OK": "确定", "valid_bank": "已支持<font weight=\"bold\">中国银行|交通银行|平安银行</font>", "valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安银行</font>", "recently_used": "最近使用过： ", "auto_input_no": "立即粘贴", "phone_tip": "手机号是你在办理该银行卡时所填写的手机号或该银行认可的第三方机构（如电信运行商等）办理的本人手机号码。", "add_new_card": "添加银行卡", "more": "查看更多", "copy_card_no": "您已复制卡号", "fold": "收起", "propagate_title": "支付宝全力保护你的信息安全", "other_option": "或选择", "scan_card_text": "点击扫描银行卡", "scan_card": "拍照添卡"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"src": "AlipaySDK.bundle/amc-meta.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": ".amc-nav-horiz-line-android{background-color:#FFFFFF;height:0PX}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._CardNoPage_o8by-c-title{font-size:22px;color:#333333;font-weight:bold;margin-top:20px}._CardNoPage_o8by-c-main-body{justify-items:flex-start}._CardNoPage_o8by-c-card-pd-lr{padding:0 16px}._CardNoPage_o8by-c-sub-title{font-size:14px;color:#333333;margin-left:4pc}._CardNoPage_o8by-c-sub-title-logo{width:15px;height:15px}._CardNoPage_o8by-c-sub-title-box{flex-direction:row;margin-top:11px}._CardNoPage_o8by-c-input-card-no-box{padding:16px;margin-top:16px;margin-bottom:16px;border-radius:8px;background-color:#FFFFFF}._CardNoPage_o8by-c-input-card-no-tip{flex-direction:row;align-content:center;justify-items:flex-start}._CardNoPage_o8by-c-input-card-no-tip-1{font-size:18px;color:#333333;font-weight:bold}._CardNoPage_o8by-c-input-card-no-tip-2{font-size:14px;color:#999999;align-self:flex-end}._CardNoPage_o8by-c-stretch-item{align-self:stretch}._CardNoPage_o8by-c-card-no-cell{border:1px solid #e1e1e1;border-radius:4px;height:52px;background-color:#fff;align-items:center;margin-top:16px;padding:16px 13px}._CardNoPage_o8by-c-card-no-input{flex:1.0;border:0;color:#333333;font-size:16px;padding:0;white-space:nowrap;placeholder-color:#999999}._CardNoPage_o8by-c-blank{height:50px;width:100px;opacity:0}._CardNoPage_o8by-c-camera-img{width:27px;height:27px}._CardNoPage_o8by-c-recent-card-area{margin-top:8px}._CardNoPage_o8by-c-recently-used{font-size:14px;color:#999999}._CardNoPage_o8by-c-recently-card{font-size:14px;color:#4B6B99}._CardNoPage_o8by-c-button-blank{height:8px}._CardNoPage_o8by-c-ocr-image{margin-top:12px;border-radius:4px}._CardNoPage_o8by-c-header-background{background-image:linear-gradient(to bottom, #FFFFFF 60%, #F5F5F5 100%);height:165px;align-self:stretch}._CardNoPage_o8by-c-bottom-background{background-color:#F5F5F5}._CardNoPage_o8by-c-bank-list-blank{height:32px}._CardNoPage_o8by-c-dcep-container{background-color:#fff;border-radius:8px;margin-top:18px;align-self:stretch}._CardNoPage_o8by-c-dcep-title{font-size:14px;color:#333333;letter-spacing:0;text-align:center;line-height:20px;margin-top:20px;margin-bottom:3px}._CardNoPage_o8by-c-dcep-inst-container{flex-wrap:wrap;display:flex;flex-direction:row;justify-content:space-between;align-items:center;align-self:stretch;padding-left:23px;padding-right:23px}._CardNoPage_o8by-c-dcep-inst{width:30%;margin-top:20px}._CardNoPage_o8by-c-camera-area{background-color:#E3EFFF;border-radius:6px;height:150px;justify-self:stretch;margin-top:19px}._CardNoPage_o8by-c-camera-box{background-color:#FFFFFF;height:40px;width:40px;border-radius:20px}._CardNoPage_o8by-c-camera-text{font-size:17px;color:#4B6B99;margin-top:10px;font-weight:bold}._CardNoPage_o8by-c-more-box{flex-direction:row;padding:12px 16px}._CardNoPage_o8by-c-more-content{font-size:14px;color:#999999}._CardNoPage_o8by-c-more-img{width:12px;height:12px;margin-left:4px}._CardNoPage_o8by-c-card-list-divide{align-self:stretch;background-color:#EFEFEF;opacity:0.75;height:1px;margin-top:12px}._CardNoPage_o8by-c-wk-logo{width:250px;height:23px;margin-top:4px}._CardInfo_35iu-c-main-body{padding:0;margin-top:16px}._CardInfo_35iu-c-cert-detail-box{border:#E0E0E0 1px;border-radius:4px;background-color:#ffffff}._CardInfo_35iu-c-cert-input-box{height:49px;align-items:center;padding:0 16px}._CardInfo_35iu-c-cert-item-title{font-size:16px;color:#333;text-align:left;width:70px;max-width:90px}._CardInfo_35iu-c-cert-item-input{flex:1.0;border:0;color:#333;padding:0;font-size:16px;-webkit-line-clamp:1;placeholder-color:#CCCCCC}._CardInfo_35iu-c-protocol-box{text-align:left;margin:24px 0 0}._CardInfo_35iu-c-protocol-title{white-space:nowrap;color:#333333;font-size:14px;-webkit-line-clamp:1}._CardInfo_35iu-c-protocol{font-size:14px;color:#4B6B99;-webkit-line-clamp:1}._CardInfo_35iu-c-arrow-right{width:12px;height:12px}._CardInfo_35iu-c-info-img{height:19px;width:19px}._CardInfo_35iu-c-line{background-color:#E0E0E0;height:1px}._CardInfo_35iu-c-dlg-txt{-webkit-line-clamp:9;-webkit-box-orient:vertical;display:-webkit-box}._CardInfo_35iu-c-dlg-title-box{margin-top:21px;margin-bottom:6px;padding:0 15px}._CardInfo_35iu-c-dlg-title{font-size:18px;font-weight:bold;text-align:center;flex:1}._CardInfo_35iu-c-dlg-dot{margin-top:7px;width:4px;height:4px;background-color:#777;border-radius:2px}._CardInfo_35iu-c-dlg-list{font-size:15px;margin-left:6px}._CardInfo_35iu-c-tip-box{padding:9px 15px 0 15px}._CardInfo_35iu-c-input-tip{color:#999999;font-size:14px;margin-top:12px}._CardInfo_35iu-c-dlg-btn-text{font-size:17px;text-align:center;margin:11.5px 15px}._CardInfo_35iu-c-dlg-line{margin-top:18px}._CardInfo_35iu-t-input:disabled{color:#999999}._CardInfo_35iu-t-input{color:#000000}._CardInfo_35iu-c-amc-dlg-box{border:0;background-color:#fff;border-radius:8px;width:300px;max-width:300px;align-items:center}._CardInfo_35iu-c-amc-dlg-width{width:300px;max-width:300px}._ButtonGroup_xfez-c-container{background-color:#FFFFFF;border-radius:12px;align-self:stretch;padding:16px;justify-content:space-between}._ButtonGroup_xfez-c-title{font-size:17px;color:#333333}._ButtonGroup_xfez-c-label-container{align-items:flex-start}._ButtonGroup_xfez-c-message{font-size:14px;color:#999999;margin-top:4px}._ButtonGroup_xfez-c-line-1{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}._ButtonGroup_xfez-c-button{background-color:#1677FF;border-radius:4px;padding:4px 12px}._ButtonGroup_xfez-c-button-text{font-size:12px;color:#FFFFFF;text-align:center}._QuickBindList_y1d4-c-card-list-box{border-radius:8px;background-color:#FFFFFF}._QuickBindList_y1d4-c-card-list-box-header{margin:16px;align-self:stretch;align-content:flex-end;justify-items:flex-start}._QuickBindList_y1d4-c-card-list-box-header-tip-1{font-size:18px;font-weight:bold;color:#333333}._QuickBindList_y1d4-c-card-list-box-header-tip-2{font-size:14px;color:#A8A6A6;align-self:flex-end}._QuickBindList_y1d4-c-card-list-divide{background-color:#EFEFEF;opacity:0.75;height:1px}._QuickBindList_y1d4-c-card-list-divide-mt{margin-top:12px}._QuickBindList_y1d4-c-card-list-divide-ml{margin-left:36px}._QuickBindList_y1d4-c-card-list-item{flex-direction:row}._QuickBindList_y1d4-c-card-list-item-logo{width:24px;height:24px;margin-right:12px}._QuickBindList_y1d4-c-card-list-item-content{justify-content:flex-start}._QuickBindList_y1d4-c-card-list-item-content-title{font-size:17px;color:#333333;letter-spacing:0;text-overflow:ellipsis;overflow:hidden;text-align:left;min-width:90px;flex:1.0;white-space:nowrap}._QuickBindList_y1d4-c-card-list-item-content-desc{font-size:14px;color:#FF6010;letter-spacing:0;text-align:right;flex:auto;white-space:nowrap}._QuickBindList_y1d4-c-card-list-item-tail{width:12px;height:12px;margin-left:4px}._QuickBindList_y1d4-c-card-more-content{font-size:14px;color:#999999}._QuickBindList_y1d4-c-card-item-container{padding:12px 16px 0px}._QuickBindList_y1d4-c-card-list-item-content-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;overflow:hidden;margin-top:4px;margin-left:36px}._QuickBindList_y1d4-c-card-list-item-content-label{border:0.8px solid #FFCFB7;color:#FF6010;margin-right:8px;border-radius:2px;font-size:10px;padding:2px 4px}._QuickBindList_y1d4-c-more-box{padding-bottom:12px}._Button_b6r6-c-text-primary{font-size:18px;color:#fff;height:49px}._Button_b6r6-c-button-primary{height:49px;max-height:49px;min-height:49px;align-self:stretch}._Button_b6r6-c-lottie-loading{width:320px;height:45px}._Button_b6r6-c-lottie-margin{margin-top:-45px}._CommonDialog_jljb-c-dlg-box{border:0;background-color:#fff;border-radius:8px;padding:25px 0 0;width:280px}._CommonDialog_jljb-c-margin-box-lr{margin-left:16px;margin-right:16px}._CommonDialog_jljb-c-title-box{margin-bottom:12px}._CommonDialog_jljb-c-msg{flex:1.0;text-align:center;color:#333333;font-size:15px;line-height:21px}._CommonDialog_jljb-c-title{font-size:18px;color:#333333;text-align:center;font-weight:bold}._CommonDialog_jljb-c-btn-text{font-size:18px}._CommonDialog_jljb-c-btn-div{padding:12px 0;background-color:#fff;align-self:stretch}._CommonDialog_jljb-c-btn-div:active{background-color:#ddd}._CommonDialog_jljb-c-align-self-box{align-self:stretch}._CommonDialog_jljb-c-btn-line{background-color:#e5e5e5}._CommonDialog_jljb-c-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;justify-content:center;margin:12px 16px 20px;align-self:stretch;overflow:hidden}._CommonDialog_jljb-c-label{background-color:#FFECE3;color:#FF6010;margin-left:4px;margin-right:4px;margin-top:6px;border-radius:2px;font-size:14px;padding:2px 4px}._CommonDialog_jljb-c-close-btn-container{margin:-12px 13px 13px 13px;align-self:stretch}._CommonDialog_jljb-c-close-btn{width:18px;height:18px}._CommonDialog_jljb-c-content-img-container{margin:0px 0px 25px 0}._CommonDialog_jljb-c-protocol-box{margin-bottom:8px}._CommonDialog_jljb-c-single-protocol-label{font-size:15px;line-height:21px;color:#4B6B99}._CommonDialog_jljb-c-addition{text-align:left;color:#666666;margin:16px 0;font-size:15px;line-height:21px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 677c52e6b5a3f98ae56f55ea5f83133769568fe7:D */!function(\nn){var i={};function o(t){if(i[t])return i[t].exports\n;var e=i[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports\n,e,e.exports,o),e.l=!0,e.exports}o.m=n,o.c=i,o.d=function(t,\ne,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,\nget:n})},o.r=function(t){\n'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(\nt,Symbol.toStringTag,{value:'Module'}),\nObject.defineProperty(t,'__esModule',{value:!0})},\no.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(\n4&t&&'object'==typeof e&&e&&e.__esModule)return e\n;var n=Object.create(null);if(o.r(n),Object.defineProperty(n\n,'default',{enumerable:!0,value:e}),2&t&&'string'!=typeof e\n)for(var i in e)o.d(n,i,function(t){return e[t]}.bind(null,i\n));return n},o.n=function(t){var e=t&&t.__esModule?function(\n){return t.default}:function(){return t};return o.d(e,'a',e)\n,e},o.o=function(t,e){\nreturn Object.prototype.hasOwnProperty.call(t,e)},o.p='',o(\no.s=4)}([function(t,e,n){'use strict';Object.defineProperty(\ne,'__esModule',{value:!0}),e.amc=window.amc},function(t,e,n\n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0});var i=n(6);e.BNComponent=i.BNComponent;var o=n(7)\n;e.ComponentRegistry=o.ComponentRegistry;var a=n(13)\n;e.Logger=a.Logger,e.logger=a.logger},function(t,e,n){\n'use strict';Object.defineProperty(e,'__esModule',{value:!0}\n);var i,r=n(0),o=n(3);function a(t,e,n,i){var o;if(\nr.amc.isAndroid)o=document.createElement('embed',e,function(\n){});else for(var a in o=document.createElement('embed'),e\n)e.hasOwnProperty(a)&&(o[a]=e[a]);return n&&(o.className=n),\ni?t.insertBefore(o,i):t.appendChild(o),o}\ne.modifyElementStyle=function(t,e,n){var i=e\n;r.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),\ni&&o.copyObj(n,i.style)},e.modifyElementAttribute=function(t\n,e,n){if(e&&n){var i=e;if(r.amc.fn.isString(e)&&(\ni=t.getViewInComponentById(e)),i)for(var o in n\n)n.hasOwnProperty(o)&&(i[o]=n[o])}},\ne.modifyElementClass=function(t,e,n,i){var o=e\n;r.amc.fn.isString(e)&&(o=t.getViewInComponentById(e)),o&&(\ni||(o.className=''),t.applyStyleTo(o,n))},\ne.visibleElement=function(t,e,n){var i;void 0===n&&(n=!0),\ne&&(i=r.amc.fn.isString(e)?t.getViewInComponentById(e):e)&&(\nn?r.amc.fn.show(i):r.amc.fn.hide(i))},\ne.modifyElementCSS=function(t,e,n){if(e){var i=e\n;r.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),\ni&&n&&(i.style.cssText=n)}},e.createEmbedViPlugin=function(t\n,e,n,i){return a(t,e,n,i)},e.createEmbedPlugin=a,\ne.getThemeColor=(i='',function(){return i||(\ni=r.amc.fn.sdkGreaterThanOrEqual('10.8.39'\n)?'#1677FF':'#108EE9'),i})},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var c=n(0)\n;function i(t,e){if(t&&e){if(e===t.src)return\n;c.amc.isAndroid&&'none'===t.style.display?(c.amc.fn.show(t)\n,window.setTimeout(function(){t.src=e},20)):t.src=e}}\nfunction o(t){if(!t)return 0;t=t.replace(/<\\/?[^>]+(>|$)/g,\n'');for(var e=0,n='',i=new RegExp('[\\\\u4E00-\\\\u9FFF]+','g'),\no=0,a=t;o<a.length;o++){var r=a[o];c.amc.isIOS&&(\n' '<=r&&r<='~'&&i.test(n)||' '<=n&&n<='~'&&i.test(r))&&(\ne+=.5),e+=' '<=r&&r<='~'?.58:1,n=r}return e}\ne.mergeObject=function(){for(var t=[],\ne=0;e<arguments.length;e++)t[e]=arguments[e];var n={};if(\nt&&t.length)for(var i=0;i<t.length;i++){var o=t[i];if(\nc.amc.fn.isObject(o))for(var a in o)o.hasOwnProperty(a)&&(\nn[a]=o[a])}return n},e.isFunction=function(t){\nreturn'[object Function]'===Object.prototype.toString.call(t\n)},e.isPreRender=function(t){return t&&(\nt.local&&t.local.isPrerender||t.rpcData&&t.rpcData.isPrerender\n)},e.copyObj=function(t,e){for(var n in e||(e={}),t\n)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},\ne.doNothing=function(){},e.tryJSONParse=function(t){if(\nnull==t)return{};if(c.amc.fn.isObject(t))return t;try{\nreturn JSON.parse(t)}catch(t){return{}}},\ne.checkEmptyObj=function(t){return c.amc.fn.isString(t\n)?0===t.length:!(t&&0!==Object.keys(t).length)},\ne.substrWithFontWidth=function(t,e,n){if(!t)return t;for(\nvar i='',o=0,a=t.length,r=0;r<a;r++){var c=n?t[a-r-1]:t[r]\n;if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?o+=.45:o++,i+=c,e-1<o)break\n}return i},e.calculateFontWidth=function(t){if(!t)return 0\n;for(var e=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,i=0;i<t.length;i++\n)n.test(t[i])?e+=.45:e++;return Math.round(e)},\ne.deepCopy=function t(e){if(null==e||'object'!=typeof e\n)return e;var n;if(e instanceof Date)return(n=new Date\n).setTime(e.getTime()),n;if(e instanceof Array){n=[];for(\nvar i=0,o=e.length;i<o;i++)n[i]=t(e[i]);return n}if(\ne instanceof Object){for(var a in n={},e)e.hasOwnProperty(a\n)&&(n[a]=t(e[a]));return n}throw new Error(\n'Unable to copy obj! Its type isn\\'t supported.')},\ne.getConfig=function(t,e){setTimeout(function(){\ndocument.invoke('queryInfo',{queryKey:'configInfo',\nconfigKey:t},function(t){e(t.available)})},20)},\ne.showLoading=function(){setTimeout(function(){\ndocument.invoke('showLoading')},20)},e.hideLoading=function(\n){setTimeout(function(){document.invoke('hideLoading')},20)}\n,e.safeInvoke=function(t,e,n){\nc.amc.isAndroid?window.setTimeout(function(){\ndocument.invoke(t,e,n)},20):document.invoke(t,e,n)},\ne.safeLoadImgSrcAndSetMode=function(t,e){t&&e&&(\nt.contentmode=e.mode,i(t,e.src))},e.safeLoadImgSrc=i,\ne.calculateStyleLineHeight=function(t,e){\nreturn c.amc.isIOS?t:(t-e)/2****},\ne.calculateLabelShowLineNumber=function(t,e,n){if(!t||n<=0\n)return 1;var i=o(t)*e;return Math.ceil(i/n)},\ne.calculateFontCount=o,e.isLowDevice=function(){return!!(\nwindow.flybird&&window.flybird.local&&window.flybird.local.isLowDevice\n)},e.safeRemoveChildNode=function(e){if(e&&e.childNodes){\nfor(var t=[],n=0,i=e.childNodes;n<i.length;n++){var o=i[n]\n;t.push(o)}t.forEach(function(t){e.removeChild(t)})}}},\nfunction(t,p,e){'use strict';var i,n=this&&this.__extends||(\ni=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[\n]}instanceof Array&&function(t,e){t.__proto__=e}||function(t\n,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)}),\nr=this&&this.__assign||function(){return(\nr=Object.assign||function(t){for(var e,n=1,\ni=arguments.length;n<i;n++)for(var o in e=arguments[n]\n)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])\n;return t}).apply(this,arguments)};Object.defineProperty(p,\n'__esModule',{value:!0});var c,m,o,a,s=e(1),f=e(0),_=e(3),\nd=e(1),g=e(8),h=e(2),b=e(14),v=e(15),y=e(21),C=e(10),I=e(22)\n,x=e(24),w=e(25),S=e(5),l=e(26),N=e(11);function A(t){\nvar e='';if(!t||!f.amc.fn.isString(t))return e;for(\nvar n=0;n<t.length;n++)n<t.length-4?e+='*':e+=t[n];return e}\nfunction k(t){return t.replace(/\\s/g,'')}function T(t,e){\nvar n,i=!1;return function(){return i||(i=!0,n=t&&t.apply(e,\narguments),t=null),n}}d.ComponentRegistry.registerComponent(\ng.Button.componentName,g.Button),\nd.ComponentRegistry.registerComponent(\nv.CardInfo.componentName,v.CardInfo),(a=c=p.INPUT_STATUS||(\np.INPUT_STATUS={}))[a.focus=0]='focus',a[a.blur=1]='blur',\na[a.disabled=2]='disabled',a[a.enabled=3]='enabled',(\no=m=p.PAGE_STYLE||(p.PAGE_STYLE={})).OLD='old',\no.YOUNG='young',o.ISOLATED='expressSignLast',\no.PAY='expressSignFirst',o.DCEP='dcep',o.NONE='none',\np.MIN_CARD_NO_LENGTH=12,p.OLD_SCALE=1.2,p.instTplSaved=!1\n;var u,P=function(t){function u(){var l=null!==t&&t.apply(\nthis,arguments)||this;return l.recommendExp=T(function(){\nf.amc.fn.spmExposure('a283.b4032.c41191.d83366',\np.spmServerParam,!1)},l),l.commandProcess=function(t){if(t){\nvar e=t;if(t.action&&(e=t.action),S.startsWith(e.name||'',\n'bn:'))switch(e.name){case'bn:bindCard':l.bindCard();break\n;case'bn:inputCardNo':e.params&&e.params.cardNo&&(\nl.cardNoInput.value=e.params.cardNo,l.formatBankNo(),\nl.vueModel.data.newVersion&&l.cardNoChanged(e.params.cardNo,\n!0),l.button.setVisible(!0));break;default:\nl.cardNoInput.focus()}else S.startsWith(e.name||'','/')?(\ne.loadtxt='',l.submitLoading(e)):document.submit({action:e})\n}},l.onMounted=function(){var t=_.tryJSONParse(\np.rpcData.cardNoPagePropagate)\n;p.spmServerParam=_.mergeObject({\nsource:p.rpcData.source||p.rpcData.sourceId||'-',\nclientPageStyle:u.pageStyle},p.spmServerParam),t.title&&(\nl.vueModel.data.title=t.title),\nl.cardNoLengthErrorTipDlg=_.tryJSONParse(\np.rpcData.cardNoLengthErrorTipDlg||{}),\nl.vueModel.data.isOld=u.pageStyle===m.OLD,D()&&(\nh.modifyElementStyle(l,'main-body',{\npadding:'0 '+16/p.OLD_SCALE+'px'}),h.modifyElementStyle(l,\n'card-no-cell',{\npadding:16/p.OLD_SCALE+'px '+13/p.OLD_SCALE+'px'}),\nh.modifyElementStyle(l,'input-area',{\npadding:16/p.OLD_SCALE+'px'})),\nl.quickBindList=new w.QuickBindList({\nsignAction:p.rpcData.signAction||'loc:none',\nspmServerParam:p.spmServerParam,spm:{\nmore:'a283.b4032.c39092.d78856',\nbankItem:'a283.b4032.c62099.d127802'}}),\nl.quickBindList.setBankListStatusListener(function(t){\nt&&h.modifyElementAttribute(l,'main-body',{scrollTop:0})})\n;var e=l.getViewInComponentById('main-body');if(\nu.pageStyle===m.YOUNG){l.familyCard=new x.Card\n;var n=_.tryJSONParse(p.rpcData.middlePropagate||{}),\ni=_.mergeObject(p.spmServerParam,_.tryJSONParse(n.spmObj||{}\n));l.familyCard.setTitle(n.title).setMessage(n.message\n).setButton(n.button).setClickListener(function(){\nf.amc.fn.spmClick('a283.b4032.c77926.d160692',i),\ndocument.submit({action:_.tryJSONParse(n.action||{})})}),\nl.familyCard.mountTo(e,l.getViewInComponentById('dcep-area')\n),f.amc.fn.spmExposure('a283.b4032.c77926',i),\nl.familyCard.setContainerStyle({marginBottom:'16px'})}if(\nu.pageStyle===m.ISOLATED||u.pageStyle===m.DCEP||u.pageStyle===m.OLD||u.pageStyle===m.YOUNG||u.pageStyle===m.NONE?(\nh.visibleElement(l,'bank-list-blank',!1),\nl.quickBindList.mountTo(e,l.getViewInComponentById(\n'dcep-area'))):(h.visibleElement(l,'bank-list-blank',!0),\nl.quickBindList.mountTo(e,l.getViewInComponentById(\n'input-area'))),u.pageStyle===m.DCEP){\nvar o=p.rpcData.supportedInstInfos;if(\np.rpcData.supportedBindCardInfos&&p.rpcData.supportedBindCardInfos.cardList\n){var a=p.rpcData.supportedBindCardInfos.cardList.map(\nfunction(t){return{instId:t.instId,instName:t.showName,\nlogoUrl:t.instLogo}});l.quickBindList.setBankList(a,a.length\n,a.length).setBankClickListener(l.bankItemCallBackInDcep\n).setHeaderTipTitle(p.rpcData.supportedBindCardInfos.title\n).setHeaderTipDesc('').setViewMore(p.rpcData.viewMore)\n}else h.visibleElement(l,'bank-list-blank',!1),\nl.quickBindList.visible(!1)\n;f.amc.isIOS&&o.instInfos&&0<o.instInfos.length&&(\no.instInfos=o.instInfos.map(function(t){return t.logoUrl&&(\nt.logoUrl=l.setPixelWidthInURL(t.logoUrl,660)),t})),\nh.visibleElement(l,'dcep-area',!0),h.visibleElement(l,\n'bind-sub-title',!1),h.visibleElement(l,'input-card-no-tip',\n!1),\nl.vueModel.data.supportedInstInfos=p.rpcData.supportedInstInfos\n}else f.amc.fn.sdkGreaterThanOrEqual('10.8.28'\n)&&p.rpcData.instInfos&&(\nu.pageStyle===m.ISOLATED?setTimeout(function(){\nl.setQuickList()},10):l.setQuickList())\n;l.cardNoInput=l.getViewInComponentById('cardNo'),\nl.button=l.getSubComponentById('button',g.Button)\n;var r=l.button.getViewInComponentById('button')\n;h.modifyElementAttribute(l,r,{behaviorInfo:JSON.stringify({\nspm:'a283.b4032.c9686.d17362',bizCode:'pay',extParam:{}})}),\nD()&&l.button.setHeight(49/p.OLD_SCALE)\n;var c=l.getSubComponentById('cardInfo',v.CardInfo);if(\nl.cardInfoHelper=new y.CardInfoHelper(c,l.getCardNo,\nl.changeButtonStatus,l.changeCardInputStatus,\nu.pageStyle===m.OLD?function(){}:l.inputFocus),\nl.questionnaire=new b.Questionnaire({\nspaceCode:p.rpcData.surveyKey,\nbizIdentifier:p.rpcData.isolated+'',\nsourceId:p.rpcData.sourceId,\nbizIdentity:p.rpcData.bizIdentity}),l.exitDlg=I.getDialog(),\np.rpcData.recommendInfos&&0<p.rpcData.recommendInfos.length&&!p.rpcData.cardNo&&(\nl.recommendExp(),l.vueModel.data.recommendCard={\ncontent:p.rpcData.recommendInfos[0].recommendContent,\ntip:p.rpcData.recommendPropagate||'{{recently_used}}',\ncardNo:p.rpcData.recommendInfos[0].cardNo}),\np.rpcData.supportedInstNum&&(h.visibleElement(l,\n'input-card-no-tip-2',!0),h.modifyElementAttribute(l,\n'input-card-no-tip-2',{\ninnerText:'（'+f.amc.fn.i18nPlaceholderReplace(\n'{{bank_support}}',p.rpcData.supportedInstNum)+'）'})),\nl.initBodyHeight(),l.initButton(),l.fillFixCardNo(),\nl.initAdvertisementTopPart(),\n!f.amc.isSDK&&p.rpcData.surveyKey&&f.amc.fn.sdkGreaterThanOrEqual(\n'10.8.26')&&l.questionnaire.start(),\np.rpcData.disable||C.invoke('scanCard',{\ntype:'isOCRSupported'},function(t){\nl.vueModel.data.OCRSupported=t&&t.isOCRSupported,\nl.vueModel.data.OCRSupported&&(\nl.viewDidAppeared||f.amc.isIOS)&&l.formatBankNo()},30),\nl.cardNoInput.onfocus=function(){f.amc.fn.spmClick(\n'a283.b4032.c62224.d128107',p.spmServerParam),l.inputFocus()\n},window.mp&&window.mp.onNotification('MODIFY_INST',\nfunction(t){if(t&&t.inst){var e=_.tryJSONParse(t.inst)\n;l.cardInfoHelper.cardInfo.hide(),\nl.cardInfoHelper.showInstName(e.instName||e.fullName,!0),\nl.cardInfoHelper.cardInfo.show(),l.changeButtonStatus({\ntext:'{{next_step}}'}),\nl.cardInfoHelper.tryQueryInfoWithInst(l.getCardNo().substr(0\n,12),e.instName||e.fullName,e.instId,t.cardType,e.cardBrand)\n}}),window.mp&&window.mp.onNotification('MODIFY_INST_BACK',\nfunction(t){l.cardInfoHelper.userSpecifiedCard=!1,\nl.cardInfoHelper.bindCardHttp=!1}),p.rpcData.exitConfirm&&(\nN.initFeature(),N.getServerFeature()),\nf.amc.fn.sdkGreaterThanOrEqual('10.8.67')&&C.invoke('copy',{\ntype:'read'},function(t){var e=[],n=O(t.pasteboardStr)\n;e.push({text:f.amc.fn.i18nValueForKey('auto_input_no'),\naction:{name:'bn:inputCardNo',params:{cardNo:n}}}),e.push({\ntext:f.amc.fn.i18nValueForKey('cancel')}),n&&(\nf.amc.fn.spmExposure('a283.b4032.c108617',p.spmServerParam,\n!1),l.exitDlg.show({title:f.amc.fn.i18nValueForKey(\n'copy_card_no'),message:A(n),btns:e.map(function(t){\nreturn t.text}),clickCallBack:function(t){e.length>t&&(\ne[t].action?f.amc.fn.spmClick('a283.b4032.c108617.d225090',\np.spmServerParam):f.amc.fn.spmClick(\n'a283.b4032.c108617.d225091',p.spmServerParam),\nl.commandProcess(e[t].action))}}))},30),\nl.cardNoInput.onlongpress=function(){f.amc.fn.spmClick(\n'a283.b4032.c75100.d225092',p.spmServerParam)},\np.rpcData.cardBindingInitDlg){\nvar s=p.rpcData.cardBindingInitDlg,d=s.buttons||[{act:{\nname:'loc:dismiss'},txt:'{{OK}}'}];f.amc.fn.spmExposure(\n'a283.b4032.c113815',p.spmServerParam),d.forEach(function(t,\ne){return f.amc.fn.spmExposure(\n'a283.b4032.c113815.d235994_'+e,_.mergeObject(\np.spmServerParam||{},t))}),l.exitDlg.show({title:s.title,\nmessage:s.msg,btns:d.map(function(t){return t.txt}),\nclickCallBack:function(t){var e=d[t];e&&(f.amc.fn.spmClick(\n'a283.b4032.c113815.d235994_'+t,_.mergeObject(\np.spmServerParam||{},e)),e.act?l.commandProcess(e.act\n):l.cardNoInput.focus())}})}f.amc.fn.spmExposure(\n'a283.b4032.c9686.d17362',_.mergeObject(p.spmServerParam,{\nisLowDevice:window.flybird&&window.flybird.local&&window.flybird.local.isLowDevice\n}),!1)},l.cardNoPagePropagateObject=_.tryJSONParse(\np.rpcData.cardNoPagePropagate),l.fixedCardNo='',\nl.recommendCardNo='',l.viewDidAppeared=!1,l.lastNoValue='',\nl.isQuickBindListShow=!0,l.cardSubmitLock=!1,\nl.cardSubmitLockCount=0,l.vueModel={data:{\ntitle:'{{add_new_card}}',subTitle:'{{propagate_title}}',\nsubTitleLogo:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*DIWVToKKMO4AAAAAAAAAAAAAARQnAQ',\nbottomIcon:'',gonggao:{content:p.rpcData.gonggao,style:{\nwidth:window.innerWidth-(f.amc.isPlus?40:30)}},\nnewVersion:p.rpcData.useSeniorCardNoPage&&p.rpcData.sessionId&&f.amc.fn.sdkGreaterThanOrEqual(\n'10.8.46'),OCRSupported:!1,recommendCard:{},\nsupportedInstInfos:{title:'',moreUrl:'',instInfos:[]},\nisOld:!1},compute:{placeHolder:function(){\nreturn l.cardNoPagePropagateObject.inputContent?l.cardNoPagePropagateObject.inputContent:p.rpcData.newCardTip&&p.rpcData.bizIdentifier&&'isolated'!==p.rpcData.bizIdentifier?p.rpcData.newCardTip:p.rpcData.userName?f.amc.fn.i18nPlaceholderReplace(\n'{{card_placeholder}}',p.rpcData.userName\n):'{{card_placeholder_user}}'}}},l.setQuickList=function(){\nl.quickBindList.setBankList(p.rpcData.instInfos,\np.rpcData.defaultShowExpressNum,\np.rpcData.supportedExpressInstNum).setBankClickListener(\nl.bankItemCallBackInCommonPage).setHeaderTipTitle(\np.rpcData.bindCardTip).setHeaderTipDesc(\n'（'+f.amc.fn.i18nPlaceholderReplace('{{bank_support}}',\np.rpcData.supportedExpressInstNum||p.rpcData.instInfos.length\n)+'）').setViewMore(p.rpcData.viewMore)},\nl.selectMore=function(){\np.rpcData.supportedInstInfos&&p.rpcData.supportedInstInfos.moreUrl&&document.submit(\n{action:{\nname:'loc:openweb(\\''+p.rpcData.supportedInstInfos.moreUrl+'\\', \\'\\')'\n}})},l.setPixelWidthInURL=function(t,e,n){\nreturn void 0===e&&(e=80),void 0===n&&(n=/\\[pixelWidth\\]/),\nt?n.test(t)?t.replace(n,e+''):t:''},\nl.bankItemCallBackInCommonPage=function(t){var e,\nn=p.rpcData.instInfos[t]||{};f.amc.fn.spmClick(\n'a283.b4032.c62099.d127802_'+t,{instId:n.instId}),\ne=p.rpcData.expressSignSelAction?{action:{\nname:p.rpcData.expressSignSelAction,params:{instId:n.instId}\n}}:{action:{name:'loc:bnvb'},param:{\ntplid:'QUICKPAY@cashier-card-type-flex',\ntpl:p.rpcData.tpl||'',data:{\ncardTypePropagate:p.rpcData.cardTypePropagate||{},\nbankInfo:n,helpURL:p.rpcData.signHelpURL,\nsubmitAction:p.rpcData.signApplyAction,\nbtnTxt:p.rpcData.btnTxt||'{{agree_next}}'}}},\ndocument.submit(e)},l.bankItemCallBackInDcep=function(t){\nvar e=p.rpcData.supportedBindCardInfos.cardList;if(e){\nvar n=e[t]||{};f.amc.fn.spmClick(\n'a283.b4032.c62099.d127802_'+t,{instId:n.cardNo});var i={}\n;i.action={name:p.rpcData.nextAction||'/card/verifyCardNo',\nloadtxt:void 0,host:p.rpcData.pci_url,https:!0,\nenctype:'application/x-www-form-urlencoded',\nrequest_param:'requestData',params:p.rpcData.params},\ni.param={cardno:n.cardNo},i.param.luhnAc=!0,document.submit(\ni)}},l.inputFocus=function(){if(u.pageStyle===m.OLD\n)return h.visibleElement(l,'camera-area',!1),\nvoid h.modifyElementAttribute(l,'main-body',{scrollTop:0})\n;if(u.pageStyle===m.PAY){h.modifyElementAttribute(l,\n'main-body',{scrollTop:0}),h.visibleElement(l,'bind-title',\n!1),h.visibleElement(l,'bind-sub-title',!1),\nh.visibleElement(l,'bank-list-blank',!1),\nl.quickBindList.visible(!1),h.modifyElementAttribute(l,\n'main-body',{scrollTop:0}),l.isQuickBindListShow=!1\n;var t=l.getHeight();h.modifyElementStyle(l,\n'bottom-background',{height:t-45+'px'}),\nh.modifyElementStyle(l,'header-background',{height:'45px'}),\nl.button.setVisible(!0)}},l.inputBlur=function(){if(\nu.pageStyle===m.PAY||u.pageStyle===m.OLD)if(\nf.amc.isAndroid?(document.submit({action:{\nname:'loc:hideKeyboard'}}),l.changeCardInputStatus(\nc.disabled),setTimeout(function(){l.changeCardInputStatus(\nc.enabled)},100)):f.amc.fn.hideKeyboard(),\nu.pageStyle===m.OLD&&l.vueModel.data.OCRSupported\n)h.visibleElement(l,'camera-area',!0);else{h.visibleElement(\nl,'bind-title',!0),h.visibleElement(l,'bind-sub-title',!0),\nh.visibleElement(l,'bank-list-blank',!0),\nl.quickBindList.visible(!0),l.isQuickBindListShow=!0\n;var t=l.getHeight();h.modifyElementStyle(l,\n'bottom-background',{height:t-165+'px'}),\nh.modifyElementStyle(l,'header-background',{height:'165px'})\n,l.getCardNo(\n).length<p.MIN_CARD_NO_LENGTH&&l.button.setVisible(!1)}},\nl.getHeight=function(){var t=f.amc.specs.bodyHeight\n;return p.rpcData.gonggao&&(t-=40),D()?t/p.OLD_SCALE:t},\nl.initBodyHeight=function(){var t=l.getHeight()\n;h.modifyElementStyle(l,'bottom-background',{\nheight:t-165+'px'}),h.modifyElementStyle(l,'main-body',{\nheight:t+'px',maxHeight:t+'px',marginTop:-t+'px'})},\nl.onScanCard=function(){l.questionnaire.setCountDownStop(!0)\n,f.amc.fn.spmClick('a283.b4032.c9686.d25399',\np.spmServerParam);var t=f.amc.isAndroid?50:70\n;l.vueModel.data.newVersion?l.onNewScanCard(t\n):l.onOldScanCard(t)},l.recommendCardClick=function(){\nvar t=l.vueModel.data.recommendCard.cardNo||''\n;l.cardNoInput.value=A(t||''),l.fixedCardNo='',\nl.recommendCardNo=t||'',l.formatBankNo(),\nl.vueModel.data.newVersion&&l.cardNoChanged(k(\nl.recommendCardNo),!0),l.button.setVisible(!0),\nf.amc.fn.spmClick('a283.b4032.c41191.d83366',\np.spmServerParam),h.visibleElement(l,'recentCardArea',!1)},\nl.onOldScanCard=function(t){C.invoke('scanCard',{\ntype:'show',ocrImageFormat:'jpeg',ocrImageQuality:t},\nfunction(t){if(t.ocrResult){var e=t.ocrResult||'',\nn=t.ocrImage||'',i=t.ocrImageWidth||0,o=t.ocrImageHeight||0\n;setTimeout(function(){document.submit({action:{\nname:'loc:bnvb'},param:{\ntplid:'QUICKPAY@cashier-card-no-recorrect-flex',\ntpl:p.rpcData.cameraTpl||'{\"format\":\"JSON\",\"platform\":\"common\",\"publishVersion\":\"150924\",\"tag\":\"QUICKPAY\",\"time\":\"0003\",\"tplId\":\"QUICKPAY@cashier-card-no-recorrect-flex\",\"tplVersion\":\"5.3.8\"} ',\ndata:{nextAction:p.rpcData.nextAction,\npci_url:p.rpcData.pci_url,params:p.rpcData.params,\nocrResult:e,ocrImage:n,ocrImageWidth:i,ocrImageHeight:o}}})}\n,10)}})},l.onNewScanCard=function(t){C.invoke('scanCard',{\ntype:'show',ocrImageFormat:'jpeg',ocrImageQuality:t},\nfunction(t){if(t.ocrResult){C.invoke('scanCard',{type:'kill'\n},function(t){},30);var e=t.ocrResult||'',n=t.ocrImage||'',\ni=t.ocrImageWidth||0,o=t.ocrImageHeight||0;h.visibleElement(\nl,'ocr-image',!0),h.visibleElement(l,'recentCardArea',!1)\n;var a=window.innerWidth-64;D()?h.modifyElementStyle(l,\n'ocr-image',{width:a/p.OLD_SCALE}):h.modifyElementStyle(l,\n'ocr-image',{width:a}),!o||o<=0||!i||i<=0||!n?(\nf.amc.fn.logError('CARD_orc_image_err',\n'ocr image data error'),h.modifyElementStyle(l,'ocr-image',{\nheight:60})):h.modifyElementStyle(l,'ocr-image',{\nheight:o*a/i}),h.modifyElementAttribute(l,'ocr-image',{src:n\n}),u.pageStyle===m.OLD&&h.modifyElementStyle(l,'ocr-image',{\nwidth:'80%'}),l.cardNoInput.value=e,l.cardNoInput.focus(),\nl.formatBankNo(),l.cardNoChanged(k(e),!0),\nl.button.setVisible(!0),l.scanFlag=!0}})},\nl.initButton=function(){\nvar t=l.cardNoPagePropagateObject.nextActionBtn?l.cardNoPagePropagateObject.nextActionBtn:'{{next_step}}'\n;l.changeButtonStatus({enable:!1,text:t}),\nl.button.setOnClick(l.onSubmit),\nu.pageStyle===m.PAY&&l.button.setVisible(!1)},\nl.formatBankNo=function(){var t=l.cardNoInput.value||'';(\nt=k(t))&&(l.cardNoInput.value=t.replace(\n/([\\d|\\*]{4})(?=[\\d|\\*])/g,'$1 ').trim()||''),l.updateState(\n)},l.updateState=function(){l.changeButtonStatus(),\nl.changeCameraStatus()},l.onceCameraExposure=T(function(){\nf.amc.fn.spmExposure('a283.b4032.c9686.d25399',\np.spmServerParam,!1),l.setCameraUep()},l),\nl.changeCameraStatus=function(){var t=0===k(\nl.cardNoInput.value).length&&l.vueModel.data.OCRSupported\n;t&&l.onceCameraExposure(),h.visibleElement(l,'camera',\nt&&!l.vueModel.data.isOld)},l.setCameraUep=function(){\nvar t={behaviorInfo:JSON.stringify({\nspm:'a283.b4032.c9686.d25399',bizCode:'pay',extParam:{}})}\n;l.vueModel.data.isOld?h.modifyElementAttribute(l,\n'camera-area',t):h.modifyElementAttribute(l,'camera',t)},\nl.changeButtonStatus=function(t){var e,n,i=l.getCardNo()\n;n=t&&void 0!==t.enable?t.enable:i.length>=p.MIN_CARD_NO_LENGTH&&(\n!l.vueModel.data.newVersion||l.cardInfoHelper.checkFormComplete(\n)),t&&t.text&&l.button.setText(((e={}\n)[g.BUTTON_STATUS.NORMAL]=t.text,\ne[g.BUTTON_STATUS.SUCCESS]=t.text,\ne[g.BUTTON_STATUS.LOADING]=t.text,e)),\nl.button.changeLoadingStatus(\nn?g.BUTTON_STATUS.NORMAL:g.BUTTON_STATUS.DISABLE)},\nl.getCardNo=function(){return k(\nl.fixedCardNo||l.recommendCardNo||l.cardNoInput.value||'')},\nl.changeCardInputStatus=function(t){switch(t){case c.blur:\nl.changeCardInputStatus(c.enabled),f.amc.isAndroid?(\ndocument.submit({action:{name:'loc:hideKeyboard'}}),\nl.changeCardInputStatus(c.disabled),setTimeout(function(){\nl.changeCardInputStatus(c.enabled)},100)\n):l.cardNoInput.blur();break;case c.disabled:\nl.cardNoInput.disabled=!0;break;case c.enabled:\nl.fixedCardNo||(l.cardNoInput.disabled=!1);break\n;case c.focus:l.changeCardInputStatus(c.enabled),\nl.cardNoInput.focus()}},l.fillFixCardNo=function(){\np.rpcData.cardNo&&(l.fixedCardNo=k(p.rpcData.cardNo),\nl.fixedCardNo.length<p.MIN_CARD_NO_LENGTH&&(l.fixedCardNo=''\n),l.fixedCardNo?(l.cardNoInput.value=A(l.fixedCardNo),\nl.formatBankNo(),\nl.vueModel.data.newVersion&&l.cardNoChanged(k(l.fixedCardNo)\n,!0),l.button.setVisible(!0),l.changeCardInputStatus(\nc.disabled)):l.updateState())},l.checkInput=function(){\nl.questionnaire.setCountDownStop(!0)\n;var t=l.cardNoInput.value||'',e=k(t);if(\nl.lastNoValue||f.amc.fn.spmClick('a283.b4032.c62224.d131708'\n,p.spmServerParam),!e.match('^\\\\d{0,21}$')\n)return l.cardNoInput.value=l.lastNoValue,void(\n0===l.lastNoValue.length&&l.resetPage());l.lastNoValue=t,\nl.vueModel.data.newVersion&&l.seniorInput(e),l.formatBankNo(\n),l.scanFlag=!1},l.cardNoChanged=function(t,e){if(\nvoid 0===e&&(e=!1),l.cardInfoHelper.dropQueryInfo(t),\ne&&l.changeCardInputStatus(c.blur),0===t.length)l.resetPage(\n);else if(t.length<8)l.cardInfoHelper.resetFlag();else if(\nt.length<12){var n=t.substr(0,8),\ni=l.cardInfoHelper.prefixCardNo[8]\n;i?n!==i&&l.cardInfoHelper.tryQueryInfo(n,e,!0\n):l.cardInfoHelper.tryQueryInfo(n,e,!0)}else{\nl.button.setVisible(!0),n=t.substr(0,12)\n;var o=l.cardInfoHelper.prefixCardNo[12];if(o\n)n!==o&&l.cardInfoHelper.tryQueryInfo(n,e,!0);else{\nvar a=l.cardInfoHelper.prefixCardNo[8];a&&a===t.substr(0,8\n)?l.cardInfoHelper.tryQueryInfo(n,e\n):l.cardInfoHelper.tryQueryInfo(n,e,!0)}}},\nl.seniorInput=function(t){l.cardNoChanged(t,\nt.length===l.cardInfoHelper.realCardNoLength)},l.formMap={\ncertType:v.ItemType.certType,certNo:v.ItemType.certId,\nuserName:v.ItemType.userName,bankMobile:v.ItemType.phoneNum,\ncvv2:v.ItemType.cvv,validate:v.ItemType.validity},\nl.resetPage=function(){l.cardInfoHelper.cardInfo.hide(),\nl.cardInfoHelper.resetFlag(),h.visibleElement(l,'ocr-image',\n!1),l.vueModel.data.recommendCard.content&&h.visibleElement(\nl,'recentCardArea',!0),h.visibleElement(l,'extraSubmitArea',\n!0),l.recommendCardNo='',l.fixedCardNo='',\nl.cardNoPagePropagateObject&&l.cardNoPagePropagateObject.nextActionBtn?l.changeButtonStatus(\n{text:l.cardNoPagePropagateObject.nextActionBtn}\n):l.changeButtonStatus({text:'{{next_step}}'}),\nl.vueModel.data.recommendCard&&l.vueModel.data.recommendCard.content&&h.visibleElement(\nl,'recentCardArea',!0)},l.bindCard=function(){\nvar t=l.cardInfoHelper.cardInfo.getFormResult();if(t){\n!t[v.ItemType.phoneNum]&&l.cardInfoHelper.realBankMobile&&(\nt[v.ItemType.phoneNum]=l.cardInfoHelper.realBankMobile),\n'WAI_CARD'===l.cardInfoHelper.submitParams.cardType&&(t={})\n;var e=r({},l.cardInfoHelper.submitParams)\n;e.cardNo=l.getCardNo()\n;var n='CREDIT'===l.cardInfoHelper.submitParams.cardType||'WAI_CARD'===l.cardInfoHelper.submitParams.cardType||l.cardInfoHelper.bindCardHttp\n;for(var i in n&&(e.inst_id=e.instId,e.cardno=e.cardNo),\ne.luhnAC=C.luhnCheck(e.cardNo),\ne.userSpecifiedCard=l.cardInfoHelper.userSpecifiedCard+'',\nl.formMap)if(l.formMap.hasOwnProperty(i)){\nvar o=t[l.formMap[i]];void 0!==o&&(e[i]=o)}var a={}\n;a.action=n?{\nname:'CREDIT'!==l.cardInfoHelper.submitParams.cardType||l.cardInfoHelper.bindCardHttp?'/card/verifyCardNo':'/card/consultAndAuth',\nneec:'6004',loadtxt:'',host:p.rpcData.pci_url,https:!0,\nenctype:'application/x-www-form-urlencoded',\nrequest_param:'requestData'}:{name:'/card/consultAndAuth',\nneec:'6004',loadtxt:''},a.param=r({},e),f.amc.fn.spmClick(\n'a283.b4032.c75100.d154662',{\ncardLen:l.cardInfoHelper.realCardNoLength,\nsubmitCardLen:l.getCardNo().length}),l.realSubmit(a)}},\nl.submitLoading=function(t){document.asyncSubmit({action:t},\nfunction(t){\n'1'===t.pageloading?l.button.changeLoadingStatus(\ng.BUTTON_STATUS.LOADING\n):'0'===t.pageloading&&l.button.changeLoadingStatus(\ng.BUTTON_STATUS.NORMAL)}),l.button.changeLoadingStatus(\ng.BUTTON_STATUS.LOADING)},l.onSubmit=function(){if(\nl.questionnaire.setCountDownStop(!0),f.amc.fn.spmClick(\n'a283.b4032.c9686.d17362',_.mergeObject(p.spmServerParam||{}\n,{instId:l.cardInfoHelper.submitParams.instId,\ncardType:l.cardInfoHelper.submitParams.cardType,\ncardBrand:l.cardInfoHelper.submitParams.cardBrand,\nuserSpecifiedCard:l.cardInfoHelper.userSpecifiedCard})),\nl.vueModel.data.newVersion&&l.cardInfoHelper.submitParams.instId&&l.cardInfoHelper.submitParams.cardType&&l.cardInfoHelper.realCardNoLength>p.MIN_CARD_NO_LENGTH||l.cardInfoHelper.userSpecifiedCard\n)l.cardNoLengthErrorTipDlg.msg&&l.getCardNo(\n).length!==l.cardInfoHelper.realCardNoLength&&!l.cardInfoHelper.userSpecifiedCard?(\nf.amc.fn.spmExposure('a283.b4032.c82521',_.mergeObject(\np.spmServerParam||{},{\nwantCardNoLength:l.cardInfoHelper.realCardNoLength,\ncardNoLength:l.getCardNo().length})),l.exitDlg.show({\ntitle:l.cardNoLengthErrorTipDlg.title,\nmessage:l.cardNoLengthErrorTipDlg.msg,\nbtns:l.cardNoLengthErrorTipDlg.btns.map(function(t){\nreturn t.txt}),clickCallBack:function(t){\nvar e=l.cardNoLengthErrorTipDlg.btns[t];e&&(\nf.amc.fn.spmClick('a283.b4032.c82521.d170198_'+t,\n_.mergeObject(p.spmServerParam||{},{btn:JSON.stringify(e)}))\n,e.act?l.commandProcess(e.act):l.cardNoInput.focus())}})\n):l.cardInfoHelper.checkFormComplete()&&l.bindCard();else{\nvar t={};t.action={\nname:p.rpcData.nextAction||'/card/verifyCardNo',loadtxt:'',\nhost:p.rpcData.pci_url,https:!0,\nenctype:'application/x-www-form-urlencoded',\nrequest_param:'requestData',params:p.rpcData.params},\nt.param={cardno:l.getCardNo()},l.scanFlag&&(\nt.param.scanflag=!0),t.param.luhnAC=C.luhnCheck(\nt.param.cardno),l.realSubmit(t)}},l.realSubmit=function(t){\nif(l.cardSubmitLock&&l.cardSubmitLockCount<3\n)return f.amc.fn.logError(\n'submit-lock-'+l.cardSubmitLockCount,'card-no'),\nvoid l.cardSubmitLockCount++;l.cardSubmitLock=!0,\nl.cardSubmitLockCount=0,document.asyncSubmit(t,function(t){\nif('1'===t.pageloading)l.cardSubmitLock=!0,\nl.cardSubmitLockCount=0,l.button.changeLoadingStatus(\ng.BUTTON_STATUS.LOADING);else if('0'===t.pageloading\n)l.button.changeLoadingStatus(g.BUTTON_STATUS.NORMAL),\nsetTimeout(function(){l.cardSubmitLock=!1,\nl.cardSubmitLockCount=0},1e3),\np.rpcData.exitConfirm&&N.getServerFeature();else if(t.status\n)'02'===t.status.substr(2,2)&&l.cardNoInput.focus(),\np.rpcData.exitConfirm&&N.getServerFeature();else if(\nt.newMsg||t.apiName||t.msg){f.amc.fn.spmExposure(\n'a283.b4032.c9686.d56090',p.spmServerParam,!1);var n=[{\ntxt:f.amc.fn.i18nValueForKey(\nt.apiName?'{{quick_bind}}':'{{OK}}'),act:{name:t.apiName,\nloadtxt:''}}];t.apiName&&n.push({\ntxt:f.amc.fn.i18nValueForKey('{{cancel}}')}),t.btns&&(\nn=t.btns),l.exitDlg.show({title:t.title||'',\nmessage:t.newMsg||t.msg||'系统繁忙，请稍后再试',btns:n.map(function(t\n){return t.txt}),clickCallBack:function(t){var e=n[t]\n;l.button.changeLoadingStatus(g.BUTTON_STATUS.NORMAL),\nl.cardSubmitLock=!1,l.cardSubmitLockCount=0,\nf.amc.fn.spmClick('a283.b4032.c9686.d170215',_.mergeObject(\np.spmServerParam||{},{btn:JSON.stringify(e)})),\ne.act?l.commandProcess(e.act):l.cardNoInput.focus()}}),\np.rpcData.exitConfirm&&N.getServerFeature()\n}else p.rpcData.exitConfirm&&N.getServerFeature()}),\nl.recommendCardNo='',l.button.changeLoadingStatus(\ng.BUTTON_STATUS.LOADING)},l.onBack=function(){if(\nl.questionnaire.setCountDownStop(!0),f.amc.fn.spmClick(\n'a283.b4032.c50020.d102921',p.spmServerParam),\nl.isQuickBindListShow){if(\np.rpcData.exitConfirm&&p.rpcData.exitConfirm.strategyOrder&&p.rpcData.exitConfirm.strategies&&0<p.rpcData.exitConfirm.strategyOrder.length\n){N.setFeature('isCardLess',l.getCardNo(\n).length!==l.cardInfoHelper.realCardNoLength),\np.rpcData.exitConfirm.strategies.NewUser?N.setFeature(\n'userTag','NewUser'\n):p.rpcData.exitConfirm.strategies.SafeText&&N.setFeature(\n'userTag','SafeText');var e=N.getResult(\np.rpcData.exitConfirm.strategies,\np.rpcData.exitConfirm.strategyOrder);if(e){\nf.amc.fn.spmExposure('a283.b4032.c50775',_.mergeObject(\np.spmServerParam,{strategy:e.strategyId}))\n;var n=e.actions&&0<e.actions.length?e.actions:[]\n;return N.notifyServer(),void l.exitDlg.show({title:e.title,\nmessage:e.message,btns:n.map(function(t){return t.text}),\nclickCallBack:function(t){n.length>t&&(f.amc.fn.spmClick(\n'a283.b4032.c50775.d211796_'+t,_.mergeObject(\np.spmServerParam,{text:(n[t]||{}).text+'',\nstrategyId:e.strategyId})),document.submit({\naction:n[t].act||{name:'loc:none'}}))}})}}if(\np.rpcData.exitConfirmDlg){f.amc.fn.spmExposure(\n'a283.b4032.c50775',p.spmServerParam);var i=_.tryJSONParse(\np.rpcData.exitConfirmDlg);return l.exitDlg.show({\ntitle:i.title,message:i.message,btns:[i.rightTxt,i.leftTxt],\nclickCallBack:function(t){1===t?(f.amc.fn.spmClick(\n'a283.b4032.c50775.d104673',p.spmServerParam),C.doAction(\ni.leftAct)):(f.amc.fn.spmClick('a283.b4032.c50775.d104674',\np.spmServerParam),C.doAction(i.rightAct))}}),void(\np.rpcData.exitConfirmDlg=void 0)}if(\n'back'===p.rpcData.backAct)f.amc.fn.back();else if(\np.rpcData.backAct){var t={action:{name:p.rpcData.backAct},\nparam:{first_enter_cashier:!1}};document.submit(t)\n}else f.amc.fn.exitConfirm('{{confirm_exit}}')\n}else l.inputBlur()},l.onChangeAccount=function(){if(\nl.questionnaire.setCountDownStop(!0),p.rpcData.switchAcc){\nvar t=[];t.push('{{switch_account}}'),document.actionSheet({\nbtns:t,cancelBtn:'{{cancel}}'},function(t){if(0===t.index){\ndocument.submit({action:{name:'/cashier/switchAccount'},\nparam:{backAct:'exit'}})}})}},\nl.initAdvertisementTopPart=function(){var t=_.tryJSONParse(\np.rpcData.propagate);if(t&&t.type&&t.data&&'bottom'!==t.type\n){var e=t.data,n=t.dataParams||{};e&&e.iconUrl&&(\nl.vueModel.data.subTitleLogo=e.iconUrl),\nf.amc.fn.spmExposure('a283.b4032.c21918.d66271',n,!1),\ne&&e.primaryText&&(l.vueModel.data.subTitle=e.primaryText),\ne&&e.bottomIconUrl&&(l.vueModel.data.subTitleLogo='',\nl.vueModel.data.bottomIcon=e.bottomIconUrl)}},\nl.onHelp=function(){l.questionnaire.setCountDownStop(!0)\n;var t=p.rpcData.helpURL||'https://csmobile.alipay.com/router.htm?scene=app_addcard'\n;document.submit({action:{\nname:'loc:openweb(\\''+t+'\\', \\'{{help}}\\')'}}),\nf.amc.fn.spmClick('a283.b4032.c9686.d24461',p.spmServerParam\n)},l.viewDidAppear=function(){l.viewDidAppeared||(\nl.vueModel.data.OCRSupported&&l.formatBankNo(),\nl.viewDidAppeared=!0,setTimeout(function(){\nh.modifyElementAttribute(l,document.body,{\nassociatedPageSpmId:'a283.b4032'})},200),\nf.amc.isAndroid&&f.amc.fn.sdkGreaterThanOrEqual('10.8.51'\n)&&!f.amc.isSDK&&'isolated'===p.rpcData.bizIdentifier&&_.getConfig(\n'msp_assistant_gray',function(t){t&&C.invoke('queryInfo',{\nqueryKey:'configJSONInfo',configKey:'msp_assistant_style'},\nfunction(t){if(t&&t.value){var e=_.tryJSONParse(t.value)\n;Object.keys(e).forEach(function(t){\nt===u.pageStyle&&f.amc.fn.openurl(\n'alipays://platformapi/startapp?appId=********&caseId=bangka'\n,'2')})}},500)}))},l}return n(u,t),\nu.getComponentCSSRules=function(){return{\n'.title':'_CardNoPage_o8by-c-title',\n'.main-body':'_CardNoPage_o8by-c-main-body',\n'.card-pd-lr':'_CardNoPage_o8by-c-card-pd-lr',\n'.sub-title':'_CardNoPage_o8by-c-sub-title',\n'.sub-title-logo':'_CardNoPage_o8by-c-sub-title-logo',\n'.sub-title-box':'_CardNoPage_o8by-c-sub-title-box',\n'.input-card-no-box':'_CardNoPage_o8by-c-input-card-no-box',\n'.input-card-no-tip':'_CardNoPage_o8by-c-input-card-no-tip',\n'.input-card-no-tip-1':'_CardNoPage_o8by-c-input-card-no-tip-1',\n'.input-card-no-tip-2':'_CardNoPage_o8by-c-input-card-no-tip-2',\n'.stretch-item':'_CardNoPage_o8by-c-stretch-item',\n'.card-no-cell':'_CardNoPage_o8by-c-card-no-cell',\n'.card-no-input':'_CardNoPage_o8by-c-card-no-input',\n'.blank':'_CardNoPage_o8by-c-blank',\n'.camera-img':'_CardNoPage_o8by-c-camera-img',\n'.recent-card-area':'_CardNoPage_o8by-c-recent-card-area',\n'.recently-used':'_CardNoPage_o8by-c-recently-used',\n'.recently-card':'_CardNoPage_o8by-c-recently-card',\n'.button-blank':'_CardNoPage_o8by-c-button-blank',\n'.ocr-image':'_CardNoPage_o8by-c-ocr-image',\n'.header-background':'_CardNoPage_o8by-c-header-background',\n'.bottom-background':'_CardNoPage_o8by-c-bottom-background',\n'.bank-list-blank':'_CardNoPage_o8by-c-bank-list-blank',\n'.dcep-container':'_CardNoPage_o8by-c-dcep-container',\n'.dcep-title':'_CardNoPage_o8by-c-dcep-title',\n'.dcep-inst-container':'_CardNoPage_o8by-c-dcep-inst-container',\n'.dcep-inst':'_CardNoPage_o8by-c-dcep-inst',\n'.camera-area':'_CardNoPage_o8by-c-camera-area',\n'.camera-box':'_CardNoPage_o8by-c-camera-box',\n'.camera-text':'_CardNoPage_o8by-c-camera-text',\n'.more-box':'_CardNoPage_o8by-c-more-box',\n'.more-content':'_CardNoPage_o8by-c-more-content',\n'.more-img':'_CardNoPage_o8by-c-more-img',\n'.card-list-divide':'_CardNoPage_o8by-c-card-list-divide',\n'.wk-logo':'_CardNoPage_o8by-c-wk-logo'}},\nu.getComponentJson=function(){return{_c:'amc-v-box',\n_t:'div',_cd:[{'v-if':'@{!!##gonggao.content##}',\n_c:'amc-marquee _CardNoPage_o8by-c-card-pd-lr',_t:'div',\n_cd:[{_c:'amc-marquee','v-style':'@{gonggao.style}',\n_t:'marquee','v-text':'@{gonggao.content}'}]},{\n'sp-view-id':'header-background',\n_c:'_CardNoPage_o8by-c-header-background',_t:'div'},{\n'sp-view-id':'bottom-background',\n_c:'_CardNoPage_o8by-c-bottom-background amc-flex-1',\n_t:'div'},{'sp-view-id':'main-body',\n_c:'amc-scroll amc-v-box amc-align-center _CardNoPage_o8by-c-main-body _CardNoPage_o8by-c-card-pd-lr amc-flex-1',\n_t:'div',_cd:[{'sp-view-id':'bind-title',\n_c:'_CardNoPage_o8by-c-title',_t:'label','v-text':'@{title}'\n},{'sp-view-id':'bind-sub-title',\n_c:'_CardNoPage_o8by-c-sub-title-box amc-align-center',\n_t:'div',_cd:[{'v-src':'@{subTitleLogo}',\n_c:'_CardNoPage_o8by-c-sub-title-logo',\n'v-if':'@{!!##subTitleLogo##}',_t:'img'},{\n_c:'_CardNoPage_o8by-c-sub-title',_t:'label',\n'v-text':'@{subTitle}'}]},{_c:'_CardNoPage_o8by-c-wk-logo',\n'v-src':'@{bottomIcon}','v-if':'@{!!##bottomIcon##}',\n_t:'img'},{'sp-view-id':'bank-list-blank',\n_c:'_CardNoPage_o8by-c-bank-list-blank amc-hidden',_t:'div'}\n,{'sp-view-id':'camera-area',\n_c:'amc-v-box amc-align-center amc-justify-center _CardNoPage_o8by-c-camera-area _CardNoPage_o8by-c-stretch-item amc-hidden',\nonclick:'onScanCard',\n'v-if':'@{##OCRSupported## && ##isOld##}',_t:'div',_cd:[{\n_c:'amc-justify-center amc-align-center _CardNoPage_o8by-c-camera-box',\n_t:'div',_cd:[{'sp-view-id':'camera-img',\n_c:'_CardNoPage_o8by-c-camera-img',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*KvTRSp2_jp4AAAAAAAAAAAAAARQnAQ',\n_t:'img'}]},{_c:'_CardNoPage_o8by-c-camera-text',_t:'label',\n_x:'{{scan_card_text}}'}]},{\n_c:'_CardNoPage_o8by-c-input-card-no-box amc-v-box _CardNoPage_o8by-c-stretch-item',\n'sp-view-id':'input-area',_t:'div',_cd:[{\n'sp-view-id':'input-card-no-tip',\n_c:'_CardNoPage_o8by-c-input-card-no-tip',_t:'div',_cd:[{\n_c:'_CardNoPage_o8by-c-input-card-no-tip-1',_t:'label',\n_x:'{{input_title}}'},{\n_c:'_CardNoPage_o8by-c-input-card-no-tip-2',\n'sp-view-id':'input-card-no-tip-2',_t:'label'}]},{\n_c:'_CardNoPage_o8by-c-card-no-cell',\n'sp-view-id':'card-no-cell',_t:'div',_cd:[{\n'sp-view-id':'cardNo',_y:'number',\n_c:'_CardNoPage_o8by-c-card-no-input _CardNoPage_o8by-c-stretch-item',\n'v-placeholder-cal':'@{placeHolder}',value:'',\noninput:'checkInput',_t:'input'},{'sp-view-id':'camera',\n_c:'_CardNoPage_o8by-c-camera-img amc-hidden',\nonclick:'onScanCard',\n'v-if':'@{##OCRSupported## && !##isOld##}}',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*PypASq2NLigAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]},{'sp-view-id':'ocr-image',\ncontentmode:'ScaleToFill',\n_c:'_CardNoPage_o8by-c-ocr-image amc-hidden',_t:'img'},{\n'sp-view-id':'recentCardArea',\n_c:'_CardNoPage_o8by-c-recent-card-area',\n'v-if':'@{!!(##recommendCard##.content)}',_t:'div',_cd:[{\n'sp-view-id':'recentCardText',\n_c:'_CardNoPage_o8by-c-recently-used',\n'v-text':'@{##recommendCard##.tip}',_t:'label'},{\n'sp-view-id':'recentCardName',\n_c:'_CardNoPage_o8by-c-recently-card',\n'v-text':'@{##recommendCard##.content}',\nonclick:'recommendCardClick',_t:'label'}]},{\n'v-if':'newVersion','sp-component':'CardInfo',\n'sp-component-id':'cardInfo',_t:'div'},{\n_c:'_CardNoPage_o8by-c-button-blank',_t:'div'},{\n'sp-component':'Button','sp-component-id':'button',_t:'div'}\n]},{'sp-view-id':'dcep-area',\n_c:'amc-v-box amc-flex-center _CardNoPage_o8by-c-dcep-container amc-hidden',\n_t:'div',_cd:[{_c:'_CardNoPage_o8by-c-dcep-title',\n_t:'label','v-text':'@{supportedInstInfos.title}'},{\n_c:'_CardNoPage_o8by-c-dcep-inst-container',_t:'div',_cd:[{\n'v-for':'@{supportedInstInfos.instInfos}',\n'v-src':'@{item.logoUrl}',_c:'_CardNoPage_o8by-c-dcep-inst',\n_t:'img'}]},{_c:'_CardNoPage_o8by-c-card-list-divide',\n_t:'div'},{'v-if':'@{!!(##supportedInstInfos##.moreUrl)}',\n_c:'_CardNoPage_o8by-c-more-box amc-align-center amc-justify-center amc-self-stretch',\nonclick:'selectMore',_t:'div',_cd:[{\n_c:'_CardNoPage_o8by-c-more-content',_t:'label',\n_x:'{{more}}'},{_c:'_CardNoPage_o8by-c-more-img',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*wi0wQI-0ODcAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]}]},{_c:'_CardNoPage_o8by-c-blank',\n'sp-view-id':'blank-area',_t:'div'}]}]}},\nu.componentName='CardNoPage',u.pageStyle=m.ISOLATED,\nu.componentHashName='CardNoPage_o8by',u}(s.BNComponent)\n;p.CardNoPage=P;var B,L=!1;function D(){\nreturn window.flybird&&window.flybird.local&&window.flybird.local.agednessVersion\n}document.viewDidAppear=function(){u&&u.viewDidAppear(),\nL?f.amc.fn.spmExposureResume():L=!0},B=f.amc.fn.docConfig,\nf.amc.fn.docConfig=function(){var t=JSON.parse(B())\n;return t.navi={naviBarColor:'#FFFFFF',statusBarStyle:'dark'\n},JSON.stringify(t)},p.isOlder=D;(new l.PreRenderManager\n).setup(function(t){!function(t){p.rpcData=t,\np.spmServerParam=_.tryJSONParse(p.rpcData.spmObj),\nf.amc.fn.spmPageCreate('a283.b4032',p.spmServerParam),\nfunction(){var t;if(f.amc.fn.sdkGreaterThanOrEqual('10.8.49'\n)){var e='QUICKPAY@cashier-card-inst-select-item-flex'\n;C.invoke('tplupdate',{tplInfoDict:(t={},\nt[e]=p.rpcData.instListTpl||'{\"format\":\"JSON\",\"gray\":false,\"needRes\":false,\"platform\":\"common\",\"publishVersion\":\"150924\",\"tag\":\"QUICKPAY\",\"time\":\"0011\",\"tplHash\":\"043042425bab8a9b6002e967db5a8a81\",\"tplId\":\"QUICKPAY@cashier-card-inst-select-item-flex\",\"tplUrl\":\"https://gw.alipayobjects.com/os/mobiletms/NlibxlwiftFgeCtMGRYO.json\",\"tplVersion\":\"5.6.7\"}'\n,t)},function(t){\nt&&t.updatedArr&&0<t.updatedArr.length&&-1!==t.updatedArr.indexOf(\ne)&&(p.instTplSaved=!0)})}}(),u=new P;var e={}\n;'back'===p.rpcData.backAct&&(e.backMode='back')\n;var n=void 0;-1===C.objectValues(m).indexOf(\np.rpcData.pageStyle||''\n)?P.pageStyle=m.NONE:P.pageStyle=p.rpcData.pageStyle,\nP.pageStyle!==m.DCEP&&(n=f.amc.path+'alipay_msp_help',\np.rpcData.switchAcc?n=f.amc.res.navMore:f.amc.fn.spmExposure(\n'a283.b4032.c9686.d24461',p.spmServerParam,!1)),\ndocument.body.style.height=f.amc.isAndroid?window.innerHeight:f.amc.specs.bodyHeight\n,D()&&(window.remScale=p.OLD_SCALE,\ndocument.body.style.height/=p.OLD_SCALE)\n;var i=f.amc.fn.getNav(f.amc.res.navBack,\nf.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'':'{{return}}',''\n,'',n,function(){u.onBack()},function(){\np.rpcData.switchAcc?u.onChangeAccount():u.onHelp()},e)\n;f.amc.isAndroid&&i&&D()&&(\ni.style.height=48/p.OLD_SCALE+'px'),\ndocument.body.appendChild(i),u.mountTo(document.body),\ndocument.setProp('focusableInTouchMode',{value:!0}),\nt.autoCommit&&t.cardNo&&setTimeout(function(){u.onSubmit()},\n10)}(t)}),window.onKeyDown=function(){\n4===window.event.which&&u&&u.onBack()};var O=function(t){if(\n!t)return'';var e=(t=t.replace(/\\s*/g,'')).match(\n/^\\d{12,22}$/);return e&&0<e.length?e[0]:void 0}},function(t\n,e,n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0}),e.randomStr=function(){return Math.floor(\n61439*Math.random()+4096).toString(16)},\ne.startsWith=function(t,e){return!!t&&0===t.indexOf(e)},\ne.tryJSONParse=function(t){if(e=t,\n'[object Object]'===Object.prototype.toString.call(e)\n)return t;var e;try{return JSON.parse(t)}catch(t){return{}}}\n,e.copyObj=function(t,e){for(var n in e||(e={}),t\n)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},\ne.queryURLParams=function(t){try{var e={};if(t.indexOf('?'\n)<0)return e;for(var n=(t=t.split('?')[1]).split('&'),\ni=0;i<n.length;i++){var o=n[i].split('=');e[o[0]]=o[1]}\nreturn e}catch(t){}return{}}},function(t,e,n){'use strict'\n;var C=this&&this.__assign||function(){return(\nC=Object.assign||function(t){for(var e,n=1,\ni=arguments.length;n<i;n++)for(var o in e=arguments[n]\n)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])\n;return t}).apply(this,arguments)};Object.defineProperty(e,\n'__esModule',{value:!0});var I=n(1),x=n(5),w=n(7),S=n(12),\ni=function(){function t(){this.vueModel={data:{},compute:{}}\n,this._componentName=this.constructor.componentName,\nthis._htmlString=this.constructor.componentHTML,\nthis._componentJson=this.constructor.getComponentJson(),\nthis._componentCSSRules=this.constructor.getComponentCSSRules(\n),this._hash=x.randomStr(),this._hasRootViewBuilt=!1,\nthis._rootView=null,this._componentId='',\nthis._subComponents=[],this._subComponentsMap={},\nthis._viewsIdMap={}}return t.getComponentCSSRules=function(\n){throw new Error('E0100')},t.getComponentJson=function(){\nthrow new Error('E0101')},t.prototype.mountTo=function(t,e){\nif(t){var n=this._acquireRootView();n?(e?t.insertBefore(n,e\n):t.appendChild(n),this._triggerOnMounted()):I.logger.e(\n'Cmp#mT','E0103 '+n)}else I.logger.e('Cmp#mT','E0102 '+t)},\nObject.defineProperty(t.prototype,'debugName',{get:function(\n){\nreturn'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'\n},enumerable:!0,configurable:!0}),\nt.prototype.getMountedRootView=function(){\nreturn this._hasRootViewBuilt?this._rootView:null},\nt.prototype.getMountedParentView=function(){\nvar t=this.getMountedRootView();return t?t.parentNode:null},\nt.prototype.getSubComponentById=function(t,e){\nvar n=this.debugName+'#SCById',i=this._subComponentsMap[t]\n;if(!i)return null;var o='',a='';try{\no=i.constructor.componentName,a=e.componentName}catch(t){\nI.logger.e(n,'E0104 '+t)}return o&&o===a?i:(I.logger.e(n,\n'E0105 '+o+', '+a),null)},\nt.prototype.getViewInComponentById=function(t){\nreturn this._viewsIdMap[t]},\nt.prototype.getComponentId=function(){\nreturn this._componentId},\nt.prototype.createStyledElement=function(t,e,n){\nvar i=document.createElement(t);if(i)return t&&(\ni.className+=' '+this._css(t,2)),n&&(\ni.className+=' '+this._csses(n,1)),e&&(\ni.className+=' '+this._css('#'+e,2)),i},\nt.prototype.applyStyleTo=function(t,e){t&&(\nt.className+=' '+this._csses(e,1))},\nt.prototype.css=function(t){return this._css(t,0)},\nt.prototype.csses=function(t){return this._csses(t,0)},\nt.prototype._csses=function(t,e){var n=this;return t.split(\n' ').map(function(t){return n._css(t,e)}).join(' ')},\nt.prototype._css=function(t,e){if(!t)return''\n;var n=this._componentCSSRules;if(!n)return t;switch(\nt.charAt(0)){case'#':case'.':return n[t]||t;default:switch(e\n){case 0:return n['.'+t]||n[t]||t;case 1:return n['.'+t]||t\n;case 2:default:return t}}},\nt.prototype._triggerOnMounted=function(){new S.Observer(\nthis.vueModel.data),I.logger.i('','I0106 '+this.debugName)\n;for(var t=0,e=this._subComponents;t<e.length;t++){\nvar n=e[t];n&&n._triggerOnMounted()}\nthis.onMounted&&this.onMounted()},\nt.prototype._getMethod=function(t){var e=this[t]\n;return e instanceof Function?e:null},\nt.prototype._acquireComponentJson=function(){\nvar t=this.debugName+'#acCJ',\ne=w.ComponentRegistry.getComponentJson(this._componentName)\n;return e?(I.logger.i(t,'I0107'),e\n):void 0!==this._componentJson?(I.logger.i(t,'I0108'),\nw.ComponentRegistry.putComponentJson(this._componentName,\nthis._componentJson),this._componentJson):(I.logger.e(t,\n'E0109'),null)},t.prototype._acquireRootView=function(){\nvar t=this.debugName+'#acRV';if(this._hasRootViewBuilt\n)return I.logger.i(t,'I0110'),this._rootView\n;var e=this._acquireComponentJson();return e?(\nthis._rootView=this._convertJsonToBNNode(e,\nthis.vueModel.data||{}),this._hasRootViewBuilt=!0,\nI.logger.i(t,'I0112'),this._rootView):(I.logger.e(t,'E0111')\n,null)},t.prototype._genArrayChildNode=function(t,e,n,i,o){\nvar a=S.vueUtils.item2ArrayIndex(o,e),\nr=this._convertJsonToBNNode(t,C({},e,{item:n,index:i,\narrayName:a}));return r?(r.setAttribute('index',i),\nr.setAttribute('for_name',o),r):null},\nt.prototype._convertJsonToBNNode=function(t,m){var f=this,\ne=this.debugName+'#cJTB';if(void 0===t._t)return null\n;var _=document.createElement(t._t),r=[];if(void 0!==t._cd\n)for(var n=function(l){if(l['v-for']||l['v-for-cal']){\nvar t=!l['v-for']&&!!l['v-for-cal'],e=(\nt?c.vueModel.compute:m)||{},n=S.vueUtils.getObject(\nt?l['v-for-cal']:l['v-for'],e,t),u=t?S.vueUtils.rmSymbol(\nl['v-for-cal']):S.vueUtils.rmSymbol(l['v-for']);if(!u||!n\n)return'continue';for(var i in n)if(n.hasOwnProperty(i)){\nvar o=c._genArrayChildNode(l,m,n[i],i,u);o&&r.push(o)}\nvar p=document.createElement('div');p&&(\np.style.display='none',p.setAttribute('for_end',u),r.push(p)\n,new S.Watcher(u,e,function(t){if(_){S.rmWatchers(u);for(\nvar e=[],n=0,i=_.childNodes;n<i.length;n++){var o=i[n]\n;o.getAttribute('for_name')===u&&e.push(o)}for(var a=0,\nr=e;a<r.length;a++){var c=r[a];_.removeChild(c)}if(t)for(\nvar s in t)if(t.hasOwnProperty(s)){\nvar d=f._genArrayChildNode(l,m,t[s],s,u);d&&_.insertBefore(d\n,p)}}},t).id=m.arrayName)}else{var a=c._convertJsonToBNNode(\nl,m);if(!a)return'continue';r.push(a)}},c=this,i=0,\no=t._cd;i<o.length;i++)n(o[i]);if(!_)return null\n;m&&m.index&&_.setAttribute('index',m.index)\n;var a=t['bn-component']||t['sp-component'];if(a){\nI.logger.i(e,'I0113 '+a)\n;var s=w.ComponentRegistry.createComponent(a);if(!s\n)return I.logger.e(e,'E0114 '+a+', '+s),null\n;var d=t['bn-component-id']||t['sp-component-id']\n;return d&&(s._componentId=d),s.onCreated&&s.onCreated(),\nI.logger.i(e,'I0115 '+s.debugName+', '+d),\nthis._subComponents.push(s),d&&!this._subComponentsMap[d]&&(\nthis._subComponentsMap[d]=s),s._acquireRootView()}\nvar l=t['bn-view-id']||t['sp-view-id'];for(var u in l&&(\nI.logger.i(e,'I0116 '+l),this._viewsIdMap[l]||(\nthis._viewsIdMap[l]=_)),t._i&&(_.id=t._i),t._c&&(\n_.className=t._c),t._s&&(_.style.cssText=t._s),t._x&&(\n_.innerText=t._x),t._y&&(_.type=t._y),t)if(t.hasOwnProperty(\nu))if(0===u.indexOf('on')){var p=this._getMethod(t[u]);p&&(\n_[u]=p.bind(this,_))}else if(0===u.indexOf('_'));else if(\n0===u.indexOf('bn-')||0===u.indexOf('sp-'));else if(\nx.startsWith(u,'v-')){var g=u.split('-');if(\n2===g.length||3===g.length){var h=g[1]\n;2===g.length?new S.NodeCompile(m).compile(h,_,t[u],t._t\n):'cal'===g[2]&&(m.arrayName&&m.index?new S.NodeCompile(C({}\n,this.vueModel.compute,{arrayName:m.arrayName,index:m.index}\n),!0).compile(h,_,t[u],t._t):new S.NodeCompile(\nthis.vueModel.compute,!0).compile(h,_,t[u],t._t))\n}else _[u]=t[u]}else _[u]=t[u];for(var b=0,\nv=r;b<v.length;b++){var y=v[b];_.appendChild(y)}return _},\nt.componentName='',t.componentHTML='',t.componentCSS='',\nt.componentHashName='',t}();e.BNComponent=i},function(t,e,n\n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0});var i=n(1),o=function(){function n(){}\nreturn n.registerComponent=function(t,e){\ne?n.facts[t]?i.logger.e('CmpReg#regCmp','E0002 '+t):(\ni.logger.i('CmpReg#regCmp','I0003 '+t),n.facts[t]=e\n):i.logger.e('CmpReg#regCmp','E0001 '+t+', '+e)},\nn.getKnownComponents=function(){return n.facts},\nn.getComponentJson=function(t){return n.jsons[t]},\nn.putComponentJson=function(t,e){e||i.logger.e(\n'CmpReg#putCmpJ','E0004 '+t+', '+e),n.getComponentJson(t\n)?i.logger.e('CmpReg#putCmpJ','E0005 '+t):(i.logger.i(\n'CmpReg#putCmpJ','I0006 '+t),n.jsons[t]=e)},\nn.createComponent=function(t){i.logger.i('CmpReg#crtCmp',\n'I0007 '+t);var e=n.facts[t];return e?new e:(i.logger.e(\n'CmpReg#crtCmp','E0008 '+t),null)},n.facts={},n.jsons={},n}(\n);e.ComponentRegistry=o},function(t,e,n){'use strict';var i,\no,a,r,c=this&&this.__extends||(i=function(t,e){return(\ni=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var s,d,l,u,p,m,f=n(0),_=n(6),g=n(2\n),h=n(3);(m=s=e.BUTTON_TYPE||(e.BUTTON_TYPE={})\n).PRIMARY='PRIMARY',m.NORMAL='NORMAL',(p=d=e.LOADING_TYPE||(\ne.LOADING_TYPE={}))[p.JuHua=0]='JuHua',\np[p.CIRCLE=1]='CIRCLE',(u=l=e.BUTTON_STATUS||(\ne.BUTTON_STATUS={}))[u.NORMAL=0]='NORMAL',\nu[u.LOADING=1]='LOADING',u[u.SUCCESS=2]='SUCCESS',\nu[u.DISABLE=3]='DISABLE';var b=((o={}\n)[s.NORMAL]=f.amc.path+'alipay_msp_loading_blue.gif',\no[s.PRIMARY]=f.amc.res.loading,o),v=((a={}\n)[s.NORMAL]='amc-loading-img amc-text-color-blue',\na[s.PRIMARY]='amc-loading-img amc-text-white-clolor',a),y=((\nr={})[s.NORMAL]=f.amc.path+'alipay_msp_success_blue.gif',\nr[s.PRIMARY]=f.amc.res.success,r),\nC='color: '+g.getThemeColor()+';',\nI='background-color: #FFF;border: 2px '+g.getThemeColor(\n)+';font-size: 18px;',x=function(e){function t(){\nvar t=null!==e&&e.apply(this,arguments)||this\n;return t.props={type:s.PRIMARY,onClick:function(){},\ncircleLoading:{},juHuaLoading:{},lottieImg:null},\nt.onCreated=function(){},t.onMounted=function(){\nt.props.circleLoading=g.createEmbedViPlugin(\nt.getViewInComponentById('buttonInnerBox'),{\ntype:'MQPPayGifView'},'amc-hidden',t.getViewInComponentById(\n'buttonText')),g.modifyElementCSS(t,t.props.circleLoading,\n'width: 24px; height: 24px;margin-right: 8px;'),\nt.props.juHuaLoading=t.getViewInComponentById('loadingJuHua'\n),f.amc.isAndroid&&g.modifyElementAttribute(t,\nt.props.juHuaLoading,{\nsrc:f.amc.path+'alipay_msp_indicator_white_loading'}),\ng.modifyElementAttribute(t,'buttonText',{\naccessibilityTraits:'Button'}),t.changeLoadingStatus(\nl.NORMAL)},t.lottieReady=!1,t.lottiePlay=!1,t}return c(t,e),\nt.prototype.setStyle=function(t){g.modifyElementStyle(this,\n'button',t)},t.prototype.setHeight=function(t){\ng.modifyElementStyle(this,'buttonText',{height:t+'px'}),\ng.modifyElementStyle(this,'button',{height:t+'px',\nmaxHeight:t+'px',minHeight:t+'px'})},\nt.prototype.setType=function(t){switch(this.props.type=t,\ng.modifyElementClass(this,this.props.juHuaLoading,v[t],!1),t\n){case s.NORMAL:g.modifyElementClass(this,'button',\n'amc-btn-secondary amc-align-center amc-justify-center button-primary'\n,!1),g.modifyElementCSS(this,'buttonText',C),\ng.modifyElementCSS(this,'button',I);break;case s.PRIMARY:\ng.modifyElementClass(this,'button',\n'amc-btn-primary amc-align-center amc-justify-center button-primary'\n,!1),g.modifyElementCSS(this,'buttonText','color: #fff;')}\nreturn this},t.prototype.setLottieImg=function(t){var e=this\n;if(!f.amc.isSDK&&t&&f.amc.fn.sdkGreaterThanOrEqual(\n'10.8.29')){var n=this.getViewInComponentById(\n'buttonBackground');this.props.lottieImg=f.amc.fn.create(\n'lottie','',n),this.applyStyleTo(this.props.lottieImg,\n'amc-flex-center lottie-loading'),\nthis.props.lottieImg.djangoId=t,\nthis.props.lottieImg.repeatCount='-1',\nthis.props.lottieImg.invoke('pause',{}),\nthis.props.lottieImg.dataReady=function(){e.lottieReady=!0,\ne.lottiePlay&&e.props.lottieImg&&e.props.lottieImg.invoke(\n'play',{})}}return this},t.prototype.startLottie=function(){\nthis.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke(\n'play',{}),this.lottiePlay=!0},\nt.prototype.stopLottie=function(){\nthis.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke(\n'stop',{}),this.lottiePlay=!1},\nt.prototype.setVisible=function(t){\nreturn g.modifyElementStyle(this,'button',{\ndisplay:t?'flex':'none'}),this},\nt.prototype.setOnClick=function(t){\nreturn this.props.onClick=t||h.doNothing,\ng.modifyElementAttribute(this,'button',{\nonclick:this.props.onClick}),this},\nt.prototype.setText=function(t){return this.props.text=t,\nthis},t.prototype.changeTextAndOnClick=function(t,e){\nvar n=void 0;switch(t){case l.SUCCESS:this.props.text&&(\nn=this.props.text[l.SUCCESS]),g.modifyElementAttribute(this,\n'button',{disabled:!1,onclick:h.doNothing});break\n;case l.NORMAL:this.props.text&&(n=this.props.text[l.NORMAL]\n),g.modifyElementAttribute(this,'button',{disabled:!1,\nonclick:this.props.onClick});break;case l.LOADING:\nthis.props.text&&(n=this.props.text[l.LOADING]),\ng.modifyElementAttribute(this,'button',{disabled:!1,\nonclick:h.doNothing});break;case l.DISABLE:\nthis.props.text&&(n=this.props.text[l.NORMAL]),\ng.modifyElementAttribute(this,'button',{disabled:!0,\nonclick:h.doNothing});break;default:n=''}\nreturn void 0!==e?g.modifyElementAttribute(this,'buttonText'\n,{innerText:e}):void 0!==n&&g.modifyElementAttribute(this,\n'buttonText',{innerText:n}),this},\nt.prototype.changeLoadingStatus=function(t,e,n){switch(e){\ncase d.CIRCLE:g.visibleElement(this,this.props.juHuaLoading,\n!1),g.visibleElement(this,this.props.circleLoading,!1),\nthis.changeTextAndOnClick(t,n),this.changeCircleLoading(t),\ng.visibleElement(this,this.props.circleLoading,\nt!==l.NORMAL&&t!==l.DISABLE);break;case d.JuHua:\ng.visibleElement(this,this.props.juHuaLoading,!1),\ng.visibleElement(this,this.props.circleLoading,!1),\nthis.changeTextAndOnClick(t,n),g.visibleElement(this,\nthis.props.juHuaLoading,t!==l.NORMAL&&t!==l.DISABLE);break\n;default:this.changeLoadingStatus(t,d.CIRCLE,n)}},\nt.prototype.changeCircleLoading=function(t){\nvar e=this.props.circleLoading;switch(t){case l.LOADING:\ng.modifyElementAttribute(this,e,{src:b[this.props.type]})\n;break;case l.NORMAL:case l.DISABLE:\ng.modifyElementAttribute(this,e,{src:''});break\n;case l.SUCCESS:g.modifyElementAttribute(this,e,{\nsrc:y[this.props.type]});break;default:f.amc.fn.logError(\n'Button','loading-'+(t||'status'))}},\nt.getComponentCSSRules=function(){return{\n'.text-primary':'_Button_b6r6-c-text-primary',\n'.button-primary':'_Button_b6r6-c-button-primary',\n'.lottie-loading':'_Button_b6r6-c-lottie-loading',\n'.lottie-margin':'_Button_b6r6-c-lottie-margin'}},\nt.getComponentJson=function(){return{'sp-view-id':'button',\n_c:'amc-btn-primary amc-align-center amc-justify-center amc-v-box _Button_b6r6-c-button-primary',\n_t:'div',_cd:[{'sp-view-id':'buttonBackground',\n_c:'_Button_b6r6-c-lottie-loading amc-align-center amc-justify-center',\n_t:'div'},{'sp-view-id':'buttonInnerBox',\n_c:'_Button_b6r6-c-lottie-margin amc-align-center amc-justify-center',\n_t:'div',_cd:[{'sp-view-id':'loadingJuHua',src:'indicatior',\n_c:'amc-loading-img amc-text-white-clolor',alt:'',_t:'img'},\n{'sp-view-id':'buttonText',\n_c:'_Button_b6r6-c-text-primary amc-ellipsis',_t:'label',\n_x:'{{pay_right_now}}'}]}]}},t.componentName='Button',\nt.componentHashName='Button_b6r6',t}(_.BNComponent)\n;e.Button=x},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var i=n(3)\n;e.mergeObject=i.mergeObject,e.isFunction=i.isFunction,\ne.isPreRender=i.isPreRender,e.copyObj=i.copyObj,\ne.doNothing=i.doNothing,e.tryJSONParse=i.tryJSONParse,\ne.checkEmptyObj=i.checkEmptyObj,\ne.substrWithFontWidth=i.substrWithFontWidth,\ne.calculateFontWidth=i.calculateFontWidth,\ne.deepCopy=i.deepCopy,e.getConfig=i.getConfig,\ne.showLoading=i.showLoading,e.hideLoading=i.hideLoading\n;var o=n(16)\n;e.VI_CHANNEL_MODE_FROM_TEMPLATE=o.VI_CHANNEL_MODE_FROM_TEMPLATE\n,e.SCALE_FACTOR=o.SCALE_FACTOR;var a=n(2)\n;e.modifyElementStyle=a.modifyElementStyle,\ne.modifyElementAttribute=a.modifyElementAttribute,\ne.modifyElementClass=a.modifyElementClass,\ne.visibleElement=a.visibleElement,\ne.modifyElementCSS=a.modifyElementCSS,\ne.createEmbedViPlugin=a.createEmbedViPlugin,\ne.getThemeColor=a.getThemeColor,\ne.createEmbedPlugin=a.createEmbedPlugin;var r=n(17)\n;e.logAction=r.logAction;var c=n(18);e.stEscape=c.stEscape,\ne.toString=c.toString,e.STPerf=c.STPerf,e.STAct=c.STAct,\ne.STRecord=c.STRecord;var s=n(19)\n;e.ImageLoader=s.ImageLoader;var d=n(20);e.ocr=d.ocr},\nfunction(t,e,n){'use strict';Object.defineProperty(e,\n'__esModule',{value:!0});var o=n(0),i=n(3);function a(t,e,n,\ni){void 0===i&&(i=0),o.amc.isAndroid||i?setTimeout(function(\n){document.invoke(t,e,n)},i||30):document.invoke(t,e,n)}\ne.invoke=a,e.luhnCheck=function(t){for(var e=0,n=!1,\ni=t.length-1;0<=i;i--){var o=parseInt(t.charAt(i))\n;e+=n?9<2*o?2*o-9:2*o:o,n=!n}return e%10==0},\ne.getCommonInfo=function(e){a('queryInfo',{\nqueryKey:'commonJSONInfo',tid:!0,uac:!0},function(t){e(t||{}\n)})},e.doAction=function(t){if(t){var e=t;o.amc.fn.isString(\nt)&&(e=i.tryJSONParse(t)),document.submit({action:e})}},\ne.objectValues=function(t){if(!o.amc.fn.isObject(t))return[]\n;var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(t[n])\n;return e}},function(t,n,e){'use strict'\n;Object.defineProperty(n,'__esModule',{value:!0});var i=e(4)\n,r=e(0),m=e(5),f={InstFirstBindCoupon:{op:'and',name:[{\ncardType:{de:'not',value:''},instId:{de:'not',value:''},\ncardGiftAmount:{de:'not',value:''}}]},InstExpressSign:{\nop:'and',name:[{op:'or',name:[{isCardLess:{de:'eq',value:!0}\n},{errorCode:{de:'eq',value:'Exsc_Err_1000003'}}]},{\nop:'and',name:[{isCardBinQuickBindSupport:{de:'eq',value:!0}\n}]},{op:'and',name:[{op:'or',name:[{preInstId:{de:'eq',\nvalue:''}},{instId:{de:'eq',value:'${preInstId}'}}]},{\nop:'or',name:[{preCardType:{de:'eq',value:''}},{cardType:{\nde:'in',value:'${preCardType}'}}]}]}]},InstExpressList:{\nop:'and',name:[{isQuickBindSupport:{de:'eq',value:!0},\npreInstId:{de:'eq',value:''},preCardType:{de:'eq',value:''}}\n]},NewUser:{op:'and',name:[{userTag:{de:'eq',value:'NewUser'\n}}]},SafeText:{op:'and',name:[{userTag:{de:'eq',\nvalue:'SafeText'}}]},Default:{op:'and',name:[]},\nCampaignOperation:{op:'and',name:[{campaignMessage:{\nde:'not',value:''},campaignTitle:{de:'not',value:''}}]}},_={\nspecifiedInstId:'preInstId',specifiedCardType:'preCardType',\ncardNoInstId:'instId',cardNoInstName:'instName',\ncardNoCardType:'cardType',cardNoCardTypeName:'cardTypeName',\ncardNoCampaignAmount:'cardGiftAmount',\nnewUserCampaignAmount:'newUserGiftAmount',\nlastErrorCode:'errorCode',\ncardNoSupportExpress:'isCardBinQuickBindSupport',\nsupportExpress:'isQuickBindSupport'},g={}\n;n.setFeature=function(t,e){if(t)switch(t){case'cardType':\ng.cardType={de:'assign',value:e+''},g.cardTypeName={\nde:'assign',value:'CC'===e?r.amc.fn.i18nValueForKey(\n'{{credit_card}}'):r.amc.fn.i18nValueForKey('{{store_card}}'\n)};break;case'isCardBinQuickBindSupport':\ng.isCardBinQuickBindSupport={de:'assign',value:'true'===e}\n;break;case'isQuickBindSupport':g.isQuickBindSupport={\nde:'assign',value:'true'===e};break;default:g[t]={\nde:'assign',value:e}}},n.initFeature=function(){\nn.setFeature('preInstId',''),n.setFeature('preCardType',''),\nn.resetCardQueryFlag()},n.resetCardQueryFlag=function(){\nn.setFeature('instId',''),n.setFeature('instName',''),\nn.setFeature('cardType',''),n.setFeature(\n'isCardBinQuickBindSupport',''),n.setFeature(\n'cardGiftAmount','')};var h=function(t){switch(t.op){\ncase'and':return a(t.name);case'or':return c(t.name)\n;default:return!1}},o=function(t){return'op'in t},\na=function(t){if(0===t.length)return!0;for(\nvar e=0;e<t.length;e++){var n=t[e];if(o(n)){if(!h(n)\n)return!1}else if(!s(n))return!1}return!0},c=function(t){if(\n0===t.length)return!0;for(var e=0;e<t.length;e++){var n=t[e]\n;if(o(n)){if(h(n))return!0}else if(s(n))return!0}return!1},\ns=function(t){var e;for(e in t){var n=t[e];if(n){\nvar i=n.value;if(r.amc.fn.isString(i)&&m.startsWith(i,'${')\n){var o=g[i.substring(2,i.length-1)];i=o?o.value:''}\nvar a=g[e];if(!a)return!1;switch(n.de){case'in':if(-1===(\ni+'').split(',').indexOf(a.value+''))return!1;break\n;case'eq':if(i!==a.value&&!!i!=!!a.value)return!1;break\n;case'not':if(i===a.value||!!i==!!a.value)return!1}}}\nreturn!0},b='';n.getResult=function(t,e){b='';for(\nvar n=0;n<e.length;n++){var i=e[n];if(t.hasOwnProperty(i\n)&&f.hasOwnProperty(i)){var o=t[i],a=f[i];if(h(a)){for(\nvar r=o.message.replace(/\\$\\{(\\w+)\\}/g,function(t,e){return(\ng[_[e]||e]||{}).value||''}),c=o.title.replace(/\\$\\{(\\w+)\\}/g\n,function(t,e){return(g[_[e]||e]||{}).value||''}),s=[],\nd=0;d<(o.actions||[]).length;d++){var l=o.actions[d],u={\nact:{name:'loc:none'},text:'OK'};if(l&&l.act){\nu.act.name=l.act.name||'loc:none';var p=(l.act.params||''\n).replace(/\\$\\{(\\w+)\\}/g,function(t,e){\nreturn g[_[e]].value||''});u.act.params=m.tryJSONParse(\np||'{}')}l&&l.text&&(u.text=l.text),s.push(m.tryJSONParse(\nJSON.stringify(u||{})))}return b=o.strategyId,{name:o.name,\nstrategyId:o.strategyId,message:r,title:c,actions:s}}}}\nreturn null},n.notifyServer=function(){document.invoke('rpc'\n,{\noperationType:'alipay.mobilechannel.cardbindingprod.hitStrategy',\nrequestData:{\nchannelContextId:i.rpcData.channelContextId||'',\nscene:i.rpcData.exitConfirm&&i.rpcData.exitConfirm.scene||'',\nstrategyId:b||''}},function(){})},\nn.getServerFeature=function(){document.invoke('rpc',{\noperationType:'alipay.mobilechannel.cardbindingprod.userBehaviors',\nrequestData:{\nchannelContextId:i.rpcData.channelContextId||'',\nscene:i.rpcData.exitConfirm&&i.rpcData.exitConfirm.scene}},\nfunction(t){var e;if(\nt&&t.success&&t.data&&t.data.success&&t.data.tags)for(\ne in t.data.tags)n.setFeature(_[e]||e,t.data.tags[e])})}},\nfunction(module,exports,__webpack_require__){'use strict'\n;Object.defineProperty(exports,'__esModule',{value:!0})\n;var util_1=__webpack_require__(5),\namc_types_1=__webpack_require__(0),watchers;function notify(\n){watchers&&watchers.forEach(function(t){t.update()})}\nfunction rmWatchers(e){watchers=watchers.filter(function(t){\nreturn t.id!==e})}exports.rmWatchers=rmWatchers\n;var Watcher=function(){function t(t,e,n,i){if(this.id='',\nthis.lazy=!1,e&&'object'==typeof e){if(this.lazy=i,\nthis.callback=n,util_1.startsWith(t,'item'\n)&&e.arrayName&&e.index){var o=t.replace('item','')\n;this.expression=e.arrayName+'.'+e.index,o&&(\nthis.expression+=o)}else this.expression=t;this.data=e,\nthis.value=vueUtils.getVal(t,e,this.lazy),watchers||(\nwatchers=[]),watchers.push(this)}}\nreturn t.prototype.update=function(){if(\nthis.data&&this.expression&&this.callback){\nvar t=vueUtils.getVal(this.expression,this.data,this.lazy),\ne=this.value;vueUtils.equals(t,e)||(this.value=t,\nthis.callback(t))}},t}();exports.Watcher=Watcher\n;var Observer=function(){function t(t){this.observe(t)}\nreturn t.prototype.observe=function(e){var n=this\n;e&&'object'==typeof e&&Object.keys(e).forEach(function(t){\ntry{n.defineReactive(e,t,e[t]),n.observe(e[t])}catch(t){}})}\n,t.prototype.defineReactive=function(t,e,n){var i=this\n;Object.defineProperty(t,e,{enumerable:!0,configurable:!1,\nget:function(){return n},set:function(t){vueUtils.equals(t,n\n)||(n=t,i.observe(t),notify())}})},t}()\n;exports.Observer=Observer;var NodeCompile=function(){\nfunction t(t,e){void 0===e&&(e=!1),this.data=t||{},\nthis.lazy=e}return t.prototype.compile=function(n,t,e,i){\nvar o=this;if(t)switch(n){case'text':this.labelProcess(t,e,\nfunction(t,e){t.innerText=void 0===e?'':e});break\n;case'html':this.labelProcess(t,e,function(t,e){\nt.innerHtml=void 0===e?'':e});break;case'class':\nthis.labelProcess(t,e,function(t,e){var n=t.className,\ni=t.getAttribute('v-class-name')||'';n=n&&n.replace(i,''\n).replace(/\\s+$/,''),t.setAttribute('v-class-name',e),\nt.className=n?n+' '+e:e});break;case'style':\nthis.eventProcess(t,e,function(t,e){\nvar n=util_1.tryJSONParse(e);util_1.copyObj(n,t.style)})\n;break;case'model':this.eventProcess(t,e,function(t,e){\nt.value=e}),'input'===i?t.oninput=function(){\nvueUtils.setTextVal(e,t.value,o.data)}:'switch'===i&&(\nt.onchange=function(t){vueUtils.setTextVal(e,t||'off',o.data\n)});break;case'if':this.eventProcess(t,e,function(t,e){\n!0===e?(t.style.display='flex',spmUtils.process(t,function(t\n){amc_types_1.amc.fn.spmExposure(t.spmId,t.param4Map,\nt.doNotResume)})):t.style.display='none'});break;case'spm':\nthis.labelProcess(t,e,function(t,e){t.setAttribute('spm',\nvoid 0===e?'':e)});break;case'uep':this.labelProcess(t,e,\nfunction(t,e){e&&util_1.startsWith(e,'a283'\n)&&t.setAttribute('behaviorInfo',JSON.stringify({spm:e,\nbizCode:'pay',extParam:{}}))});break;case'click':\nthis.eventProcess(t,e,function(t,e){vueUtils.isFunction(e\n)?t.onclick=function(){e(t),spmUtils.process(t,function(t){\namc_types_1.amc.fn.spmClick(t.spmId,t.param4Map)})\n}:t.onclick=function(){}});break;case'focus':\nthis.eventProcess(t,e,function(t,e){vueUtils.isFunction(e\n)?t.onfocus=function(){e(t)}:t.onfocus=function(){}});break\n;case'blur':this.eventProcess(t,e,function(t,e){\nvueUtils.isFunction(e)?t.onblur=function(){e(t)\n}:t.onfocus=function(){}});break;default:-1===e.indexOf('@{'\n)?t.setAttribute(n,e):this.labelProcess(t,e,function(t,e){\nt.setAttribute(n,void 0===e?'':e)})}},\nt.prototype.labelProcess=function(n,i,o){var a=this,\nt=i.match(/@\\{([^}]+)\\}/g),e=i;t&&0<t.length&&(\ne=vueUtils.getTextVal(i,this.data,this.lazy),t&&t.forEach(\nfunction(t){var e=/@\\{([^}]+)\\}/g.exec(t);e&&1<e.length&&(\nnew Watcher(e[1],a.data,function(t){o(n,vueUtils.getTextVal(\ni,a.data,a.lazy))},a.lazy).id=a.data.arrayName)})),o(n,e)},\nt.prototype.eventProcess=function(e,t,n){\nvar i=/@\\{([^}]+)\\}/g.exec(t),o=vueUtils.getObject(t,\nthis.data,this.lazy);i&&1<i.length&&(new Watcher(i[1],\nthis.data,function(t){n(e,t)},this.lazy\n).id=this.data.arrayName),n(e,o)},t}()\n;exports.NodeCompile=NodeCompile;var spmUtils=function(){\nfunction t(){}return t.process=function(t,e){\nvar n=t.getAttribute('spm');if(n)try{var i=JSON.parse(n)\n;i&&i.spmId&&e(i)}catch(t){}},t}(),vueUtils=function(){\nfunction vueUtils(){}\nreturn vueUtils.item2ArrayIndex=function(t,e){var n=t;if(\nutil_1.startsWith(t,'item')&&e.arrayName&&e.index){\nvar i=t.replace('item','');n=e.arrayName+'.'+e.index,i&&(\nn+=i)}return n},vueUtils.getVal=function(expr,data,lazy){if(\nexpr){var values=expr.match(/##([^#]+)##/g);if(\nvalues&&0<values.length){for(var func_1=expr,\nindex=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(\n/##([^#]+)##/,'vueArgs['+index+']'),index++;for(\nvar _vueArgs=[],i=0;i<index;i++)_vueArgs.push(\nthis.getRealVal(values[i].replace(/##/g,''),data,lazy))\n;return function(vueArgs){return eval(func_1)}(_vueArgs)}\nreturn this.getRealVal(expr,data,lazy)}},\nvueUtils.getRealVal=function(t,e,n){if(t){var i=t.split('.')\n,o=n&&vueUtils.isFunction(e[i[0]])?e[i[0]]():e;return(\nn?i.slice(1):i).reduce(function(t,e){return t[e]},o)}},\nvueUtils.getTextVal=function(t,o,a){var r=this\n;return t.replace(/@\\{([^}]+)\\}/g,function(){for(var t,e=[],\nn=0;n<arguments.length;n++)e[n]=arguments[n]\n;var i=vueUtils.item2ArrayIndex(e[1],o);return void 0===(\nt=r.getVal(i,o,a))?'':t})},vueUtils.getObject=function(t,e,n\n){var i=/@\\{([^}]+)\\}/g.exec(t);if(i&&1<i.length\n)return this.getVal(i[1],e,n)},vueUtils.rmSymbol=function(t\n){var e=/@\\{([^}]+)\\}/g.exec(t);return e&&1<e.length?e[1]:''\n},vueUtils.setVal=function(t,i,e){var o=t.split('.')\n;return o.reduce(function(t,e,n){\nreturn n===o.length-1?t[e]=i:t[e]},e)},\nvueUtils.setTextVal=function(t,e,n){\nvar i=/@\\{([^}]+)\\}/g.exec(t);i&&1<i.length&&this.setVal(\ni[1],e,n)},vueUtils.equals=function(t,e){return this.eq(t,e,\nvoid 0,void 0)},vueUtils.eq=function(t,e,n,i){if(t===e\n)return 0!==t||1/t==1/e;if(null==t||null==e)return t===e\n;var o=toString.call(t);if(o!==toString.call(e))return!1\n;switch(o){case'[object RegExp]':case'[object String]':\nreturn''+t==''+e;case'[object Number]':\nreturn+t!=+t?+e!=+e:0==+t?1/+t==1/e:+t==+e\n;case'[object Date]':case'[object Boolean]':return+t==+e}\nvar a='[object Array]'===o;if(!a){if(\n'object'!=typeof t||'object'!=typeof e)return!1\n;var r=t.constructor,c=e.constructor;if(r!==c&&!(\nthis.isFunction(r)&&r instanceof r&&this.isFunction(c\n)&&c instanceof c)&&'constructor'in t&&'constructor'in e\n)return!1}i=i||[];for(var s=(n=n||[]).length;s--;)if(\nn[s]===t)return i[s]===e;if(n.push(t),i.push(e),a){if((\ns=t.length)!==e.length)return!1;for(;s--;)if(!this.eq(t[s],\ne[s],n,i))return!1}else{var d=Object.keys(t),l=void 0;if(\ns=d.length,Object.keys(e).length!==s)return!1;for(;s--;)if(\nl=d[s],!e.hasOwnProperty(l)||!this.eq(t[l],e[l],n,i)\n)return!1}return n.pop(),i.pop(),!0},\nvueUtils.isFunction=function(t){\nreturn'function'==typeof t||!1},vueUtils}()\n;exports.vueUtils=vueUtils},function(t,e,n){'use strict'\n;var i,o=this&&this.__extends||(i=function(t,e){return(\ni=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var a=function(){function a(){\nthis.enabled=!0}return a.fmtLine=function(t,e,n,i){var o=''\n;return i&&(\no=i instanceof Error?'- '+i.name+': '+i.message+' - '+i.stack:'- '+i\n),'['+t+']['+a.fmtTime()+']['+e+']'+n+' '+o},\na.fmtTime=function(){var t=new Date;return t.getHours(\n)+':'+t.getMinutes()+':'+t.getSeconds(\n)+'.'+t.getMilliseconds()},a.prototype.enable=function(){\nthis.enabled=!0},a.prototype.disable=function(){\nthis.enabled=!1},a}();e.Logger=a,e.logger=new(function(t){\nfunction e(){return null!==t&&t.apply(this,arguments)||this}\nreturn o(e,t),e.prototype.e=function(t,e,n){},\ne.prototype.i=function(t,e,n){},e}(a))},function(t,e,n){\n'use strict';Object.defineProperty(e,'__esModule',{value:!0}\n);var i=n(0),o=function(){function t(t){var e=this\n;this.countDownStop=!1,this.countDown=5,\nthis.countdownHelpTip=function(){e.countDownStop||(\n0<e.countDown--?setTimeout(e.countdownHelpTip,1e3\n):e.questionnaireParams&&document.invoke('questionnaire',{\nspaceCode:e.questionnaireParams.spaceCode,\ntype:'AccurateOperate',bizParams:{\nbizIdentifier:e.questionnaireParams.bizIdentifier,\nsourceId:e.questionnaireParams.sourceId,\nbizIdentity:e.questionnaireParams.bizIdentity}},function(t){\ni.amc.fn.logCount('card-no','survey-'+(t?t.code:'null'),'')}\n))},this.start=function(){setTimeout(e.countdownHelpTip,1e3)\n},this.questionnaireParams=t}\nreturn t.prototype.setCountDownStop=function(t){\nthis.countDownStop=t},t}();e.Questionnaire=o},function(t,e,n\n){'use strict';var i,o=this&&this.__extends||(i=function(t,e\n){return(i=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var c,a,r=n(1),d=n(2),l=n(0),u=n(4)\n,s=n(9);(a=c=e.ItemType||(e.ItemType={})\n).userName='userName',a.instInfo='instInfo',\na.validity='validity',a.cvv='cvv',a.certType='certType',\na.certId='certId',a.phoneNum='phoneNum',\na.certIdCommon='certIdCommon'\n;var p=l.amc.path+'alipay_msp_cvv',\nm=l.amc.path+'alipay_msp_validate';function f(t){\nreturn t.split('').map(function(t){return t.toUpperCase(\n)===t?'-'+t.toLowerCase():t}).join('')}function _(t){\nreturn!!t&&'flex'===t.style.display}var g=function(e){\nfunction t(){var t,a=null!==e&&e.apply(this,arguments)||this\n;return a.inputAttrs=((t={})[c.userName]={lastValue:'',\ninitValue:'',spmId:'a283.b4032.c62098.d127799'},\nt[c.instInfo]={lastValue:'',initValue:''},t[c.certType]={\nlastValue:'',initValue:'',spmId:'a283.b4032.c62098.d127800'}\n,t[c.cvv]={regx:'^\\\\d{0,4}$',lastValue:'',initValue:''},\nt[c.certId]={regx:'(^\\\\d{0,18}$)|(^\\\\d{17}([X|x]{0,1}))$',\nlastValue:'',initValue:'',spmId:'a283.b4032.c62098.d127801'}\n,t[c.certIdCommon]={lastValue:'',initValue:'',\nspmId:'a283.b4032.c62098.d127801'},t[c.phoneNum]={\nregx:'(^\\\\+?\\\\d*$)|(^\\\\+?\\\\d+\\\\-?\\\\d*$)',lastValue:'',\ninitValue:'',spmId:'a283.b4032.c62098.d127797'},\nt[c.validity]={regx:'^\\\\d{2}/\\\\d{2}$',lastValue:'',\ninitValue:''},t),a.certTypeTextArray=[],\na.certTypeValueArray=[],a.currentCertTypeIndex=0,\na.onMounted=function(){a.container=a.getViewInComponentById(\n'cert-detail'),a.commonDialog=a.getViewInComponentById(\n'common-dlg'),a.phoneDialog=a.getViewInComponentById(\n'phone-dlg')},a.wantShowInputTip=function(t){\nd.visibleElement(a,'input-tip',!0),d.modifyElementAttribute(\na,'input-tip',{innerText:t||''})},a.vueModel={data:{\nINFO:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*h7rbTKnUcmQAAAAAAAAAAAAAARQnAQ',\nCVV_IMG:l.amc.path+'alipay_msp_cvv',showCvvDlg:function(){\na.showCvvDialog()},showValidityDlg:function(){\na.showValidityDialog()},showPhoneDlg:function(){\nl.amc.fn.spmClick('a283.b4032.c62098.d127798',\nu.spmServerParam),\nl.amc.isIOS&&l.amc.fn.sdkGreaterThanOrEqual('10.8.35'\n)?a.phoneDialog.show():a.phoneDialog.showModal()},\nonCommonDlgClose:function(){a.commonDialog.close()},\ndial:function(){var t='95188';0<=(l.amc.fn.i18nValueForKey(\n'{{phone_tip_4}}')||'').indexOf('+*************')&&(\nt='+*************');var e={action:{\nname:'loc:tel(\\''+t+'\\', \\'1\\')'}};document.submit(e),\na.phoneDialog.close()},onPhoneDlgClose:function(){\na.phoneDialog.close()},certTypeClick:function(){if(!(\na.certTypeTextArray.length<=1)){l.amc.fn.spmClick(\n'a283.b4032.c62098.d127800',u.spmServerParam);var t={\nbtns:a.certTypeTextArray,default:a.currentCertTypeIndex,\ntitle:'{{sel_id_no}}'};l.amc.isAndroid&&document.submit({\naction:{name:'loc:hideKeyboard'}}),document.picker(t,\nfunction(t){var e=t.index;a.currentCertTypeIndex=e\n;var n=a.certTypeTextArray[e],i=a.getViewInComponentById(\n'cert-type'),o=a.getViewInComponentById('cert-id')\n;n&&i&&n!==i.innerText&&(d.modifyElementAttribute(a,\n'cert-type',{innerText:n}),a.setCertId('')),\nl.amc.fn.spmClick('a283.b4032.c62098.d159440',s.mergeObject(\nu.spmServerParam,{certType:n||'none'})),_(\na.getViewInComponentById('cert-id-box'))&&o.focus()})}},\nshowPhoneInfo:function(){d.visibleElement(a,'phone-num-img',\n!0)},hidePhoneInfo:function(){d.visibleElement(a,\n'phone-num-img',!1)},showCvvInfo:function(){\nd.visibleElement(a,'cvv-img',!0)},hideCvvInfo:function(){\nd.visibleElement(a,'cvv-img',!1)}},compute:{}},a}return o(t,\ne),t.prototype.showCvvDialog=function(){\nd.modifyElementAttribute(this,'dlg-title',{\ninnerText:'{{cvv}}'}),d.modifyElementAttribute(this,\n'dlg-img',{src:p}),d.modifyElementAttribute(this,'dlg-text',\n{innerText:'{{cvv_info}}'}),this.showCommonDialog()},\nt.prototype.showValidityDialog=function(){\nd.modifyElementAttribute(this,'dlg-title',{\ninnerText:'{{validity}}'}),d.modifyElementAttribute(this,\n'dlg-img',{src:m}),d.modifyElementAttribute(this,'dlg-text',\n{innerText:'{{validity_info}}'}),this.showCommonDialog()},\nt.prototype.showCommonDialog=function(){\nl.amc.isIOS&&l.amc.fn.sdkGreaterThanOrEqual('10.8.35'\n)?this.commonDialog.show():this.commonDialog.showModal()},\nt.prototype.wantShowName=function(t,e,n){\nreturn this.wantShowInputItem(c.userName,t,void 0,e,n)},\nt.prototype.wantShowInstInfo=function(t,e,n){\nvar i=this.wantShowSelectItem(c.instInfo,t);return n&&(\nthis.getViewInComponentById(''+f(c.instInfo)).onclick=n,\nthis.getViewInComponentById(f(c.instInfo)+'-arrow'\n).onclick=n),i},t.prototype.wantShowInstInfoArrow=function(t\n){d.visibleElement(this,'inst-info-arrow',t)},\nt.prototype.wantShowValidity=function(t,e,n){\nreturn e?d.visibleElement(this,'validity-info',!1\n):d.visibleElement(this,'validity-info',!0),\nthis.wantShowInputItem(c.validity,t,void 0,e,n)},\nt.prototype.wantShowCVV=function(t,e,n){var i=this\n;return this.wantShowInputItem(c.cvv,t,function(t){\nt!==i.inputAttrs[c.cvv].lastValue&&document.toast({\ntext:'{{input_valid_cvv2}}',type:'fail'},function(){})},e,n)\n},t.prototype.wantShowCertTypeAndCertId=function(t,e){\nreturn t&&e&&(\nthis.currentCertTypeIndex<this.certTypeValueArray.length?this.certTypeValueArray[this.currentCertTypeIndex]===t.certType&&this.wantShowCertId(\ne.certId,e.disable,e.tip):(this.wantShowCertType(t.certType,\nt.certTypeData,t.disable),this.wantShowCertId(e.certId,\ne.disable,e.tip))),this},\nt.prototype.wantShowCertType=function(t,e,n){var i=this\n;return this.currentCertTypeIndex=this.initCertTypePicker(t,\ne),n?(d.modifyElementAttribute(this,'cert-type-box',{\nonclick:function(){}}),d.visibleElement(this,\n'cert-type-info',!1)):(d.modifyElementAttribute(this,\n'cert-type-box',{onclick:function(){\ni.vueModel.data.certTypeClick()}}),d.visibleElement(this,\n'cert-type-info',!0)),this.wantShowSelectItem(c.certType,\nthis.certTypeTextArray[this.currentCertTypeIndex])},\nt.prototype.initCertTypePicker=function(t,e){var n=0;for(\nvar i in this.certTypeTextArray=[],this.certTypeValueArray=[\n],e)if(e.hasOwnProperty(i)){var o=e[i]\n;this.certTypeTextArray.push(i),\nthis.certTypeValueArray.push(o),o===t&&(\nn=this.certTypeTextArray.length-1)}return d.visibleElement(\nthis,'cert-type-info',1!==this.certTypeTextArray.length),n},\nt.prototype.setCertId=function(t,e){this.isIdentity()?(\nd.visibleElement(this,'cert-id-common-box',!1),\nthis.wantShowInputItem(c.certId,t,void 0,e)):(\nd.visibleElement(this,'cert-id-box',!1),\nthis.wantShowInputItem(c.certIdCommon,t,void 0,e)),\nthis.checkFormComplete()},\nt.prototype.wantShowCertId=function(t,e,n){var i=this\n;return this.wantShowInputItem(c.certId,t,function(t){if(\nt!==i.inputAttrs[c.certId].lastValue){var e=t&&t[t.length-1]\n;'x'!=e&&'X'!=e||document.toast({text:'{{id_card_tip}}',\ntype:'fail'},function(){})}},e),void 0!==n&&(\nd.modifyElementAttribute(this,'cert-id',{placeholder:n}),\nd.modifyElementAttribute(this,'cert-id-common',{\nplaceholder:n})),this.setCertId(t,e),this},\nt.prototype.wantShowPhoneNum=function(t,e,n){var i=this\n;return this.wantShowInputItem(c.phoneNum,t,function(t){\nt!==i.inputAttrs[c.phoneNum].lastValue&&document.toast({\ntext:'{{input_right_phone}}',type:'fail'},function(){})},e,n\n)},t.prototype.checkInput=function(t,e,n){var i,o=e.value,\na=this.inputAttrs[t];if(a.regx){if(o.match(a.regx)\n)a.lastValue=o;else if(a.lastValue.match(a.regx))n&&n(o\n);else{var r=(i=function(t,e){t=t||'',e=e||'';for(\nvar n=0;n<e.length;){if(n>=t.length||t[n]!==e[n]\n)return e.substring(n);n++}return''}(a.lastValue,o),\nl.amc.fn.isString(i)?i.replace(/\\s/g,''):'');r.match(a.regx\n)?a.lastValue=r:n&&n(o)}e.value=a.lastValue}},\nt.prototype.wantShowInputItem=function(t,e,n,i,o){var a=this\n;void 0===e&&(e='');var r=f(t),\nc=this.getViewInComponentById(r),\ns=this.getViewInComponentById(r+'-box');return c&&s&&(\nvoid 0!==o&&(c.placeholder=o),_(s)?c.value||(\nthis.inputAttrs[t].lastValue=e,\nthis.inputAttrs[t].initValue=e,c.value=e,c.disabled=!!i):(\nthis.inputAttrs[t].lastValue=e,\nthis.inputAttrs[t].initValue=e,c.oninput=function(){\na.checkInput(t,c,n),a.checkFormComplete()},\nc.onfocus=function(){\na.inputAttrs[t].spmId&&l.amc.fn.spmClick(\na.inputAttrs[t].spmId,u.spmServerParam),d.visibleElement(a,\nr+'-img',!c.value),a.checkFormComplete()},\nl.amc.fn.spmExposure(this.inputAttrs[t].spmId,\nu.spmServerParam),this.wantShowItem(t,void 0!==e?{value:e,\ndisabled:!!i}:void 0))),this},\nt.prototype.setFormCompletedListener=function(t){\nthis.formCompletedListener=t,this.checkFormComplete()},\nt.prototype.checkFormComplete=function(){var t=!1;for(\nvar e in c){var n=f(e);if(_(this.getViewInComponentById(\nn+'-box'))){var i=this.getViewInComponentById(n);if(\ni&&!i.disabled&&e!==c.certType&&e!==c.instInfo&&(i.value||(\nt=!0),e!==c.cvv||i.value.match('^\\\\d{3,4}$')||(t=!0),\ne!==c.validity||i.value.match('^\\\\d{2}/\\\\d{2}$')||(t=!0),t)\n){l.amc.fn.logAction(e+'|'+i.value+'|'+i.disabled,\n'btnDisabled');break}}}\nreturn this.formCompletedListener&&this.formCompletedListener(\n!t),!t},t.prototype.getFormResult=function(){var n=this,t={}\n;for(var e in c){var i=c[e];if(i===c.phoneNum){if(\n!this.setResultItem(t,c.phoneNum,function(t,e){\nreturn!t.match(\n'(^([+]?[0]{0,2}86|([+]?0{0,2}86-))?1\\\\d{10}$)|(^[+]?[0]{0,2}[1-9]{1}\\\\d{0,7}[-]{0,1}\\\\d{7,11}$)'\n)&&(n.toast('{{input_right_phone}}',e),l.amc.fn.logError(\n'bindCardPhone',t),!0)}))return}else if(i===c.cvv){if(\n!this.setResultItem(t,c.cvv,function(t,e){return!t.match(\n'^\\\\d{3,4}$')&&(n.toast('{{input_valid_cvv2}}',e),\nl.amc.fn.logError('bindCardCvv',t),!0)}))return}else if(\ni===c.certId||i===c.certIdCommon);else if(i===c.certType){\nvar o=this.isIdentity(\n)?this.inputAttrs[c.certId]:this.inputAttrs[c.certIdCommon],\na=this.isIdentity()?this.getItemValue(c.certId\n):this.getItemValue(c.certIdCommon);if(\n0<this.certTypeValueArray.length&&(!o||a&&o.initValue!==a\n)&&(\nt[c.certType]=this.certTypeValueArray[this.currentCertTypeIndex]\n),this.isIdentity()){if(!this.setResultItem(t,c.certId,\nfunction(t,e){return!(!n.isIdentity()||t.match(\n'(^\\\\d{15}$)|(^\\\\d{17}([0-9]|X|x)$)')||(n.toast(\n'{{input_right_id_card}}',e),l.amc.fn.logError(\n'bindCardIdentity',t),0))}))return}else this.setResultItem(t\n,c.certIdCommon),t[c.certId]=t[c.certIdCommon]\n;t[c.certIdCommon]=''}else if(i===c.validity){\nvar r=this.getItemValue(c.validity);r&&(\nt[c.validity]=r.replace('/',''))}else this.setResultItem(t,i\n)}return t},t.prototype.setResultItem=function(t,e,n){\nvar i=this.getItemValue(e),o=this.inputAttrs[e],a=f(e),\nr=this.getViewInComponentById(a);return!(\nvoid 0!==i&&n&&o.initValue!==i&&n(i,r)||(\ni&&o.initValue!==i&&(t[e]=i),0))},\nt.prototype.getItemValue=function(t){var e=f(t),\nn=this.getViewInComponentById(e);if(_(\nthis.getViewInComponentById(e+'-box')))return n.value},\nt.prototype.isIdentity=function(){\nreturn'A'===this.certTypeValueArray[this.currentCertTypeIndex]\n},t.prototype.toast=function(t,e){e&&t&&document.toast({\ntext:t,type:'fail'},function(){e.focus()})},\nt.prototype.wantShowSelectItem=function(t,e){\nreturn this.wantShowItem(t,e?{innerText:e}:void 0)},\nt.prototype.wantShowItem=function(t,e){var n=f(t)\n;return d.visibleElement(this,n+'-box',!0),d.visibleElement(\nthis,n+'-line',!0),this.modifyEndLineId(c[t],n+'-line'),\nd.modifyElementAttribute(this,n,e),u.isOlder(\n)&&d.modifyElementStyle(this,n+'-box',{\npadding:'0 '+16/u.OLD_SCALE+'px'}),this},\nt.prototype.wantShowProtocols=function(t){\nreturn t&&0!==t.length&&(d.modifyElementAttribute(this,\n'protocol',{innerText:'{{quickpay_protocol}}'}),\nd.modifyElementAttribute(this,'protocol',{onclick:function(\n){l.amc.fn.showProtocolList(t),l.amc.fn.spmClick(\n'a283.b4032.c62098.d127965',u.spmServerParam)}}),\nd.visibleElement(this,'protocol-box',!0),\nl.amc.fn.spmExposure('a283.b4032.c62098.d127965',\nu.spmServerParam)),this},\nt.prototype.modifyEndLineId=function(t,e){var n=-1;switch(t\n){case c.instInfo:n=0;break;case c.userName:n=1;break\n;case c.validity:n=2;break;case c.cvv:n=3;break\n;case c.certType:n=4;break;case c.certId:\ncase c.certIdCommon:n=5;break;case c.phoneNum:n=6}\nthis.endLineId?this.endLineId.weight<n&&(this.endLineId={\nweight:n,id:e}):this.endLineId={weight:n,id:e}},\nt.prototype.show=function(){l.amc.fn.show(this.container),\nthis.endLineId&&d.visibleElement(this,this.endLineId.id,!1)}\n,t.prototype.hide=function(){for(var t in l.amc.fn.hide(\nthis.container),d.visibleElement(this,'input-tip',!1),\nd.visibleElement(this,'protocol-box',!1),this.inputAttrs\n)this.inputAttrs[t].initValue='',\nthis.inputAttrs[t].lastValue='';for(var t in c){var e=f(c[t]\n),n=this.getViewInComponentById(e)\n;t!==c.certType?n.value='':n.innerText='{{papers_type}}',\nd.visibleElement(this,e+'-box',!1)}\nthis.endLineId&&d.visibleElement(this,this.endLineId.id,!0),\nthis.endLineId=void 0,this.certTypeTextArray=[],\nthis.certTypeValueArray=[],this.currentCertTypeIndex=0,\nthis.formCompletedListener=function(){}},\nt.getComponentCSSRules=function(){return{\n'.main-body':'_CardInfo_35iu-c-main-body',\n'.cert-detail-box':'_CardInfo_35iu-c-cert-detail-box',\n'.cert-input-box':'_CardInfo_35iu-c-cert-input-box',\n'.cert-item-title':'_CardInfo_35iu-c-cert-item-title',\n'.cert-item-input':'_CardInfo_35iu-c-cert-item-input',\n'.protocol-box':'_CardInfo_35iu-c-protocol-box',\n'.protocol-title':'_CardInfo_35iu-c-protocol-title',\n'.protocol':'_CardInfo_35iu-c-protocol',\n'.arrow-right':'_CardInfo_35iu-c-arrow-right',\n'.info-img':'_CardInfo_35iu-c-info-img',\n'.line':'_CardInfo_35iu-c-line',\n'.dlg-txt':'_CardInfo_35iu-c-dlg-txt',\n'.dlg-title-box':'_CardInfo_35iu-c-dlg-title-box',\n'.dlg-title':'_CardInfo_35iu-c-dlg-title',\n'.dlg-dot':'_CardInfo_35iu-c-dlg-dot',\n'.dlg-list':'_CardInfo_35iu-c-dlg-list',\n'.tip-box':'_CardInfo_35iu-c-tip-box',\n'.input-tip':'_CardInfo_35iu-c-input-tip',\n'.dlg-btn-text':'_CardInfo_35iu-c-dlg-btn-text',\n'.dlg-line':'_CardInfo_35iu-c-dlg-line',\ninput:'_CardInfo_35iu-t-input',\n'.amc-dlg-box':'_CardInfo_35iu-c-amc-dlg-box',\n'.amc-dlg-width':'_CardInfo_35iu-c-amc-dlg-width'}},\nt.getComponentJson=function(){return{\n_c:'amc-v-box _CardInfo_35iu-c-main-body',_t:'div',_cd:[{\n'sp-view-id':'cert-detail',\n_c:'amc-v-box _CardInfo_35iu-c-cert-detail-box amc-hidden',\n_t:'div',_cd:[{'sp-view-id':'inst-info-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{bank_card_type}}'},{'sp-view-id':'inst-info',\n_c:'_CardInfo_35iu-c-cert-item-input',_t:'label'},{\n'sp-view-id':'inst-info-arrow',\n_c:'_CardInfo_35iu-c-info-img amc-justify-center amc-align-center amc-hidden',\n_t:'div',_cd:[{_c:'_CardInfo_35iu-c-arrow-right',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*ZG6bSoBQYpAAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]}]},{'sp-view-id':'inst-info-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'user-name-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{name}}'},{'sp-view-id':'user-name',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input',\nplaceholder:'{{enter_real_name}}',_t:'input'}]},{\n'sp-view-id':'user-name-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'validity-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{validity_text}}'},{'sp-view-id':'validity',_y:'month',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input amc-flex-1 amc-ellipsis',\nplaceholder:'{{month_year}}',_t:'input'},{\n'sp-view-id':'validity-img',_c:'_CardInfo_35iu-c-info-img',\nalt:'{{info}}','v-src':'@{INFO}',\n'v-click':'@{showValidityDlg}',_t:'img'}]},{\n'sp-view-id':'validity-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'cvv-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{cvv_text}}'},{'sp-view-id':'cvv',_y:'number',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input',\nplaceholder:'{{card_three_num}}','v-focus':'@{hideCvvInfo}',\n'v-blur':'@{showCvvInfo}',_t:'input'},{\n'sp-view-id':'cvv-img',_c:'_CardInfo_35iu-c-info-img',\nalt:'{{info}}','v-src':'@{INFO}','v-click':'@{showCvvDlg}',\n_t:'img'}]},{'sp-view-id':'cvv-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'cert-type-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',\n'v-click':'@{certTypeClick}',_t:'div',_cd:[{\n_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{papers_type}}'},{'sp-view-id':'cert-type',\n_c:'_CardInfo_35iu-c-cert-item-input amc-flex-1 amc-ellipsis',\n_t:'label',_x:'{{papers_type}}'},{\n'sp-view-id':'cert-type-info',\n_c:'_CardInfo_35iu-c-info-img amc-justify-center amc-align-center',\n_t:'div',_cd:[{_c:'_CardInfo_35iu-c-arrow-right',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*ZG6bSoBQYpAAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]}]},{'sp-view-id':'cert-type-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'cert-id-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{'sp-view-id':'cert-id-title',\n_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{id_no}}'},{'sp-view-id':'cert-id',_y:'idcard',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input',\nplaceholder:'{{enter_cert_id}}',_t:'input'}]},{\n'sp-view-id':'cert-id-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'cert-id-common-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{'sp-view-id':'cert-id-common-title',\n_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{id_no}}'},{'sp-view-id':'cert-id-common',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input',\nplaceholder:'{{enter_cert_id}}',_t:'input'}]},{\n'sp-view-id':'cert-id-common-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'},{\n'sp-view-id':'phone-num-box',\n_c:'_CardInfo_35iu-c-cert-input-box amc-hidden',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-cert-item-title',_t:'label',\n_x:'{{phone_no}}'},{'sp-view-id':'phone-num',_y:'phone',\n_c:'_CardInfo_35iu-t-input _CardInfo_35iu-t-input _CardInfo_35iu-c-cert-item-input',\nplaceholder:'{{input_bank_phone}}',\n'v-focus':'@{hidePhoneInfo}','v-blur':'@{showPhoneInfo}',\n_t:'input'},{'sp-view-id':'phone-num-img',\n_c:'_CardInfo_35iu-c-info-img',alt:'{{info}}',\n'v-src':'@{INFO}','v-click':'@{showPhoneDlg}',_t:'img'}]},{\n'sp-view-id':'phone-num-line',\n_c:'_CardInfo_35iu-c-line amc-hidden',_t:'div'}]},{\n'sp-view-id':'input-tip',\n_c:'_CardInfo_35iu-c-input-tip amc-hidden',_t:'label'},{\n'sp-view-id':'protocol-box',\n_c:'_CardInfo_35iu-c-protocol-box amc-hidden',_t:'div',_cd:[\n{_c:'_CardInfo_35iu-c-protocol-title',_t:'label',\n_x:'{{look}}'},{'sp-view-id':'protocol',\n_c:'_CardInfo_35iu-c-protocol',_t:'label',\n_x:'{{quickpay_protocol}}'}]},{'sp-view-id':'common-dlg',\n_c:'_CardInfo_35iu-c-amc-dlg-box amc-v-box',_t:'dialog',\n_cd:[{_c:'amc-self-stretch',_t:'div',_cd:[{\n'sp-view-id':'dlg-title',\n_c:'amc-dlg-title amc-ellipsis-3-line amc-flex-1 amc-text-center',\n_t:'label'}]},{_c:'amc-justify-center',_t:'div',_cd:[{\n'sp-view-id':'dlg-img',_t:'img'}]},{_c:'amc-self-stretch',\n_t:'div',_cd:[{'sp-view-id':'dlg-text',\n_c:'amc-dlg-txt _CardInfo_35iu-c-dlg-txt amc-flex-1 amc-text-center',\n'<':'',label:'',_t:'label'}]},{\n_c:'amc-adapt-line _CardInfo_35iu-c-amc-dlg-width',_t:'div'}\n,{'v-click':'@{onCommonDlgClose}',\n_c:'amc-theme-color amc-dlg-btn',_t:'button',\n_x:'{{i_know_it}}'}]},{'sp-view-id':'phone-dlg',\n_c:'_CardInfo_35iu-c-amc-dlg-box amc-v-box',_t:'dialog',\n_cd:[{_c:'_CardInfo_35iu-c-dlg-title-box',_t:'div',_cd:[{\n_c:'_CardInfo_35iu-c-dlg-title',_t:'label',_x:'{{phone}}'}]}\n,{_c:'amc-v-box _CardInfo_35iu-c-amc-dlg-width',_t:'div',\n_cd:[{_c:'_CardInfo_35iu-c-tip-box',_t:'div',_cd:[{\n_c:'_CardInfo_35iu-c-dlg-dot',_t:'div'},{\n'sp-view-id':'phoneTip1',\n_c:'_CardInfo_35iu-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_1}}'}]},{_c:'_CardInfo_35iu-c-tip-box',\n_t:'div',_cd:[{_c:'_CardInfo_35iu-c-dlg-dot',_t:'div'},{\n_c:'_CardInfo_35iu-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_2}}'}]},{_c:'_CardInfo_35iu-c-tip-box',\n_t:'div',_cd:[{_c:'_CardInfo_35iu-c-dlg-dot',_t:'div'},{\n_c:'_CardInfo_35iu-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_3}}'}]},{_c:'_CardInfo_35iu-c-tip-box',\n_t:'div',_cd:[{_c:'_CardInfo_35iu-c-dlg-dot',_t:'div'},{\n'sp-view-id':'phoneTip4Label',\n_c:'_CardInfo_35iu-c-dlg-list amc-flex-1',\n'v-click':'@{dial}',_t:'label',_x:'{{phone_tip_4}}'}]},{\n_c:'_CardInfo_35iu-c-tip-box',_t:'div',_cd:[{\n_c:'_CardInfo_35iu-c-dlg-dot',_t:'div'},{\n_c:'_CardInfo_35iu-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_5}}'}]},{\n_c:'amc-adapt-line _CardInfo_35iu-c-dlg-line',_t:'div'},{\n_c:'amc-align-center amc-justify-center',\n'v-click':'@{onPhoneDlgClose}',_t:'div',_cd:[{\n_c:'_CardInfo_35iu-c-dlg-btn-text amc-flex-1 amc-ellipsis amc-theme-color',\n_t:'label',_x:'{{cashier_no_dlg_btn}}'}]}]}]}]}},\nt.componentName='CardInfo',\nt.componentHashName='CardInfo_35iu',t}(r.BNComponent)\n;e.CardInfo=g},function(t,e,n){'use strict';var i\n;Object.defineProperty(e,'__esModule',{value:!0}),\ne.VI_CHANNEL_MODE_FROM_TEMPLATE='1',(i=e.SCALE_FACTOR||(\ne.SCALE_FACTOR={})).LEVEL_0='0',i.LEVEL_1='1',i.LEVEL_2='2',\ni.LEVEL_3='3',i.LEVEL_4='4'},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var i=n(0)\n;e.logAction=function(t,e){window.pageId||(\nwindow.pageId='|'+Math.random().toString(36).substr(2,3)),\ne=e?e+window.pageId:window.pageId,i.amc.fn.logAction(t,e)}},\nfunction(t,e,n){'use strict';function i(t){for(var e=[],\nn=0;n<t.length;n+=1)e.push(t[n]||'-');return e.join('\\'')}\nObject.defineProperty(e,'__esModule',{value:!0}),\ne.stEscape=function(t){return t&&t.replace('\\'','%27'\n).replace('`','%60').replace('#','%23')},e.toString=i\n;var o=function(){function t(t,e,n){this.prefs=[],\nthis.initTime=Date.now(),this.cache={},this.submited=!1,\nthis.record=t,this.prefs[0]=e,this.prefs[1]=n,\nthis.prefs[2]=String(this.initTime),this.prefs[7]='',\nthis.record.addSTPref(this)}\nreturn t.prototype.toString=function(){return i(this.prefs)}\n,t.prototype.putCache=function(t,e){this.cache[t]=e},\nt.prototype.getCache=function(t,e){return this.cache[t]||e},\nt.prototype.isSubmited=function(){return this.submited},\nt.prototype.submit=function(){this.isSubmited()||(\nthis.submited=!0,this.submitInner())},\nt.prototype.submitInner=function(){},t}();e.STPerf=o\n;var a=function(){function t(t,e,n){this.acts=[],\nthis.record=t,this.acts[0]=e,this.acts[1]=n,\nthis.acts[6]=String(Date.now()),this.record.addSTAct(this)}\nreturn t.prototype.setActName=function(t){this.acts[2]=t},\nt.prototype.toString=function(){return i(this.acts)},t}()\n;e.STAct=a;var r=function(){function t(t,e,n,i,o,a){\nthis.ids=[],this.prefs=[],this.acts=[],\nthis.initTime=Date.now(),this.ids[0]=String(this.initTime),\nthis.ids[1]=t,this.ids[2]=e,this.ids[3]=n,this.ids[4]=i,\nthis.ids[5]=o,this.ids[7]='',this.logHandle=a}\nreturn t.prototype.addSTAct=function(t){this.acts.push(t)},\nt.prototype.addSTPref=function(t){this.prefs.push(t)},\nt.prototype.submit=function(t){var e=this.toString(t)\n;this.logHandle(e)},t.prototype.toString=function(e){for(\nvar t=[],n=0;n<this.ids.length;n+=1)t.push(this.ids[n]||'-')\n;var i=[];for(n=0;n<this.acts.length;n+=1)i.push(\nthis.acts[n].toString());this.acts=[];var o=[]\n;return this.prefs=this.prefs.filter(function(t){\nreturn e&&t.submit(),!t.isSubmited()||(o.push(t.toString()),\n!1)}),t.join('\\'')+'#'+i.join('`')+'#'+o.join('`')},\nt.prototype.getInitTime=function(){return this.initTime},t}(\n);e.STRecord=r},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var c=n(0)\n,i=function(){function t(){this.mUrlCache=null,\nthis.mPixelWidthCache={}}return t.getInstance=function(){\nreturn t.sImageLoader||(t.sImageLoader=new t),t.sImageLoader\n},t.prototype.loadImageHelper=function(t,e,n){var a=this;if(\n!this.mUrlCache){this.mUrlCache={}\n;var r=document.onImgLoaded;document.onImgLoaded=function(t,\ne){var n=a.mUrlCache[e];if(!n){var i='';for(\nvar o in a.mPixelWidthCache)if(\na.mPixelWidthCache.hasOwnProperty(o)&&0===e.indexOf(o)){i=o,\nn=a.mPixelWidthCache[o];break}n&&!n.validate&&(\ndelete a.mPixelWidthCache[i],n=null)}n&&n.callback?(\nn.validate=!1,n.callback(t,e,n.img)):r&&r(t,e),\ndelete a.mUrlCache[e]}}var i={callback:n,img:t,validate:!0}\n;if(this.mUrlCache[e]=i,0<e.indexOf('[pixelWidth]')){\nvar o=e.substr(0,e.indexOf('[pixelWidth]'))\n;this.mPixelWidthCache[o]=i}t.src=e},\nt.prototype.loadImage=function(i,t,o,a){void 0===a&&(a=!0)\n;var r=!1,e=c.amc.isSDK&&c.amc.isIOS,n=function(t,e,n){\nt&&i&&a&&c.amc.fn.show(i),r=t,o&&o(t,e,i)}\n;this.loadImageHelper(i,t,e?void 0:n),e&&n(!0,t),\na&&setTimeout(function(){r||c.amc.fn.hide(i)},10)},t}()\n;e.ImageLoader=i},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var r=n(0)\n;e.ocr=function(i,o,a,t){if(void 0===a&&(a=!0),void 0===t&&(\nt=500),\ni&&r.amc.isAndroid&&!r.amc.isSDK&&r.amc.fn.sdkGreaterThanOrEqual(\n'10.8.53')){var e=(new Date).getUTCMilliseconds()\n;i.className+=' '+e,setTimeout(function(){document.invoke(\n'ocr',{selector:'.'+e},function(t){if(\nt&&t.data&&t.data[0]&&t.data[0].body){var e=''\n;t.data[0].body.forEach(function(t){t.label&&(e+=t.label)})\n;var n=i.innerText.replace(/<[^>]+>([^<]+)<\\/\\w+>/g,'$1'\n).split('，').join(',').split('？').join('?').split('！').join(\n'!').split('：').join(':').split('“').join('\"').split('”'\n).join('\"');e!==n&&a&&r.amc.fn.logError(\n'render_label_exception',e),o&&o({result:e,pureInnerText:n})\n}})},t)}}},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var y=n(0)\n,C=n(4),a=n(10),I=n(9),x=n(11);e.CardInfoHelper=function(t,e\n,n,i,o){var v=this;this.queryCardNo='',this.submitParams={},\nthis.realCardNoLength=-1,this.newVersion=!1,\nthis.prefixCardNo={8:'',12:''},this.realBankMobile='',\nthis.userSpecifiedCard=!1,this.bindCardHttp=!1,\nthis.resetFlag=function(){v.submitParams={},\nv.realCardNoLength=-1,v.newVersion=!1,v.userSpecifiedCard=!1\n,v.bindCardHttp=!1,v.prefixCardNo={8:'',12:''},\nx.resetCardQueryFlag()},this.dropQueryInfo=function(t){\nt.length<8?v.queryCardNo='':t.length<12?(\n12<=v.queryCardNo.length||v.queryCardNo!==t.substr(0,8))&&(\nv.queryCardNo=''):v.queryCardNo!==t.substr(0,12)&&(\nv.queryCardNo='')},this.updatePrefixCardNo=function(t,e){\n12===t.length?v.prefixCardNo=e?{8:'',12:t}:{8:t.substr(0,8),\n12:t}:8===t.length&&(v.prefixCardNo={8:t,12:''})},\nthis.showInstName=function(t,e){\nvar n=t||y.amc.fn.i18nValueForKey('{{select_bank}}')\n;'CREDIT'===v.submitParams.cardType?n+=' '+y.amc.fn.i18nValueForKey(\n'{{credit_card}}'\n):'WAI_CARD'===v.submitParams.cardType?n+='':'DEBIT'===v.submitParams.cardType&&(\nn+=' '+y.amc.fn.i18nValueForKey('{{store_card}}')),\ne&&y.amc.fn.spmExposure('a283.b4032.c62098.d218935',\nI.mergeObject(C.spmServerParam,{instName:n}),!1),\nv.cardInfo.wantShowInstInfoArrow(e),\nv.cardInfo.wantShowInstInfo(n,!0,function(){e&&(\ny.amc.fn.spmClick('a283.b4032.c62098.d218935',I.mergeObject(\nC.spmServerParam,{instName:n})),v.bindCardHttp=!0,\nv.userSpecifiedCard=!0,document.submit({action:{\nname:'/card/instList',params:{\nchannelContextId:C.rpcData.channelContextId}}}))})},\nthis.parseCardInfo=function(t,e,n){void 0===n&&(n=!1)\n;var i=t.data;if(!(i&&t.success||n)\n)return v.submitParams.cardType=void 0,\nv.submitParams.instId=void 0,v.submitParams.cardBrand=void 0\n,v.updatePrefixCardNo(e,!0),v.submitParams.cardType=void 0,\nv.submitParams.instId=void 0,void x.resetCardQueryFlag()\n;var o=i.cardType||i.cardtype,a=i.instId||i.inst_id,\nr=v.submitParams.cardType!==o||v.submitParams.instId!==a||n\n;if(!n||i&&t.success&&a&&o){n&&(v.bindCardHttp=!1),\nn||v.updatePrefixCardNo(e,r)\n;var c=i.bankName||i.bankname||i.wkBankName;if(e.length<12){\nif(!c)return}else if(!c){if(!i.allowCardTypeSwitch)return\n;if(!n)return v.cardInfo.hide(),v.showInstName(c,!0),\nv.cardInfo.show(),v.changeButtonStatus({text:'{{next_step}}'\n}),void y.amc.fn.spmExposure('a283.b4032.c62098',\nI.mergeObject(C.spmServerParam,{bankName:c,cardType:'null',\ninstId:'null',allowCardTypeSwitch:!0}),!1)}\nv.submitParams.cardType=o,v.submitParams.instId=a,\nC.rpcData.exitConfirm&&x.getServerFeature(),v.newVersion=!0,\ni.cardLen&&i.cardLen>=C.MIN_CARD_NO_LENGTH&&(\nv.realCardNoLength=i.cardLen),r&&(v.cardInfo.hide(),\ny.amc.fn.spmExposure('a283.b4032.c62098',I.mergeObject(\nC.spmServerParam,{bankName:c,\ncardType:v.submitParams.cardType,\ninstId:v.submitParams.instId,\ncardBrand:v.submitParams.cardBrand,\nprefixCard:v.prefixCardNo,cardLen:v.realCardNoLength}),!1)),\n'WAI_CARD'===v.submitParams.cardType&&v.changeButtonStatus({\ntext:'{{next_step}}'}),v.showInstName(c,\ni.allowCardTypeSwitch||n),i.realBankMobile&&(\nv.realBankMobile=i.realBankMobile)\n;var s=i.needInput||i.needinput;if(s){var d=s.certtype;if(\nd&&d.display){var l=s.certno,u=l&&l.display?{certId:l.value,\ndisable:l.disable,tip:l.tip}:{}\n;v.cardInfo.wantShowCertTypeAndCertId({\ncertType:d.default||'A',certTypeData:d.value,\ndisable:d.disable},u)}var p=s.username\n;p&&p.display&&v.cardInfo.wantShowName(p.value,p.disable,\np.tip);var m=s.bankmobile;m&&m.display&&(\nv.cardInfo.wantShowPhoneNum(m.value,m.disable,m.tip),\nm.explainText&&v.cardInfo.wantShowInputTip(m.explainText))\n;var f=s.cvv2;f&&f.display&&v.cardInfo.wantShowCVV(f.value,\nf.disable,f.tip);var _=s.validate\n;_&&_.display&&v.cardInfo.wantShowValidity(_.value,_.disable\n,_.tip)}var g=i.cvv2;g&&g.display&&v.cardInfo.wantShowCVV(\ng.value,g.disable,g.tip);var h=i.validate\n;h&&h.display&&v.cardInfo.wantShowValidity(h.value,h.disable\n,h.tip);var b=i.protocols;b&&v.cardInfo.wantShowProtocols(b)\n,v.cardInfo.setFormCompletedListener(function(t){\nv.getCardNo(\n).length>=C.MIN_CARD_NO_LENGTH&&v.changeButtonStatus({\nenable:t}),v.hideCardList()}),v.cardInfo.show(),\ni.btnName&&v.changeButtonStatus({text:i.btnName})}},\nthis.tryQueryInfoWithInst=function(e,t,n,i,o){\nv.submitParams.cardType=i,v.submitParams.instId=n,\nv.submitParams.cardBrand=o,v.showInstName(t,!0),a.invoke(\n'rpc',{type:'json',\noperationType:'alipay.mobilechannel.cardbindingprod.preConsult',\nrequestData:{instId:n,cardType:i,cardBrand:o,cardNoPrefix:e,\nuserSpecifiedCard:'true',\nchannelContextId:C.rpcData.channelContextId,extParams:{\nhighPerformance:C.instTplSaved+''}}},function(t){\nt&&t.success&&t.data&&t.data.success&&t.data.data.instId===n&&v.parseCardInfo(\nt.data,e,!0)})},this.tryQueryInfo=function(e,n,t){\nvoid 0===n&&(n=!1),void 0===t&&(t=!1),\ne&&!v.userSpecifiedCard&&v.queryCardNo!==e&&(t&&v.resetFlag(\n),v.queryCardNo=e,a.invoke('rpc',{type:'json',\noperationType:'alipay.mobilechannel.cardbindingprod.preConsult',\nrequestData:{cardNoPrefix:e,\nchannelContextId:C.rpcData.channelContextId,extParams:{\nhighPerformance:C.instTplSaved+''}}},function(t){\nn||v.changeCardInputStatus(C.INPUT_STATUS.focus),\ne!==v.queryCardNo||v.userSpecifiedCard||(v.queryCardNo='',\nt&&t.success&&t.data&&(v.parseCardInfo(t.data,e),\nn||v.changeCardInputStatus(C.INPUT_STATUS.focus)))}),\nn||v.changeCardInputStatus(C.INPUT_STATUS.focus))},\nthis.checkFormComplete=function(){\nreturn v.cardInfo.checkFormComplete()},this.cardInfo=t,\nthis.getCardNo=e,this.changeButtonStatus=n,\nthis.changeCardInputStatus=i,this.hideCardList=o}},function(\nt,e,n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0});var i=n(0),o=n(3),a=n(23),r=void 0\n;e.getDialog=function(){return r||(r=new c),r}\n;var c=function(){function t(){this.birdnestDialog=void 0,\nthis.antUISwitch=!1,this.getBirdnestDialog(),\nthis.getAntUIGray()}\nreturn t.prototype.getBirdnestDialog=function(){\nreturn this.birdnestDialog||(\nthis.birdnestDialog=new a.CommonDialog,\nthis.birdnestDialog.mountTo(document.body)),\nthis.birdnestDialog},t.prototype.show=function(n,t){if(n)if(\nvoid 0!==t&&(this.antUISwitch=t),this.getAntuiEnable(n)){\nvar e={title:n.title,message:n.message,antui:!0},i=n.btns,\no=void 0,a=1;i&&0<i.length&&(a=i.length,o=i[i.length-1],\ni=1<i.length?i.slice(0,i.length-1):void 0),e.btnsText=i,\ne.cancelButton=o,'column'===n.buttonDirection&&(\ne.buttonDirection='column'),document.confirm(e,function(t){\nvar e=0;try{e=parseInt(t.index||'0')}catch(t){}\n0===e?n.clickCallBack&&n.clickCallBack(a-1\n):n.clickCallBack&&n.clickCallBack(e-1)},!0)\n}else this.getBirdnestDialog().show(n)},\nt.prototype.getAntuiEnable=function(t){if(\nt.labels&&0<t.labels.length)return!1;if(t.strongBtn)return!1\n;if(t.contentImg)return!1;if(t.msgExtraCss)return!1;if(\ni.amc.isSDK)return!1;if(!i.amc.fn.sdkGreaterThanOrEqual(\n'10.8.41'))return!1;var e=String.fromCharCode(60),\nn=String.fromCharCode(62);return!(\n!t.message||t.message&&-1!==t.message.indexOf(e+'/a'+n))&&!(\nt.protocols&&1<=t.protocols.length)&&!t.addition&&(\ni.amc.isAndroid?21<=window.flybird.local.osVersion&&this.antUISwitch:this.antUISwitch\n)},t.prototype.getAntUIGray=function(){var e=this\n;o.getConfig('msp_antui_dialog_gray',function(t){\ne.antUISwitch=t})},t}();e.Dialog=c},function(t,e,n){\n'use strict';var i,o=this&&this.__extends||(i=function(t,e){\nreturn(i=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var a=n(7),r=n(0),c=n(6),s=n(8),\nd=n(2);a.ComponentRegistry.registerComponent(\ns.Button.componentName,s.Button);var l=function(t){\nfunction e(){var i=null!==t&&t.apply(this,arguments)||this\n;return i.onCreated=function(){},i.onMounted=function(){},\ni.onDialogKeyDown=function(){},i.buttonClick=function(t){\ni.getViewInComponentById('dlg').close(),\ni.buttonClickListener&&i.buttonClickListener(t)},\ni.vueModel={data:{btns:[],msg:'',title:'',addition:'',\nstrongBtn:'',labels:[],btnBoxStyle:{flexDirection:'column'},\nonBtnClick:function(t){var e=t.getAttribute('index'),n=0\n;try{n=parseInt(e||'0')}catch(t){}\n2===i.vueModel.data.btns.length&&i.isHorizontal&&(\nn=Math.abs(n-1)),i.buttonClick(n)},showCloseBtn:!1},\ncompute:{msgBoxStyle:function(){\nreturn i.vueModel.data.labels&&0<i.vueModel.data.labels.length||i.vueModel.data.addition?{\nmarginBottom:'0px'}:{marginBottom:'28px'}}}},\ni.isHorizontal=!1,i.onCloseDlg=function(){\ni.getViewInComponentById('dlg').close(),\ni.onCloseListener&&i.onCloseListener()},\ni.showContentImg=function(t){var e=i.getViewInComponentById(\n'dlg');if(i.imgBox&&e.removeChild(i.imgBox),t.contentImg){\ni.imgBox=i.createStyledElement('div','contentImgBox',\n'content-img-container amc-align-center amc-justify-center')\n,t.contentImgBoxStyle&&d.modifyElementStyle(i,i.imgBox,\nt.contentImgBoxStyle);var n=i.createStyledElement('img',\n'contentImg');switch(i.imgBox.appendChild(n),\nt.contentImgAnchor){case'title':e.insertBefore(i.imgBox,\ni.getViewInComponentById('titleBox'));break;case'message':\ne.insertBefore(i.imgBox,i.getViewInComponentById('msgBox'))\n;break;case'labels':e.insertBefore(i.imgBox,\ni.getViewInComponentById('labelsContainer'));break;default:\ne.insertBefore(i.imgBox,i.getViewInComponentById('mainBody')\n)}t.contentImgStyle?d.modifyElementStyle(i,n,\nt.contentImgStyle):d.modifyElementStyle(i,n,{width:'220px'})\n,n.src=t.contentImg}},i}return o(e,t),\ne.prototype.setData=function(t){var e,n=this\n;this.vueModel.data.title=t.title||'',\nthis.vueModel.data.msg=t.message||'',\nthis.vueModel.data.addition=t.addition||'',\nthis.buttonClickListener=t.clickCallBack,\nthis.onCloseListener=t.onCloseCallBack,\nt.labels&&0<t.labels.length&&(\nthis.vueModel.data.labels=t.labels)\n;var i=t.btns&&0<t.btns.length?t.btns:t.strongBtn?[]:[\n'{{confirm_btn}}'];if(\nthis.isHorizontal='column'!==t.buttonDirection&&2===i.length&&!t.strongBtn\n,this.isHorizontal&&(i=i.reverse()),\nthis.vueModel.data.btnBoxStyle=t.btnBoxStyle||{\nflexDirection:this.isHorizontal?'row':'column',\nmargin:t.strongBtn&&i.length<=1?'12px 0':''},\nthis.vueModel.data.strongBtn=t.strongBtn||'',\nthis.vueModel.data.strongBtn){\nvar o=this.getSubComponentById('strongButton',s.Button);o&&(\no.setText(((e={}\n)[s.BUTTON_STATUS.NORMAL]=this.vueModel.data.strongBtn,\ne[s.BUTTON_STATUS.SUCCESS]=this.vueModel.data.strongBtn,\ne[s.BUTTON_STATUS.LOADING]=this.vueModel.data.strongBtn,e)),\no.setOnClick(function(){n.buttonClick(-1)}),\no.changeLoadingStatus(s.BUTTON_STATUS.NORMAL))}\nthis.vueModel.data.btns=i.map(function(t,e){return{\nlineStyle:0===e?{display:'none'}:n.isHorizontal?{width:'1px'\n}:{height:'1px'},text:t||''}}),\nthis.vueModel.data.onMsgLink=t.onMsgLink,t.msgExtraCss&&(\nthis.getViewInComponentById('msg'\n).style.cssText=t.msgExtraCss),t.additionExtraCss&&(\nthis.getViewInComponentById('addition'\n).style.cssText=t.additionExtraCss),\nthis.protocolNode=this.getViewInComponentById(\n'singleProtocolLabel'),t.protocolsExtraCss&&(\nthis.protocolNode.style.cssText=t.protocolsExtraCss)},\ne.prototype.initViews=function(t){\nvar e=this.vueModel.data.onMsgLink;if(e&&(\nthis.getViewInComponentById('msg').onlink=function(t){e(t)})\n,t&&t.protocols&&r.amc.fn.isArray(t.protocols\n)&&1<=t.protocols.length){r.amc.fn.show(this.protocolNode)\n;var n=t.protocols[0]\n;this.protocolNode.innerText=n.innerText||'《'+n.text+'》',\nthis.protocolNode.onclick=function(){\nt.protocolsSpmInfo&&1<=t.protocolsSpmInfo.length&&r.amc.fn.spmClick(\nt.protocolsSpmInfo[0].spmId,t.protocolsSpmInfo[0].spmObj),\nr.amc.fn.openweb(n.url)}}},e.prototype.show=function(t){\nthis.setData(t),this.initViews(t),this.showContentImg(t)\n;var e=this.getViewInComponentById('dlg')\n;r.amc.isIOS&&r.amc.fn.sdkGreaterThanOrEqual('10.8.35'\n)?e.show():e.showModal(),t.showCloseBtn&&(r.amc.fn.show(\nthis.getViewInComponentById('closeBtnBox')),\nthis.getViewInComponentById('closeBtn').src=r.amc.res.close)\n},e.getComponentCSSRules=function(){return{\n'.dlg-box':'_CommonDialog_jljb-c-dlg-box',\n'.margin-box-lr':'_CommonDialog_jljb-c-margin-box-lr',\n'.title-box':'_CommonDialog_jljb-c-title-box',\n'.msg':'_CommonDialog_jljb-c-msg',\n'.title':'_CommonDialog_jljb-c-title',\n'.btn-text':'_CommonDialog_jljb-c-btn-text',\n'.btn-div':'_CommonDialog_jljb-c-btn-div',\n'.align-self-box':'_CommonDialog_jljb-c-align-self-box',\n'.btn-line':'_CommonDialog_jljb-c-btn-line',\n'.labels-container':'_CommonDialog_jljb-c-labels-container',\n'.label':'_CommonDialog_jljb-c-label',\n'.close-btn-container':'_CommonDialog_jljb-c-close-btn-container',\n'.close-btn':'_CommonDialog_jljb-c-close-btn',\n'.content-img-container':'_CommonDialog_jljb-c-content-img-container',\n'.protocol-box':'_CommonDialog_jljb-c-protocol-box',\n'.single-protocol-label':'_CommonDialog_jljb-c-single-protocol-label',\n'.addition':'_CommonDialog_jljb-c-addition'}},\ne.getComponentJson=function(){return{'sp-view-id':'dlg',\nonkeydown:'onDialogKeyDown',\n_c:'_CommonDialog_jljb-c-dlg-box amc-v-box amc-justify-center amc-align-center',\n_t:'dialog',_cd:[{'sp-view-id':'closeBtnBox',\n_c:'_CommonDialog_jljb-c-close-btn-container amc-hidden',\n_t:'div',_cd:[{'sp-view-id':'closeBtn',\n_c:'_CommonDialog_jljb-c-close-btn',onclick:'onCloseDlg',\n_t:'img'}]},{'v-if':'@{##msg## && ##msg##.length > 0}',\n'sp-view-id':'titleBox',\n_c:'_CommonDialog_jljb-c-align-self-box _CommonDialog_jljb-c-title-box _CommonDialog_jljb-c-margin-box-lr',\n_t:'div',_cd:[{'sp-view-id':'title',\n_c:'amc-ellipsis _CommonDialog_jljb-c-title amc-flex-1 amc-text-center',\n_t:'label','v-text':'@{title}'}]},{'sp-view-id':'msgBox',\n_c:'amc-flex-center _CommonDialog_jljb-c-align-self-box _CommonDialog_jljb-c-margin-box-lr',\n'v-style-cal':'@{msgBoxStyle}',_t:'div',_cd:[{\n'sp-view-id':'msg',_c:'_CommonDialog_jljb-c-msg',_t:'label',\n'v-text':'@{msg}'}]},{'sp-view-id':'addition',\n_c:'_CommonDialog_jljb-c-addition _CommonDialog_jljb-c-margin-box-lr',\n'v-if':'@{!!##addition##}',_t:'label','v-text':'@{addition}'\n},{'v-if':'@{##labels## && ##labels##.length > 0}',\n'sp-view-id':'labelsContainer',\n_c:'_CommonDialog_jljb-c-labels-container amc-align-center',\n_t:'div',_cd:[{'v-for':'@{labels}',\n_c:'_CommonDialog_jljb-c-label amc-text-center',_t:'label',\n'v-text':'@{item}'}]},{'sp-view-id':'mainBody',\n_c:'_CommonDialog_jljb-c-align-self-box amc-v-box amc-justify-center',\n_t:'div',_cd:[{'sp-view-id':'singleProtocolLabel',\n_c:'_CommonDialog_jljb-c-single-protocol-label amc-flex-1 amc-ellipsis-2-line _CommonDialog_jljb-c-protocol-box _CommonDialog_jljb-c-margin-box-lr amc-hidden',\n_t:'label'},{'v-if':'@{!!##strongBtn##}',\n_c:'_CommonDialog_jljb-c-margin-box-lr',_t:'div',_cd:[{\n'sp-component':'Button','sp-component-id':'strongButton',\n_t:'div'}]},{'v-if':'@{!##strongBtn##}',_c:'amc-adapt-line',\n_t:'div'},{'sp-view-id':'btnBox',_c:'amc-align-center',\n'v-style':'@{btnBoxStyle}',_t:'div',_cd:[{'v-for':'@{btns}',\n'v-style':'@{btnBoxStyle}',\n_c:'amc-align-center amc-flex-1 _CommonDialog_jljb-c-align-self-box',\n_t:'div',_cd:[{'v-style':'@{item.lineStyle}',\n_c:'_CommonDialog_jljb-c-btn-line _CommonDialog_jljb-c-align-self-box',\n_t:'div'},{'v-click':'@{onBtnClick}',\n_c:'_CommonDialog_jljb-c-btn-div _CommonDialog_jljb-c-btn-div amc-flex-1',\n_t:'div',_cd:[{\n_c:'_CommonDialog_jljb-c-btn-text amc-theme-color amc-flex-1 amc-text-center amc-ellipsis',\n_t:'label','v-text':'@{item.text}'}]}]}]}]}]}},\ne.componentName='CommonDialog',\ne.componentHashName='CommonDialog_jljb',e}(c.BNComponent)\n;e.CommonDialog=l},function(t,e,n){'use strict';var i,\no=this&&this.__extends||(i=function(t,e){return(\ni=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var a=n(1),r=n(2),c=function(t){\nfunction e(){var e=null!==t&&t.apply(this,arguments)||this\n;return e.action={name:'loc:none'},e.clickListener=function(\n){},e.vueModel={data:{title:'',message:'',button:''},\ncompute:{}},e.onCreated=function(){},e.onMounted=function(\n){},e.onClickListener=function(){try{e.clickListener()\n}catch(t){}},e.setTitle=function(t){\nreturn e.vueModel.data.title=t||'',e},e.setMessage=function(\nt){return e.vueModel.data.message=t||'',e},\ne.setButton=function(t){return e.vueModel.data.button=t||'',\ne},e.setContainerStyle=function(t){\nreturn r.modifyElementStyle(e,'card-container',t),e},\ne.setClickListener=function(t){return t&&(e.clickListener=t)\n,e},e}return o(e,t),e.getComponentCSSRules=function(){\nreturn{'.container':'_ButtonGroup_xfez-c-container',\n'.title':'_ButtonGroup_xfez-c-title',\n'.label-container':'_ButtonGroup_xfez-c-label-container',\n'.message':'_ButtonGroup_xfez-c-message',\n'.line-1':'_ButtonGroup_xfez-c-line-1',\n'.button':'_ButtonGroup_xfez-c-button',\n'.button-text':'_ButtonGroup_xfez-c-button-text'}},\ne.getComponentJson=function(){return{\n'sp-view-id':'card-container',\n_c:'_ButtonGroup_xfez-c-container amc-align-center',\nonclick:'onClickListener',_t:'div',_cd:[{\n_c:'amc-v-box amc-justify-center _ButtonGroup_xfez-c-label-container',\n_t:'div',_cd:[{\n_c:'_ButtonGroup_xfez-c-title _ButtonGroup_xfez-c-line-1',\n'v-if':'@{!!##title##}',_t:'label','v-text':'@{title}'},{\n_c:'_ButtonGroup_xfez-c-message _ButtonGroup_xfez-c-line-1',\n'v-if':'@{!!##message##}',_t:'label','v-text':'@{message}',\n_x:'<'}]},{_c:'_ButtonGroup_xfez-c-button',\n'v-if':'@{!!##button##}',_t:'div',_cd:[{\n_c:'_ButtonGroup_xfez-c-button-text',_t:'label',\n'v-text':'@{button}'}]}]}},e.componentName='ButtonGroup',\ne.componentHashName='ButtonGroup_xfez',e}(a.BNComponent)\n;e.Card=c},function(t,e,n){'use strict';var i,\no=this&&this.__extends||(i=function(t,e){return(\ni=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var a=n(1),r=n(0),c=n(2),\ns=function(e){function t(t){var i=e.call(this)||this\n;return i.onMounted=function(){},i.onCreated=function(){},\ni.vueModel={data:{selectBank:function(t){if(\ni.bankClickListener){var e=t.getAttribute('index')\n;i.bankClickListener(function(t,e){void 0===e&&(e=0);try{\nvar n=parseInt(t,10);return isNaN(n)?e:n}catch(t){return e}\n}(e||''))}},bankList:[]},compute:{}},i.fullBankList=[],\ni.defaultShowNum=0,i.supportNum=0,i.headerTipDesc='',\ni.viewMore='',i.bankListStatusListener=function(){},\ni.setBankListStatusListener=function(t){\ni.bankListStatusListener=t},i.bankClickListener=function(){}\n,i.selectMore=function(){if(r.amc.fn.spmClick(\ni.props.spm.more,i.props.spmServerParam),\ni.fullBankList.length>i.defaultShowNum)if(\ni.fullBankList.length<i.supportNum)if(\ni.vueModel.data.bankList.length===i.fullBankList.length){\nvar t={name:i.props.signAction};document.submit({action:t})\n}else i.vueModel.data.bankList=i.fullBankList,\nc.visibleElement(i,'card-more-tail-img',!1),\ni.bankListStatusListener(!1);else c.visibleElement(i,\n'card-more-tail-img',!0),\ni.vueModel.data.bankList.length===i.fullBankList.length?(\ni.vueModel.data.bankList=i.fullBankList.slice(0,\ni.defaultShowNum)||[],c.modifyElementAttribute(i,\n'card-more-content',{innerText:'{{more}}'}),\nc.modifyElementAttribute(i,'card-more-tail-img',{\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*wi0wQI-0ODcAAAAAAAAAAAAAARQnAQ'\n}),i.bankListStatusListener(!0)):(\ni.vueModel.data.bankList=i.fullBankList,\nc.modifyElementAttribute(i,'card-more-content',{\ninnerText:'{{fold}}'}),c.modifyElementAttribute(i,\n'card-more-tail-img',{\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*C_R-RI4Srf4AAAAAAAAAAAAAARQnAQ'\n}),i.bankListStatusListener(!1));else t={\nname:i.props.signAction},document.submit({action:t})},\ni.setBankClickListener=function(t){\nreturn i.bankClickListener=t,i},i.setHeaderTipDesc=function(\nt){return c.modifyElementAttribute(i,\n'card-list-box-header-tip-2',{innerText:t}),\ni.headerTipDesc=t,i},i.setHeaderTipTitle=function(t){\nreturn c.modifyElementAttribute(i,\n'card-list-box-header-tip-1',{innerText:t}),i},\ni.setViewMore=function(t){t?(r.amc.fn.spmExposure(\ni.props.spm.more,i.props.spmServerParam),c.visibleElement(i,\n'more-box',!0),i.viewMore=t):c.visibleElement(i,'more-box',\n!1)},i.setBankList=function(t,e,n){\nreturn i.fullBankList=t||[],i.defaultShowNum=e||7,\ni.supportNum=n||i.fullBankList.length,i.setHeaderTipDesc(\ni.headerTipDesc),i.vueModel.data.bankList=t.slice(0,\ni.defaultShowNum)||[],\nt.length<=i.defaultShowNum?c.visibleElement(i,'more-box',!1\n):i.viewMore&&(r.amc.fn.spmExposure(i.props.spm.more,\ni.props.spmServerParam),c.visibleElement(i,'more-box',!0)),\nt.forEach(function(t,e){r.amc.fn.spmExposure(\ni.props.spm.bankItem+'_'+e,{instId:t.instId})}),i},\ni.visible=function(t){\n0<i.fullBankList.length&&c.visibleElement(i,'card-list',t)},\ni.visibleHeader=function(t){c.visibleElement(i,\n'card-list-box-header',t),c.visibleElement(i,\n'card-list-divide',t)},i.props=t,i}return o(t,e),\nt.getComponentCSSRules=function(){return{\n'.card-list-box':'_QuickBindList_y1d4-c-card-list-box',\n'.card-list-box-header':'_QuickBindList_y1d4-c-card-list-box-header',\n'.card-list-box-header-tip-1':'_QuickBindList_y1d4-c-card-list-box-header-tip-1',\n'.card-list-box-header-tip-2':'_QuickBindList_y1d4-c-card-list-box-header-tip-2',\n'.card-list-divide':'_QuickBindList_y1d4-c-card-list-divide',\n'.card-list-divide-mt':'_QuickBindList_y1d4-c-card-list-divide-mt',\n'.card-list-divide-ml':'_QuickBindList_y1d4-c-card-list-divide-ml',\n'.card-list-item':'_QuickBindList_y1d4-c-card-list-item',\n'.card-list-item-logo':'_QuickBindList_y1d4-c-card-list-item-logo',\n'.card-list-item-content':'_QuickBindList_y1d4-c-card-list-item-content',\n'.card-list-item-content-title':'_QuickBindList_y1d4-c-card-list-item-content-title',\n'.card-list-item-content-desc':'_QuickBindList_y1d4-c-card-list-item-content-desc',\n'.card-list-item-tail':'_QuickBindList_y1d4-c-card-list-item-tail',\n'.card-more-content':'_QuickBindList_y1d4-c-card-more-content',\n'.card-item-container':'_QuickBindList_y1d4-c-card-item-container',\n'.card-list-item-content-labels-container':'_QuickBindList_y1d4-c-card-list-item-content-labels-container',\n'.card-list-item-content-label':'_QuickBindList_y1d4-c-card-list-item-content-label',\n'.more-box':'_QuickBindList_y1d4-c-more-box'}},\nt.getComponentJson=function(){return{\n'sp-view-id':'card-list',\n'v-if':'@{!!(##bankList##.length)}',\n_c:'_QuickBindList_y1d4-c-card-list-box amc-v-box amc-align-center amc-self-stretch',\n_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-box-header',\n'sp-view-id':'card-list-box-header',_t:'div',_cd:[{\n'sp-view-id':'card-list-box-header-tip-1',\n_c:'_QuickBindList_y1d4-c-card-list-box-header-tip-1',\n_t:'label',_x:'{{bind_card_tip}}'},{\n'sp-view-id':'card-list-box-header-tip-2',\n_c:'_QuickBindList_y1d4-c-card-list-box-header-tip-2',\n_t:'label'}]},{'sp-view-id':'card-list-divide',\n_c:'_QuickBindList_y1d4-c-card-list-divide amc-self-stretch',\n_t:'div'},{\n_c:'amc-v-box amc-self-stretch _QuickBindList_y1d4-c-card-item-container',\n'v-for':'@{bankList}',_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item amc-align-center amc-self-stretch',\n'v-click':'@{selectBank}',_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item-logo',\n'v-src':'@{##item.logoUrl##||\\'\\'}',_t:'img'},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content amc-align-center amc-flex-1',\n_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-title',\n_t:'label','v-text':'@{item.instName}'},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-desc',\n'v-if':'@{!##item.instActivityTags##}',_t:'label',\n'v-text':'@{##item.instActivityText##||\\'\\'}'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-item-tail',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*ZG6bSoBQYpAAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-labels-container amc-align-center',\n'v-if':'@{##item.instActivityTags## && ##item.instActivityTags##.length > 0}',\n_t:'div',_cd:[{'v-for':'@{item.instActivityTags}',\n_c:'_QuickBindList_y1d4-c-card-list-item-content-label amc-text-center',\n_t:'label','v-text':'@{item}'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-divide _QuickBindList_y1d4-c-card-list-divide-ml _QuickBindList_y1d4-c-card-list-divide-mt',\n_t:'div'}]},{'sp-view-id':'more-box',\n_c:'_QuickBindList_y1d4-c-card-list-item _QuickBindList_y1d4-c-card-item-container _QuickBindList_y1d4-c-more-box amc-align-center amc-justify-center amc-self-stretch',\nonclick:'selectMore',_t:'div',_cd:[{\n'sp-view-id':'card-more-content',\n_c:'_QuickBindList_y1d4-c-card-more-content',_t:'label',\n_x:'{{more}}'},{'sp-view-id':'card-more-tail-img',\n_c:'_QuickBindList_y1d4-c-card-list-item-tail',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*wi0wQI-0ODcAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]}]}},t.componentName='QuickBindList',\nt.componentHashName='QuickBindList_y1d4',t}(a.BNComponent)\n;e.QuickBindList=s},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0})\n;var i=function(){function o(){this._loaded=!1,\nthis._isReload=!1}return Object.defineProperty(o.prototype,\n'loaded',{get:function(){return this._loaded},enumerable:!0,\nconfigurable:!0}),Object.defineProperty(o.prototype,\n'isReload',{get:function(){return this._isReload},\nenumerable:!0,configurable:!0}),o.isPrerender=function(t){\nreturn t&&(\nt.local&&t.local.isPrerender||t.rpcData&&t.rpcData.isPrerender\n)},o.prototype.setup=function(e,n){var i=this,\nt=window.onload;window.onload=function(){o.isPrerender(\nwindow.flybird)||(i._loaded||(i._loaded=!0,e(\nwindow.flybird.rpcData)),t&&t())},\ndocument.onreload=function(t){o.isPrerender(t)||(\ni._isReload=!0,t&&t.local&&(window.flybird.local=t.local),\nt&&t.rpcData&&(window.flybird.rpcData=t.rpcData,i._loaded||(\ni._loaded=!0,e(window.flybird.rpcData))),n&&n(t))}},o}()\n;e.PreRenderManager=i}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body", "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-card-no-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0480", "tplId": "QUICKPAY@cashier-card-no-flex", "tplVersion": "5.4.1"}