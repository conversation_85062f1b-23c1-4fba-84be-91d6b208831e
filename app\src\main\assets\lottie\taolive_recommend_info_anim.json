{
	"api": "mtop.taobao.media.guang.template.listmaterialtools",
	"data": {
		"cursor": "2",
		"data": [{
			"createTime": "1702968667000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "635",
				"adaptMediaType": "video",
				"displayShootInAlbum": true,
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": "7",
					"time": "10",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231212/1702351357560/1702351357378791064.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702547347615/1702547347014174593.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703065869000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231208/1702028925539/1702028925529994924.jpg",
			"materialType": "7",
			"name": "科幻电音热舞",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702547364780/1702547364778860418.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702547368671/1702547368654953908.mai",
			"tid": "4932001",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "714",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": 7.1,
					"time": "11",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702280904692/1702280904587565298.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702280634147/1702280632760548867.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703065857000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231208/1702029800817/1702029800807368213.jpg",
			"materialType": "7",
			"name": "科技未来风变身",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702280617390/1702280617373161902.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702280615044/1702280614635580087.mai",
			"tid": "4932002",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "37",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": 7.1,
					"time": "9",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231226/1703556814626/1703556814414865148.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702267664051/1702267663694238336.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703573440000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231226/1703573436276/1703573436260910114.jpg",
			"materialType": "7",
			"name": "赛博朋克变身",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702267929086/1702267929084157109.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702267927080/1702267927062332343.mai",
			"tid": "4932004",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "30",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": "5",
					"time": "12",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702261292123/1702261292052418049.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702541307190/170254130696313689.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703065809000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702261232347/1702261232337455946.jpg",
			"materialType": "7",
			"name": "秋日漫画风",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702541308275/1702541308272801697.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702541311824/1702541311791127321.mai",
			"tid": "4932005",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "135",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": "12",
					"time": "16",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702268268685/170226826855748188.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231218/1702891854108/170289185324681067.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703065825000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702268206813/1702268206800821134.jpg",
			"materialType": "7",
			"name": "3d卡通风格变身",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231218/1702891853418/1702891853415807967.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231218/1702891853402/1702891853218913207.mai",
			"tid": "4932006",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "178",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": "6",
					"time": "7",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702274353352/1702274353257322906.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702542591986/1702542590557184165.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703065774000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231211/1702274290394/1702274290384629499.jpg",
			"materialType": "7",
			"name": "一键漫画变身",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702542580699/170254258069728100.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231214/1702542578155/1702542578105503228.mai",
			"tid": "4932007",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1702968668000",
			"extend": {
				"adaptVideoTemplateType": "MULTI",
				"adaptNeedCloudEdit": "true",
				"relatedTopicId": [],
				"useNum": "49",
				"adaptMediaType": "video",
				"videoConfig": {
					"segNums": "1",
					"featureType": ["2", "5"],
					"coverCaptureTime": "4",
					"time": "11",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20231212/1702363492168/1702363492023517666.mp4"
				},
				"tags": {
					"produceTags": []
				},
				"basicInfo": {
					"tmpResourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231222/1703229689528/1703229688973542225.tmai"
				},
				"iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01izHVOv222uJOPiAQV_!!6000000007063-2-tps-160-68.png"
			},
			"lastModified": "1703229695000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231212/1702363381432/1702363381424345169.jpg",
			"materialType": "7",
			"name": "一键日系漫画变身",
			"resourceExtUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231222/1703229679434/1703229679433548127.json",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231222/1703229677421/1703229677162199976.mai",
			"tid": "4932008",
			"utLog": "",
			"version": "758"
		}, {
			"createTime": "1693126295000",
			"extend": {
				"adaptNeedCloudEdit": "false",
				"relatedTopicId": ["424238188619"],
				"useNum": "1098",
				"adaptMediaType": "video",
				"specialMark": "8",
				"videoConfig": {
					"vendorType": "11",
					"musicId": "1775283016",
					"skipDupCheck": "true",
					"author": "IQOO",
					"audioId": "1579994861",
					"useInOneClick": "1",
					"featureType": [],
					"coverCaptureTime": "8",
					"musicVendor": "11",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20230829/1693283025928/iqoo%E5%8F%98%E8%BA%AB.mp4",
					"minSeqNums": "1"
				},
				"desc": "只需1张半身照片",
				"tags": {
					"produceTags": []
				},
				"topicInfo": {
					"ref_id": "424238188619",
					"ref_name": "为亚运电竞赛事加油 ",
					"default_topic_modifiable": "1"
				}
			},
			"lastModified": "1702970825000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20230829/1693282189144/%E5%B0%81%E9%9D%A2.jpg",
			"materialType": "7",
			"name": "运动赛博朋克变身照IQOO",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20231030/1698648758105/iqoo_202310301440.mai",
			"tid": "4796317",
			"utLog": "",
			"version": "751"
		}, {
			"createTime": "1693125467000",
			"extend": {
				"adaptNeedCloudEdit": "false",
				"relatedTopicId": ["424272096963"],
				"useNum": "255",
				"adaptMediaType": "video",
				"specialMark": "8",
				"videoConfig": {
					"vendorType": "11",
					"author": "VIVO",
					"audioId": "1579993404",
					"useInOneClick": "1",
					"coverCaptureTime": "11",
					"url": "https://ossgw.alicdn.com/videotool/material_cpp/20230829/1693283094508/%E4%BA%9A%E8%BF%90vivo.mp4",
					"minSeqNums": "1",
					"musicId": "1773330965",
					"segNums": "2",
					"skipDupCheck": "true",
					"featureType": [],
					"time": "10",
					"musicVendor": "11"
				},
				"desc": "只需1张半身运动照片",
				"tags": {
					"produceTags": []
				},
				"topicInfo": {
					"ref_id": "424272096963",
					"ref_name": "亚运有我运动挑战赛",
					"default_topic_modifiable": "1"
				}
			},
			"lastModified": "1702970834000",
			"logoUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20230829/1693283059576/%E5%B0%81%E9%9D%A2.jpg",
			"materialType": "7",
			"name": "运动动漫变身照VIVO",
			"resourceUrl": "https://ossgw.alicdn.com/videotool/material_cpp/20230901/1693540601017/VIVO_20230901.mai",
			"tid": "4794318",
			"utLog": "",
			"version": "751"
		}],
		"hasNext": "false",
		"size": "9"
	},
	"ret": ["SUCCESS::调用成功"],
	"v": "1.0"
}

http://market.m.taobao.com/apps/market/trade/finish.html?spm=a2116h.app.0.0.5d2aabebkYxF05&wh_weex=true&orderIds=3773941813934927048,3781802665777927048&degrade=0&wx_navbar_transparent=true&_wvUseWKWebView=YES

http://market.m.taobao.com/apps/market/trade/finish.html?spm=a2116h.app.0.0.5d2aabebkYxF05&wh_weex=true&orderIds=3773941813934927048,3781802665777927048&degrade=0&wx_navbar_transparent=true&_wvUseWKWebView=YES

http://h5.m.taobao.com/awp/mtb/rate.htm?orderId=3781802665777927048&cpp=1&spm=a2141.11512060.tradeinfo.d1

MainRateLoadingActivity

http://h5.m.taobao.com/ocean/publish.htm?page=page_rate&trackId=3781802665777927048&api=%7B%22name%22%3A%22mtop.taobao.rate.component.render%22%2C%22params%22%3A%7B%22pageType%22%3A%22taobaoTotalPublishRate%22%2C%22orderId%22%3A3781802665777927048%2C%22platformType%22%3A%22wireless%22%7D%2C%22version%22%3A%221.0%22%7D

https://web.m.taobao.com/app/mtb-guang/comment-success/home?wh_weex=true&weex_mode=dom&disableNav=YES&wx_navbar_transparent=true&wx_navbar_hidden=true&_wx_statusbar_hidden=true&spm=w-a2141.12310610&orderId=3781802665777927048&sellerId=2168147041&sellerId=2168147041&listVersion=1.0&allGoodRate=true&autoShare2GuangGuang=false&rateId=1231935565639&rewardStatus=notReward&unlockRaterTask=0

https://web.m.taobao.com/app/mtb-guang/comment-success/home?wh_weex=true&weex_mode=dom&disableNav=YES&wx_navbar_transparent=true&wx_navbar_hidden=true&_wx_statusbar_hidden=true&spm=w-a2141.12310610&orderId=3781802665777927048&sellerId=2168147041&sellerId=2168147041&listVersion=1.0&allGoodRate=true&autoShare2GuangGuang=false&rateId=1231935565639&rewardStatus=notReward&unlockRaterTask=0

http://market.m.taobao.com/apps/market/trade/finish.html?spm=a2116h.app.0.0.5d2aabebkYxF05&wh_weex=true&orderIds=3773941813934927048,3781802665777927048&degrade=0&wx_navbar_transparent=true&_wvUseWKWebView=YES
//交易成功

http://h5.m.taobao.com/awp/mtb/rate.htm?orderId=3781802665777927048&cpp=1&spm=a2141.11512060.tradeinfo.d1
//客户端

http://h5.m.taobao.com/ocean/publish.htm?page=page_rate&trackId=3781802665777927048&api={"name":"mtop.taobao.rate.component.render","params":{"pageType":"taobaoTotalPublishRate","orderId":3781802665777927048,"platformType":"wireless"},"version":"1.0"}

http://market.m.taobao.com/apps/market/trade/finish.html?spm=a2116h.app.0.0.5d2aabebkYxF05&wh_weex=true&orderIds=3773941813934927048,3781802665777927048&degrade=0&wx_navbar_transparent=true&_wvUseWKWebView=YES

http://h5.m.taobao.com/awp/base/order/detailultron.htm?source=1&bizOrderId=3788237844178841107&archive=0&serverV2=true
//交易成功详情页

http://meta.m.taobao.com/app/mtb/pay-success-v2/confirm-success?wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_navbar_transparent=true&_wx_statusbar_hidden=true&status_bar_transparent=true&orderIds=2057250576387953792,2057106795045953792&degrade=0
//交易成功

{
    "tipsIcon": "https://www.icon.com",
    "fixedTipsContent": "写评价抽免单啦!",
    "jumpUrl": "https://wwww.linkUrl.com",
    "jumUrlText": "查看",
    "nonFixedTipsBeginContent": "真实、有趣的分享更受欢迎哦",
    "nonFixedTipsEditingContent": "，有机会被推荐上首页",
    "nonFixedTipsDoneContent": "公开分享，有机会被推荐上首页",
    "rewardNumberFormat": "￥5",
    "rewardType": "红包",
    
    "taskContent": {
        "begain": {
            "title": [
                {
                    "type": 0,
                    "text": "$nonFixedTipsBeginContent"
                }
            ]
        },
        "editing": {
            "title": [
                {
                    "type": 0,
                    "text": "还差"
                },
                {
                    "type": 1,
                    "text": "张图"
                },
                {
                    "hidenWithText": true,
                    "hidenWithImage": true,
                    "type": 0,
                    "text": "+"
                },
                {
                    "type": 2,
                    "text": "字"
                },
                {
                    "type": 0,
                    "text": "$nonFixedTipsEditingContent"
                }
            ]
        },
        "done": {
            "title": [
                {
                    "type": 0,
                    "text": "$nonFixedTipsDoneContent"
                }
            ]
        }
    }
}
