{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var hide = amc.fn.hide;\n            var show = amc.fn.show;\n            var getTag = amc.fn.getById;\n            var rpc = amc.rpcData;\n            var gCardNoInputElement = getTag(\"cardNoInput\");\n            var gExpiryInputElement = getTag(\"expiryInput\");\n            var gCardNoInputTitle = getTag(\"cardNoInputTitle\");\n            var gExpiryInputTitle = getTag(\"expiryInputTitle\");\n            var gSubmitBtn = getTag(\"submitBtn\");\n            var gSubTitle = getTag(\"subTitle\");\n            var gCheckCardNo = Boolean(rpc.checkCardNo);\n            var gCheckExpiry = Boolean(rpc.checkExpiry);\n            var gCurrentExpiryInputLength = 0; //记录当前有效期输入框的长度\n            \n            function init() {\n                try {\n                    initUI();\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit(true);\n                    }\n                } \n                catch(e) {\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit();\n                    }\n                }\n            }\n\n            function initUI() {\n                //head_title\n                var naviTitle = rpc.head_title || \"身份驗證\";\n                var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', naviTitle, null, null, onBack, null);\n                getTag(\"bodyContainer\").insertBefore(nav, getTag(\"mainBody\"));\n\n                getTag(\"bodyTitle\").innerText = rpc.body_title || \"驗證銀行卡信息\";\n                if (rpc.bankName && rpc.bankName.length > 0) {\n                    gSubTitle.innerText = rpc.bankName + \"（\" + (rpc.cardLast4 || \"\") + \"）\";\n                    show(gSubTitle);\n                }\n                else {\n                    hide(gSubTitle);\n                }\n\n                //input相关\n                gCardNoInputTitle.innerText = rpc.card_num_input_title || \"卡號\";\n                gCardNoInputElement.placeholder = rpc.card_num_input_placeholder || \"請輸入完整的已綁卡卡號\";\n                gExpiryInputTitle.innerText = rpc.expiry_date_input_title || \"有效期\";\n                gExpiryInputElement.placeholder = rpc.expiry_date_placeholder || \"MM / YY\";\n                gExpiryInputElement.maxlength = 7;   //12 / 12 验证有效期时，最多7位\n\n                if (!gCheckCardNo) {\n                    hide(getTag(\"cardNoContainer\"));\n                }\n\n                if (!gCheckExpiry) {\n                    hide(getTag(\"expiryContainer\"));\n                }\n\n                //卡号和有效期同时存在时，需要设置有效期的marginTop\n                if (gCheckCardNo && gCheckExpiry) {\n                    getTag(\"expiryContainer\").style.marginTop = \"20px\";\n                }\n                \n                gSubmitBtn.innerText = rpc.form_button || \"下一步\";\n                getTag(\"changeModule\").innerText = rpc.foot_tip || \"換個驗證方式\";\n                if (Boolean(rpc.HAS_OTHERS)) {\n                    getTag(\"changeModule\").style.visibility = \"visible\";\n                }\n\n                var timeInterval = 100;\n                if (amc.isAnroid) {\n                    timeInterval = 400;\n                }\n                setTimeout(function() {\n                    if (gCheckCardNo) {\n                        gCardNoInputElement.focus();\n                    }\n                    else {\n                        gExpiryInputElement.focus();\n                    }\n                }, timeInterval);\n            }\n\n            function onKeyDown() {\n                if (event.which == 4) {\n                    onBack();\n                }\n            }\n\n            function onBack() {\n                var obj = {\n                    \"eventName\" : \"vi_quit_module\"\n                };\n                document.submit(obj);\n            }\n\n            function onCardNoInputChanged() {\n                checkSubmitDisabled();\n                //格式化银行卡号，4位分开\n                var text = gCardNoInputElement.value || \"\";\n                var trimedText = text.replace(/\\s/g, \"\");\n                if (!trimedText) {\n                    return;\n                }\n                gCardNoInputElement.value = (trimedText.replace(/([\\d|\\*]{4})(?=[\\d|\\*])/g, \"$1\" + \" \")).trim() || \"\";\n            }\n\n            function onExpiryInputChanged() {\n                checkSubmitDisabled();\n                //格式化日期，中间要增加 /\n                var text = gExpiryInputElement.value || \"\";\n                var trimedText = text.replace(/\\//g, \"\");\n                trimedText = trimedText.replace(/\\s/g, \"\");\n                //是否是增加字符\n                var isAdd = text.length > gCurrentExpiryInputLength ? true : false;\n\n                if (isAdd && text.length === 2) {\n                    gExpiryInputElement.value += \" / \";\n                }\n                else {\n                    gExpiryInputElement.value = (trimedText.replace(/([\\S|\\*]{2})(?=[\\S|\\*])/g, \"$1\" + \" / \")).trim() || \"\";\n                }\n                \n                gCurrentExpiryInputLength = gExpiryInputElement.value.length;\n            }\n\n            function checkSubmitDisabled() {\n                if (gCheckCardNo && !gCheckExpiry) {\n                    //只显示银行卡\n                    gSubmitBtn.disabled = gCardNoInputElement.value.length > 0 ? false : true;\n                }\n                else if (!gCheckCardNo && gCheckExpiry) {\n                    //如果只显示有效期\n                    gSubmitBtn.disabled = gExpiryInputElement.value.length >= 7 ? false : true;\n                }\n                else {\n                    //同时显示银行卡号和有效期\n                    if (gExpiryInputElement.value.length >= 7 && gCardNoInputElement.value.length > 0) {\n                        gSubmitBtn.disabled = false;\n                    }\n                    else {\n                        gSubmitBtn.disabled = true;\n                    }\n                }\n            }\n\n            function submit() {\n                //隐藏键盘\n                gCardNoInputElement.blur();\n                gExpiryInputElement.blur();\n                var cardNo = gCardNoInputElement.value;\n                cardNo = cardNo.replace(/\\s/g, \"\");\n                var expiryText = gExpiryInputElement.value;\n                expiryText = expiryText.replace(/\\//g, \"\");\n                expiryText = expiryText.replace(/\\s/g, \"\");\n\n                var param = {};\n                param.clientId = rpc.clientId || \"\";\n                param.purpose = \"VERIFY\";\n                param.reqTime = new Date().toString();\n                param.reqMsgId = \"\"; //是verifyId，需要从native拿，上传给卡中心\n                param.url = (rpc.icardcenterHKUrl || \"\") + \"/cacheCard.json\";\n                param.encryptPubKey = rpc.encryptPubKey || \"\";\n                param.cardDetailIndex = rpc.cardDetailIndex || \"\";\n                param.cardNo = cardNo || \"\";\n                if (expiryText.length >= 4) {\n                    param.expiryYear = expiryText.substr(0, 2);\n                    param.expiryMonth = expiryText.substr(2, 2);\n                }\n                param.refererUrl = rpc.refererUrl || \"\";\n\n                var obj = {\n                    \"eventName\": \"vi_rpc_validate\",\n                    \"moduleName\": \"CC_PAINTEXT_HK\",\n                    \"actionName\": \"VERIFY\",\n                    \"showLoading\": \"true\",\n                    \"params\": param\n                };\n\n                document.asyncSubmit(obj, function(data) {\n                    if (true === Boolean(data[\"verifySuccess\"])) {\n                        mic.fn.onBackWithResponse(data);\n                    }\n                    else {\n                        //验证失败\n                        var verifyMessage = data[\"verifyMessage\"] || \"人氣太旺了，請稍後再試\";\n                        if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === \"CC_PAINTEXT_HK\") {\n                            //重试\n                            document.toast({\n                                text: verifyMessage,\n                            }, function() {});\n                        }\n                        else {\n                            //验证次数限制\n                            var buttonText = Boolean(rpc.HAS_OTHERS) ? \"換個驗證方式\" : \"確定\";\n                            amc.fn.viAlert({\n                                \"title\": \"\",\n                                \"message\": verifyMessage,\n                                \"button\": buttonText\n                            }, function() {\n                                if (Boolean(rpc.HAS_OTHERS)) {\n                                    changeModule();\n                                }\n                                else {\n                                    mic.fn.onBackWithResponse(data);\n                                }\n                            });\n                        }\n                    }\n                });\n            }\n\n            function changeModule() {\n                gCardNoInputElement.blur();\n                gExpiryInputElement.blur();\n                mic.fn.changeModule();\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n                background-color: #ffffff;\n            }\n\n            .main-title-label {\n                text-align: center;\n                font-size: 24px;\n                color: #000000;\n                line-height: 34px;\n                margin-top: 32px;\n            }\n\n            .sub-title-label {\n                text-align: center;\n                font-size: 18px;\n                color: #888888;\n                line-height: 24px;\n                margin-top: 16px;\n                margin-left: 30px;\n                margin-right: 30px;\n            }\n\n            .card-no-input-container {\n                margin-top: 38px;\n                display: flex;\n                flex-direction: column;\n            }\n\n            .expiry-input-container {\n                margin-top: 38px;\n                display: flex;\n                flex-direction: column;\n            }\n\n            .input-title-label {\n                text-align: left;\n                font-size: 18px;\n                color: #333333;\n                line-height: 24px;\n                margin-left: 16px;\n                margin-right: 16px;\n            }\n\n            .input {\n                border: 0;\n                color:#000;\n                font-size: 18px;\n                white-space: nowrap;\n                margin-top: 12px;\n                margin-left: 16px;\n                margin-right: 16px;\n                height: 40px;\n                padding: 0px 0px 0px 0px;\n            }\n\n            .input-line {\n                margin-left: 16px;\n                margin-right: 16px;\n                height: 0.5px;\n                background-color: #d1d1d1;\n            }\n\n            .submit-btn {\n                margin-top: 28px;\n                margin-left: 16px;\n                margin-right: 16px;\n                border-radius: 2px;\n                border: 0;\n                color: #fff;\n                font-size: 18px;\n                height: 48px;\n                background-color: #1677ff;\n            }\n\n            .submit-btn:active {\n                background-color: rgba(16, 142, 233, 1);\n            }\n\n            .submit-btn:disabled {\n                background-color: rgba(16, 142, 233, 0.6);\n            }\n\n            .change-module-label {\n                text-align: center;\n                font-size: 18px;\n                color: #1677ff;\n                line-height: 22px;\n                margin-top: 24px;\n                width: 140px;\n                align-self: center;\n                visibility: hidden; \n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "mic-fullscreen main-body", "children": [{"css": "main-title-label", "tag": "label", "id": "bodyTitle"}, {"css": "sub-title-label", "tag": "label", "id": "subTitle"}, {"css": "card-no-input-container", "children": [{"css": "input-title-label", "tag": "label", "id": "cardNoInputTitle"}, {"css": "input", "tag": "input", "id": "cardNoInput", "type": "number", "oninput": "onCardNoInputChanged()"}, {"css": "input-line", "tag": "div"}], "tag": "div", "id": "cardNoContainer"}, {"css": "expiry-input-container", "children": [{"css": "input-title-label", "tag": "label", "id": "expiryInputTitle"}, {"css": "input", "tag": "input", "id": "expiryInput", "type": "number", "oninput": "onExpiryInputChanged()"}, {"css": "input-line", "tag": "div"}], "tag": "div", "id": "expiry<PERSON><PERSON><PERSON>"}, {"css": "submit-btn", "onclick": "submit()", "disabled": "true", "tag": "button", "id": "submitBtn"}, {"css": "change-module-label", "onclick": "changeModule()", "tag": "label", "id": "changeModule"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "bankCardNoMacau", "format": "JSON", "tag": "MOBILEIC", "time": "0011", "tplId": "MOBILEIC@bankCardNoMacau", "tplVersion": "5.4.4"}