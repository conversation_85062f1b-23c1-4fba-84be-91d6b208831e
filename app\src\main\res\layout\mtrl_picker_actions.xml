<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="end"
    android:orientation="horizontal"
    android:id="@+id/date_picker_actions"
    android:paddingRight="@dimen/mtrl_calendar_action_padding"
    android:layout_width="match_parent"
    android:layout_height="@dimen/mtrl_calendar_action_height"
    android:paddingEnd="@dimen/mtrl_calendar_action_padding">
    <Button
        android:layout_gravity="center_vertical"
        android:id="@+id/cancel_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/mtrl_picker_cancel"
        style="?attr/buttonBarNegativeButtonStyle"/>
    <Button
        android:layout_gravity="center_vertical"
        android:id="@+id/confirm_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/mtrl_picker_confirm"
        style="?attr/buttonBarPositiveButtonStyle"/>
</LinearLayout>
