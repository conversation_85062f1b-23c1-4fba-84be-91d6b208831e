{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var getTag = amc.fn.getById;\n            var createTag = amc.fn.create;\n            var rpc = amc.rpcData;\n            var countDownNum;\n            var countDownTag;\n\n            //### 初始化\n            function init() {\n                var remindInfo = {};\n                try {\n                    remindInfo = JSON.parse(rpc['remindInfo']);\n                } catch (error) {\n                    print(\"fail to get remindInfo: \" + error.message);\n                }\n\n                // 修改导航栏标题\n                var titleContent = remindInfo['titleContent'] && remindInfo['titleContent'].length > 0 ? remindInfo['titleContent']: '{{safe_remind}}'\n                var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', titleContent, null, null, onBack, null);\n                getTag('bodyContainer').insertBefore(nav, getTag('mainBody'));\n                \n                // 图标\n                getTag('remindImg').src = remindInfo['icon'];\n                // 标题\n                getTag('titleLable').innerText = remindInfo['header'];\n                // 副标题\n                var subtitle = remindInfo['subtitle'];\n                if (remindInfo['point'] && remindInfo['point'].length > 0) {\n                    var points = remindInfo['point'];\n                    for (var i = 0; i < points.length; i++) {\n                        var point = points[i];\n                        subtitle += ('\\n<font color=\"#F5A623\"> - ' + point['text'] + '</font>');\n                    }\n                }\n                getTag('descLable').innerText = subtitle;\n\n                // 倒计时\n                countDownNum = remindInfo['count_down_time'];\n\n                // 创建按钮\n                if (remindInfo['button'] && remindInfo['button'].length > 0) {\n\n                    var buttons = remindInfo['button'];\n                    for (var i = 0; i < buttons.length; i++) {\n                        createButton(buttons[i]);\n                    }\n                }\n            }\n\n            // 创建按钮\n            function createButton(button) {\n                var mainBody = getTag('mainBody');\n                var btnDiv = createTag('div');\n                var btn = createTag('button');\n\n                if (button['type'] == 'marjor') {   // 蓝底\n                    btnDiv.className = 'margin-lr btnDiv amc-align-center amc-justify-center';\n                    btn.className = 'btn-blue';\n                    btn.id = 'submitBtn-blue';\n                } else if (button['type'] == 'second') {  // 白底\n                    btnDiv.className = 'margin-lr btnDiv amc-align-center amc-justify-center';\n                    btn.className = 'btn-white';\n                    btn.id = 'submitBtn-white';\n                } else {    // 链接\n                    btnDiv.className = 'margin-lr btnDiv amc-align-center amc-justify-center';\n                    btn.className = 'btn-link';\n                    btn.id = 'submitBtn-link';\n                }\n                btn.innerText = button['name'];\n                btn.value = button['name'];\n\n                mainBody.appendChild(btnDiv);\n                btnDiv.appendChild(btn);\n\n                btn.onclick = function() {\n                    submitAction(button['code']);\n                }\n\n                if (button['count_down'] == 'true') {\n                    countDownTag = btn.id;\n                    countDown();\n                }\n            }\n\n            //### 后退\n            function onBack() {\n                mic.fn.onBack();\n            }\n\n            // 倒计时\n            function countDown() {\n                var btn = getTag(countDownTag);\n                if (countDownNum > 0) {\n                    btn.disabled = true;\n                    btn.innerText = btn.value + '(' + countDownNum + 's)';\n                    countDownNum -= 1;\n                    setTimeout(countDown, 1000);\n                } else {\n                    btn.disabled = false;\n                    btn.innerText = btn.value;\n                }\n            }\n\n            //### 按钮点击事件\n            function submitAction(code) {\n                var obj = {};\n                obj['showLoading'] = 'true';\n                obj['eventName'] = 'vi_rpc_validate';\n                obj['params'] = {\n                    'operation': code\n                };\n                obj['moduleName'] = 'REMIND';\n                obj['actionName'] = 'VERIFY_REMIND';\n                document.asyncSubmit(obj, function(data) {\n                    if (Boolean(rpc['VISwitchConfig']['remindNext'])) {\n                        mic.fn.onBackWithResponse(data);\n                    } else {\n                        obj = {};\n                        obj['success'] = data['success'];\n                        obj['verifySuccess'] = data['verifySuccess'];\n                        obj['finish'] = data['finish'];\n                        obj['finishCode'] = data['finishCode'];\n                        mic.fn.onBackWithResponse(obj);\n                    }\n                });\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".remindDiv {\n                flex-direction: column;\n                padding: 20px 16px;\n                background-color: #fff;\n                margin-bottom: 15px;\n            }\n\n            .remindImg {\n                width:90px;\n                height:90px;\n                margin-bottom:16px;\n            }\n\n            .titleLabel {\n                text-align:center;\n                font-size: 20px;\n                color: #333333;\n                margin-bottom:9px;\n            }\n\n            .descLabel {\n                text-align:left;\n                font-size: 14px;\n                color: #999999;\n            }\n\n            .margin-lr {\n                margin-left: 15px;\n                margin-right: 15px;\n                margin-top: 15px;\n            }\n\n            .btnDiv {\n                border: 0;\n                border-radius: 3px;\n                font-size: 18px;\n                height: 47px;\n                max-height: 47px;\n                min-height: 47px;\n                flex: 1.0;\n            }\n\n            .btn-blue {\n                background-color: #1677ff;\n                border: 0;\n                border-radius: 3px;\n                color: #fff;\n                font-size: 18px;\n                height: 47px;\n                max-height: 47px;\n                min-height: 47px;\n                flex: 1.0;\n            }\n            .btn-blue:active {\n                background-color: #1677ff;\n                color: #fff;\n            }\n            .btn-blue:disabled {\n                background-color: #7BBBF4;\n                color: rgba(255, 255, 255, 0.6);\n            }\n\n            .btn-white {\n                background-color: #fff;\n                border: 0;\n                border-radius: 3px;\n                color: #000;\n                font-size: 18px;\n                height: 47px;\n                max-height: 47px;\n                min-height: 47px;\n                flex: 1.0;\n            }\n            .btn-white:active {\n                background-color: #fff;\n                color: #000;\n            }\n            .btn-white:disabled {\n                background-color: #fff;\n                color: #9B9B9B;\n            }\n\n            .btn-link {\n                background-color:#f5f5f9;\n                border: 0;\n                border-radius: 3px;\n                color: #1677ff;\n                font-size: 18px;\n                height: 47px;\n                max-height: 47px;\n                min-height: 47px;\n                flex: 1.0;\n            }\n            .btn-link:active {\n                background-color:#f5f5f9;\n                color: #1677ff;\n            }\n            .btn-link:disabled {\n                background-color:#f5f5f9;\n                color: #9B9B9B;\n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "amc-scroll-flex", "children": [{}, {"css": "amc-pd-lr remindDiv", "children": [{"css": "amc-justify-center", "children": [{"css": "remindImg", "src": "VIVerifyCore.bundle/alipay_vi_warning.png", "tag": "img", "id": "remindImg"}], "tag": "div"}, {"css": "titleLabel", "children": [{"tag": "text", "text": "您可能正在遭遇诈骗"}], "tag": "label", "id": "titleLable"}, {"css": "desc<PERSON><PERSON><PERSON>", "children": [{"tag": "text", "text": "描述"}], "tag": "label", "id": "descLable"}], "tag": "div", "id": "remindDiv"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "riskRemind", "format": "JSON", "tag": "MOBILEIC", "time": "0013", "tplId": "MOBILEIC@riskRemind", "tplVersion": "5.3.5"}