{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"children": [{"tag": "text", "text": "function isPrerender(n){return n&&(n.local&&n.local.isPrerender||n.rpcData&&n.rpcData.isPrerender)}function onload(){if(!isPrerender(flybird))try{window.gOnReloadExecCount+=1,window._onload()}catch(n){}}window.gOnReloadExecCount=0,document.onreload=function(n){isPrerender(n)||(window.gOnReloadExecCount+=1,1<window.gOnReloadExecCount||(n&&n.local&&(window.flybird.local=n.local),n&&n.rpcData&&(window.flybird.rpcData=n.rpcData,amc.rpcData=n.rpcData),window._onload()))}"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".deduct-label-list{padding:13px 16px 13px 16px}.deduct-no-padding{padding:0}.amc-nav-horiz-line-android{background-color:#FFFFFF;height:1PX}.amc-bg-white{background-color:#FFFFFF}.amc-nav-box-android{align-items:center;display:flex;flex:1.0;justify-content:center;height:47px;background-color:#FFFFFF}.full-body{height:100%;display:flex;flex-direction:column;padding:0px;margin:0px;background-color:#fff}.black-mask{width:100%;height:100%;position:absolute;background-color:#000000;top:0}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._PayAndSignPage_aoan-c-de-1px-line{height:1px;background-color:#eee}._PayAndSignPage_aoan-c-main-body{flex-direction:column;padding-left:16px;padding-right:16px;overflow:scroll}._PayAndSignPage_aoan-c-cell-box{padding:10px 15px;min-height:60px}._PayAndSignPage_aoan-c-flex-row{display:flex;justify-content:center;align-items:center}._PayAndSignPage_aoan-c-algin-item-start{align-items:flex-start}._PayAndSignPage_aoan-c-flex-between{justify-content:space-between}._PayAndSignPage_aoan-c-flex-justify-start{justify-content:flex-start}._PayAndSignPage_aoan-c-flex-justify-end{justify-content:flex-end}._PayAndSignPage_aoan-c-am-message{position:relative;color:#000;overflow:hidden;padding:10px 15px 24px 15px;background-color:#fff;text-align:center;display:flex;align-items:center}._PayAndSignPage_aoan-c-am-ft-price{font-size:45px;font-family:AlipayNumber;font-weight:400}._PayAndSignPage_aoan-c-am-ft-unit{margin-bottom:-14px;font-size:27px;font-family:AlipayNumber;font-weight:bolder}._PayAndSignPage_aoan-c-am-message-main{margin-top:0px}._PayAndSignPage_aoan-c-message-subject{width:90%;margin-top:12px;margin-bottom:5px;text-align:center;font-size:17px;color:#000}._PayAndSignPage_aoan-c-am-message-sub{margin-top:0px}._PayAndSignPage_aoan-c-merchant-name{width:90%;margin:0 20px;text-align:center;line-height:19px;font-size:14px;color:#999999}._PayAndSignPage_aoan-c-am-amount{margin-top:10px;justify-content:center;align-items:flex-end}._PayAndSignPage_aoan-c-checkbox-ios{width:19px;height:19px;background-image:url(AlipaySDK.bundle/alipay_msp_check);background-size:19px 19px}._PayAndSignPage_aoan-c-checkbox-ios:checked{background-image:url(AlipaySDK.bundle/alipay_msp_checked)}._PayAndSignPage_aoan-c-checkbox-ios:disabled{background-image:url(AlipaySDK.bundle/alipay_msp_check_disable)}._PayAndSignPage_aoan-c-checkbox-android{width:19px;height:19px;background-image:url(com.alipay.android.app/alipay_msp_check);background-size:22px 22px}._PayAndSignPage_aoan-c-checkbox-android:checked{background-image:url(com.alipay.android.app/alipay_msp_checked)}._PayAndSignPage_aoan-c-checkbox-android:disabled{background-image:url(com.alipay.android.app/alipay_msp_check_disable)}._PayAndSignPage_aoan-c-amc-item-m-box{flex:1.0;display:flex;overflow:hidden;align-items:center}._PayAndSignPage_aoan-c-amc-item-m-text{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;flex:1.0;font-size:16px;color:#666}._PayAndSignPage_aoan-c-amc-item-r-box{flex:1.0;overflow:hidden;justify-content:flex-end;align-items:center}._PayAndSignPage_aoan-c-merchant-info-wrapper{padding:4px 0 4px 16px}._PayAndSignPage_aoan-c-deduct-pay-setting{margin-top:10px}._PayAndSignPage_aoan-c-merchant-product-name{padding-right:16px;flex:7}._PayAndSignPage_aoan-c-product-name{flex:1;font-size:15px;color:#333;font-weight:bold;margin:12px 15px 12px 0px}._PayAndSignPage_aoan-c-merchant-product-desc{width:92%;padding:16px 16px 8px 16px;line-height:22px}._PayAndSignPage_aoan-c-deduct-label-item{padding:5px 0}._PayAndSignPage_aoan-c-deduct-label-list-l{padding:16px 16px 16px 16px}._PayAndSignPage_aoan-c-deduct-label-list-no-right-padding{padding:13px 0 13px 16px}._PayAndSignPage_aoan-c-deduct-label-list-no-right-padding-l{padding:16px 0 16px 16px}._PayAndSignPage_aoan-c-deduct-label-list-right-padding{padding-right:16px}._PayAndSignPage_aoan-c-deduct-label{flex:1.0;color:#999999;font-size:15px}._PayAndSignPage_aoan-c-deduct-label-content{flex:3;text-align:left;color:#333333;font-size:15px}._PayAndSignPage_aoan-c-deduct-label-content-light{flex:2.2;text-align:right;color:#999999;font-size:15px}._PayAndSignPage_aoan-c-deduct-switch{min-height:44px;width:85px;justify-content:flex-end}._PayAndSignPage_aoan-c-de-agreement-box{padding:16px 0px 10px 0px}._PayAndSignPage_aoan-c-de-agreement-list{display:flex;align-items:flex-start;padding:20px 16px 0 16px}._PayAndSignPage_aoan-c-de-agreement-checkbox{width:22px;margin-right:6px;margin-top:-2px;align-items:flex-start}._PayAndSignPage_aoan-c-de-agreement-links{font-size:15px;flex:1;line-height:21px;word-break:break-all;word-wrap:break-word;flex-wrap:wrap}._PayAndSignPage_aoan-c-link-text{font-size:14px}._PayAndSignPage_aoan-c-btn-primary-wrapper{margin:0 15px;border:0;border-radius:2px;color:#fff;font-size:18px;height:47px;max-height:47px;min-height:47px;flex:1.0}._PayAndSignPage_aoan-c-btn-direct-pay-wrapper{margin:0 15px;border:0;border-radius:2px;border:#1677FF 1px;font-size:18px;height:47px;max-height:47px;min-height:47px;flex:1.0}._PayAndSignPage_aoan-c-btn-primary{border:0;border-radius:5px;font-size:18px;height:47px;max-height:47px;min-height:47px;flex:1.0}._PayAndSignPage_aoan-c-de-info-dialog-item{padding:5px 15px;display:flex;justify-content:flex-start;align-items:flex-start}._PayAndSignPage_aoan-c-de-dialog-prefix{flex:1;line-height:1;align-self:flex-start}._PayAndSignPage_aoan-c-de-dialog-content{flex:10;word-break:break-all;word-wrap:break-word}._PayAndSignPage_aoan-c-de-dialog-prefix-logo{height:6px;width:6px;border-radius:3px;background-color:#333;align-self:flex-start;margin-right:5px;margin-top:5px}._PayAndSignPage_aoan-c-deduct-pay-setting-label{color:#333333;flex:1.0;font-size:15px}._PayAndSignPage_aoan-c-huazhi-border{margin-top:5px;height:1PX;background-color:#dddddd}._PayAndSignPage_aoan-c-huazhi-box{padding-top:12px;text-align:right}._PayAndSignPage_aoan-c-huazhi-text{justify-content:flex-end;color:#E8A010}._PayAndSignPage_aoan-c-main-content{flex-direction:column}._PayAndSignPage_aoan-c-half-px-line{height:0.5px;margin-left:16px;margin-right:16px;background-color:#e5e5e5}._PayAndSignPage_aoan-c-amc-check-img{height:19px;width:19px}._PayAndSignPage_aoan-c-amc-checkbox{width:19px;height:19px;background-image:url(com.alipay.android.app/alipay_msp_check);background-size:19px 19px}._PayAndSignPage_aoan-c-item-box-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}._PayAndSignPage_aoan-c-propagate-arrow{position:absolute;width:16px;height:8px;top:0px;left:16px}._PayAndSignPage_aoan-c-propagate-icon{margin-right:8px;height:16px;width:16px}._PayAndSignPage_aoan-c-info-text-propagate{font-size:13px}._PayAndSignPage_aoan-c-font-color-primary{color:#333}._PayAndSignPage_aoan-c-item-btn{background-color:#f7fcff;margin-left:5px;border-radius:1px;padding:2px 8px;min-height:24px;min-width:48px}._PayAndSignPage_aoan-c-item-btn-text{font-size:13px;max-width:90px}._PayAndSignPage_aoan-c-double-line-for-propagate{height:34px}._PayAndSignPage_aoan-c-item-box-s-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}._PayAndSignPage_aoan-c-bottom-container{flex:1.0}._PayAndSignPage_aoan-c-slogan-box{padding:16px 0px 10px 0px}._PayAndSignPage_aoan-c-slogan-img{height:14px}._PayAndSignPage_aoan-c-total-cost-img{height:35px;width:17px;max-width:35px;margin-bottom:1px}._PayAndSignPage_aoan-c-service-desc-img-box{padding:8px 0px 8px 0px}._PayAndSignPage_aoan-c-service-desc-img{height:15px}._PayAndSignPage_aoan-c-direct-pay-btn-area{margin-top:20px}._PayAndSignPage_aoan-c-btn-secondary{background-color:#fff;color:#1677FF;height:47px;max-height:47px;min-height:47px;width:100%;border-radius:2px;font-size:18px}._DeductOperationComponent_hww0-c-item-box-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}._DeductOperationComponent_hww0-c-propagate-arrow{position:absolute;width:16px;height:8px;top:0px;left:16px}._DeductOperationComponent_hww0-c-propagate-icon{margin-right:8px;height:16px;width:16px}._DeductOperationComponent_hww0-c-channel-propagate{vertical-align:top;align-self:flex-start}._DeductOperationComponent_hww0-c-info-text-propagate{font-size:13px}._DeductOperationComponent_hww0-c-info-text-channel-propagate{font-size:15px;text-align:start;vertical-align:top}._DeductOperationComponent_hww0-c-font-color-primary{color:#333}._DeductOperationComponent_hww0-c-item-btn{background-color:#f7fcff;margin-left:5px;border-radius:1px;padding:2px 8px;min-width:48px}._DeductOperationComponent_hww0-c-item-btn-text{font-size:13px;max-width:90px}._DeductOperationComponent_hww0-c-double-line-for-propagate{height:34px}._DeductOperationComponent_hww0-c-item-box-s-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from ae8cf5a3dd3b3facd7899c14b1ab479e8e05f303:D */!function(n){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}i.m=n,i.c=o,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&'object'==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,'a',t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p='',i(i.s=12)}([function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(13);t.BNComponent=o.BNComponent;var i=n(5);t.ComponentRegistry=i.ComponentRegistry;var a=n(15);t.Logger=a.Logger,t.logger=a.logger},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(19);t.BNComponent=o.BNComponent;var i=n(10);t.ComponentRegistry=i.ComponentRegistry;var a=n(21);t.Logger=a.Logger,t.logger=a.logger},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},t.startsWith=function(e,t){return!!e&&0===e.indexOf(t)}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(2),i=function(){function n(){}return n.registerComponent=function(e,t){t?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(o.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(e){return n.jsons[e]},n.putComponentJson=function(e,t){t||o.logger.e('CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i('CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},n.createComponent=function(e){o.logger.i('CmpReg#crtCmp','I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e('CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}();t.ComponentRegistry=i},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var u=n(1),o=n(7),l=n(8),d=function(){function e(){}return e.nativeRenderSucceed=function(e,t){t&&(e.parentNode&&e.pluginNode&&(e.parentNode.style.height=t,e.pluginNode.style.height=t),e.pluginHeight=t),e.renderFinishCallback&&e.renderFinishCallback(e)},e.allPlugins={},e}(),i=function(){function s(e){this.mqpToken=e}return s.listAll=function(){var e=[];for(var t in d.allPlugins)d.allPlugins.hasOwnProperty(t)&&e.push({mqpToken:t,plugin:d.allPlugins[t]});return e},s.find=function(e){return d.allPlugins[e]},s.remove=function(e,t,n){var o=d.allPlugins[e];o&&o.remove(t,n)},s.create=function(e,t,n,o){if(e&&'PluginBN'===e.type&&e.tplInfo&&e.data){var i=new s(l.Utils.randomStr(6)),a=e.tplInfo,r=a.tplHash?a.tplId+'_'+a.tplHash:a.tplId,c=e.data;l.Utils.niceTry(function(){u.amc.fn.logAction('new|'+Date.now()+'|'+r+'|'+i.mqpToken,'BNPlugin')}),i.init({mqpToken:i.mqpToken,tpl:{tplid:r,tpl:JSON.stringify({time:'0001',tplId:r,tplVersion:'5.4.9',publishVersion:'150924',tplUrl:a.tplUrl,tplHash:a.tplHash}),data:{config:c.config,bizData:c.bizData,spm:c.spm,mqpToken:i.mqpToken}}},t,n,o)}},s.prototype.init=function(e,t,n,o){if(o){var i=e.tpl.data.config;'object'==typeof i.loc&&null!==i.loc||(i.loc={});var a=i.loc;o.width&&(a.width=o.width),o.height&&(a.height=o.height)}var r;if((d.allPlugins[this.mqpToken]=this).parentNode=t,this.renderFinishCallback=n,this.initData=e,'android'===l.Utils.getPlatform()?((r=document.createElement('embed',{type:'MQPBNFrame',src:JSON.stringify(e)},function(){})).type='MQPBNFrame',this.pluginNode=r):(r=document.createElement('embed'),(this.pluginNode=r).type='MQPBNFrame',r.src=JSON.stringify(e)),o&&o.mountAsFirstChild){var c=t.hasChildNodes()&&t.childNodes[0];c?t.insertBefore(r,c):t.appendChild(r)}else t.appendChild(r);o&&o.doNotClearParentHeight||(t.style.height=0)},s.prototype.remove=function(n,e){var o=this;delete d.allPlugins[this.mqpToken];var t=this.pluginNode;if(t&&(t.style.width=0,t.style.height=0),e&&e.resetParentHeight){var i=this.parentNode;i&&(i.style.height='auto')}var a=function(){var e=o.parentNode,t=o.pluginNode;e&&t&&e.removeChild(t),n&&n()};e&&e.delayMs?window.setTimeout(function(){a()},e.delayMs):a()},s}();t.BNFramePlugin=i,o.BNFrameChannelOuter.onInnerEvent('MQPBNFRAME_RENDER_SUCCESS',function(e,t){if(l.Utils.niceTry(function(){u.amc.fn.logAction('ack|'+Date.now()+'|'+(e?e.height:'-')+'|'+(t?t.mqpToken:'-'),'BNPlugin')}),!(t&&e&&e.mqpToken&&e.height))return{result:!1};var n=e.height;return d.nativeRenderSucceed(t,n),{result:!0}})},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var a=n(1),s=n(6),r=n(8),c=function(){function e(){}return e.onFramePluginEventLifetimeNotifier=function(e){var t='string'==typeof e?JSON.parse(e):e;if('onEvent'===t.type){var n=t.key;if(!n)return;var o=this.EventTypeInnerHandlers[n];o&&o.forEach(function(e){try{e(t.args)}catch(e){}});var i=this.EventTypeOuterHandlers[n];if(i&&t.mqpToken&&t.id)try{var a=i(t.args,s.BNFramePlugin.find(t.mqpToken));u.sendEventResultToPlugin(n,t.id,a||{},t.mqpToken)}catch(e){}}else if('onEventResult'===t.type){var r=t.id;if(!r)return;var c=this.EventResultCallbacks[r];if(c){try{c(!0,t.result)}catch(e){}delete this.EventResultCallbacks[r]}}},e.addEventHandler=function(e,t,n){e?(this.EventTypeInnerHandlers[t]||(this.EventTypeInnerHandlers[t]=[]),this.EventTypeInnerHandlers[t].push(n)):this.EventTypeOuterHandlers[t]=n},e.setEventResultCallback=function(e,t){t&&(this.EventResultCallbacks[e]=t)},e.listKnownEventTypes=function(){var e=[];for(var t in this.EventTypeOuterHandlers)this.EventTypeOuterHandlers.hasOwnProperty(t)&&e.push(t);return e},e.EventResultCallbacks={},e.EventTypeInnerHandlers={},e.EventTypeOuterHandlers={},e}(),u=function(){function i(){}return i.onInnerEvent=function(e,t){c.addEventHandler(!1,e,t)},i.sendEventToInner=function(t,n,e){if(e){var o=s.BNFramePlugin.find(e);o&&i.sendEventToOnePlugin(t,n||{},o)}else s.BNFramePlugin.listAll().forEach(function(e){i.sendEventToOnePlugin(t,n||{},e.plugin)})},i.sendEventResultToPlugin=function(e,t,n,o){if(o){var i=s.BNFramePlugin.find(o);i&&this.sendEventResultToOnePlugin(e,t,n,i)}},i.sendEventToOnePlugin=function(e,t,n){var o={type:'onEvent',id:this.generateEvId(e),mqpToken:n.mqpToken,key:e,args:t};this._setDomEvent(n,o)},i.sendEventResultToOnePlugin=function(e,t,n,o){var i={type:'onEventResult',id:t,mqpToken:o.mqpToken,key:e,result:n};this._setDomEvent(o,i)},i._setDomEvent=function(e,t){e.pluginNode&&window.setTimeout(function(){e.pluginNode.setAttribute('event',JSON.stringify(t))},1)},i.generateEvId=function(e){return'ev_o_'+(e||'')+'_'+r.Utils.randomStr(6)},i}();t.BNFrameChannelOuter=u;var l=function(){function e(){}return e.sendEventToOuter=function(e,t,n){var o={type:'onEvent',id:this.generateEvId(e),mqpToken:window.flybird&&window.flybird.rpcData&&window.flybird.rpcData.mqpToken,key:e,args:t||{}};c.setEventResultCallback(o.id,n),document.submit({action:{name:'onBnFrameEvent'},param:o})},e.onOuterEvent=function(e,t){c.addEventHandler(!0,e,t)},e.generateEvId=function(e){return'ev_i_'+(e||'')+'_'+r.Utils.randomStr(6)},e}();t.BNFrameChannelInner=l;var o=function(){function e(){}return e.listApis=function(n){l.sendEventToOuter(i,{},function(e,t){e&&t&&t.apis?n(t.apis):n([])})},e.renderFinished=function(o,i,e){void 0===e&&(e=20),window.setTimeout(function(){var e,t=window.flybird&&window.flybird.rpcData||{},n=t.mqpToken||'';e='number'==typeof o?o:o&&o.style&&'number'==typeof o.style.offsetHeight?r.Utils.convPX2px(o.style.offsetHeight):0,r.Utils.niceTry(function(){a.amc.fn.logAction('ok|'+Date.now()+'|'+e+'|'+(t?t.mqpToken:'-'),'BNPlugin')}),l.sendEventToOuter('MQPBNFRAME_RENDER_SUCCESS',{mqpToken:n,height:e},function(e,t){i&&i(e)})},e)},e._renderFinished=function(e,t){},e}();t.BNFrameCommonInnerEvents=o,window.onFramePluginEvent=function(e){c.onFramePluginEventLifetimeNotifier(e)};var i='LIST_APIS';u.onInnerEvent(i,function(e,t){return{apis:c.listKnownEventTypes()}})},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=function(){function e(){}return e.randomStr=function(e){for(var t='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',n=t.length,o=isNaN(e)?1:e,i='',a=0;a<o;a++)i+=t[Math.floor(Math.random()*n)];return i},e.alert=function(e,t){document.alert({title:e||'-',message:t||'-',button:'OK'},function(){})},e.niceTry=function(e){try{return e()}catch(e){}},e.getPlatform=function(){switch(document.platform){case'android':return'android';case'iOS':return'iOS';default:return''}},e.convPX2px=function(e){return e/window.devicePixelRatio},e}();t.Utils=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},t.startsWith=function(e,t){return!!e&&0===e.indexOf(t)},t.tryJSONParse=function(e){if(t=e,'[object Object]'===Object.prototype.toString.call(t))return e;var t;try{return JSON.parse(e)}catch(e){return{}}},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(3),i=function(){function n(){}return n.registerComponent=function(e,t){t?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(o.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(e){return n.jsons[e]},n.putComponentJson=function(e,t){t||o.logger.e('CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i('CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},n.createComponent=function(e){o.logger.i('CmpReg#crtCmp','I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e('CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}();t.ComponentRegistry=i},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var c=n(0);function o(e,t){if(e&&t){if(t===e.src)return;c.amc.isAndroid&&'none'===e.style.display?(c.amc.fn.show(e),window.setTimeout(function(){e.src=t},20)):e.src=t}}function i(e){if(!e)return 0;e=e.replace(/<\\/?[^>]+(>|$)/g,'');for(var t=0,n='',o=new RegExp('[\\\\u4E00-\\\\u9FFF]+','g'),i=0,a=e;i<a.length;i++){var r=a[i];c.amc.isIOS&&(' '<=r&&r<='~'&&o.test(n)||' '<=n&&n<='~'&&o.test(r))&&(t+=.5),t+=' '<=r&&r<='~'?.58:1,n=r}return t}t.mergeObject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(e&&e.length)for(var o=0;o<e.length;o++){var i=e[o];if(c.amc.fn.isObject(i))for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])}return n},t.isFunction=function(e){return'[object Function]'===Object.prototype.toString.call(e)},t.isPreRender=function(e){return e&&(e.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender)},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},t.doNothing=function(){},t.tryJSONParse=function(e){if(null==e)return{};if(c.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}},t.checkEmptyObj=function(e){return c.amc.fn.isString(e)?0===e.length:!(e&&0!==Object.keys(e).length)},t.substrWithFontWidth=function(e,t,n){if(!e)return e;for(var o='',i=0,a=e.length,r=0;r<a;r++){var c=n?e[a-r-1]:e[r];if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break}return o},t.calculateFontWidth=function(e){if(!e)return 0;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++)n.test(e[o])?t+=.45:t++;return Math.round(t)},t.deepCopy=function e(t){if(null==t||'object'!=typeof t)return t;var n;if(t instanceof Date)return(n=new Date).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(var o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(t instanceof Object){for(var a in n={},t)t.hasOwnProperty(a)&&(n[a]=e(t[a]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},t.getConfig=function(e,t){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:e},function(e){t(e.available)})},20)},t.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},t.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)},t.safeInvoke=function(e,t,n){c.amc.isAndroid?window.setTimeout(function(){document.invoke(e,t,n)},20):document.invoke(e,t,n)},t.safeLoadImgSrcAndSetMode=function(e,t){e&&t&&(e.contentmode=t.mode,o(e,t.src))},t.safeLoadImgSrc=o,t.calculateStyleLineHeight=function(e,t){return c.amc.isIOS?e:(e-t)/2****},t.calculateLabelShowLineNumber=function(e,t,n){if(!e||n<=0)return 1;var o=i(e)*t;return Math.ceil(o/n)},t.calculateFontCount=i,t.isLowDevice=function(){return!!(window.flybird&&window.flybird.local&&window.flybird.local.isLowDevice)},t.safeRemoveChildNode=function(t){if(t&&t.childNodes){for(var e=[],n=0,o=t.childNodes;n<o.length;n++){var i=o[n];e.push(i)}e.forEach(function(e){t.removeChild(e)})}},t.stringHashCode=function(e){for(var t=0,n=0;n<e.length;n++)t=31*t+e.charCodeAt(n),t&=16777215;return t}},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var u,a,r=n(2),s=n(1),l=n(16),c=n(17),d=n(18),p=s.amc.rpcData,m={},f=!s.amc.isSDK&&window.flybird.local.agednessVersion||!1,h=function(e){function t(){var c=null!==e&&e.apply(this,arguments)||this;return c.vueModel={data:{settingBox:{tip:'',descr:'',tailImg:'',action:''},deductChannelFromZhuge:!1,switchStatus:!0,settingBoxClick:function(){if(c.vueModel.data.settingBox&&c.vueModel.data.settingBox.action){var e=g(c.vueModel.data.settingBox.action);s.amc.fn.spmClick('a259.b13453.c43929.d89251',m),e&&e.name&&('/channelPriority/query'===e.name&&(e.params||(e.params={}),e.params.sdkVersion=s.amc.sdkVersion,e.params.mspType=s.amc.mspType),document.submit({action:e}))}else c.vueModel.data.settingBox&&c.vueModel.data.settingBox.bnframe&&c.channelSelectBnframeToken&&(s.amc.fn.logAction('sendEvent|deduct-channal','snippet'),l.BNFrameChannelOuter.sendEventToInner('SWITCH_CHANNEL',void 0,c.channelSelectBnframeToken))},settingBoxClass:'',settingItemClass:'deduct-label-list'},compute:{settingBoxExist:function(){return c.vueModel.data.switchStatus&&(!!c.vueModel.data.settingBox||!c.vueModel.data.deductChannelFromZhuge)},settingBoxTip:function(){return c.vueModel.data.settingBox&&c.vueModel.data.settingBox.tip||'扣款方式'},settingBoxDescr:function(){return c.vueModel.data.settingBox&&c.vueModel.data.settingBox.descr||'按设置的扣款顺序支付'},settingBoxTailImg:function(){return c.vueModel.data.settingBox&&'Y'===c.vueModel.data.settingBox.tailImg?s.amc.res.arrowRight:''}}},c.btnPrimaryWrapperId='btn-primary-wrapper',c.deductSwitchId='deduct-switch',c.MAX_PAY_QUOTA=99999999,c.isShowDeductSwitch=p.allowUserChooseSign,c.isOnDeductSwitch=p.needDeductSign,c.isCheckedDeductAgreement=!!p.signAgreementDefaultRead,c.isDeductChannelExp=p.deductChannelUseCommonText,c.deductProductCode=p.personalProductCode,c.deductTypeText='CYCLE_PAY_AUTH_P'===c.deductProductCode?'自动续费':'免密支付',c.hideAgreementWhenShowSwitch=c.isShowDeductSwitch&&(!c.isOnDeductSwitch||c.isOnDeductSwitch&&c.isCheckedDeductAgreement),c.hideAgreementWhenHideSwitch=!c.isShowDeductSwitch&&c.isCheckedDeductAgreement,c.hideAgreeCheckbox=c.hideAgreementWhenShowSwitch||c.hideAgreementWhenHideSwitch,c.agreementTip=c.hideAgreeCheckbox?'开通'+c.deductTypeText+'需同意':'我已阅读并同意',c.isBtnShowOpacity=!c.isCheckedDeductAgreement&&c.isOnDeductSwitch,c.operationInfo=p.deductPageOperationInfo,c.specialSortAssetDesc=p.specialSortAssetDesc||'优先使用花呗扣款',c.needOpenHuazhi=p.needOpenHuazhi,c.canUseALinkColor=s.amc.fn.sdkGreaterThanOrEqual('10.8.34'),c.aLinkAppend=c.canUseALinkColor?'alinkcolor=\"#999999\"':'',c.agreementSuffix=p.agreementSuffix?'<a color=\"#999999\" '+c.aLinkAppend+' href=\"toggle_check\">'+p.agreementSuffix+'</a>':'',c.onMounted=function(){c.remScale=window.remScale,window.remScale=1,c.vueModel.data.deductChannelFromZhuge=!!p.deductChannelFromZhuge,c.vueModel.data.settingBox=p.deductChannelSettingBox;var e=c.getViewInComponentById('mainBody');s.amc.isAndroid?(c.getViewInComponentById('loading').src=s.amc.path+'alipay_msp_indicator_white_loading',c.getViewInComponentById('direct-pay-loading').src=s.amc.path+'alipay_msp_indicator_blue_loading'):e.style.height=s.amc.specs.bodyHeight,f&&(c.getViewInComponentById('direct-pay-btn').style.fontSize='20px',c.getViewInComponentById('submit-btn').style.fontSize='20px'),c.renderOrderInfo();var t=new Array;p.merchantDetailInfo&&(t.push({labelName:'服务名称',labelValue:p.merchantDetailInfo.merchantProductName}),t.push({labelName:'服务内容',labelValue:p.merchantDetailInfo.serviceDescription})),p.deductPageLabelInfos.length&&t.push.apply(t,p.deductPageLabelInfos),c.renderDeductLabelsDom(t),c.renderDeductMerchant(p.merchantDetailInfo),c.renderAgreementInfo(p.deductAgreementDetailInfos),c.renderDeductPaySettingDom(),s.amc.fn.show(c.getViewInComponentById('deductServiceDesc')),c.operationInfo&&c.createOperationComponents(c.operationInfo,!1);var n=c.getViewInComponentById(c.deductSwitchId);if(n.value=c.isOnDeductSwitch?'on':'off',c.toggleDomDeductSwitch(c.isOnDeductSwitch),f&&(n.style.height=44*c.remScale+'px'),c.getViewInComponentById(c.btnPrimaryWrapperId).style.opacity=c.isBtnShowOpacity?'0.5':'1',s.amc.fn.sdkGreaterThanOrEqual('10.8.47')&&!s.amc.isSDK&&f&&(c.getViewInComponentById('btn-area').style.marginTop='10px'),p.alipayLogoUrl&&p.alipayLogoUrl.length){var o=function(e,t,n){e&&!c.isOnDeductSwitch&&s.amc.fn.show(c.getViewInComponentById('sloganBox'))},i=c.getViewInComponentById('sloganImg'),a=s.amc.isSDK&&s.amc.isIOS;window.loadImageHelper(i,p.alipayLogoUrl,a?null:o),a&&o(!0,p.alipayLogoUrl),setTimeout(function(){s.amc.fn.hide(c.getViewInComponentById('sloganBox'))},10)}if(p.deductChannelSettingBox&&p.deductChannelSettingBox.bnframe){var r=p.deductChannelSettingBox.bnframe;r&&(s.amc.fn.spmExposure('a259.b13453.c43929.d89251',m),s.amc.fn.logAction('create|deduct-channal','snippet'),c.createDynamicFrame(r))}p.deductChannelSettingBox&&(p.deductChannelSettingBox.curChannelIndexList&&(c.currentChannelList=p.deductChannelSettingBox.curChannelIndexList),c.channelContextId=p.deductChannelSettingBox.channelContextId||''),s.amc.fn.show(e)},c.onDeductSwitch=function(e,t){s.amc.fn.spmClick('a259.b13453.c32495.d64620',m),c.toggleDomDeductSwitch('on'===t)},c.submitPay=function(e){var t={},n={needDeductSign:!1};s.amc.fn.sdkGreaterThanOrEqual('10.8.36')&&(n.cashier_can_goback=!0),t.action={},1!==e&&(t.action.loadtxt='',s.amc.fn.spmClick('a259.b13453.c115488.d239345',m)),t.action.name='/cashier/main',t.action.params=p.params||{},t.param=n;var o=!1;document.asyncSubmit(t,function(e){'0'===(e=e||{}).pageloading?(o||(c.initBlackMask(),o=!0),c.enablePayBtn()):'1'===e.pageloading&&c.disablePayBtn()})},c}return i(t,e),t.prototype.gotoLink=function(e){document.submit({action:{name:'loc:openweb(\\''+e+'\\', \\'\\')'}})},t.prototype.renderOrderInfo=function(){var e=this.getViewInComponentById('orderAmount'),t=this.getViewInComponentById('subjectLabel'),n=this.getViewInComponentById('merchantName'),o=this.getViewInComponentById('totalCostImg');p.displayOrderAmount&&(e.innerText=p.displayOrderAmount),p.subject&&(t.innerText=p.subject),p.merchantDetailInfo&&p.merchantDetailInfo.merchantName&&(n.innerText=p.merchantDetailInfo.merchantName),f&&(t.style.fontSize='20px',n.style.fontSize='16px',e.style.fontSize='50px',e.style.fontFamily='Alibaba Sans 102',this.getViewInComponentById('am-amount').style.marginTop='0px'),o.src=s.amc.path+'alipay_msp_rmb',s.amc.isIOS&&(o.style.marginBottom='4px')},t.prototype.renderDeductMerchant=function(e){if(p.deductServiceDescImg)this.getViewInComponentById('deductServiceDescImg').src=p.deductServiceDescImg;else{var t=this.getViewInComponentById('deductServiceDescImgBox');s.amc.fn.hide(t)}if(this.isShowDeductSwitch&&'showOnlyPayBtn'!==p.pageVersion){var n=this.getViewInComponentById('deduct-switch-name'),o=15;if(f&&(n.style.fontSize='20px',o=20),n.innerText='开通'+e.merchantProductName,'CYCLE_PAY_AUTH_P'===this.deductProductCode){var i=window.innerWidth-15-15-85-16;c.calculateFontWidth('开通'+e.merchantProductName)*o>=i&&(n.innerText='开通自动续费')}s.amc.fn.show(this.getViewInComponentById('deductSwitch')),s.amc.fn.hide(this.getViewInComponentById('deductServiceName'))}},t.prototype.renderDeductLabelsDom=function(e){if(e&&!(e.length<1)){for(var t=this.getViewInComponentById('deduct-label-list'),n=0;n<e.length;n++)this.createSingleDeductLabel(e[n],t);f&&(t.style.marginTop='-6px')}},t.prototype.createSingleDeductLabel=function(e,t){if(t){var n=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item');t.appendChild(n);var o=this.createStyledElement('label','','deduct-label');n.appendChild(o),o.innerText=e.labelName||'';var i=this.createStyledElement('label','','deduct-label-content');n.appendChild(i),i.innerText=e.labelValue||'',f&&(o.style.fontSize='18px',o.style.marginRight='16px',i.style.fontSize='18px')}},t.prototype.renderDeductPaySettingDom=function(){var e=this.getViewInComponentById('deduct-pay-setting'),t=this.getViewInComponentById('deduct-pay-setting-label'),n=this.getViewInComponentById('deduct-label-content-light');this.vueModel.data.settingBoxClass='deduct-no-padding',this.vueModel.data.settingItemClass='deduct-label-list',f&&(t.style.fontSize='18px',n.style.fontSize='18px',e.style.height='49px',e.style.marginTop='-6px')},t.prototype.renderAgreementInfo=function(e){var t=this;if(e&&!(e.length<1)){for(var n='',o='',i=0;i<e.length;i++){var a=e[i];n=a.agreementName,o=a.agreementUrl}var r=this.getAgreementInfoNode();s.amc.fn.sdkGreaterThanOrEqual('10.8.47')&&!s.amc.isSDK?(s.amc.fn.show(this.getViewInComponentById('de-agreement-list-bottom')),s.amc.fn.hide(this.getViewInComponentById('de-agreement-list'))):f&&(r.style.marginTop='10px'),r.style.maxWidth=window.innerWidth-32,r.innerText='<font color=\"#999999\">我已阅读并同意</font><font color=\"#6F7892\">'+n+'</font>',r.onclick=function(){s.amc.fn.spmClick('a259.b13453.c32495.d64288',m),t.gotoLink(o)},r.style.fontSize='15px',f&&(r.style.fontSize='16px')}},t.prototype.getAgreementInfoNode=function(){var e=this.getViewInComponentById('de-agreement-links');if(s.amc.fn.sdkGreaterThanOrEqual('10.8.47')&&!s.amc.isSDK){var t=this.getViewInComponentById('de-agreement-links-bottom');t&&(e=t)}return e},t.prototype.createOperationComponents=function(e,t){if(e.channelOperationLabel&&e.channelOperationData){var n=this.getViewInComponentById('deduct-label-list');t&&this.channelOperationBox&&n.removeChild(this.channelOperationBox),this.channelOperationBox=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item');var o=this.createStyledElement('label','','deduct-label');this.channelOperationBox.appendChild(o),o.innerText=e.channelOperationLabel,f&&(o.style.fontSize='18px',o.style.marginRight='16px');var i=this.createStyledElement('div','','deduct-label-content');this.channelOperationBox.appendChild(i);var a=new d.DeductOperationComponent({operationData:e.channelOperationData,clickCallback:function(e){u.onChannelChanage(e)},bigFontVersion:f,isPayTool:p.payTool,operationType:'channel',spmInfo:{componentSpmID:'a259.b13453.c128781',contentSpmID:'a259.b13453.c128781.d266493',actBtnSpmID:'a259.b13453.c128781.d266494',spmObj:m}});a&&!a.needToHideOperationBox()&&(a.mountTo(i),n.appendChild(this.channelOperationBox))}if(e.accountOperationData){var r=this.getViewInComponentById('anchoredBox'),c=this.accountOperationBox;t&&c&&r.removeChild(c),this.accountOperationBox=this.createStyledElement('div','','');var s=new d.DeductOperationComponent({operationData:e.accountOperationData,bigFontVersion:f,isPayTool:p.payTool,operationType:'common',spmInfo:{componentSpmID:'a259.b13453.c105038',contentSpmID:'a259.b13453.c105038.d217942',spmObj:m}});r.appendChild(this.accountOperationBox),s&&!s.needToHideOperationBox()&&s.mountTo(this.accountOperationBox)}},t.prototype.toggleDomDeductSwitch=function(e){var t=this.getViewInComponentById('deduct-label-list'),n=this.getAgreementInfoNode(),o=this.getViewInComponentById('submit-btn'),i=this.getViewInComponentById('anchoredBox'),a=this.getViewInComponentById('sloganBox');if(this.vueModel.data.switchStatus=e){if(s.amc.fn.show(t),s.amc.fn.show(n),this.toggleBtnOpacity(!1),o.innerText=p.submitButtonText||'付款并开通',s.amc.fn.show(i),s.amc.fn.hide(a),s.amc.fn.show(this.getViewInComponentById('deductServiceDesc')),'showOnlyPayBtn'===p.pageVersion&&p.onlyPayButtonText){var r=this.getViewInComponentById('direct-pay-btn-area'),c=this.getViewInComponentById('direct-pay-btn');s.amc.fn.show(r),c.innerText=p.onlyPayButtonText,s.amc.fn.spmExposure('a259.b13453.c115488',m)}}else s.amc.fn.hide(t),s.amc.fn.hide(t),s.amc.fn.hide(n),this.toggleBtnOpacity(!1),o.innerText='立即付款',s.amc.fn.show(a),s.amc.fn.hide(this.getViewInComponentById('deductServiceDesc'))},t.prototype.toggleBtnOpacity=function(e){this.getViewInComponentById(this.btnPrimaryWrapperId).style.opacity=e?'0.5':'1'},t.prototype.disableSubmitBtn=function(){s.amc.fn.hide(this.getViewInComponentById('submit-btn')),s.amc.fn.show(this.getViewInComponentById('loading'))},t.prototype.enableSubmitBtn=function(){s.amc.fn.show(this.getViewInComponentById('submit-btn')),f&&(this.getViewInComponentById('submit-btn').style.fontSize='20px'),s.amc.fn.hide(this.getViewInComponentById('loading'))},t.prototype.disablePayBtn=function(){s.amc.fn.hide(this.getViewInComponentById('direct-pay-btn')),s.amc.fn.show(this.getViewInComponentById('direct-pay-loading'))},t.prototype.enablePayBtn=function(){s.amc.fn.show(this.getViewInComponentById('direct-pay-btn')),s.amc.fn.hide(this.getViewInComponentById('direct-pay-loading'))},t.prototype.submitInfo=function(){var e='on'===this.getViewInComponentById(this.deductSwitchId).value;if(this.needOpenHuazhi&&e&&(this.huazhiTplObj={},this.huazhiTplObj.action={},this.huazhiTplObj.action.name='loc:bnvb',this.huazhiTplObj.action.params={tplid:'QUICKPAY@mdeduct-credit-sign-flex',tpl:p.creditSignTpl,data:{needDeductSign:e,halfScreenGuideModel:p.halfScreenGuideModel,nextAction:p.nextAction,params:p.params,huazhiDegradable:p.huazhiDegradable}}),this.needShowConfirmDialog()){e?s.amc.fn.spmClick('a259.b13453.c32495.d64289',m):s.amc.fn.spmClick('a259.b13453.c32495.d64290',m);var t={action:{}};t.action.name='loc:bnvb',t.action.loadtxt='',t.action.params={tplid:p.submitConfirmDialog.tplId,tpl:p.submitConfirmDialog.tpl,data:{isDeduct:e,nextAction:p.nextAction,params:p.params,spmServerParam4:m,huazhiTplObj:this.huazhiTplObj,submitConfirmDialog:p.submitConfirmDialog}},document.submit(t)}else this.doSubmit(e)},t.prototype.doSubmit=function(e){var t=this;if(this.huazhiTplObj)document.submit(this.huazhiTplObj);else{this.disableSubmitBtn(),e?s.amc.fn.spmClick('a259.b13453.c32495.d64289',m):s.amc.fn.spmClick('a259.b13453.c32495.d64290',m);var n={},o={needDeductSign:e};s.amc.fn.sdkGreaterThanOrEqual('10.8.36')&&(o.cashier_can_goback=!0),n.action={},n.action.loadtxt='',n.action.name=p.nextAction||'/cashier/main',n.action.params=p.params||{},n.param=o,document.asyncSubmit(n,function(e){'0'===(e=e||{}).pageloading?t.enableSubmitBtn():'1'===e.pageloading&&t.disableSubmitBtn()})}},t.prototype.onBack=function(){s.amc.fn.spmClick('a259.b13453.c32477.d64253',m);var e='放弃';s.amc.fn.sdkGreaterThanOrEqual('10.8.47')&&!s.amc.isSDK&&(e='<font color=\"#000000\">放弃</font>');var t={title:'',message:'是否放弃本次付款',okButton:'继续付款',cancelButton:e},n=!1;'quitRedemptionOptimize'===p.pageVersion&&p.quitRedemptionDialog&&p.quitRedemptionDialog.content&&p.quitRedemptionDialog.cancelBtnContent&&p.quitRedemptionDialog.submitBtnContent&&(t={title:'',message:p.quitRedemptionDialog.content,okButton:p.quitRedemptionDialog.submitBtnContent,cancelButton:p.quitRedemptionDialog.cancelBtnContent},n=!0,s.amc.fn.spmExposure('a259.b13453.c113394',m),s.amc.fn.spmExposure('a259.b13453.c113394.d234992',m),s.amc.fn.spmExposure('a259.b13453.c113394.d234993',m)),s.amc.fn.spmExposure('a259.b13453.c32627',m,!1),document.confirm(t,function(e){e.ok?n?(s.amc.fn.spmClick('a259.b13453.c113394.d234993',m),u.submitPay(1)):s.amc.fn.spmClick('a259.b13453.c32627.d64615',m):'0'!==e.clickIndex&&'0'!==e.index||(n?s.amc.fn.spmClick('a259.b13453.c113394.d234992',m):s.amc.fn.spmClick('a259.b13453.c32627.d64614',m),s.amc.fn.spmPageDestroy('a259.b13453',m),document.submit({action:{name:'loc:exit'}}))})},t.prototype.createDynamicFrame=function(e){var a=this;if(!s.amc.fn.sdkGreaterThanOrEqual('10.8.38'))return!0;s.amc.fn.logAction('start|deduct-channal','snippet'),l.BNFramePlugin.create(e,this.getViewInComponentById('bnframeBox'),function(e){s.amc.fn.logAction('done|deduct-channal','snippet'),e&&e.mqpToken&&(a.channelSelectBnframeToken=e.mqpToken)}),l.BNFrameChannelOuter.onInnerEvent('ON_SELECT_CHANNEL',function(e,t){s.amc.fn.logAction('onEvent|deduct-channal','snippet');var n=t&&t.mqpToken;if(p.deductChannelSettingBox&&n===a.channelSelectBnframeToken){var o=!1,i=new Array;e.selectedChannels.forEach(function(e,t){i[t]=e.channelIndex,(!u.currentChannelList||u.currentChannelList.indexOf(e.channelIndex)<0)&&(o=!0)}),(o||u.currentChannelList.length!==i.length)&&u.onChannelChanage(i)}})},t.prototype.initBlackMask=function(){document.body&&(this.blackMask=s.amc.fn.create('div','black-mask',document.body),this.blackMask.onclick=function(){})},t.prototype.hideBlackMask=function(){this.blackMask&&s.amc.fn.hide(this.blackMask)},t.prototype.onChannelChanage=function(e){p.deductChannelSettingBox&&p.deductChannelSettingBox.refreshAction&&(p.initPageToken,u.channelContextId,setTimeout(function(){document.invoke('showLoading')},20),document.invoke('rpc',{type:'json',operationType:s.amc.isSDK?'alipay.msp.cashier.dispatch.json.tb':'alipay.msp.cashier.dispatch.json',action:{name:p.deductChannelSettingBox.refreshAction,params:{confirmHuazhiDegrade:'DEDUCT_HUAZHI'===p.creditAuthMode,curChannelIndexList:e,channelContextId:u.channelContextId,bizToken:p.initPageToken,extInfos:{personal_product_code:p.personalProductCode}}}},function(o){setTimeout(function(){if(document.invoke('hideLoading'),o&&o.params&&o.params.data&&o.params.success){if(p.deductChannelSettingBox&&'PAGE'===p.deductChannelSettingBox.dataRefreshType){var e={action:{}};e.action.name='loc:bnvb',e.action.params={tplid:'QUICKPAY@mdeduct-payandsign-flex',tpl:'',data:o.params.data},document.submit(e)}else if(p.deductChannelSettingBox&&'CHANNEL'===p.deductChannelSettingBox.dataRefreshType&&u.vueModel.data.settingBox&&o.params.data.deductChannelSettingBox){var t=o.params.data.deductChannelSettingBox.descr;u.vueModel.data.settingBox.descr=t;var n=g(o.params.data.deductPageOperationInfo);u.createOperationComponents(n,!0),o.params.data.deductChannelSettingBox&&o.params.data.deductChannelSettingBox.curChannelIndexList&&(u.currentChannelList=o.params.data.deductChannelSettingBox.curChannelIndexList,u.channelContextId=o.params.data.deductChannelSettingBox.channelContextId)}}else document.toast({text:'网络开小差，请稍后重试',type:'none'},function(){})},10)}))},t.prototype.needShowConfirmDialog=function(){return!(!(p.submitConfirmDialog&&!0===p.submitConfirmDialog.needConfirm&&p.submitConfirmDialog.title&&p.submitConfirmDialog.content&&p.submitConfirmDialog.submitBtnContent&&p.submitConfirmDialog.cancelBtnContent&&p.submitConfirmDialog.tplId&&p.submitConfirmDialog.tpl)||p.allowUserChooseSign&&(!0!==p.allowUserChooseSign||'on'!==this.getViewInComponentById(this.deductSwitchId).value))},t.getComponentCSSRules=function(){return{'.de-1px-line':'_PayAndSignPage_aoan-c-de-1px-line','.main-body':'_PayAndSignPage_aoan-c-main-body','.cell-box':'_PayAndSignPage_aoan-c-cell-box','.flex-row':'_PayAndSignPage_aoan-c-flex-row','.algin-item-start':'_PayAndSignPage_aoan-c-algin-item-start','.flex-between':'_PayAndSignPage_aoan-c-flex-between','.flex-justify-start':'_PayAndSignPage_aoan-c-flex-justify-start','.flex-justify-end':'_PayAndSignPage_aoan-c-flex-justify-end','.am-message':'_PayAndSignPage_aoan-c-am-message','.am-ft-price':'_PayAndSignPage_aoan-c-am-ft-price','.am-ft-unit':'_PayAndSignPage_aoan-c-am-ft-unit','.am-message-main':'_PayAndSignPage_aoan-c-am-message-main','.message-subject':'_PayAndSignPage_aoan-c-message-subject','.am-message-sub':'_PayAndSignPage_aoan-c-am-message-sub','.merchant-name':'_PayAndSignPage_aoan-c-merchant-name','.am-amount':'_PayAndSignPage_aoan-c-am-amount','.checkbox-ios':'_PayAndSignPage_aoan-c-checkbox-ios','.checkbox-android':'_PayAndSignPage_aoan-c-checkbox-android','.amc-item-m-box':'_PayAndSignPage_aoan-c-amc-item-m-box','.amc-item-m-text':'_PayAndSignPage_aoan-c-amc-item-m-text','.amc-item-r-box':'_PayAndSignPage_aoan-c-amc-item-r-box','.merchant-info-wrapper':'_PayAndSignPage_aoan-c-merchant-info-wrapper','.deduct-pay-setting':'_PayAndSignPage_aoan-c-deduct-pay-setting','.merchant-product-name':'_PayAndSignPage_aoan-c-merchant-product-name','.product-name':'_PayAndSignPage_aoan-c-product-name','.merchant-product-desc':'_PayAndSignPage_aoan-c-merchant-product-desc','.deduct-label-item':'_PayAndSignPage_aoan-c-deduct-label-item','.deduct-label-list-l':'_PayAndSignPage_aoan-c-deduct-label-list-l','.deduct-label-list-no-right-padding':'_PayAndSignPage_aoan-c-deduct-label-list-no-right-padding','.deduct-label-list-no-right-padding-l':'_PayAndSignPage_aoan-c-deduct-label-list-no-right-padding-l','.deduct-label-list-right-padding':'_PayAndSignPage_aoan-c-deduct-label-list-right-padding','.deduct-label':'_PayAndSignPage_aoan-c-deduct-label','.deduct-label-content':'_PayAndSignPage_aoan-c-deduct-label-content','.deduct-label-content-light':'_PayAndSignPage_aoan-c-deduct-label-content-light','.deduct-switch':'_PayAndSignPage_aoan-c-deduct-switch','.de-agreement-box':'_PayAndSignPage_aoan-c-de-agreement-box','.de-agreement-list':'_PayAndSignPage_aoan-c-de-agreement-list','.de-agreement-checkbox':'_PayAndSignPage_aoan-c-de-agreement-checkbox','.de-agreement-links':'_PayAndSignPage_aoan-c-de-agreement-links','.link-text':'_PayAndSignPage_aoan-c-link-text','.btn-primary-wrapper':'_PayAndSignPage_aoan-c-btn-primary-wrapper','.btn-direct-pay-wrapper':'_PayAndSignPage_aoan-c-btn-direct-pay-wrapper','.btn-primary':'_PayAndSignPage_aoan-c-btn-primary','.de-info-dialog-item':'_PayAndSignPage_aoan-c-de-info-dialog-item','.de-dialog-prefix':'_PayAndSignPage_aoan-c-de-dialog-prefix','.de-dialog-content':'_PayAndSignPage_aoan-c-de-dialog-content','.de-dialog-prefix-logo':'_PayAndSignPage_aoan-c-de-dialog-prefix-logo','.deduct-pay-setting-label':'_PayAndSignPage_aoan-c-deduct-pay-setting-label','.huazhi-border':'_PayAndSignPage_aoan-c-huazhi-border','.huazhi-box':'_PayAndSignPage_aoan-c-huazhi-box','.huazhi-text':'_PayAndSignPage_aoan-c-huazhi-text','.main-content':'_PayAndSignPage_aoan-c-main-content','.half-px-line':'_PayAndSignPage_aoan-c-half-px-line','.amc-check-img':'_PayAndSignPage_aoan-c-amc-check-img','.amc-checkbox':'_PayAndSignPage_aoan-c-amc-checkbox','.item-box-for-propagate':'_PayAndSignPage_aoan-c-item-box-for-propagate','.propagate-arrow':'_PayAndSignPage_aoan-c-propagate-arrow','.propagate-icon':'_PayAndSignPage_aoan-c-propagate-icon','.info-text-propagate':'_PayAndSignPage_aoan-c-info-text-propagate','.font-color-primary':'_PayAndSignPage_aoan-c-font-color-primary','.item-btn':'_PayAndSignPage_aoan-c-item-btn','.item-btn-text':'_PayAndSignPage_aoan-c-item-btn-text','.double-line-for-propagate':'_PayAndSignPage_aoan-c-double-line-for-propagate','.item-box-s-for-propagate':'_PayAndSignPage_aoan-c-item-box-s-for-propagate','.bottom-container':'_PayAndSignPage_aoan-c-bottom-container','.slogan-box':'_PayAndSignPage_aoan-c-slogan-box','.slogan-img':'_PayAndSignPage_aoan-c-slogan-img','.total-cost-img':'_PayAndSignPage_aoan-c-total-cost-img','.service-desc-img-box':'_PayAndSignPage_aoan-c-service-desc-img-box','.service-desc-img':'_PayAndSignPage_aoan-c-service-desc-img','.direct-pay-btn-area':'_PayAndSignPage_aoan-c-direct-pay-btn-area','.btn-secondary':'_PayAndSignPage_aoan-c-btn-secondary'}},t.getComponentJson=function(){return{'sp-view-id':'mainBody',_c:'amc-v-box amc-flex-1 amc-scroll amc-bg-white',_t:'div',_cd:[{_c:'amc-v-box _PayAndSignPage_aoan-c-am-message',_t:'div',_cd:[{_c:'_PayAndSignPage_aoan-c-am-message-main',_t:'div',_cd:[{'sp-view-id':'subjectLabel',_c:'_PayAndSignPage_aoan-c-message-subject',_t:'label'}]},{_c:'_PayAndSignPage_aoan-c-am-message-sub',_t:'div',_cd:[{'sp-view-id':'merchantName',_c:'_PayAndSignPage_aoan-c-merchant-name',_t:'label'}]},{'sp-view-id':'am-amount',_c:'_PayAndSignPage_aoan-c-am-amount',_t:'div',_cd:[{'sp-view-id':'totalCostImg',_c:'_PayAndSignPage_aoan-c-total-cost-img',_t:'img'},{'sp-view-id':'orderAmount',_c:'_PayAndSignPage_aoan-c-am-ft-price',_t:'label'}]}]},{'sp-view-id':'main-content',_c:'_PayAndSignPage_aoan-c-main-content',_t:'div',_cd:[{_c:'amc-1px-line amc-margin-lr',_t:'div'},{'sp-view-id':'deductSwitch',_c:'amc-v-box amc-bg-white _PayAndSignPage_aoan-c-merchant-info-wrapper amc-hidden',_t:'div',_cd:[{_c:'_PayAndSignPage_aoan-c-flex-row _PayAndSignPage_aoan-c-flex-between _PayAndSignPage_aoan-c-merchant-product-name',_t:'div',_cd:[{_c:'amc-flex-1',_t:'div',_cd:[{'sp-view-id':'deduct-switch-name',_c:'_PayAndSignPage_aoan-c-product-name amc-flex-1',_t:'label'},{_t:'div',_cd:[{'sp-view-id':'deduct-switch',_c:'_PayAndSignPage_aoan-c-deduct-switch',value:'on',onchange:'onDeductSwitch',_t:'switch'}]}]}]},{_c:'_PayAndSignPage_aoan-c-service-desc-img-box','sp-view-id':'deductServiceDescImgBox',_t:'div',_cd:[{_c:'_PayAndSignPage_aoan-c-service-desc-img','sp-view-id':'deductServiceDescImg',_t:'img'}]}]},{_c:'amc-1px-line amc-margin-lr',_t:'div'},{'sp-view-id':'deduct-label-list',_c:'amc-v-box _PayAndSignPage_aoan-c-deduct-label-list-l',_t:'div'},{'sp-view-id':'deduct-pay-setting',_c:'amc-v-box amc-bg-white _PayAndSignPage_aoan-c-deduct-pay-setting','v-if-cal':'@{settingBoxExist}','v-class':'@{settingBoxClass}',_t:'div',_cd:[{_c:'_PayAndSignPage_aoan-c-flex-row _PayAndSignPage_aoan-c-flex-between _PayAndSignPage_aoan-c-algin-item-start','v-class':'@{settingItemClass}','v-click':'@{settingBoxClick}',_t:'div',_cd:[{'sp-view-id':'deduct-pay-setting-label',_c:'_PayAndSignPage_aoan-c-deduct-pay-setting-label','v-text-cal':'@{settingBoxTip}',_t:'label'},{_c:'_PayAndSignPage_aoan-c-flex-row flex-end algin-item-center',_t:'div',_cd:[{'sp-view-id':'deduct-label-content-light',_c:'_PayAndSignPage_aoan-c-deduct-label-content-light','v-text-cal':'@{settingBoxDescr}',_t:'label'},{_c:'amc-margin-l-xs','v-src-cal':'@{settingBoxTailImg}',_t:'img'}]}]}]},{'sp-view-id':'anchoredBox',_c:'amc-v-box amc-hidden',_t:'div'},{'sp-view-id':'bnframeBox',_c:'amc-v-box amc-hidden',_t:'div'},{'sp-view-id':'de-agreement-list',_c:'_PayAndSignPage_aoan-c-de-agreement-list',_t:'div',_cd:[{'sp-view-id':'de-agreement-links',_c:'_PayAndSignPage_aoan-c-de-agreement-links amc-ellipsis-2-line',_t:'label'}]},{_c:'amc-abs-space-v-l',_t:'div'},{'sp-view-id':'btn-area',_c:'amc-align-center amc-justify-center',_t:'div',_cd:[{'sp-view-id':'btn-primary-wrapper',_c:'_PayAndSignPage_aoan-c-btn-primary-wrapper amc-align-center amc-justify-center amc-bg-blue',_t:'div',_cd:[{'sp-view-id':'loading',src:'indicatior',_c:'amc-loading-img amc-text-white-clolor amc-hidden',_t:'img'},{'sp-view-id':'submit-btn',onclick:'submitInfo',_c:'amc-btn-primary _PayAndSignPage_aoan-c-btn-primary',_t:'button',_x:'同意协议并支付'}]}]},{'sp-view-id':'direct-pay-btn-area',_c:'amc-align-center amc-justify-center _PayAndSignPage_aoan-c-direct-pay-btn-area amc-hidden',_t:'div',_cd:[{'sp-view-id':'btn-direct-pay-wrapper',_c:'_PayAndSignPage_aoan-c-btn-direct-pay-wrapper amc-align-center amc-justify-center amc-bg-white',_t:'div',_cd:[{'sp-view-id':'direct-pay-loading',src:'indicatior',_c:'amc-loading-img amc-hidden',_t:'img'},{'sp-view-id':'direct-pay-btn',onclick:'submitPay',_c:'_PayAndSignPage_aoan-c-btn-secondary',_t:'button'}]}]}]},{_c:'_PayAndSignPage_aoan-c-bottom-container',_t:'div'},{'sp-view-id':'de-agreement-list-bottom',_c:'amc-flex-center amc-v-box _PayAndSignPage_aoan-c-de-agreement-box amc-hidden',_t:'div',_cd:[{'sp-view-id':'de-agreement-links-bottom',_c:'_PayAndSignPage_aoan-c-de-agreement-links amc-ellipsis-2-line',_t:'label'}]},{_c:'_PayAndSignPage_aoan-c-slogan-box amc-flex-center','sp-view-id':'sloganBox',_t:'div',_cd:[{_c:'_PayAndSignPage_aoan-c-slogan-img','sp-view-id':'sloganImg',_t:'img'}]},{_c:'amc-iphone-x-pd-b',_t:'div'}]}},t.componentName='PayAndSignPage',t.componentHashName='PayAndSignPage_aoan',t}(r.BNComponent);function g(e){if(s.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}}t.PayAndSignPage=h,a=s.amc.fn.docConfig;var y=!(s.amc.fn.docConfig=function(){var e=JSON.parse(a());if(e.navi={naviBarColor:'#FFFFFF',statusBarStyle:'dark'},p.deductChannelSettingBox&&p.deductChannelSettingBox.action){var t=g(p.deductChannelSettingBox.action);t&&'loc:openurl'===t.name&&(e.useDTNav='true')}return JSON.stringify(e)});document.viewDidAppear=function(){y?(u.hideBlackMask(),s.amc.fn.spmExposureResume()):y=!0};var v=null;window.loadImageHelper=function(e,t,n){if(!v){v={};var o=document.onImgLoaded;document.onImgLoaded=function(e,t){var n=v[t];n&&n.callback?n.callback(e,t,n.img):o&&o(e,t),delete v[t]}}v[t]={callback:n,img:e},e.src=t},window._onload=function(){p=s.amc.rpcData,m.outTradeNo=p.outTradeNo,m.personalProductCode=p.personalProductCode,m.externalAgreementNo=p.externalAgreementNo,m.salesProductCode=p.salesProductCode,m.userId=p.userId,m.partnerId=p.partnerId,m.requestToken=p.requestToken,m.signScene=p.signScene,u=new h,s.amc.isIOS&&(document.body.style.height=s.amc.isAndroid?window.innerHeight:s.amc.specs.bodyHeight);var e=s.amc.fn.getNav(s.amc.res.navBack,s.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'':'{{return}}','','','',function(){u.onBack()},function(){});s.amc.fn.spmPageCreate('a259.b13453',m),window.onKeyDown=function(){4==event.which&&u.onBack()},document.body.appendChild(e),u.mountTo(document.body),s.amc.fn.spmExposure('a283.b13453.c43928.d89250',m,!1)}},function(e,t,n){'use strict';var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,'__esModule',{value:!0});var C=n(2),w=n(4),S=n(5),P=n(14),o=function(){function e(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=w.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return e.getComponentCSSRules=function(){throw new Error('E0100')},e.getComponentJson=function(){throw new Error('E0101')},e.prototype.mountTo=function(e,t){if(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t):e.appendChild(n),this._triggerOnMounted()):C.logger.e('Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},Object.defineProperty(e.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),e.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},e.prototype.getMountedParentView=function(){var e=this.getMountedRootView();return e?e.parentNode:null},e.prototype.getSubComponentById=function(e,t){var n=this.debugName+'#SCById',o=this._subComponentsMap[e];if(!o)return null;var i='',a='';try{i=o.constructor.componentName,a=t.componentName}catch(e){C.logger.e(n,'E0104 '+e)}return i&&i===a?o:(C.logger.e(n,'E0105 '+i+', '+a),null)},e.prototype.getViewInComponentById=function(e){return this._viewsIdMap[e]},e.prototype.getComponentId=function(){return this._componentId},e.prototype.createStyledElement=function(e,t,n){var o=document.createElement(e);if(o)return e&&(o.className+=' '+this._css(e,2)),n&&(o.className+=' '+this._csses(n,1)),t&&(o.className+=' '+this._css('#'+t,2)),o},e.prototype.applyStyleTo=function(e,t){e&&(e.className+=' '+this._csses(t,1))},e.prototype.css=function(e){return this._css(e,0)},e.prototype.csses=function(e){return this._csses(e,0)},e.prototype._csses=function(e,t){var n=this;return e.split(' ').map(function(e){return n._css(e,t)}).join(' ')},e.prototype._css=function(e,t){if(!e)return'';var n=this._componentCSSRules;if(!n)return e;switch(e.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e;case 2:default:return e}}},e.prototype._triggerOnMounted=function(){new P.Observer(this.vueModel.data),C.logger.i('','I0106 '+this.debugName);for(var e=0,t=this._subComponents;e<t.length;e++){var n=t[e];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},e.prototype._getMethod=function(e){var t=this[e];return t instanceof Function?t:null},e.prototype._acquireComponentJson=function(){var e=this.debugName+'#acCJ',t=S.ComponentRegistry.getComponentJson(this._componentName);return t?(C.logger.i(e,'I0107'),t):void 0!==this._componentJson?(C.logger.i(e,'I0108'),S.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(C.logger.e(e,'E0109'),null)},e.prototype._acquireRootView=function(){var e=this.debugName+'#acRV';if(this._hasRootViewBuilt)return C.logger.i(e,'I0110'),this._rootView;var t=this._acquireComponentJson();return t?(this._rootView=this._convertJsonToBNNode(t,this.vueModel.data||{}),this._hasRootViewBuilt=!0,C.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111'),null)},e.prototype._genArrayChildNode=function(e,t,n,o,i){var a=this._convertJsonToBNNode(e,r({},t,{item:n,index:o,arrayName:i}));return a?(a.setAttribute('index',o),a.setAttribute('for_name',i),a):null},e.prototype._convertJsonToBNNode=function(e,u){var l=this,t=this.debugName+'#cJTB';if(void 0===e._t)return null;var d=document.createElement(e._t),p=[];if(void 0!==e._cd)for(var n=function(r){if(r['v-for']||r['v-for-cal']){var e=!r['v-for']&&!!r['v-for-cal'],t=(e?m.vueModel.compute:u)||{},n=P.vueUtils.getObject(e?r['v-for-cal']:r['v-for'],t,e),c=e?P.vueUtils.rmSymbol(r['v-for-cal']):P.vueUtils.rmSymbol(r['v-for']);if(!c||!n)return'continue';for(var o in r['v-for']='',r['v-for-cal']='',n)if(n.hasOwnProperty(o)){var i=m._genArrayChildNode(r,u,n[o],o,c);i&&p.push(i)}var s=document.createElement('div');s&&(s.style.display='none',s.setAttribute('for_end',c),p.push(s),new P.Watcher(c,t,function(e){if(d){P.rmWatchers(c);for(var t=0,n=d.childNodes;t<n.length;t++){var o=n[t];o.getAttribute('for_name')===c&&d.removeChild(o)}if(e)for(var i in e)if(e.hasOwnProperty(i)){var a=l._genArrayChildNode(r,u,e[i],i,c);a&&d.insertBefore(a,s)}}},e).id=u.arrayName)}else{var a=m._convertJsonToBNNode(r,u);if(!a)return'continue';p.push(a)}},m=this,o=0,i=e._cd;o<i.length;o++)n(i[o]);if(!d)return null;u&&u.index&&d.setAttribute('index',u.index);var a=e['bn-component']||e['sp-component'];if(a){C.logger.i(t,'I0113 '+a);var r=S.ComponentRegistry.createComponent(a);if(!r)return C.logger.e(t,'E0114 '+a+', '+r),null;var c=e['bn-component-id']||e['sp-component-id'];return c&&(r._componentId=c),r.onCreated&&r.onCreated(),C.logger.i(t,'I0115 '+r.debugName+', '+c),this._subComponents.push(r),c&&!this._subComponentsMap[c]&&(this._subComponentsMap[c]=r),r._acquireRootView()}var s=e['bn-view-id']||e['sp-view-id'];for(var f in s&&(C.logger.i(t,'I0116 '+s),this._viewsIdMap[s]||(this._viewsIdMap[s]=d)),e._i&&(d.id=e._i),e._c&&(d.className=e._c),e._s&&(d.style.cssText=e._s),e._x&&(d.innerText=e._x),e._y&&(d.type=e._y),e)if(e.hasOwnProperty(f))if(0===f.indexOf('on')){var h=this._getMethod(e[f]);h&&(d[f]=h.bind(this,d))}else if(0===f.indexOf('_'));else if(0===f.indexOf('bn-')||0===f.indexOf('sp-'));else if(w.startsWith(f,'v-')){var g=f.split('-');if(2===g.length||3===g.length){var y=g[1];2===g.length?new P.NodeCompile(u).compile(y,d,e[f],e._t):'cal'===g[2]&&new P.NodeCompile(this.vueModel.compute,!0).compile(y,d,e[f],e._t)}else d[f]=e[f]}else d[f]=e[f];for(var v=0,_=p;v<_.length;v++){var b=_[v];d.appendChild(b)}return d},e.componentName='',e.componentHTML='',e.componentCSS='',e.componentHashName='',e}();t.BNComponent=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var a,r=n(4),c=n(1);t.rmWatchers=function(t){a=a.filter(function(e){return e.id!==t})};var s=function(){function e(e,t,n,o){if(this.id='',this.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,this.callback=n,r.startsWith(e,'item')&&t.arrayName&&t.index){var i=e.replace('item','');this.expression=t.arrayName+'.'+t.index,i&&(this.expression+=i)}else this.expression=e;this.data=t,this.value=l.getVal(e,t,this.lazy),a||(a=[]),a.push(this)}}return e.prototype.update=function(){if(this.data&&this.expression&&this.callback){var e=l.getVal(this.expression,this.data,this.lazy),t=this.value;l.equals(e,t)||(this.value=e,this.callback(e))}},e}();t.Watcher=s;var o=function(){function e(e){this.observe(e)}return e.prototype.observe=function(t){var n=this;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){try{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})},e.prototype.defineReactive=function(e,t,n){var o=this;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){l.equals(e,n)||(n=e,o.observe(e),a&&a.forEach(function(e){e.update()}))}})},e}();t.Observer=o;var i=function(){function e(e,t){void 0===t&&(t=!1),this.data=e||{},this.lazy=t}return e.prototype.compile=function(n,e,t,o){var i=this;if(e)switch(n){case'text':this.labelProcess(e,t,function(e,t){e.innerText=void 0===t?'':t});break;case'html':this.labelProcess(e,t,function(e,t){e.innerHtml=void 0===t?'':t});break;case'class':this.labelProcess(e,t,function(e,t){var n=e.className,o=(n=n.replace(t,'').replace(/\\s$/,''))&&String(t)?' ':'';e.className=n+o+t});break;case'model':this.eventProcess(e,t,function(e,t){e.value=t}),'input'===o?e.oninput=function(){l.setTextVal(t,e.value,i.data)}:'switch'===o&&(e.onchange=function(e){l.setTextVal(t,e||'off',i.data)});break;case'if':this.eventProcess(e,t,function(e,t){!0===t?(e.style.display='flex',u.process(e,function(e){c.amc.fn.spmExposure(e.spmId,e.param4Map,e.doNotResume)})):e.style.display='none'});break;case'spm':this.labelProcess(e,t,function(e,t){e.setAttribute('spm',void 0===t?'':t)});break;case'click':this.eventProcess(e,t,function(e,t){l.isFunction(t)?e.onclick=function(){t(e),u.process(e,function(e){c.amc.fn.spmClick(e.spmId,e.param4Map)})}:e.onclick=function(){}});break;default:this.labelProcess(e,t,function(e,t){e[n]=void 0===t?'':t})}},e.prototype.labelProcess=function(n,o,i){var a=this,e=o.match(/@\\{([^}]+)\\}/g),t=l.getTextVal(o,this.data,this.lazy);e&&e.forEach(function(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(new s(t[1],a.data,function(e){i(n,l.getTextVal(o,a.data,a.lazy))},a.lazy).id=a.data.arrayName)}),i(n,t)},e.prototype.eventProcess=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(e),i=l.getObject(e,this.data,this.lazy);o&&1<o.length&&(new s(o[1],this.data,function(e){n(t,e)},this.lazy).id=this.data.arrayName),n(t,i)},e}();t.NodeCompile=i;var u=function(){function e(){}return e.process=function(e,t){var n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&t(o)}catch(e){}},e}(),l=function(){function c(){}return c.item2ArrayIndex=function(e,t){var n=e;if(r.startsWith(e,'item')&&t.arrayName&&t.index){var o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(n+=o)}return n},c.getVal=function(e,t,n){if(e){var o=e.split('.').reduce(function(e,t){return e[t]},t);return n?c.isFunction(o)?o():void 0:o}},c.getTextVal=function(e,i,a){var r=this;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(a)e=r.getVal(t[1],i,a);else{var o=c.item2ArrayIndex(t[1],i);e=r.getVal(o,i,!1)}return void 0===e?'':e})},c.getObject=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length)return this.getVal(o[1],t,n)},c.rmSymbol=function(e){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''},c.setVal=function(e,o,t){var i=e.split('.');return i.reduce(function(e,t,n){return n===i.length-1?e[t]=o:e[t]},t)},c.setTextVal=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(o[1],t,n)},c.equals=function(e,t){return this.eq(e,t,void 0,void 0)},c.eq=function(e,t,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var i=toString.call(e);if(i!==toString.call(t))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+e==''+t;case'[object Number]':return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case'[object Date]':case'[object Boolean]':return+e==+t}var a='[object Array]'===i;if(!a){if('object'!=typeof e||'object'!=typeof t)return!1;var r=e.constructor,c=t.constructor;if(r!==c&&!(this.isFunction(r)&&r instanceof r&&this.isFunction(c)&&c instanceof c)&&'constructor'in e&&'constructor'in t)return!1}o=o||[];for(var s=(n=n||[]).length;s--;)if(n[s]===e)return o[s]===t;if(n.push(e),o.push(t),a){if((s=e.length)!==t.length)return!1;for(;s--;)if(!this.eq(e[s],t[s],n,o))return!1}else{var u=Object.keys(e),l=void 0;if(s=u.length,Object.keys(t).length!==s)return!1;for(;s--;)if(l=u[s],!t.hasOwnProperty(l)||!this.eq(e[l],t[l],n,o))return!1}return n.pop(),o.pop(),!0},c.isFunction=function(e){return'function'==typeof e||!1},c}();t.vueUtils=l},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var a=function(){function a(){this.enabled=!0}return a.fmtLine=function(e,t,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+e+']['+a.fmtTime()+']['+t+']'+n+' '+i},a.fmtTime=function(){var e=new Date;return e.getHours()+':'+e.getMinutes()+':'+e.getSeconds()+'.'+e.getMilliseconds()},a.prototype.enable=function(){this.enabled=!0},a.prototype.disable=function(){this.enabled=!1},a}();t.Logger=a,t.logger=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.e=function(e,t,n){},t.prototype.i=function(e,t,n){},t}(a))},function(e,n,t){'use strict';function o(e){for(var t in e)n.hasOwnProperty(t)||(n[t]=e[t])}Object.defineProperty(n,'__esModule',{value:!0}),o(t(6)),o(t(7))},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var r=n(1);t.mergeObject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(e&&e.length)for(var o=0;o<e.length;o++){var i=e[o];if(r.amc.fn.isObject(i))for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])}return n},t.isPreRender=function(e){return e&&(e.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender)},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},t.doNothing=function(){},t.tryJSONParse=function(e){if(r.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}},t.checkEmptyObj=function(e){return r.amc.fn.isString(e)?0===e.length:!(e&&0!==Object.keys(e).length)},t.substrWithFontWidth=function(e,t,n){if(!e)return e;for(var o='',i=0,a=e.length,r=0;r<a;r++){var c=n?e[a-r-1]:e[r];if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break}return o},t.calculateFontWidth=function(e){if(!e)return 0;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++)n.test(e[o])?t+=.45:t++;return Math.round(t)},t.deepCopy=function e(t){if(null==t||'object'!=typeof t)return t;var n;if(t instanceof Date)return(n=new Date).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(var o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(t instanceof Object){for(var a in n={},t)t.hasOwnProperty(a)&&(n[a]=e(t[a]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},t.getConfig=function(e,t){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:e},function(e){t(e.available)})},20)},t.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},t.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)}},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var a=n(3),_=n(0),b=n(22),C=n(29),r=function(n){function e(e){var t=n.call(this)||this;return t.hideOperationBox=!1,t.onCreated=function(){},t.onMounted=function(){t.deductOperationContainer=t.getViewInComponentById('deductOperationContainer'),t.render()},t.props=e,t.propagate=b.tryJSONParse(t.props.operationData),t.propagate&&t.propagate.title&&t.propagate.displayType&&'recommend'===t.propagate.displayType||(t.hideOperationBox=!0),t}return i(e,n),e.prototype.render=function(){if(C.BNIDELog.prodLog('rendDeductOperationComponent',this.props.operationData),!this.hideOperationBox){var e=this.props.spmInfo.spmObj||{},t=b.tryJSONParse(this.propagate.spmObj);for(var n in t)e[n]=t[n];_.amc.fn.spmExposure(this.props.spmInfo.componentSpmID,e,!1);var o=this.createStyledElement('div','propagateBox','amc-flex-1 amc-v-box');this.deductOperationContainer.appendChild(o);var i=this.propagate.actTitle,a=this.createStyledElement('div','','amc-flex-1 amc-align-center');if(o.appendChild(a),'common'===this.props.operationType){this.applyStyleTo(a,'item-box-for-propagate');var r=this.createStyledElement('div','','amc-1px-line');o.appendChild(r)}if(this.props.isPayTool){var c=this.createStyledElement('img','','propagate-arrow');o.appendChild(c),c.contentMode='center',c.src=_.amc.path+'alipay_msp_propagate_arrow'}var s=this.propagate.channelLogo,u=this.propagate.channelLogoH5;if('money_fund'===s||'ant_credit_pay'===s||this.propagate.showIcon){var l=this.createStyledElement('img','','propagate-icon');'channel'===this.props.operationType&&(this.applyStyleTo(l,'channel-propagate'),_.amc.isAndroid?l.style.marginTop='3px':l.style.marginTop='1px'),a.appendChild(l),l.src='money_fund'===s?_.amc.path+'alipay_msp_moneyfund_logo':'ant_credit_pay'===s?_.amc.path+'alipay_msp_pcredit_logo':u||_.amc.path+'alipay_msp_icon_notice'}var d=this.propagate.action?a:null,p=this.createStyledElement('div','','amc-flex-1'),m=this.createStyledElement('label','','amc-flex-1 info-text-propagate font-color-primary');if(p.appendChild(m),a.appendChild(p),i&&this.props.clickCallback){var f=this.createStyledElement('div','','item-btn amc-flex-center');'channel'===this.props.operationType&&this.applyStyleTo(f,'channel-propagate'),f.style.cssText='border: 0.5px solid '+b.getThemeColor()+';',a.appendChild(f);var h=this.createStyledElement('label','','item-btn-text amc-ellipsis amc-theme-color');f.appendChild(h),h.innerText=i,this.props.spmInfo.actBtnSpmID&&_.amc.fn.spmExposure(this.props.spmInfo.actBtnSpmID,e,!1),this.props.bigFontVersion&&(h.style.fontSize='16px'),f.onmousedown=function(){f.style.backgroundColor=b.getThemeColor(),h.style.color='#fff'},f.onmouseup=function(){f.style.backgroundColor='#fff',h.style.color=b.getThemeColor()},d=f}if(m&&('channel'===this.props.operationType?(this.applyStyleTo(m,'info-text-channel-propagate'),this.applyStyleTo(m,'amc-ellipsis-2-line'),this.applyStyleTo(this.deductOperationContainer,'info-text-channel-propagate')):'common'===this.props.operationType&&this.applyStyleTo(m,'amc-ellipsis'),m.innerText=this.propagate.title,_.amc.fn.spmExposure(this.props.spmInfo.contentSpmID,e,!1)),this.props.bigFontVersion&&('channel'===this.props.operationType?m.style.fontSize='18px':'common'===this.props.operationType&&(m.style.fontSize='16px')),d){var g=this.propagate,y=this.props.clickCallback,v=this.props.spmInfo.actBtnSpmID;d.onclick=function(){v&&_.amc.fn.spmClick(v,e),g.param&&g.param.channelIndex&&y&&y([g.param.channelIndex])}}}},e.prototype.needToHideOperationBox=function(){return this.hideOperationBox},e.getComponentCSSRules=function(){return{'.item-box-for-propagate':'_DeductOperationComponent_hww0-c-item-box-for-propagate','.propagate-arrow':'_DeductOperationComponent_hww0-c-propagate-arrow','.propagate-icon':'_DeductOperationComponent_hww0-c-propagate-icon','.channel-propagate':'_DeductOperationComponent_hww0-c-channel-propagate','.info-text-propagate':'_DeductOperationComponent_hww0-c-info-text-propagate','.info-text-channel-propagate':'_DeductOperationComponent_hww0-c-info-text-channel-propagate','.font-color-primary':'_DeductOperationComponent_hww0-c-font-color-primary','.item-btn':'_DeductOperationComponent_hww0-c-item-btn','.item-btn-text':'_DeductOperationComponent_hww0-c-item-btn-text','.double-line-for-propagate':'_DeductOperationComponent_hww0-c-double-line-for-propagate','.item-box-s-for-propagate':'_DeductOperationComponent_hww0-c-item-box-s-for-propagate'}},e.getComponentJson=function(){return{'bn-view-id':'deductOperationContainer',_c:'amc-flex-1 amc-align-center',_t:'div'}},e.componentName='DeductOperationComponent',e.componentHashName='DeductOperationComponent_hww0',e}(a.BNComponent);t.DeductOperationComponent=r},function(e,t,n){'use strict';var c=this&&this.__assign||function(){return(c=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,'__esModule',{value:!0});var C=n(3),w=n(9),S=n(10),P=n(20),o=function(){function e(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=w.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return e.getComponentCSSRules=function(){throw new Error('E0100')},e.getComponentJson=function(){throw new Error('E0101')},e.prototype.mountTo=function(e,t){if(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t):e.appendChild(n),this._triggerOnMounted()):C.logger.e('Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},Object.defineProperty(e.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),e.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},e.prototype.getMountedParentView=function(){var e=this.getMountedRootView();return e?e.parentNode:null},e.prototype.getSubComponentById=function(e,t){var n=this.debugName+'#SCById',o=this._subComponentsMap[e];if(!o)return null;var i='',a='';try{i=o.constructor.componentName,a=t.componentName}catch(e){C.logger.e(n,'E0104 '+e)}return i&&i===a?o:(C.logger.e(n,'E0105 '+i+', '+a),null)},e.prototype.getViewInComponentById=function(e){return this._viewsIdMap[e]},e.prototype.getComponentId=function(){return this._componentId},e.prototype.createStyledElement=function(e,t,n){var o=document.createElement(e);if(o)return e&&(o.className+=' '+this._css(e,2)),n&&(o.className+=' '+this._csses(n,1)),t&&(o.className+=' '+this._css('#'+t,2)),o},e.prototype.applyStyleTo=function(e,t){e&&(e.className+=' '+this._csses(t,1))},e.prototype.css=function(e){return this._css(e,0)},e.prototype.csses=function(e){return this._csses(e,0)},e.prototype._csses=function(e,t){var n=this;return e.split(' ').map(function(e){return n._css(e,t)}).join(' ')},e.prototype._css=function(e,t){if(!e)return'';var n=this._componentCSSRules;if(!n)return e;switch(e.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e;case 2:default:return e}}},e.prototype._triggerOnMounted=function(){new P.Observer(this.vueModel.data),C.logger.i('','I0106 '+this.debugName);for(var e=0,t=this._subComponents;e<t.length;e++){var n=t[e];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},e.prototype._getMethod=function(e){var t=this[e];return t instanceof Function?t:null},e.prototype._acquireComponentJson=function(){var e=this.debugName+'#acCJ',t=S.ComponentRegistry.getComponentJson(this._componentName);return t?(C.logger.i(e,'I0107'),t):void 0!==this._componentJson?(C.logger.i(e,'I0108'),S.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(C.logger.e(e,'E0109'),null)},e.prototype._acquireRootView=function(){var e=this.debugName+'#acRV';if(this._hasRootViewBuilt)return C.logger.i(e,'I0110'),this._rootView;var t=this._acquireComponentJson();return t?(this._rootView=this._convertJsonToBNNode(t,this.vueModel.data||{}),this._hasRootViewBuilt=!0,C.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111'),null)},e.prototype._genArrayChildNode=function(e,t,n,o,i){var a=P.vueUtils.item2ArrayIndex(i,t),r=this._convertJsonToBNNode(e,c({},t,{item:n,index:o,arrayName:a}));return r?(r.setAttribute('index',o),r.setAttribute('for_name',i),r):null},e.prototype._convertJsonToBNNode=function(e,u){var l=this,t=this.debugName+'#cJTB';if(void 0===e._t)return null;var d=document.createElement(e._t),p=[];if(void 0!==e._cd)for(var n=function(r){if(r['v-for']||r['v-for-cal']){var e=!r['v-for']&&!!r['v-for-cal'],t=(e?m.vueModel.compute:u)||{},n=P.vueUtils.getObject(e?r['v-for-cal']:r['v-for'],t,e),c=e?P.vueUtils.rmSymbol(r['v-for-cal']):P.vueUtils.rmSymbol(r['v-for']);if(!c||!n)return'continue';for(var o in n)if(n.hasOwnProperty(o)){var i=m._genArrayChildNode(r,u,n[o],o,c);i&&p.push(i)}var s=document.createElement('div');s&&(s.style.display='none',s.setAttribute('for_end',c),p.push(s),new P.Watcher(c,t,function(e){if(d){P.rmWatchers(c);for(var t=0,n=d.childNodes;t<n.length;t++){var o=n[t];o.getAttribute('for_name')===c&&d.removeChild(o)}if(e)for(var i in e)if(e.hasOwnProperty(i)){var a=l._genArrayChildNode(r,u,e[i],i,c);a&&d.insertBefore(a,s)}}},e).id=u.arrayName)}else{var a=m._convertJsonToBNNode(r,u);if(!a)return'continue';p.push(a)}},m=this,o=0,i=e._cd;o<i.length;o++)n(i[o]);if(!d)return null;u&&u.index&&d.setAttribute('index',u.index);var a=e['bn-component']||e['sp-component'];if(a){C.logger.i(t,'I0113 '+a);var r=S.ComponentRegistry.createComponent(a);if(!r)return C.logger.e(t,'E0114 '+a+', '+r),null;var c=e['bn-component-id']||e['sp-component-id'];return c&&(r._componentId=c),r.onCreated&&r.onCreated(),C.logger.i(t,'I0115 '+r.debugName+', '+c),this._subComponents.push(r),c&&!this._subComponentsMap[c]&&(this._subComponentsMap[c]=r),r._acquireRootView()}var s=e['bn-view-id']||e['sp-view-id'];for(var f in s&&(C.logger.i(t,'I0116 '+s),this._viewsIdMap[s]||(this._viewsIdMap[s]=d)),e._i&&(d.id=e._i),e._c&&(d.className=e._c),e._s&&(d.style.cssText=e._s),e._x&&(d.innerText=e._x),e._y&&(d.type=e._y),e)if(e.hasOwnProperty(f))if(0===f.indexOf('on')){var h=this._getMethod(e[f]);h&&(d[f]=h.bind(this,d))}else if(0===f.indexOf('_'));else if(0===f.indexOf('bn-')||0===f.indexOf('sp-'));else if(w.startsWith(f,'v-')){var g=f.split('-');if(2===g.length||3===g.length){var y=g[1];2===g.length?new P.NodeCompile(u).compile(y,d,e[f],e._t):'cal'===g[2]&&new P.NodeCompile(this.vueModel.compute,!0).compile(y,d,e[f],e._t)}else d[f]=e[f]}else d[f]=e[f];for(var v=0,_=p;v<_.length;v++){var b=_[v];d.appendChild(b)}return d},e.componentName='',e.componentHTML='',e.componentCSS='',e.componentHashName='',e}();t.BNComponent=o},function(module,exports,__webpack_require__){'use strict';Object.defineProperty(exports,'__esModule',{value:!0});var util_1=__webpack_require__(9),amc_types_1=__webpack_require__(0),watchers;function notify(){watchers&&watchers.forEach(function(e){e.update()})}function rmWatchers(t){watchers=watchers.filter(function(e){return e.id!==t})}exports.rmWatchers=rmWatchers;var Watcher=function(){function e(e,t,n,o){if(this.id='',this.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,this.callback=n,util_1.startsWith(e,'item')&&t.arrayName&&t.index){var i=e.replace('item','');this.expression=t.arrayName+'.'+t.index,i&&(this.expression+=i)}else this.expression=e;this.data=t,this.value=vueUtils.getVal(e,t,this.lazy),watchers||(watchers=[]),watchers.push(this)}}return e.prototype.update=function(){if(this.data&&this.expression&&this.callback){var e=vueUtils.getVal(this.expression,this.data,this.lazy),t=this.value;vueUtils.equals(e,t)||(this.value=e,this.callback(e))}},e}();exports.Watcher=Watcher;var Observer=function(){function e(e){this.observe(e)}return e.prototype.observe=function(t){var n=this;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){try{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})},e.prototype.defineReactive=function(e,t,n){var o=this;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){vueUtils.equals(e,n)||(n=e,o.observe(e),notify())}})},e}();exports.Observer=Observer;var NodeCompile=function(){function e(e,t){void 0===t&&(t=!1),this.data=e||{},this.lazy=t}return e.prototype.compile=function(n,e,t,o){var i=this;if(e)switch(n){case'text':this.labelProcess(e,t,function(e,t){e.innerText=void 0===t?'':t});break;case'html':this.labelProcess(e,t,function(e,t){e.innerHtml=void 0===t?'':t});break;case'class':this.labelProcess(e,t,function(e,t){var n=e.className,o=e.getAttribute('v-class-name')||'';n=n&&n.replace(o,'').replace(/\\s+$/,''),e.setAttribute('v-class-name',t),e.className=n?n+' '+t:t});break;case'style':this.eventProcess(e,t,function(e,t){var n=util_1.tryJSONParse(t);util_1.copyObj(n,e.style)});break;case'model':this.eventProcess(e,t,function(e,t){e.value=t}),'input'===o?e.oninput=function(){vueUtils.setTextVal(t,e.value,i.data)}:'switch'===o&&(e.onchange=function(e){vueUtils.setTextVal(t,e||'off',i.data)});break;case'if':this.eventProcess(e,t,function(e,t){!0===t?(e.style.display='flex',spmUtils.process(e,function(e){amc_types_1.amc.fn.spmExposure(e.spmId,e.param4Map,e.doNotResume)})):e.style.display='none'});break;case'spm':this.labelProcess(e,t,function(e,t){e.setAttribute('spm',void 0===t?'':t)});break;case'uep':this.labelProcess(e,t,function(e,t){t&&util_1.startsWith(t,'a283')&&e.setAttribute('behaviorInfo',JSON.stringify({spm:t,bizCode:'pay',extParam:{}}))});break;case'click':this.eventProcess(e,t,function(e,t){vueUtils.isFunction(t)?e.onclick=function(){t(e),spmUtils.process(e,function(e){amc_types_1.amc.fn.spmClick(e.spmId,e.param4Map)})}:e.onclick=function(){}});break;case'focus':this.eventProcess(e,t,function(e,t){vueUtils.isFunction(t)?e.onfocus=function(){t(e)}:e.onfocus=function(){}});break;case'blur':this.eventProcess(e,t,function(e,t){vueUtils.isFunction(t)?e.onblur=function(){t(e)}:e.onfocus=function(){}});break;default:-1===t.indexOf('@{')?e.setAttribute(n,t):this.labelProcess(e,t,function(e,t){e.setAttribute(n,void 0===t?'':t)})}},e.prototype.labelProcess=function(n,o,i){var a=this,e=o.match(/@\\{([^}]+)\\}/g),t=o;e&&0<e.length&&(t=vueUtils.getTextVal(o,this.data,this.lazy),e&&e.forEach(function(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(new Watcher(t[1],a.data,function(e){i(n,vueUtils.getTextVal(o,a.data,a.lazy))},a.lazy).id=a.data.arrayName)})),i(n,t)},e.prototype.eventProcess=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(e),i=vueUtils.getObject(e,this.data,this.lazy);o&&1<o.length&&(new Watcher(o[1],this.data,function(e){n(t,e)},this.lazy).id=this.data.arrayName),n(t,i)},e}();exports.NodeCompile=NodeCompile;var spmUtils=function(){function e(){}return e.process=function(e,t){var n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&t(o)}catch(e){}},e}(),vueUtils=function(){function vueUtils(){}return vueUtils.item2ArrayIndex=function(e,t){var n=e;if(util_1.startsWith(e,'item')&&t.arrayName&&t.index){var o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(n+=o)}return n},vueUtils.getVal=function(expr,data,lazy){if(expr){var values=expr.match(/##([^#]+)##/g);if(values&&0<values.length){for(var func_1=expr,index=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(/##([^#]+)##/,'vueArgs['+index+']'),index++;for(var _vueArgs=[],i=0;i<index;i++)_vueArgs.push(this.getRealVal(values[i].replace(/##/g,''),data,lazy));return function(vueArgs){return eval(func_1)}(_vueArgs)}return this.getRealVal(expr,data,lazy)}},vueUtils.getRealVal=function(e,t,n){if(e){var o=e.split('.'),i=n&&vueUtils.isFunction(t[o[0]])?t[o[0]]():t;return(n?o.slice(1):o).reduce(function(e,t){return e[t]},i)}},vueUtils.getTextVal=function(e,i,a){var r=this;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(a)e=r.getVal(t[1],i,a);else{var o=vueUtils.item2ArrayIndex(t[1],i);e=r.getVal(o,i,!1)}return void 0===e?'':e})},vueUtils.getObject=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length)return this.getVal(o[1],t,n)},vueUtils.rmSymbol=function(e){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''},vueUtils.setVal=function(e,o,t){var i=e.split('.');return i.reduce(function(e,t,n){return n===i.length-1?e[t]=o:e[t]},t)},vueUtils.setTextVal=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(o[1],t,n)},vueUtils.equals=function(e,t){return this.eq(e,t,void 0,void 0)},vueUtils.eq=function(e,t,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var i=toString.call(e);if(i!==toString.call(t))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+e==''+t;case'[object Number]':return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case'[object Date]':case'[object Boolean]':return+e==+t}var a='[object Array]'===i;if(!a){if('object'!=typeof e||'object'!=typeof t)return!1;var r=e.constructor,c=t.constructor;if(r!==c&&!(this.isFunction(r)&&r instanceof r&&this.isFunction(c)&&c instanceof c)&&'constructor'in e&&'constructor'in t)return!1}o=o||[];for(var s=(n=n||[]).length;s--;)if(n[s]===e)return o[s]===t;if(n.push(e),o.push(t),a){if((s=e.length)!==t.length)return!1;for(;s--;)if(!this.eq(e[s],t[s],n,o))return!1}else{var u=Object.keys(e),l=void 0;if(s=u.length,Object.keys(t).length!==s)return!1;for(;s--;)if(l=u[s],!t.hasOwnProperty(l)||!this.eq(e[l],t[l],n,o))return!1}return n.pop(),o.pop(),!0},vueUtils.isFunction=function(e){return'function'==typeof e||!1},vueUtils}();exports.vueUtils=vueUtils},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var a=function(){function a(){this.enabled=!0}return a.fmtLine=function(e,t,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+e+']['+a.fmtTime()+']['+t+']'+n+' '+i},a.fmtTime=function(){var e=new Date;return e.getHours()+':'+e.getMinutes()+':'+e.getSeconds()+'.'+e.getMilliseconds()},a.prototype.enable=function(){this.enabled=!0},a.prototype.disable=function(){this.enabled=!1},a}();t.Logger=a,t.logger=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.e=function(e,t,n){},t.prototype.i=function(e,t,n){},t}(a))},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(11);t.mergeObject=o.mergeObject,t.isFunction=o.isFunction,t.isPreRender=o.isPreRender,t.copyObj=o.copyObj,t.doNothing=o.doNothing,t.tryJSONParse=o.tryJSONParse,t.checkEmptyObj=o.checkEmptyObj,t.substrWithFontWidth=o.substrWithFontWidth,t.calculateFontWidth=o.calculateFontWidth,t.deepCopy=o.deepCopy,t.getConfig=o.getConfig,t.showLoading=o.showLoading,t.hideLoading=o.hideLoading;var i=n(23);t.VI_CHANNEL_MODE_FROM_TEMPLATE=i.VI_CHANNEL_MODE_FROM_TEMPLATE,t.SCALE_FACTOR=i.SCALE_FACTOR;var a=n(24);t.modifyElementStyle=a.modifyElementStyle,t.modifyElementAttribute=a.modifyElementAttribute,t.modifyElementClass=a.modifyElementClass,t.visibleElement=a.visibleElement,t.modifyElementCSS=a.modifyElementCSS,t.createEmbedViPlugin=a.createEmbedViPlugin,t.getThemeColor=a.getThemeColor,t.createEmbedPlugin=a.createEmbedPlugin;var r=n(25);t.logAction=r.logAction;var c=n(26);t.stEscape=c.stEscape,t.toString=c.toString,t.STPerf=c.STPerf,t.STAct=c.STAct,t.STRecord=c.STRecord;var s=n(27);t.ImageLoader=s.ImageLoader;var u=n(28);t.ocr=u.ocr},function(e,t,n){'use strict';var o;Object.defineProperty(t,'__esModule',{value:!0}),t.VI_CHANNEL_MODE_FROM_TEMPLATE='1',(o=t.SCALE_FACTOR||(t.SCALE_FACTOR={})).LEVEL_0='0',o.LEVEL_1='1',o.LEVEL_2='2',o.LEVEL_3='3',o.LEVEL_4='4'},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o,r=n(0),i=n(11);function a(e,t,n,o){var i;if(r.amc.isAndroid)i=document.createElement('embed',t,function(){});else for(var a in i=document.createElement('embed'),t)t.hasOwnProperty(a)&&(i[a]=t[a]);return n&&(i.className=n),o?e.insertBefore(i,o):e.appendChild(i),i}t.modifyElementStyle=function(e,t,n){var o=t;r.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&i.copyObj(n,o.style)},t.modifyElementAttribute=function(e,t,n){if(t&&n){var o=t;if(r.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o)for(var i in n)n.hasOwnProperty(i)&&(o[i]=n[i])}},t.modifyElementClass=function(e,t,n,o){var i=t;r.amc.fn.isString(t)&&(i=e.getViewInComponentById(t)),i&&(o||(i.className=''),e.applyStyleTo(i,n))},t.visibleElement=function(e,t,n){var o;void 0===n&&(n=!0),t&&(o=r.amc.fn.isString(t)?e.getViewInComponentById(t):t)&&(n?r.amc.fn.show(o):r.amc.fn.hide(o))},t.modifyElementCSS=function(e,t,n){if(t){var o=t;r.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&n&&(o.style.cssText=n)}},t.createEmbedViPlugin=function(e,t,n,o){return a(e,t,n,o)},t.createEmbedPlugin=a,t.getThemeColor=(o='',function(){return o||(o=r.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'#1677FF':'#108EE9'),o})},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(0);t.logAction=function(e,t){window.pageId||(window.pageId='|'+Math.random().toString(36).substr(2,3)),t=t?t+window.pageId:window.pageId,o.amc.fn.logAction(e,t)}},function(e,t,n){'use strict';function o(e){for(var t=[],n=0;n<e.length;n+=1)t.push(e[n]||'-');return t.join('\\'')}Object.defineProperty(t,'__esModule',{value:!0}),t.stEscape=function(e){return e&&e.replace('\\'','%27').replace('`','%60').replace('#','%23')},t.toString=o;var i=function(){function e(e,t,n){this.prefs=[],this.initTime=Date.now(),this.cache={},this.submited=!1,this.record=e,this.prefs[0]=t,this.prefs[1]=n,this.prefs[2]=String(this.initTime),this.prefs[7]='',this.record.addSTPref(this)}return e.prototype.toString=function(){return o(this.prefs)},e.prototype.putCache=function(e,t){this.cache[e]=t},e.prototype.getCache=function(e,t){return this.cache[e]||t},e.prototype.isSubmited=function(){return this.submited},e.prototype.submit=function(){this.isSubmited()||(this.submited=!0,this.submitInner())},e.prototype.submitInner=function(){},e}();t.STPerf=i;var a=function(){function e(e,t,n){this.acts=[],this.record=e,this.acts[0]=t,this.acts[1]=n,this.acts[6]=String(Date.now()),this.record.addSTAct(this)}return e.prototype.setActName=function(e){this.acts[2]=e},e.prototype.toString=function(){return o(this.acts)},e}();t.STAct=a;var r=function(){function e(e,t,n,o,i,a){this.ids=[],this.prefs=[],this.acts=[],this.initTime=Date.now(),this.ids[0]=String(this.initTime),this.ids[1]=e,this.ids[2]=t,this.ids[3]=n,this.ids[4]=o,this.ids[5]=i,this.ids[7]='',this.logHandle=a}return e.prototype.addSTAct=function(e){this.acts.push(e)},e.prototype.addSTPref=function(e){this.prefs.push(e)},e.prototype.submit=function(e){var t=this.toString(e);this.logHandle(t)},e.prototype.toString=function(t){for(var e=[],n=0;n<this.ids.length;n+=1)e.push(this.ids[n]||'-');var o=[];for(n=0;n<this.acts.length;n+=1)o.push(this.acts[n].toString());this.acts=[];var i=[];return this.prefs=this.prefs.filter(function(e){return t&&e.submit(),!e.isSubmited()||(i.push(e.toString()),!1)}),e.join('\\'')+'#'+o.join('`')+'#'+i.join('`')},e.prototype.getInitTime=function(){return this.initTime},e}();t.STRecord=r},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var c=n(0),o=function(){function e(){this.mUrlCache=null,this.mPixelWidthCache={}}return e.getInstance=function(){return e.sImageLoader||(e.sImageLoader=new e),e.sImageLoader},e.prototype.loadImageHelper=function(e,t,n){var a=this;if(!this.mUrlCache){this.mUrlCache={};var r=document.onImgLoaded;document.onImgLoaded=function(e,t){var n=a.mUrlCache[t];if(!n){var o='';for(var i in a.mPixelWidthCache)if(a.mPixelWidthCache.hasOwnProperty(i)&&0===t.indexOf(i)){o=i,n=a.mPixelWidthCache[i];break}n&&!n.validate&&(delete a.mPixelWidthCache[o],n=null)}n&&n.callback?(n.validate=!1,n.callback(e,t,n.img)):r&&r(e,t),delete a.mUrlCache[t]}}var o={callback:n,img:e,validate:!0};if(this.mUrlCache[t]=o,0<t.indexOf('[pixelWidth]')){var i=t.substr(0,t.indexOf('[pixelWidth]'));this.mPixelWidthCache[i]=o}e.src=t},e.prototype.loadImage=function(o,e,i,a){void 0===a&&(a=!0);var r=!1,t=c.amc.isSDK&&c.amc.isIOS,n=function(e,t,n){e&&o&&a&&c.amc.fn.show(o),r=e,i&&i(e,t,o)};this.loadImageHelper(o,e,t?void 0:n),t&&n(!0,e),a&&setTimeout(function(){r||c.amc.fn.hide(o)},10)},e}();t.ImageLoader=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var r=n(0);t.ocr=function(o,i,a,e){if(void 0===a&&(a=!0),void 0===e&&(e=500),o&&r.amc.isAndroid&&!r.amc.isSDK&&r.amc.fn.sdkGreaterThanOrEqual('10.8.53')){var t=(new Date).getUTCMilliseconds();o.className+=' '+t,setTimeout(function(){document.invoke('ocr',{selector:'.'+t},function(e){if(e&&e.data&&e.data[0]&&e.data[0].body){var t='';e.data[0].body.forEach(function(e){e.label&&(t+=e.label)});var n=o.innerText.replace(/<[^>]+>([^<]+)<\\/\\w+>/g,'$1').split('，').join(',').split('？').join('?').split('！').join('!').split('：').join(':').split('“').join('\"').split('”').join('\"');t!==n&&a&&r.amc.fn.logError('render_label_exception',t),i&&i({result:t,pureInnerText:n})}})},e)}}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=function(){function e(){}return e.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.enabled&&this.logObjs.apply(this,e)},e.prodLog=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.logObjs.apply(this,e)},e.logObjs=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var o=0,i=this.chunkSubstr(e.map(function(e){return t.prettyPrintAnyObj(e)[1]}).join(' '),100);o<i.length;o++){var a=i[o];print('msp:'+a)}},e.prettyPrintAnyObj=function(e){var t=this;if(void 0===e)return['undefined','undefined'];if(null===e)return['null','null'];if('string'==typeof e)return['string',e];if('number'==typeof e)return['number',e.toString()];if('boolean'==typeof e)return['boolean',e.toString()];if('function'==typeof e)return['function',e.toString()];if(e instanceof Array)return['array','['+e.map(function(e){return t.prettyPrintAnyObj(e)[1]}).join(', ')+']'];if('object'!=typeof e)return['unknown',e.toString()];var n=[];for(var o in e)if(e.hasOwnProperty(o)){var i=this.prettyPrintAnyObj(e[o])[1];n.push(o+': '+i)}return['object','{'+n.join(', ')+'}']},e.chunkSubstr=function(e,t){for(var n=Math.ceil(e.length/t),o=new Array(n),i=0,a=0;i<n;++i,a+=t)o[i]=e.substr(a,t);return o},e.enabled=!1,e}();t.BNIDELog=o}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "full-body", "tag": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "mdeduct-payandsign-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0217", "tplId": "QUICKPAY@mdeduct-payandsign-flex", "tplVersion": "5.3.7"}