[{"scene": "home", "tabName": "首页", "activities": ["com.taobao.tao.TBMainActivity"], "dependsOn": ["taobao_shake_sdk", "TBSubscribe", "ta<PERSON><PERSON>_favorite_aar", "voice_assistant", "TBSubscribe", "left_sdk", "elemeadapter_android"]}, {"scene": "guangguang", "tabName": "逛逛", "activities": ["com.taobao.tao.TBMainActivity"], "dependsOn": ["taopai_business", "litecreator", "taopai_sdk", "rxandroid", "ratefeature", "tbvsvideofeature", "tbsocialsdk", "layoutmanager_feature", "umipublish", "fluid_remote"]}, {"scene": "message", "tabName": "消息", "activities": ["com.taobao.tao.TBMainActivity"], "dependsOn": ["taopai_sdk", "rxandroid", "ta<PERSON><PERSON>_wangxin", "messagesdkwrapper"]}, {"scene": "detail", "activities": ["com.taobao.android.detail2.core.framework.NewDetailActivity", "com.taobao.android.detail.wrapper.activity.DetailActivity"], "dependsOn": ["ta<PERSON><PERSON>_favorite_aar", "ratefeature", "fliggy_vacation_detail"]}, {"scene": "taolive", "activities": ["com.taobao.taolivehome.TaoLiveHomepageActivity", "com.taobao.taolive.room.TaoLiveVideoActivity"], "dependsOn": ["tblive_gift_android", "LivehomeAtype", "liver<PERSON>_android_plugin_AType", "liver<PERSON>_android_plugin_BType", "taolivesearch"]}, {"scene": "triver", "activities": ["com.alibaba.triver.container.TriverMainActivity"], "launchURLs": [], "dependsOn": ["triver_taobao"]}, {"scene": "mytaobao", "activities": ["mytaobao-tab"], "dependsOn": ["ta<PERSON><PERSON>_favorite_aar", "order_map"]}, {"scene": "orderlist", "activities": ["com.taobao.android.order.bundle.TBOrderListActivity"], "dependsOn": ["order_map"]}, {"scene": "search", "activities": ["com.taobao.search.searchdoor.SearchDoorActivity", "com.taobao.search.sf.MainSearchResultActivity"], "dependsOn": ["tbsearch_remote", "taowise_android_aar", "voice_assistant"]}]