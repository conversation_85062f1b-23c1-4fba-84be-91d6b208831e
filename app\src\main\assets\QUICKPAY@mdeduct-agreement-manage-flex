{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安銀行</font>", "bind_card_description": "已和以下銀行合作，可查詢本人卡號", "next_step": "提交卡號", "add_new_card": "添加銀行卡", "more": "查看更多", "card_placeholder": "點擊輸入 #val# 的銀行卡號", "bind_card_tip": "無需手動輸入卡號，快速綁卡", "card_placeholder_user": "點擊輸入本人銀行卡號", "other_option": "或選擇", "valid_bank": "已支持<font weight=\"bold\">中國銀行|交通銀行|平安銀行</font>", "scan_card": "拍照添卡", "security_info": "信息加密處理，僅用於銀行驗證"}, "zh_TW": {"valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安銀行</font>", "bind_card_description": "已和以下銀行合作，可查詢本人卡號", "next_step": "提交卡號", "add_new_card": "添加銀行卡", "more": "查看更多", "card_placeholder": "點擊輸入 #val# 的銀行卡號", "bind_card_tip": "無需手動輸入卡號，快速綁卡", "card_placeholder_user": "點擊輸入本人銀行卡號", "other_option": "或選擇", "valid_bank": "已支持<font weight=\"bold\">中國銀行|交通銀行|平安銀行</font>", "scan_card": "拍照添卡", "security_info": "信息加密處理，僅用於銀行驗證"}, "en_US": {"valid_bank_icbc": "<font weight=\"bold\">ICBC, BOC, BOCOM, PAB</font> Available", "bind_card_description": "Have cooperated with the following banks, you can check your card number", "next_step": "Submit", "add_new_card": "Add Bank Card", "more": "View more", "card_placeholder": "Click to enter the bank card number of #val#", "bind_card_tip": "No need to manually enter the Card NO.", "card_placeholder_user": "Click to enter my bank card number", "other_option": "Or choose", "valid_bank": "<font weight=\"bold\">BOC, BOCOM, PAB</font> Available", "scan_card": "Photo card", "security_info": "Information encryption processing, only for bank verification"}, "zh_CN": {"valid_bank_icbc": "已支持<font weight=\"bold\">工行|中行|交行|平安银行</font>", "bind_card_description": "已和以下银行合作，可查询本人卡号", "next_step": "提交卡号", "add_new_card": "添加银行卡", "more": "查看更多", "card_placeholder": "点击输入 #val# 的银行卡号", "bind_card_tip": "无需手动输入卡号，快速绑卡", "card_placeholder_user": "点击输入本人银行卡号", "other_option": "或选择", "valid_bank": "已支持<font weight=\"bold\">中国银行|交通银行|平安银行</font>", "scan_card": "拍照添卡", "security_info": "信息加密处理，仅用于银行验证"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"tag": "style"}, {"children": [{"tag": "text", "text": "._MdeductSignPage_z8mb-t-textarea{font-family:\"PingFangSC-Regular\",\"Helvetica Neue\",Helvetica,STHeiTi,sans-serif;font-size:14px;color:#333333}._MdeductSignPage_z8mb-t-body{background-color:#f5f5f5}._MdeductSignPage_z8mb-i-mainBody{overflow:scroll}._MdeductSignPage_z8mb-c-de-1px-line{height:1px;background-color:#eeeeee}._MdeductSignPage_z8mb-c-no-top-padding{padding-top:0}._MdeductSignPage_z8mb-c-cell-box{padding:10px 15px;min-height:60px}._MdeductSignPage_z8mb-c-flex-row{display:flex;justify-content:center;align-items:center}._MdeductSignPage_z8mb-c-algin-item-start{align-items:flex-start}._MdeductSignPage_z8mb-c-flex-between{justify-content:space-between}._MdeductSignPage_z8mb-c-flex-justify-start{justify-content:flex-start}._MdeductSignPage_z8mb-c-checkbox-ios{width:15px;height:15px;background-image:url(AlipaySDK.bundle/alipay_msp_check);background-size:15px 15px}._MdeductSignPage_z8mb-c-checkbox-ios:checked{background-image:url(AlipaySDK.bundle/alipay_msp_checked)}._MdeductSignPage_z8mb-c-checkbox-ios:disabled{background-image:url(AlipaySDK.bundle/alipay_msp_check_disable)}._MdeductSignPage_z8mb-c-checkbox-android{width:15px;height:15px;background-image:url(com.alipay.android.app/alipay_msp_check);background-size:17px 17px}._MdeductSignPage_z8mb-c-checkbox-android:checked{background-image:url(com.alipay.android.app/alipay_msp_checked)}._MdeductSignPage_z8mb-c-checkbox-android:disabled{background-image:url(com.alipay.android.app/alipay_msp_check_disable)}._MdeductSignPage_z8mb-c-order-amount{color:#f96268}._MdeductSignPage_z8mb-c-merchant-logo{margin:10px 0;height:60px;width:60px}._MdeductSignPage_z8mb-c-product-name{flex:1;font-size:17px;font-weight:bold;margin-left:16px;margin-right:8px}._MdeductSignPage_z8mb-c-dialog-info-icon{width:16px;height:16px}._MdeductSignPage_z8mb-c-de-product-desc{color:#999999;margin-top:10px;font-size:14px}._MdeductSignPage_z8mb-c-deduct-label-list{padding:12px 16px}._MdeductSignPage_z8mb-c-deduct-label-item{padding:4px 0}._MdeductSignPage_z8mb-c-deduct-label{flex:1.0;color:#999999;font-size:14px}._MdeductSignPage_z8mb-c-deduct-label-content{flex:2.0;text-align:right;font-size:14px}._MdeductSignPage_z8mb-c-de-agreement-list{display:flex;align-items:flex-start;padding:16px 16px 0}._MdeductSignPage_z8mb-c-de-agreement-checkbox{width:20px;margin-right:6px;align-items:flex-start}._MdeductSignPage_z8mb-c-de-agreement-links{margin-left:0;margin-top:-5px;font-size:15px;flex:1;line-height:21px;word-break:break-all;word-wrap:break-word;flex-wrap:wrap}._MdeductSignPage_z8mb-c-de-info-dialog-item{padding:5px 15px;display:flex;justify-content:flex-start;align-items:flex-start}._MdeductSignPage_z8mb-c-de-dialog-prefix{flex:1;line-height:1;align-self:flex-start}._MdeductSignPage_z8mb-c-de-dialog-content{flex:10;word-break:break-all;word-wrap:break-word}._MdeductSignPage_z8mb-c-btn-primary-wrapper{margin:0 15px;background-color:#FFFFFF;border:0;border-radius:5px;color:#fff;font-size:18px;height:42px;max-height:42px;min-height:42px;flex:1.0}._MdeductSignPage_z8mb-c-btn-primary{background-color:#FFFFFF;border:0;border-radius:5px;color:#fff;font-size:18px;height:42px;max-height:42px;min-height:42px;flex:1.0}._MdeductSignPage_z8mb-c-de-dialog-prefix-logo{height:6px;width:6px;border-radius:3px;background-color:#333;align-self:flex-start;margin-right:5px;margin-top:5px}._MdeductSignPage_z8mb-c-de-toast{position:absolute;top:300px;left:70px;right:70px;height:auto;display:flex;justify-content:center;align-items:center}._MdeductSignPage_z8mb-c-de-toast-label{flex:1;text-align:center;color:#fff;padding:5px 15px;border-radius:4px;opacity:0.7;background-color:#333}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 3954393f23de3635b17c74bdc20e3e35ac363812:D */!function(n){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}i.m=n,i.c=o,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&'object'==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,'a',t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p='',i(i.s=5)}([function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(6);t.BNComponent=o.BNComponent;var i=n(3);t.ComponentRegistry=i.ComponentRegistry;var r=n(8);t.Logger=r.Logger,t.logger=r.logger},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},t.startsWith=function(e,t){return!!e&&0===e.indexOf(t)}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(1),i=function(){function n(){}return n.registerComponent=function(e,t){t?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(o.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(e){return n.jsons[e]},n.putComponentJson=function(e,t){t||o.logger.e('CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i('CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},n.createComponent=function(e){o.logger.i('CmpReg#crtCmp','I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e('CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}();t.ComponentRegistry=i},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var a=n(0);t.mergeObject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(e&&e.length)for(var o=0;o<e.length;o++){var i=e[o];if(a.amc.fn.isObject(i))for(var r in i)i.hasOwnProperty(r)&&(n[r]=i[r])}return n},t.isPreRender=function(e){return e&&(e.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender)},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},t.doNothing=function(){},t.tryJSONParse=function(e){if(a.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}},t.checkEmptyObj=function(e){return a.amc.fn.isString(e)?0===e.length:!(e&&0!==Object.keys(e).length)},t.substrWithFontWidth=function(e,t,n){if(!e)return e;for(var o='',i=0,r=e.length,a=0;a<r;a++){var c=n?e[r-a-1]:e[a];if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break}return o},t.calculateFontWidth=function(e){if(!e)return 0;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++)n.test(e[o])?t+=.45:t++;return Math.round(t)},t.deepCopy=function e(t){if(null==t||'object'!=typeof t)return t;var n;if(t instanceof Date)return(n=new Date).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(var o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(t instanceof Object){for(var r in n={},t)t.hasOwnProperty(r)&&(n[r]=e(t[r]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},t.getConfig=function(e,t){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:e},function(e){t(e.available)})},20)},t.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},t.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)}},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var r,a=n(1),c=n(0),s=n(9),d=n(4),u=c.amc.rpcData,l={outTradeNo:u.outTradeNo,personalProductCode:u.personalProductCode,externalAgreementNo:u.agreementNo,salesProductCode:u.salesProductCode,userId:u.userId,partnerId:u.partnerId,requestToken:u.requestToken,signScene:u.signScene};c.amc.fn.spmPageCreate('a259.b17214',l);var m=function(e){function t(){var i=null!==e&&e.apply(this,arguments)||this;return i.canUseALinkColor=c.amc.fn.sdkGreaterThanOrEqual('10.8.34'),i.onMounted=function(){var e=u;e.errCode&&'USER_AGREEMENT_NOT_EXIST'===e.errCode&&(document.invoke('setResult',{success:!0,errCode:e.errCode}),c.amc.fn.exit()),i.merchantLogDom=i.getViewInComponentById('merchant-logo'),i.deProductNameDom=i.getViewInComponentById('de-product-name'),i.deProductDescDom=i.getViewInComponentById('de-product-desc'),i.deLableListDom=i.getViewInComponentById('deduct-label-list'),i.agreementLinkListDom=i.getViewInComponentById('de-agreement-links'),i.submitBtn=i.getViewInComponentById('submit-btn'),i.toastWrapper=i.getViewInComponentById('de-toast'),i.toastLabel=i.getViewInComponentById('de-toast-label'),i.btnLoading=i.getViewInComponentById('btn-loading'),i.deChannelLable=i.getViewInComponentById('deduct-channel-label'),i.deQuotaLabel=i.getViewInComponentById('deduct-quota-label'),i.confirmCheckbox=i.getViewInComponentById('de-agreement-confirm-checkbox'),e.merchantDetailInfo&&i.renderDeductMerchant(e.merchantDetailInfo),e.deductPageLabelInfos&&i.renderDeductLabelsDom(e.deductPageLabelInfos),e.deductAgreementDetailInfos&&i.renderAgreementInfo(e.deductAgreementDetailInfos);var t='按设置的扣款顺序支付';e.deductChannelSettingBox&&e.deductChannelSettingBox.descr&&(t=e.deductChannelSettingBox.descr),i.renderDeductChannelLabelDom(t),i.submitBtn.onclick=i.submitInfo,u.quotaPageLableInfo&&u.quotaPageLableInfo.singlePayQuota&&u.quotaPageLableInfo.singleQuotaList?(i.modifyDeductQuota(u.quotaPageLableInfo),i.deQuotaLabel.onclick=i.onQuotaLabelClick):c.amc.fn.hide(i.deQuotaLabel),c.amc.isAndroid&&(i.btnLoading.src=c.amc.path+'alipay_msp_indicator_white_loading')},i.showToast=function(e){i.toastLabel.innerText=e,i.toastTimeoutRef&&clearTimeout(i.toastTimeoutRef),c.amc.fn.show(i.toastWrapper),c.amc.fn.show(i.toastLabel),i.toastTimeoutRef=setTimeout(function(){c.amc.fn.hide(r.toastWrapper),r.toastLabel.innerText=''},3e3)},i.submitInfo=function(){document.confirm({title:'',message:'确认解除该协议？',okButton:'确认解除',cancelButton:'我再想想'},function(e){e.ok&&(c.amc.fn.spmClick('a259.b17214.c43071.d87344',l),r.disableSubmitBtn(),r.applySign())})},i.applySign=function(){!function(n,e,o,i){if(n){var t={operationType:n,requestData:e};setTimeout(function(){document.invoke('rpc',t,function(e){var t;g(e=e||{})?o&&o(e):(function(e,t){try{c.amc.fn.logError(e,t)}catch(e){c.amc.fn.logError('log_error_failed','')}}(n,e),(t=(t=e)||{}).errorMsg||('10'==t.error||'11'==t.error?_('网络不给力','fail'):t.error&&_('网络开小差，请稍后重试','none')),i&&i(e))})},10)}}('alipay.mdeduct.sdk.unsign',{managePageToken:u.managePageToken},function(e){e.success&&i.successCallback(e)},function(e){'USER_AGREEMENT_PERIOD_CONFILICT'===e.errorCode?p(e):(e.errorMsg&&i.errorToast(e.errorMsg),i.enableSubmitBtn())})},i.onQuotaLabelClick=function(){if(!b&&u.quotaPageLableInfo&&u.quotaPageLableInfo.singleQuotaList){c.amc.isIOS&&(b=!0);var n=u.quotaPageLableInfo.singleQuotaList,o=d.tryJSONParse(JSON.stringify(u.quotaPageLableInfo.singleQuotaList));o.forEach(function(e,t){o[t]='¥'+e}),document.actionSheet({btns:o,cancelBtn:'取消'},function(e){if(n&&e&&e.index<n.length&&n[e.index]!==i.currentQuota){var o=n[e.index],t={operationType:'alipay.mdeduct.sdk.litepay.quota.verify',requestData:{quota:o,agreementNo:u.agreementNo,managePageToken:u.managePageToken}};window.setTimeout(function(){document.invoke('showLoading')},20),setTimeout(function(){document.invoke('rpc',t,function(n){window.setTimeout(function(){document.invoke('hideLoading')},20),(n=n||{}).verifyId?document.invoke('verifyIdentity',{verifyId:n.verifyId},function(e){if('1000'===e.code){var t={operationType:'alipay.mdeduct.sdk.litepay.quota.modify',requestData:{quota:o,externalAgreementId:u.agreementNo,verifyId:e.verifyId,securityId:n.securityId,applyToken:n.applyToken,managePageToken:u.managePageToken}};window.setTimeout(function(){document.invoke('showLoading')},20),setTimeout(function(){document.invoke('rpc',t,function(e){window.setTimeout(function(){document.invoke('hideLoading')},20),g(e=e||{})?(i.deQuotaLabelDesc.innerText='¥'+o,i.currentQuota=o,e.serviceDescription&&null!=e.serviceDescription&&''!=e.serviceDescription&&(i.serviceDescription.innerText=e.serviceDescription)):_('网络开小差，请稍后重试','none'),b=!1})},10)}else b=!1}):(_('网络开小差，请稍后重试','none'),b=!1)})},10)}b=!1})}},i}return i(t,e),t.prototype.renderDeductMerchant=function(e){c.amc.fn.spmExposure('a259.b17214.c43071',l,!1);var t=e.merchantLogoUrl||'';t=t.replace(/\\[pixelWidth\\]x\\[pixelHeight\\]/g,'60x60'),this.merchantLogDom.src=t,this.deProductNameDom.innerText=e.merchantProductName||'',this.deProductDescDom.innerText=e.merchantName||'';var n=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),o=this.createStyledElement('label','','deduct-label');this.serviceDescription=this.createStyledElement('label','','deduct-label-content'),this.serviceDescription.innerText=e.serviceDescription||'',o.innerText='服务详情',this.submitBtn.innerText=u.confirmBtnText||'关闭服务',this.submitBtn.style.color='#FF0000',this.getViewInComponentById('merchant-label').appendChild(n),n.appendChild(o),n.appendChild(this.serviceDescription)},t.prototype.renderDeductLabelsDom=function(e){if(e&&!(e.length<1)&&this.deLableListDom)for(var t=0;t<e.length;t++){var n=e[t],o=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),i=this.createStyledElement('label','','deduct-label'),r=this.createStyledElement('label','','deduct-label-content');r.innerText=n.labelValue||'',i.innerText=n.labelName||'',this.deLableListDom.appendChild(o),o.appendChild(i),o.appendChild(r)}},t.prototype.renderDeductChannelLabelDom=function(e){if(e&&this.deChannelLable){var t=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),n=this.createStyledElement('label','','deduct-label'),o=this.createStyledElement('label','','deduct-label-content');o.innerText=e||'',n.innerText='扣款方式',n.style.color='#000',this.getViewInComponentById('deduct-channel-label').appendChild(t),t.appendChild(n),t.appendChild(o)}},t.prototype.modifyDeductQuota=function(e){var t=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),n=this.createStyledElement('label','','deduct-label'),o=this.createStyledElement('div','',''),i=this.createStyledElement('label','','deduct-label-content'),r=this.createStyledElement('img','','amc-margin-l-xs amc-self-center amc-align-center');r.src=c.amc.res.arrowRight,i.innerText='¥'+e.singlePayQuota,n.innerText=e.quotaDesc||'设置单笔免密额度',n.style.color='#000';var a=this.getViewInComponentById('deduct-quota-label');this.deQuotaLabelDesc=i,this.currentQuota=e.singlePayQuota,o.appendChild(i),o.appendChild(r),a.appendChild(t),t.appendChild(n),t.appendChild(o)},t.prototype.renderAgreementInfo=function(i){c.amc.fn.spmExposure('a259.b17214.c43070',l,!1);var e='';if(!i||i.length<1)this.confirmCheckbox.innerText='';else{this.confirmCheckbox.innerHTML='',e+='<font color=\"#999999\" >查看</font>';for(var t=0;t<i.length;t++){var n=i[t],o='<a color=\"'+s.getThemeColor()+'\" href=\"'+n.agreementUrl+'\">'+n.agreementName+'</a>';this.canUseALinkColor&&(o='<a color=\"'+s.getThemeColor()+'\" alinkcolor=\"'+s.getThemeColor()+'\" href=\"'+n.agreementUrl+'\">'+n.agreementName+'</a>'),e+=o}this.agreementLinkListDom.innerText=e,this.agreementLinkListDom.onlink=function(e){c.amc.fn.spmClick('a259.b17214.c43070.d87343',l),e&&function(e){for(var t='支付宝',n=0;n<i.length;n++){var o=i[n];if(o.agreementUrl===e){t=o.agreementName;break}}document.submit({action:{name:'loc:openweb(\\''+e+'\\',\\''+t+'\\')'}})}(e)}}},t.prototype.successCallback=function(e){document.invoke('setResult',e),c.amc.fn.exit()},t.prototype.disableSubmitBtn=function(){c.amc.fn.hide(this.submitBtn),c.amc.fn.show(this.btnLoading)},t.prototype.enableSubmitBtn=function(){c.amc.fn.hide(this.btnLoading),c.amc.fn.show(this.submitBtn)},t.prototype.errorToast=function(e){this.showToast(e||''),this.enableSubmitBtn()},t.getComponentCSSRules=function(){return{textarea:'_MdeductSignPage_z8mb-t-textarea',body:'_MdeductSignPage_z8mb-t-body','#mainBody':'_MdeductSignPage_z8mb-i-mainBody','.de-1px-line':'_MdeductSignPage_z8mb-c-de-1px-line','.no-top-padding':'_MdeductSignPage_z8mb-c-no-top-padding','.cell-box':'_MdeductSignPage_z8mb-c-cell-box','.flex-row':'_MdeductSignPage_z8mb-c-flex-row','.algin-item-start':'_MdeductSignPage_z8mb-c-algin-item-start','.flex-between':'_MdeductSignPage_z8mb-c-flex-between','.flex-justify-start':'_MdeductSignPage_z8mb-c-flex-justify-start','.checkbox-ios':'_MdeductSignPage_z8mb-c-checkbox-ios','.checkbox-android':'_MdeductSignPage_z8mb-c-checkbox-android','.order-amount':'_MdeductSignPage_z8mb-c-order-amount','.merchant-logo':'_MdeductSignPage_z8mb-c-merchant-logo','.product-name':'_MdeductSignPage_z8mb-c-product-name','.dialog-info-icon':'_MdeductSignPage_z8mb-c-dialog-info-icon','.de-product-desc':'_MdeductSignPage_z8mb-c-de-product-desc','.deduct-label-list':'_MdeductSignPage_z8mb-c-deduct-label-list','.deduct-label-item':'_MdeductSignPage_z8mb-c-deduct-label-item','.deduct-label':'_MdeductSignPage_z8mb-c-deduct-label','.deduct-label-content':'_MdeductSignPage_z8mb-c-deduct-label-content','.de-agreement-list':'_MdeductSignPage_z8mb-c-de-agreement-list','.de-agreement-checkbox':'_MdeductSignPage_z8mb-c-de-agreement-checkbox','.de-agreement-links':'_MdeductSignPage_z8mb-c-de-agreement-links','.de-info-dialog-item':'_MdeductSignPage_z8mb-c-de-info-dialog-item','.de-dialog-prefix':'_MdeductSignPage_z8mb-c-de-dialog-prefix','.de-dialog-content':'_MdeductSignPage_z8mb-c-de-dialog-content','.btn-primary-wrapper':'_MdeductSignPage_z8mb-c-btn-primary-wrapper','.btn-primary':'_MdeductSignPage_z8mb-c-btn-primary','.de-dialog-prefix-logo':'_MdeductSignPage_z8mb-c-de-dialog-prefix-logo','.de-toast':'_MdeductSignPage_z8mb-c-de-toast','.de-toast-label':'_MdeductSignPage_z8mb-c-de-toast-label'}},t.getComponentJson=function(){return{'sp-view-id':'mainBody',_c:'amc-v-box amc-flex-1 _MdeductSignPage_z8mb-i-mainBody',_t:'div',_cd:[{_c:'amc-main amc-margin-b-s _MdeductSignPage_z8mb-c-no-top-padding',_t:'div',_cd:[{_c:'amc-v-box amc-bg-white amc-pd amc-align-center',_t:'div',_cd:[{'sp-view-id':'merchant-logo',_c:'_MdeductSignPage_z8mb-c-merchant-logo',_t:'img'},{'sp-view-id':'de-product-name',_c:'_MdeductSignPage_z8mb-c-product-name',_t:'label'},{'sp-view-id':'de-product-desc',_c:'_MdeductSignPage_z8mb-c-de-product-desc',_t:'label'}]},{'sp-view-id':'merchant-label',_c:'_MdeductSignPage_z8mb-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{_c:'amc-1px-line',_t:'div'},{'sp-view-id':'deduct-label-list',_c:'_MdeductSignPage_z8mb-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{_c:'_MdeductSignPage_z8mb-c-de-1px-line',_t:'div'},{_c:'amc-abs-space-v-m',_t:'div'},{'sp-view-id':'deduct-channel-label',_c:'_MdeductSignPage_z8mb-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{_c:'_MdeductSignPage_z8mb-c-de-1px-line',_t:'div'},{'sp-view-id':'deduct-quota-label',_c:'_MdeductSignPage_z8mb-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{'sp-view-id':'de-agreement-list',_c:'_MdeductSignPage_z8mb-c-de-agreement-list',_t:'div',_cd:[{'sp-view-id':'de-agreement-confirm-checkbox',_c:'_MdeductSignPage_z8mb-c-de-agreement-checkbox amc-hidden',_t:'div',_cd:[{_y:'checkbox',_c:'amc-checkbox amc-margin-r-xs','sp-view-id':'de-agree-checkbox',onclick:'toggleAgreeCheckbox();',_t:'input'}]},{'sp-view-id':'de-agreement-links',_c:'_MdeductSignPage_z8mb-c-de-agreement-links',_t:'label'}]},{_c:'amc-abs-space-v-m',_t:'div'},{_c:'amc-abs-space-v-m',_t:'div'},{_c:'_MdeductSignPage_z8mb-c-btn-primary-wrapper amc-align-center amc-justify-center',_t:'div',_cd:[{'sp-view-id':'btn-loading',src:'indicatior',_c:'amc-loading-img amc-text-dark-color amc-hidden',_t:'img'},{'sp-view-id':'submit-btn',_c:'_MdeductSignPage_z8mb-c-btn-primary',_t:'button',_x:'关闭服务'}]}]},{'sp-view-id':'de-toast',_c:'_MdeductSignPage_z8mb-c-de-toast amc-hidden',_t:'div',_cd:[{'sp-view-id':'de-toast-label',_c:'_MdeductSignPage_z8mb-c-de-toast-label amc-hidden',_t:'label'}]}]}},t.componentName='MdeductSignPage',t.componentHashName='MdeductSignPage_z8mb',t}(a.BNComponent);function f(){c.amc.fn.spmPageDestroy('a259.b17214',l),p({cancel:!0})}function p(e){document.invoke('setResult',e,function(e){}),c.amc.fn.exit()}function g(e){return e&&(e.success||'SUCCESS'===e.resultCode||'success'===e.resultCode)}t.MdeductSignPage=m;var h=!(window.onload=function(){var e=u,t=new m;r=t;var n=c.amc.fn.getNav(c.amc.res.navBack,c.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'':'{{return}}',e.pageTitle,'','',f);document.body.style.height=c.amc.isAndroid?window.innerHeight:c.amc.specs.bodyHeight,document.body.appendChild(n),window.onKeyDown=function(){4==event.which&&f()},t.mountTo(document.body)}),b=!1;function _(e,t){if(e){if(c.amc.isAndroid){if(h)return;setTimeout(function(){h=!1},2e3),h=!0}document.toast({text:e,type:t||'none'},function(){})}}},function(e,t,n){'use strict';var a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,'__esModule',{value:!0});var C=n(1),w=n(2),x=n(3),S=n(7),o=function(){function e(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=w.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return e.getComponentCSSRules=function(){throw new Error('E0100')},e.getComponentJson=function(){throw new Error('E0101')},e.prototype.mountTo=function(e,t){if(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t):e.appendChild(n),this._triggerOnMounted()):C.logger.e('Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},Object.defineProperty(e.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),e.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},e.prototype.getMountedParentView=function(){var e=this.getMountedRootView();return e?e.parentNode:null},e.prototype.getSubComponentById=function(e,t){var n=this.debugName+'#SCById',o=this._subComponentsMap[e];if(!o)return null;var i='',r='';try{i=o.constructor.componentName,r=t.componentName}catch(e){C.logger.e(n,'E0104 '+e)}return i&&i===r?o:(C.logger.e(n,'E0105 '+i+', '+r),null)},e.prototype.getViewInComponentById=function(e){return this._viewsIdMap[e]},e.prototype.getComponentId=function(){return this._componentId},e.prototype.createStyledElement=function(e,t,n){var o=document.createElement(e);if(o)return e&&(o.className+=' '+this._css(e,2)),n&&(o.className+=' '+this._csses(n,1)),t&&(o.className+=' '+this._css('#'+t,2)),o},e.prototype.applyStyleTo=function(e,t){e&&(e.className+=' '+this._csses(t,1))},e.prototype.css=function(e){return this._css(e,0)},e.prototype.csses=function(e){return this._csses(e,0)},e.prototype._csses=function(e,t){var n=this;return e.split(' ').map(function(e){return n._css(e,t)}).join(' ')},e.prototype._css=function(e,t){if(!e)return'';var n=this._componentCSSRules;if(!n)return e;switch(e.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e;case 2:default:return e}}},e.prototype._triggerOnMounted=function(){new S.Observer(this.vueModel.data),C.logger.i('','I0106 '+this.debugName);for(var e=0,t=this._subComponents;e<t.length;e++){var n=t[e];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},e.prototype._getMethod=function(e){var t=this[e];return t instanceof Function?t:null},e.prototype._acquireComponentJson=function(){var e=this.debugName+'#acCJ',t=x.ComponentRegistry.getComponentJson(this._componentName);return t?(C.logger.i(e,'I0107'),t):void 0!==this._componentJson?(C.logger.i(e,'I0108'),x.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(C.logger.e(e,'E0109'),null)},e.prototype._acquireRootView=function(){var e=this.debugName+'#acRV';if(this._hasRootViewBuilt)return C.logger.i(e,'I0110'),this._rootView;var t=this._acquireComponentJson();return t?(this._rootView=this._convertJsonToBNNode(t,this.vueModel.data||{}),this._hasRootViewBuilt=!0,C.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111'),null)},e.prototype._genArrayChildNode=function(e,t,n,o,i){var r=this._convertJsonToBNNode(e,a({},t,{item:n,index:o,arrayName:i}));return r?(r.setAttribute('index',o),r.setAttribute('for_name',i),r):null},e.prototype._convertJsonToBNNode=function(e,d){var u=this,t=this.debugName+'#cJTB';if(void 0===e._t)return null;var l=document.createElement(e._t),m=[];if(void 0!==e._cd)for(var n=function(a){if(a['v-for']||a['v-for-cal']){var e=!a['v-for']&&!!a['v-for-cal'],t=(e?f.vueModel.compute:d)||{},n=S.vueUtils.getObject(e?a['v-for-cal']:a['v-for'],t,e),c=e?S.vueUtils.rmSymbol(a['v-for-cal']):S.vueUtils.rmSymbol(a['v-for']);if(!c||!n)return'continue';for(var o in a['v-for']='',a['v-for-cal']='',n)if(n.hasOwnProperty(o)){var i=f._genArrayChildNode(a,d,n[o],o,c);i&&m.push(i)}var s=document.createElement('div');s&&(s.style.display='none',s.setAttribute('for_end',c),m.push(s),new S.Watcher(c,t,function(e){if(l){S.rmWatchers(c);for(var t=0,n=l.childNodes;t<n.length;t++){var o=n[t];o.getAttribute('for_name')===c&&l.removeChild(o)}if(e)for(var i in e)if(e.hasOwnProperty(i)){var r=u._genArrayChildNode(a,d,e[i],i,c);r&&l.insertBefore(r,s)}}},e).id=d.arrayName)}else{var r=f._convertJsonToBNNode(a,d);if(!r)return'continue';m.push(r)}},f=this,o=0,i=e._cd;o<i.length;o++)n(i[o]);if(!l)return null;d&&d.index&&l.setAttribute('index',d.index);var r=e['bn-component']||e['sp-component'];if(r){C.logger.i(t,'I0113 '+r);var a=x.ComponentRegistry.createComponent(r);if(!a)return C.logger.e(t,'E0114 '+r+', '+a),null;var c=e['bn-component-id']||e['sp-component-id'];return c&&(a._componentId=c),a.onCreated&&a.onCreated(),C.logger.i(t,'I0115 '+a.debugName+', '+c),this._subComponents.push(a),c&&!this._subComponentsMap[c]&&(this._subComponentsMap[c]=a),a._acquireRootView()}var s=e['bn-view-id']||e['sp-view-id'];for(var p in s&&(C.logger.i(t,'I0116 '+s),this._viewsIdMap[s]||(this._viewsIdMap[s]=l)),e._i&&(l.id=e._i),e._c&&(l.className=e._c),e._s&&(l.style.cssText=e._s),e._x&&(l.innerText=e._x),e._y&&(l.type=e._y),e)if(e.hasOwnProperty(p))if(0===p.indexOf('on')){var g=this._getMethod(e[p]);g&&(l[p]=g.bind(this,l))}else if(0===p.indexOf('_'));else if(0===p.indexOf('bn-')||0===p.indexOf('sp-'));else if(w.startsWith(p,'v-')){var h=p.split('-');if(2===h.length||3===h.length){var b=h[1];2===h.length?new S.NodeCompile(d).compile(b,l,e[p],e._t):'cal'===h[2]&&new S.NodeCompile(this.vueModel.compute,!0).compile(b,l,e[p],e._t)}else l[p]=e[p]}else l[p]=e[p];for(var _=0,v=m;_<v.length;_++){var y=v[_];l.appendChild(y)}return l},e.componentName='',e.componentHTML='',e.componentCSS='',e.componentHashName='',e}();t.BNComponent=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var r,a=n(2),c=n(0);t.rmWatchers=function(t){r=r.filter(function(e){return e.id!==t})};var s=function(){function e(e,t,n,o){if(this.id='',this.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,this.callback=n,a.startsWith(e,'item')&&t.arrayName&&t.index){var i=e.replace('item','');this.expression=t.arrayName+'.'+t.index,i&&(this.expression+=i)}else this.expression=e;this.data=t,this.value=u.getVal(e,t,this.lazy),r||(r=[]),r.push(this)}}return e.prototype.update=function(){if(this.data&&this.expression&&this.callback){var e=u.getVal(this.expression,this.data,this.lazy),t=this.value;u.equals(e,t)||(this.value=e,this.callback(e))}},e}();t.Watcher=s;var o=function(){function e(e){this.observe(e)}return e.prototype.observe=function(t){var n=this;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){try{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})},e.prototype.defineReactive=function(e,t,n){var o=this;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){u.equals(e,n)||(n=e,o.observe(e),r&&r.forEach(function(e){e.update()}))}})},e}();t.Observer=o;var i=function(){function e(e,t){void 0===t&&(t=!1),this.data=e||{},this.lazy=t}return e.prototype.compile=function(n,e,t,o){var i=this;if(e)switch(n){case'text':this.labelProcess(e,t,function(e,t){e.innerText=void 0===t?'':t});break;case'html':this.labelProcess(e,t,function(e,t){e.innerHtml=void 0===t?'':t});break;case'class':this.labelProcess(e,t,function(e,t){var n=e.className,o=(n=n.replace(t,'').replace(/\\s$/,''))&&String(t)?' ':'';e.className=n+o+t});break;case'model':this.eventProcess(e,t,function(e,t){e.value=t}),'input'===o?e.oninput=function(){u.setTextVal(t,e.value,i.data)}:'switch'===o&&(e.onchange=function(e){u.setTextVal(t,e||'off',i.data)});break;case'if':this.eventProcess(e,t,function(e,t){!0===t?(e.style.display='flex',d.process(e,function(e){c.amc.fn.spmExposure(e.spmId,e.param4Map,e.doNotResume)})):e.style.display='none'});break;case'spm':this.labelProcess(e,t,function(e,t){e.setAttribute('spm',void 0===t?'':t)});break;case'click':this.eventProcess(e,t,function(e,t){u.isFunction(t)?e.onclick=function(){t(e),d.process(e,function(e){c.amc.fn.spmClick(e.spmId,e.param4Map)})}:e.onclick=function(){}});break;default:this.labelProcess(e,t,function(e,t){e[n]=void 0===t?'':t})}},e.prototype.labelProcess=function(n,o,i){var r=this,e=o.match(/@\\{([^}]+)\\}/g),t=u.getTextVal(o,this.data,this.lazy);e&&e.forEach(function(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(new s(t[1],r.data,function(e){i(n,u.getTextVal(o,r.data,r.lazy))},r.lazy).id=r.data.arrayName)}),i(n,t)},e.prototype.eventProcess=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(e),i=u.getObject(e,this.data,this.lazy);o&&1<o.length&&(new s(o[1],this.data,function(e){n(t,e)},this.lazy).id=this.data.arrayName),n(t,i)},e}();t.NodeCompile=i;var d=function(){function e(){}return e.process=function(e,t){var n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&t(o)}catch(e){}},e}(),u=function(){function c(){}return c.item2ArrayIndex=function(e,t){var n=e;if(a.startsWith(e,'item')&&t.arrayName&&t.index){var o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(n+=o)}return n},c.getVal=function(e,t,n){if(e){var o=e.split('.').reduce(function(e,t){return e[t]},t);return n?c.isFunction(o)?o():void 0:o}},c.getTextVal=function(e,i,r){var a=this;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(r)e=a.getVal(t[1],i,r);else{var o=c.item2ArrayIndex(t[1],i);e=a.getVal(o,i,!1)}return void 0===e?'':e})},c.getObject=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length)return this.getVal(o[1],t,n)},c.rmSymbol=function(e){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''},c.setVal=function(e,o,t){var i=e.split('.');return i.reduce(function(e,t,n){return n===i.length-1?e[t]=o:e[t]},t)},c.setTextVal=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(o[1],t,n)},c.equals=function(e,t){return this.eq(e,t,void 0,void 0)},c.eq=function(e,t,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var i=toString.call(e);if(i!==toString.call(t))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+e==''+t;case'[object Number]':return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case'[object Date]':case'[object Boolean]':return+e==+t}var r='[object Array]'===i;if(!r){if('object'!=typeof e||'object'!=typeof t)return!1;var a=e.constructor,c=t.constructor;if(a!==c&&!(this.isFunction(a)&&a instanceof a&&this.isFunction(c)&&c instanceof c)&&'constructor'in e&&'constructor'in t)return!1}o=o||[];for(var s=(n=n||[]).length;s--;)if(n[s]===e)return o[s]===t;if(n.push(e),o.push(t),r){if((s=e.length)!==t.length)return!1;for(;s--;)if(!this.eq(e[s],t[s],n,o))return!1}else{var d=Object.keys(e),u=void 0;if(s=d.length,Object.keys(t).length!==s)return!1;for(;s--;)if(u=d[s],!t.hasOwnProperty(u)||!this.eq(e[u],t[u],n,o))return!1}return n.pop(),o.pop(),!0},c.isFunction=function(e){return'function'==typeof e||!1},c}();t.vueUtils=u},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var r=function(){function r(){this.enabled=!0}return r.fmtLine=function(e,t,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+e+']['+r.fmtTime()+']['+t+']'+n+' '+i},r.fmtTime=function(){var e=new Date;return e.getHours()+':'+e.getMinutes()+':'+e.getSeconds()+'.'+e.getMilliseconds()},r.prototype.enable=function(){this.enabled=!0},r.prototype.disable=function(){this.enabled=!1},r}();t.Logger=r,t.logger=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.e=function(e,t,n){},t.prototype.i=function(e,t,n){},t}(r))},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o,a=n(0),i=n(4);function r(e,t,n){var o=t;a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&i.copyObj(n,o.style)}function c(e,t,n){if(t&&n){var o=t;if(a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o)for(var i in n)n.hasOwnProperty(i)&&(o[i]=n[i])}}t.modifyElementStyle=r,t.modifyElementAttribute=c,t.modifyElementClass=function(e,t,n,o){var i=t;a.amc.fn.isString(t)&&(i=e.getViewInComponentById(t)),i&&(o?i.className+=n:i.className=n)},t.visibleElement=function(e,t,n){var o;void 0===n&&(n=!0),t&&(o=a.amc.fn.isString(t)?e.getViewInComponentById(t):t)&&(n?a.amc.fn.show(o):a.amc.fn.hide(o))},t.modifyElementCSS=function(e,t,n){if(t){var o=t;a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&n&&(o.style.cssText=n)}},t.createEmbedViPlugin=function(e,t,n,o){var i;if(a.amc.isAndroid)i=document.createElement('embed',t,function(){});else for(var r in i=document.createElement('embed'),t)t.hasOwnProperty(r)&&(i[r]=t[r]);return n&&(i.className=n),o?e.insertBefore(i,o):e.appendChild(i),i},t.getThemeColor=(o='',function(){return o||(o=a.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'#1677FF':'#108EE9'),o}),window.DomUtils={modifyElementStyle:r,modifyElementAttribute:c,getThemeColor:t.getThemeColor}}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body", "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "mdeduct-agreement-manage-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0147", "tplId": "QUICKPAY@mdeduct-agreement-manage-flex", "tplVersion": "5.3.7"}