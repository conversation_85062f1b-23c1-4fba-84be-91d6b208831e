!function(){var t={2976:function(t){var e,n,r;e={497:function(t,e,n){var r=n(516),o=n(209),a=n(648);t.exports={isEquals:function(t,e){return a.isEquals(t,e)},clone:function(t){return a.deepClone(t)},diff:function(t,e,n){return r.diff(t,e,n)},applyPatch:function(t,e,n){return o.apply(t,e,n)}}},209:function(t,e,n){function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var o=n(648),a={add:function(t,e){t[e]=this.value},remove:function(t,e){this.removed=t[e],delete t[e]},replace:function(t,e){this.removed=t[e],t[e]=this.value},move:function(t,e,n){this.removed=t[e];var r={op:"remove",path:this.from};c(n.vm,r,n.options),t[e]=r.removed},copy:function(t,e,n){var r={op:"_get",path:this.from};c(n.vm,options,n.options),t[e]=o.deepClone(r.value)},test:function(t,e){this.test=t[e]==this.value},_get:function(t,e){this.value=t[e]}},i={add:function(t,e){o.isInteger(e)?t.splice(e,0,this.value):t[e]=this.value},remove:function(t,e){var n=t.splice(e,1);this.removed=n[0]},replace:function(t,e){this.removed=t[e],t[e]=this.value},move:a.move,copy:a.copy,test:a.test,_get:a._get};function c(t,e){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},c=n.validateOperation,s=e.path.split("/"),u=s.length,f=1,l=t,p=void 0;;){if((p=s[f++])&&-1!=p.indexOf("~")&&(p=o.unescapePathComponent(p)),Array.isArray(l)){if(c&&!o.isInteger(p))return"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index";if(p=~~p,f>=u)return c&&"add"==e.op&&p>=l.length?"The specified index MUST NOT be greater than the number of elements in the array":i[e.op].call(e,l,p,{vm:t,options:n})}else if(f>=u)return a[e.op].call(e,l,p,{vm:t,options:n});if(l=l[p],c&&f<u&&(!l||"object"!==r(l)))return"Cannot perform operation at the desired path"}}t.exports={apply:function(t,e,n){if(t&&Array.isArray(e))for(var r=0;r<e.length;r++){var o=c(t,e[r],n);if(o)throw new Error(o)}return t}}},516:function(t,e,n){function r(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return o(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var i=n(648),c=i.isNull,s=i.ObjectKeys,u=i.escapePathComponent,f=i.unique,l=i.arrayToMap,p=i.merge,h=i.deepClone;function d(t,e){if(t instanceof Array){if(!(e instanceof Array))return!1;if(t.length!==e.length)return!1;for(var n=0;n<t.length;n++)if(!d(t[n],e[n]))return!1;return!0}if(t instanceof Object){if(!(e instanceof Object))return!1;var r=Object.keys(t),o=Object.keys(e);if(r.length!==o.length)return!1;for(n=0;n<r.length;n++){var a=r[n];if(!d(t[a],e[a]))return!1}return!0}return t===e||Number.isNaN(t)&&Number.isNaN(e)}function g(t,e,n,r,o,a,i){for(var c=r-a,s=o-i,u=Math.max(c,s),f=1;f<=u;f++){var l=e[r-f],p=n[o-f];if(f<=s&&f<=c&&t(l,p))return{a:r-f,b:o-f};for(var h=0;h<f;h++){var d=e[r-h],g=n[o-h];if(f<=s&&t(d,p))return{a:r-h,b:o-f};if(f<=c&&t(l,g))return{a:r-f,b:o-h}}}return{a:a-1,b:i-1}}function m(t,e){if(t instanceof Array){if(!(e instanceof Array))return!1;for(var n=t.length/10,r=Math.abs(t.length-e.length),o=0;o<t.length;o++)if(d(t[o],e[o])){if(r>=2&&r>n||r===t.length)return!1;r++}return!0}if(t instanceof Object){if(!(e instanceof Object))return!1;var a=p(l(Object.keys(t)),l(Object.keys(e))),i=Object.keys(a).length;for(var c in n=i/10,r=0,a)if(!d(t[c],e[c])){if(r>=2&&r>n||r+1===i)return!1;r++}return!0}return t===e||Number.isNaN(t)&&Number.isNaN(e)}t.exports={diff:function(t,e,n){var r=[];return this._diff(t,e,r,"",n),r},_diff:function(t,e,n,o,i){if(!(t===e||Number.isNaN(t)&&Number.isNaN(e)))if(a(t)===a(e))if(Array.isArray(t)&&Array.isArray(e)){for(var l=t.length-1,p=e.length-1;l>=0&&p>=0;)if(d(t[l],e[p]))l--,p--;else{for(var y=g(d,t,e,l,p,0,0),v=l,b=p;v>y.a&&b>y.b;)if(m(t[v],e[b]))this._diff(t[v],e[b],n,"".concat(o,"/").concat(v),i),v--,b--;else{var O=g(m,t,e,v,b,y.a+1,y.b+1),x=v-O.a,S=b-O.b;1===x&&1===S?I("".concat(o,"/").concat(O.a+1),e[O.b+1]):1===x&&2===S?(j(o,O.a+2,e.slice(O.b+2,b+1)),I("".concat(o,"/").concat(O.a+1),e[O.b+1])):2===x&&1===S?(N(o,O.a+2,1),I("".concat(o,"/").concat(O.a+1),e[O.b+1])):2===x&&2===S?(I("".concat(o,"/").concat(O.a+2),e[O.b+2]),I("".concat(o,"/").concat(O.a+1),e[O.b+1])):(x>0&&N(o,O.a+1,x),S>0&&j(o,O.a+1,e.slice(O.b+1,b+1))),v=O.a,b=O.b}v>y.a?N(o,y.a+1,v-y.a):b>y.b&&j(o,v+1,e.slice(y.b+1,b+1)),l=y.a,p=y.b}l>=0?N(o,0,l+1):p>=0&&j(o,0,e.slice(0,p+1))}else if(t instanceof Object&&e instanceof Object)for(var w=f([].concat(r(s(t)),r(s(e)))),$=0;$<w.length;++$){var k=w[$];this._diff(t[k],e[k],n,"".concat(o,"/").concat(u(k)),i)}else t!==e&&I(o,e);else c(t)||c(e)?c(t)?A(o,e):c(e)&&_(o):I(o,e);function A(t,e){n.push({op:"add",path:t,value:h(e)})}function j(t,e,n){for(var r=0;r<(n||[]).length;++r)A("".concat(t,"/").concat(e+r),n[r])}function _(t){n.push({op:"remove",path:t})}function N(t,e,n){for(;n-- >0;)_("".concat(t,"/").concat(e+n))}function I(t,e){n.push({op:"replace",path:t,value:h(e)})}}}},648:function(t){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}var n=Object.prototype.hasOwnProperty;function r(t,e){return n.call(t,e)}t.exports={hasOwnProperty:r,deepClone:function(t){switch(e(t)){case"object":return null==t?null:JSON.parse(JSON.stringify(t,(function(t,e){return e instanceof Map?Object.fromEntries(e.entries()):null!==e&&"undefined"!==e&&e==e?e:void 0})));case"undefined":return null;default:return t}},ObjectKeys:function(t){if(Array.isArray(t)){for(var e=new Array(t.length),n=0;n<e.length;n++)e[n]=""+n;return e}if(Object.keys)return Object.keys(t);var o=[];for(var a in t)r(t,a)&&o.push(a);return o},isNull:function(t){return null==t||null==t},isInteger:function(t){for(var e,n=0,r=t.length;n<r;){if(!((e=t.charCodeAt(n))>=48&&e<=57))return!1;n++}return!0},isEquals:function t(n,r){if(n===r)return!0;if(n&&r&&"object"==e(n)&&"object"==e(r)){var o,a,i,c=Array.isArray(n),s=Array.isArray(r);if(c&&s){if((a=n.length)!=r.length)return!1;for(o=a;0!=o--;)if(!t(n[o],r[o]))return!1;return!0}if(c!=s)return!1;var u=Object.keys(n);if((a=u.length)!==Object.keys(r).length)return!1;for(o=a;0!=o--;)if(!r.hasOwnProperty(u[o]))return!1;for(o=a;0!=o--;)if(!t(n[i=u[o]],r[i]))return!1;return!0}return n!=n&&r!=r},unique:function(t){for(var e=0;e<t.length;e++)for(var n=e+1;n<t.length;n++)t[e]==t[n]&&(t.splice(n,1),n--);return t},arrayToMap:function(t){var e={};return t.forEach((function(t){e[t]=!0})),e},merge:function(t,e){for(var n in e)t[n]=e[n];return t},escapePathComponent:function(t){return-1===t.indexOf("/")&&-1===t.indexOf("~")?t:t.replace(/~/g,"~0").replace(/\//g,"~1")},unescapePathComponent:function(t){return t.replace(/~1/g,"/").replace(/~0/g,"~")}}}},n={},r=function t(r){var o=n[r];if(void 0!==o)return o.exports;var a=n[r]={exports:{}};return e[r](a,a.exports,t),a.exports}(497),t.exports=r},4253:function(t,e,n){function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}t.exports={version:n(306).i8,combinecommit:!1,$:void 0===("undefined"==typeof Symbol?"undefined":r(Symbol))?"$":Symbol("")}},9201:function(t){t.exports={ERR_NO_MODULE:"no module error",ERR_NO_FUNCTION:"no function error",ERR_PARAM_ERROR:"param error，module.method"}},3381:function(t,e,n){var r=n(1788);t.exports=function(t){r.alert||(r.alert=function(e){t.c("$app.alert",{message:e,confirmTitle:"确定"})})}},9234:function(t,e,n){function r(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function o(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var a=n(7351),i=n(4253),c=n(5472),s=n(9201),u=n(3156),f=n(3212),l=n(8426),p=[],h=0,d={},g=null,m={$:{},console:a,env:f,util:c,version:i.version,_instances:{},setting:function(t){Object.keys(t).forEach((function(e){i[e]=t[e]}))},getSetting:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r(Object(n),!0).forEach((function(e){o(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},i)},register:function(){u.register.apply(u,arguments)},addFilter:function(t){p.push(t)},removeFilter:function(t){var e=p.indexOf(t);e>-1&&p.splice(e,1)},reportJSError:function(t,e,n){var r={msg:e.message,stack:null!=n?e.stack+n:e.stack};return this.c("$.report",{name:"js_error",params:r,action:t})},nanotime:function(){return this._nanotime?this._nanotime():1e6*(new Date).getTime()},cb:function(t){if(t&&t.methodId){var e=d[t.methodId];if(e){e.cnt--,e.cnt<=0&&delete d[t.methodId];try{g=e.stack,e.cb&&e.cb(t.data,t.error)}catch(t){this.reportJSError(e.action+".cb",t,g)}g=null}}},c:function(t,e,n,r){if(t)try{var o={name:t,args:e||{}};if(n){var a=Error().stack,i=a.indexOf("\n");i>0&&(a=a.substring(i)),a="\nAsyncStack: \n"+a;var c=g?a+g:a,s=++h;o.cid=s,d[s]={cnt:r||1,cb:n,action:t,stack:c}}for(var u=p.length;u-- >0;){var l=p[u];if(l.call&&1==l.call.call(l,o))return}if(this._c&&o){var m=1==f.config.jsi?[o]:1==f.config.dataformat?JSON.stringify([o],(function(t,e){if(null!==e&&"undefined"!==e&&e==e)return e})):JSON.stringify([o]);this._c(m)}for(u=p.length;u-- >0;){var y=p[u];y.callhandler&&y.callhandler.call(y,o)}}catch(e){"$.report"!==t&&this.reportJSError(t,e,g)}},dispatch:function(t){var e=this;try{var n="string"==typeof t?JSON.parse(t):t;n&&n.forEach((function(t){e.message(t)})),l.flush()}catch(t){this.reportJSError("dispatch",t)}},message:function(t){var e=this,n=t.name,r=t.cid,o=t.args,i=t.flag,f=function(t,n){var o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];n&&a.error(n),r&&"undefined"!==r&&(n&&c.isString(n)&&(n={message:n}),e.c("$.cb",{methodId:r,data:t,error:n,retain:o},null))};if(n){var l=n.split(".");if(l.length>=2){var h=l.shift(),d="$"===h?this:u.require(h);if(!d)return void f(null,c.stringformat("action:{0} error:{1}",n,s.ERR_NO_MODULE));for(var m=d;l.length>1&&m;)m=m[l.shift()];if(m&&l.length>0){var y,v=l.shift(),b=m[v]||(null===(y=m)||void 0===y?void 0:y.prototype[v]);if(!c.isFunction(b))return void f(null,c.stringformat("function:{0} error:{1}",n,s.ERR_NO_FUNCTION));var O={m:m,fn:b,args:o,flag:i,cb:f};try{for(var x=p,S=x.length;S-- >0;){var w=x[S];if(w.message&&1==w.message(O))return}for(Array.isArray(o)?(O.cb&&o.push(O.cb),b.apply(O.m,O.args)):O.cb?b.call(O.m,O.args,O.cb):b.call(O.m,O.args),S=x.length;S-- >0;){var $=x[S];$.messagehandler&&$.messagehandler(O)}return}catch(t){return f(null,c.stringformat("action:{0} error:{1}",n,t.message)),this.reportJSError(n,t,g),void(g=null)}}}}f(null,c.stringformat("action:{0} error:{1}",n,s.ERR_PARAM_ERROR))}};t.exports=m},6495:function(t){function e(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function n(t,e){if(""==e||null==e)return t;for(var n=Array.isArray(e)?e:e.split("."),r=0,o=null,a=null,i=null;null!=t&&r<n.length;)if(1==(i=(o=n[r++]).split("[")).length)t=t[o];else{t=t[i[0]];for(var c=1;c<i.length;++c){if(a=parseInt(i[c]),!Array.isArray(t)||a>=t.length)return null;t=t[a]}}return t}var r=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.instance=e,this.version=0,this.pageData={},this.options={},this.states={},this.orginPageData={}}var r,o,a;return r=t,(o=[{key:"setPageData",value:function(t){this.pageData=t,this.orginPageData=JSON.parse(JSON.stringify(t)),this.version++}},{key:"getPageData",value:function(){return this.pageData}},{key:"updatePageData",value:function(t){this.pageData=t}},{key:"getDataForKeyPath",value:function(t){return n(this.pageData||{},t)}},{key:"setDataForKeyPath",value:function(t,e){if(null==t||""==t)return this.setPageData(e);!function(t,e,r){var o=null,a=null;if(/\[[0-9]+\]$/.test(e)){var i=e.split("[");o=parseInt(i[i.length-1]),a=e.substr(0,e.length-2-(o+"").length)}else a=e.split("."),o=e[e.length-1],a.splice(a.length-1,1);var c=n(t,a);null!=c&&(c[o]=r)}(this.pageData||{},t,e),this.version++}},{key:"getOrginDataForKeyPath",value:function(t){return n(this.orginPageData||{},t)}},{key:"setOptions",value:function(t){this.options=t}},{key:"getOptions",value:function(){return this.options}},{key:"setState",value:function(t,e){this.states[t]=e}},{key:"getState",value:function(t){return this.states[t]}},{key:"flush",value:function(){this.orginPageData=JSON.parse(JSON.stringify(this.pageData)),this.version++}}])&&e(r.prototype,o),a&&e(r,a),Object.defineProperty(r,"prototype",{writable:!1}),t}(),o={};t.exports=function(t){var e=o[t];return e||(e=new r(t),o[t]=e),e}},8868:function(t,e,n){function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var o=n(9758),a=n(5472),i=n(6495),c=n(3624);var s=new(n(8813)),u=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n,r=e.args.instanceId,o=i(r);null!=e.args.data&&o.setPageData(e.args.data),null!=e.args.options&&o.setOptions(e.args.options),this.instanceId=r,this.keypath=e.args.keypath,this.type=e.args.msg,this.target=e.args.target,this.index=(n=this.keypath)&&(n=n.match(/\[(\d+)\]/g))?parseInt(n[n.length-1].substr(1)):0,this.time=e.args.time,this.attrs=e.args.attrs||{},this.params=e.args.params||{},this.props=e.args.props||{},this.reload()}var e,n,u;return e=t,n=[{key:"reload",value:function(){var t=i(this.instanceId);this.options=t.getOptions()||{},this.data=o.clone(t.getDataForKeyPath(this.keypath))||{},this.originalData=o.clone(this.data)||{}}},{key:"makesnapshot",value:function(t){var e={target:this.target};t&&(e.data=t),a.tnode().c("$app.makeCache",e)}},{key:"commit",value:function(t,e){t=(a.isObject(t)?t:{force:1==t})||{};var n=i(this.instanceId);if(1!=t.flush){var r={data:this.originalData},c={data:this.data},s=t.patches?t.patches:o.diff(r,c);if(a.tnode().c("$app.commit",{msg:this.type,time:this.time,target:this.target,instanceId:this.instanceId,keypath:this.keypath,patches:s,force:t.force,options:t.options},e),s&&s.length>0){var u=n.getDataForKeyPath(this.keypath)||{},f=o.applyPatch({data:u},s);n.setDataForKeyPath(this.keypath,f.data),this.data=o.clone(f.data),this.originalData=o.clone(this.data)}return s}n.setDataForKeyPath(this.keypath,o.clone(this.data))}},{key:"postMessage",value:function(t,e,n,r){return a.tnode().c("$app.postMessage",{target:this.target,msg:t,value:r,args:e,flag:n})}},{key:"postEvent",value:function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=1==n?c.Bubble:c.Depth;return this.postMessage(t,e,r|c.Container|c.ExceptSelf)}},{key:"on",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return s.on.apply(s,[t].concat(n))}},{key:"once",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.once.apply(s,[t].concat(n))}},{key:"off",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.off.apply(s,[t].concat(n))}},{key:"send",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.send.apply(s,[t].concat(n))}}],n&&r(e.prototype,n),u&&r(e,u),Object.defineProperty(e,"prototype",{writable:!1}),t}();t.exports=u},8330:function(t,e,n){function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var o=n(9758),a=n(5472),i=n(6495),c=n(3624);var s=new(n(8813)),u=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.instanceId=e.args.instanceId,this.tid=e.args.tid,this.keypath=e.args.keypath;var n,r=i(this.instanceId);this.isvalid()?(null!=e.args.data&&r.setPageData(e.args.data),null!=e.args.options&&r.setOptions(e.args.options)):this._.data=e.args.data,this.type=e.args.msg,this.target=e.args.target,this.index=(n=this.keypath)&&(n=n.match(/\[(\d+)\]/g))?parseInt(n[n.length-1].substr(1)):0,this.time=e.args.time,this.attrs=e.args.attrs||{},this.params=e.args.params||{},this.props=e.args.props||{},this.options=r.getOptions()||{}}var e,n,u;return e=t,n=[{key:"isvalid",value:function(){return null!=this.keypath}},{key:"reload",value:function(){}},{key:"makesnapshot",value:function(t){var e={target:this.target};t&&(e.data=t),a.tnode().c("$app.makeCache",e)}},{key:"commit",value:function(t,e){if(this.isvalid()){t=(a.isObject(t)?t:{force:1==t})||{};var n={data:i(this.instanceId).getOrginDataForKeyPath(this.keypath)},r={data:this.data},c=t.patches?t.patches:o.diff(n,r);return a.tnode().c("$app.commit",{msg:this.type,time:this.time,target:this.target,instanceId:this.instanceId,keypath:this.keypath,patches:c,force:t.force,options:t.options},e),c&&c.length>0&&o.applyPatch(n,c),c}}},{key:"postMessage",value:function(t,e,n,r){return a.tnode().c("$app.postMessage",{target:this.target,msg:t,value:r,args:e,flag:n})}},{key:"postEvent",value:function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=1==n?c.Bubble:c.Depth;return this.postMessage(t,e,r|c.Container|c.ExceptSelf)}},{key:"on",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return s.on.apply(s,[t].concat(n))}},{key:"once",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.once.apply(s,[t].concat(n))}},{key:"off",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.off.apply(s,[t].concat(n))}},{key:"send",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];s.send.apply(s,[t].concat(n))}}],n&&r(e.prototype,n),u&&r(e,u),Object.defineProperty(e,"prototype",{writable:!1}),t}();Object.defineProperty(u.prototype,"data",{get:function(){return this.isvalid()?i(this.instanceId).getDataForKeyPath(this.keypath)||{}:this._.data},set:function(t){var e=this.data;if(e!==t){var n=i(this.instanceId);e===n.getPageData()?n.updatePageData(t):(Object.keys(e).forEach((function(t){delete e[t]})),Object.keys(t).forEach((function(n){e[n]=t[n]})))}},configurable:!1,enumerable:!1}),Object.defineProperty(u.prototype,"_",{value:{},configurable:!1,enumerable:!1,writable:!1}),t.exports=u},3711:function(t,e,n){function r(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var o=n(9758),a=n(5472),i=n(6495),c=n(3624),s=n(8813),u=n(8426),f=n(4253).$;function l(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t&&(t=t.match(/\[(\d+)\]/g))?parseInt(t[t.length-1].substr(1)):e}var p=0,h=new s,d=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=e.args.keypath;this[f]={data:e.args.data,cid:l(n,-1),kp:n,flush:!1},this.instanceId=e.args.instanceId,this.tid=e.args.tid,this.target=e.args.target,this.type=e.args.msg,this.time=e.args.time,this.attrs=e.args.attrs||{},this.params=e.args.params||{},this.props=e.args.props||{};var r=i(this.instanceId);this.isvalid()&&(null!=e.args.data&&r.setPageData(e.args.data),null!=e.args.options&&r.setOptions(e.args.options),this[f].data=r.getDataForKeyPath(this.keypath)),this.options=r.getOptions()||{}}var e,n,s;return e=t,n=[{key:"keypath",get:function(){var t=this,e=this[f];return e.cid>=0&&e.cid!=p&&(e.cid=p,a.tnode().c("$app.keypathForTarget",{target:this.target},(function(n){n&&n!==e.kp&&(e.kp=n,t.index=l(n))}))),e.kp}},{key:"index",get:function(){return l(this.keypath)}},{key:"isvalid",value:function(){return null!=this.keypath}},{key:"reload",value:function(){}},{key:"makesnapshot",value:function(t){var e={target:this.target};t&&(e.data=t),a.tnode().c("$app.makeCache",e)}},{key:"commit",value:function(t,e){if(this.isvalid()){t=(a.isObject(t)?t:{force:1==t})||{};var n=i(this.instanceId);if(1==t.flush||e||!u.isEnabled()){this[f].flush=!1;var r={data:n.getOrginDataForKeyPath(this.keypath)},c={data:this.data},s=t.patches?t.patches:o.diff(r,c);return a.tnode().c("$app.commit",{msg:this.type,time:this.time,target:this.target,instanceId:this.instanceId,keypath:this.keypath,patches:s,force:t.force,options:t.options},e),s&&s.length>0&&o.applyPatch(r,s),p++,s}u.push(this),this[f].flush=!0}}},{key:"postMessage",value:function(t,e,n,r){return a.tnode().c("$app.postMessage",{target:this.target,msg:t,value:r,args:e,flag:n})}},{key:"postEvent",value:function(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=1==n?c.Bubble:c.Depth;return this.postMessage(t,e,r|c.Container|c.ExceptSelf)}},{key:"on",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return h.on.apply(h,[t].concat(n))}},{key:"once",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];h.once.apply(h,[t].concat(n))}},{key:"off",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];h.off.apply(h,[t].concat(n))}},{key:"send",value:function(t){t=[this.instanceId,t].join(":");for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];h.send.apply(h,[t].concat(n))}}],n&&r(e.prototype,n),s&&r(e,s),Object.defineProperty(e,"prototype",{writable:!1}),t}();Object.defineProperty(d.prototype,"data",{get:function(){return this[f].data},set:function(t){var e=this.data;if(e!==t){var n=i(this.instanceId);e===n.getPageData()?(n.updatePageData(t),this[f].data=t):(Object.keys(e).forEach((function(t){delete e[t]})),Object.keys(t).forEach((function(n){e[n]=t[n]})))}},configurable:!1,enumerable:!1}),t.exports=d},9758:function(t,e,n){var r=n(2976);t.exports={clone:function(t){return r.clone(t)},diff:function(t,e,n){return r.diff(t,e,n)},applyPatch:function(t,e,n){return r.applyPatch(t,e,n)}}},7488:function(){function t(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return e(t,n);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return e(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var n=Array.prototype;n.remove||(n.remove=function(t){var e=this.indexOf(t);return e>-1&&this.splice(e,1),this}),n.removeAt||(n.removeAt=function(t){return this.splice(t,1),this}),n.contains||(n.contains=function(t){return-1!=this.indexOf(t)}),n.includes||(n.includes=n.contains),n.clear||(n.clear=function(){return this.length=0,this}),n.insertAt||(n.insertAt=function(t,e){return this.splice(t,0,e),this}),n.insertListAt||(n.insertListAt=function(e,n){return this.splice.apply(this,[e,0].concat(t(n))),this}),n.shuffle||(n.shuffle=function(){for(var t,e,n=this,r=n.length;r;)e=Math.floor(Math.random()*r--),t=n[r],n[r]=n[e],n[e]=t;return n})},9831:function(){void 0===Object.assign&&Object.defineProperty(Object,"assign",{value:function(t){var e,n,r,o,a,i,c,s,u;if(null==t)throw new Exception("target null or undefined");for(e=1,n=arguments.length;e<n;e++)if(null!=(i=arguments[e]))for(i=Object(i),r=0,o=(c=Object.keys(i)).length;r<o;r++){a=c[r];try{t[a]=i[a]}catch(t){s||(s=!0,u=t)}}if(s)throw u},writable:!0,enumerable:!1,configurable:!0})},1225:function(){var t=Date.prototype;t.formatPassTime||(t.formatPassTime=function(){var t=Date.parse(new Date)-this,e=parseInt(t/864e5),n=parseInt(t/36e5),r=parseInt(t/6e4),o=parseInt(e/30),a=parseInt(o/12);return a?a+"年前":o?o+"个月前":e?e+"天前":n?n+"小时前":r?r+"分钟前":"刚刚"}),t.formatRemainTime||(t.formatRemainTime=function(){var t=(new Date).getTime()-this.getTime();return t<0?"":t/1e3<30?"刚刚":t/1e3<60?parseInt(t/1e3)+"秒前":t/6e4<60?parseInt(t/6e4)+"分钟前":t/36e5<24?parseInt(t/36e5)+"小时前":t/864e5<31?parseInt(t/864e5)+"天前":t/2592e6<12?parseInt(t/2592e6)+"月前":parseInt(t/31536e6)+"年前"})},4319:function(t,e,n){n(1182),n(6699),n(7488),n(1225),n(9831);var r=n(1788);r.Promise||(r.Promise=n(5936))},1182:function(){Object.entries||(Object.entries=function(t){for(var e=Object.keys(t),n=e.length,r=new Array(n);n--;)r[n]=[e[n],t[e[n]]];return r})},5936:function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}function n(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function r(t,e,r){return e&&n(t.prototype,e),r&&n(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var o=setTimeout;function a(){}var i=r((function t(e){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),!(this instanceof t))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],p(e,this)}));function c(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var r;try{r=n(t._value)}catch(t){return void u(e.promise,t)}s(e.promise,r)}else(1===t._state?s:u)(e.promise,t._value)}))):t._deferreds.push(e)}function s(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"===e(n)||"function"==typeof n)){var r=n.then;if(n instanceof i)return t._state=3,t._value=n,void f(t);if("function"==typeof r)return void p((o=r,a=n,function(){o.apply(a,arguments)}),t)}t._state=1,t._value=n,f(t)}catch(e){u(t,e)}var o,a}function u(t,e){t._state=2,t._value=e,f(t)}function f(t){2===t._state&&0===t._deferreds.length&&i._immediateFn((function(){t._handled||i._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)c(t,t._deferreds[e]);t._deferreds=null}function l(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=n}function p(t,e){var n=!1;try{t((function(t){n||(n=!0,s(e,t))}),(function(t){n||(n=!0,u(e,t))}))}catch(t){if(n)return;n=!0,u(e,t)}}i.prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,e){var n=new this.constructor(a);return c(this,new l(t,e,n)),n},i.prototype.finally=function(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))},i.all=function(t){return new i((function(n,r){if(!t||void 0===t.length)throw new TypeError("Promise.all accepts an array");var o=Array.prototype.slice.call(t);if(0===o.length)return n([]);var a=o.length;function i(t,c){try{if(c&&("object"===e(c)||"function"==typeof c)){var s=c.then;if("function"==typeof s)return void s.call(c,(function(e){i(t,e)}),r)}o[t]=c,0==--a&&n(o)}catch(t){r(t)}}for(var c=0;c<o.length;c++)i(c,o[c])}))},i.resolve=function(t){return t&&"object"===e(t)&&t.constructor===i?t:new i((function(e){e(t)}))},i.reject=function(t){return new i((function(e,n){n(t)}))},i.race=function(t){return new i((function(e,n){for(var r=0,o=t.length;r<o;r++)t[r].then(e,n)}))},i._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){o(t,0)},i._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},t.exports=i},6699:function(){var t=String.prototype;t.trim||(t.trim=function(){return this.replace(/(^\s*)|(\s*$)/g,"")}),t.trimLeft||(t.trimLeft=function(){return this.replace(/(^\s*)/g,"")}),t.trimRight||(t.trimRight=function(){return this.replace(/(\s*$)/g,"")}),t.interceptString||(t.interceptString=function(t){return this.length>t?this.substring(0,t)+"...":this}),t.startsWith||(t.startsWith=function(t){return this.substring(0,t.length)==t}),t.endWith||(t.endWith=function(t){return this.substring(this.length-t.length)==t}),t.reserve||(t.reserve=function(){for(var t="",e=this.length-1;e>=0;e--)t=t.concat(this.charAt(e));return t})},2505:function(t){t.exports={reset:"[0m",bright:"[1m",dim:"[2m",underscore:"[4m",blink:"[5m",reverse:"[7m",hidden:"[8m",black:"[30m",red:"[31m",green:"[32m",yellow:"[33m",blue:"[34m",magenta:"[35m",cyan:"[36m",white:"[37m"}},3212:function(t){t.exports={debug:!1,app:{name:null,schema:null,version:null},engine:{name:null,version:null},system:{model:null,name:null,os:null,uuid:null,version:null,isIOS:function(){return"iOS"==this.os},isAndroid:function(){return!this.isIOS()}},config:{tdiffer:!0,jsi:!1,dataformat:!0,eventversion:0}}},8426:function(t,e,n){var r=n(4253),o=r.$,a=r.combinecommit;t.exports={list:[],isEnabled:function(){return a},push:function(t){var e;!0!==(null==t||null===(e=t[o])||void 0===e?void 0:e.flush)&&this.list.push(t)},flush:function(){var t=this;if(this.list.length>0){var e=this.list;this.list=[],e&&e.forEach((function(e){t._commit(e)}))}},_commit:function(t){var e;t&&1==(null==t||null===(e=t[o])||void 0===e?void 0:e.flush)&&t.commit({flush:!0})}}},1045:function(t,e,n){var r=n(8868),o=n(8330),a=n(3711),i=(n(4253),n(5472));t.exports={createInstance:function(t,e){var n=t.m,r=t.args;if(i.isFunction(n)){var o=i.tnode(),a=r.instanceId,c=o._instances[a];c||(o._instances[a]=c={});var s=r&&r.tid?r.tid:n,u=c[s];u||(c[s]=u=new n(e)),t.m=u}},message:function(t){var e=i.isString(t.flag)?(t.flag||"").split("|"):[];if(-1!=e.indexOf("newEvent")){var n=3==i.tnode().env.config.eventversion?new a(t):new o(t);t.cb=null,t.args=n,this.createInstance(t,n)}else if(-1!=e.indexOf("jsonpatchex")){var c=new r(t);t.cb=null,t.args=c,this.createInstance(t,c)}}}},1788:function(t){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"===("undefined"==typeof window?"undefined":e(window))&&(n=window)}t.exports=n},7351:function(t,e,n){var r=n(8975).sprintf,o=n(5472),a=n(2505),i=n(1788);function c(t,e){return function(){try{if(arguments.length>0){var n=arguments[0];if(o.isString(n)&&arguments.length>1)n=r.apply(r,arguments);else if(arguments.length>1){var a=[];Array.prototype.slice.call(arguments,0).forEach((function(t){a.push(o.stringify(t))})),n=a.join(" ")}"undefined"!=typeof process&&1==o.tnode().env.debug?t.call(console,e+"[TNodeJs] "+n):t.call(console,"[TNodeJs] "+n)}}catch(e){t.call(console,e)}}}i.console||(i.console={debug:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.splice(0,0,0),t._logger.apply(t,e)}},log:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.splice(0,0,1),t._logger.apply(t,e)}},info:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.splice(0,0,1),t._logger.apply(t,e)}},warn:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.splice(0,0,2),t._logger.apply(t,e)}},error:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.splice(0,0,3),t._logger.apply(t,e)}},assert:function(){var t=o.tnode();if(t._logger){var e=Array.prototype.slice.call(arguments);e.length>0&&!0!=!!e[0]&&(e.splice(0,1),e.splice(0,0,3),t._logger.apply(t,e))}}}),console.info=c(console.info,a.blue),console.debug=c(console.debug,a.blue),console.warn=c(console.warn,a.yellow),console.error=c(console.error,a.red),t.exports=console},3156:function(t,e,n){var r=n(5472),o=n(7351),a={};t.exports={register:function(t,e,n){o.debug("[actionservice] js register:".concat(t)),!a[t]&&e&&(a[t]={code:e},(n||{}).preload&&this.require(t))},require:function(t){var e=t,n=a[e],i=n?n.module:null;if(!i&&n)try{i={id:e,name:t,loaded:!1,require:this.require,exports:{id:e}},n.module=i,o.debug("[actionservice] load module:".concat(e));var c=r.requierBuilder(n.code);r.isFunction(c)?c.call(i.exports,i,i.exports,i.require):o.error("load module:".concat(n.code," error")),i.loaded=!0,o.debug("[actionservice] load module:".concat(e," end"))}catch(t){o.error("load module error:".concat(t)),delete n.module}return i?i.exports:null}}},5472:function(t){function e(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function n(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?e(Object(o),!0).forEach((function(e){r(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))}))}return t}function r(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}t.exports={isTrue:function(t){var e=o(t);return"boolean"==e&&t||"string"==e&&"true"==t},isFunction:function(t){return t&&"[object Function]"=={}.toString.call(t)},isString:function(t){return t&&"string"==typeof t},isJsonString:function(t){try{return this.isObject(JSON.parse(t))}catch(t){}return!1},isObject:function(t){return t instanceof Object},unique:function(t){if(Array.isArray(t))for(var e=0;e<t.length;e++)for(var n=e+1;n<t.length;n++)t[e]==t[n]&&(t.splice(n,1),n--);return t},execute:function(t,e,n,r){var o=null;if(n)if(Array.isArray(n))o=n;else{var a=n,i=function(t){var e=t.toString();return 0!=e.indexOf("function")&&(e="function "+e),e.match(/function\s.*?\(([^)]*)\)/)[1].split(",").map((function(t){return t.replace(/\/\*.*\*\//,"").trim()})).filter((function(t){return t}))}(e),c=new Array,s=(c=i.toString().split(",")).length,u=Object.keys(a).length,f=s<u?s:u;if(f){o=[];for(var l=0;l<f;l++){var p=a[c[l]];o.push(p)}}else a&&(o=[a])}o&&Array.isArray(o)&&r&&o.push(r),e.apply(t,o)},stringify:function(t){return null==t?"":"object"===o(t)?t instanceof RegExp?t.toString():t instanceof Date?JSON.parse(JSON.stringify(t)):JSON.stringify(t,(function(t,e){if(null!==e&&"undefined"!==e)return e})):t.toString()},def:function(t,e,n,r){Object.defineProperty(t,e,{value:n,enumerable:!!r,writable:!0,configurable:!0})},enabledEnumerables:function(t,e,n){var r=this;e.forEach((function(e){r.def(t,e,t[e],n)}))},extend:function(t,e,n){var r=this;if(t===e||!e)return t;if(n){var o=[];o.push([t,e]);for(var a=function(t,e,n){for(var o in e){var a=t[o],i=e[o];if(r.isObject(i)&&!r.isFunction(i)){if(!r.isObject(a)){if(Array.isArray(i)){var c=[];t[o]=c,i.forEach((function(t){var e={};c.push(e),n.push([e,t])}));continue}t[o]=a={}}n.push([a,i])}else t[o]=i}};o.length>0;){var i=o.pop();a(i[0],i[1],o)}}else for(var c in e)t[c]=e[c];return t},copy:function(t){return extend({},t)},loadScript:function(t,e){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,e())}:n.onload=function(){e()},n.src=t,document.getElementsByTagName("head").item(0).appendChild(n)},requierBuilder:function(t){if(this.isString(t))return new Function("module, exports, require","return "+t);if(this.isObject(t)){var e=this;return function(n,r,o){e.extend(r,t)}}},parserURL:function(t){var e=t.match(/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/);return{url:t,schema:e[1],slash:e[2],host:e[3],port:e[4],path:e[5],query:function(t){var e=new Object;if(t)if(-1!=t.indexOf("&"))for(var n=t.split("&"),r=0;r<n.length;r++)e[n[r].split("=")[0]]=decodeURIComponent(n[r].split("=")[1]);else e[t.split("=")[0]]=decodeURIComponent(t.split("=")[1]);return e}(e[6]),hash:e[7]}},encodeURLQuery:function(t){var e=[];for(var n in t){var r=t[n];switch(o(r)){case"string":case"number":e.push(encodeURIComponent(n)+"="+encodeURIComponent(r));break;case"boolean":e.push(encodeURIComponent(n)+"="+(1==r?"true":"false"));break;case"object":e.push(encodeURIComponent(n)+"="+encodeURIComponent(JSON.stringify(r)))}}return e.join("&")},appendURLQuery:function(t,e){var r=this,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t&&t.length>0&&e&&Object.keys(e).length>0){var a=t.indexOf("?");if(-1!=a){var i={},c=t.substring(a+1),s=c.split("&");return s&&s.forEach((function(t){var e=t.split("=");if(2==e.length){var n=decodeURIComponent(e[0]),r=decodeURIComponent(e[1]);i[n]=r}})),o?(Object.keys(e).forEach((function(t){var o=i[t],a=e[t];null!==o&&a instanceof Object&&o instanceof Object?i[t]=n(n({},o),a):null!=o&&a instanceof Object&&r.isJsonString(o)?i[t]=n(n({},JSON.parse(o)),a):i[t]=a})),"".concat(t.substring(0,a),"?").concat(this.encodeURLQuery(n({},i)))):"".concat(t.substring(0,a),"?").concat(this.encodeURLQuery(n(n({},i),e)))}return"".concat(t,"?").concat(this.encodeURLQuery(e))}return t},stringformat:function(t){var e=this;if(0==arguments.length)return null;if(t){if(this.isString(t)){var n=Array.prototype.slice.call(arguments,1);return t.replace(/\{(\d+)\}/g,(function(t,r){var o=n[r];return e.stringify(o)}))}var r=[];return Array.prototype.slice.call(arguments,0).forEach((function(t){r.push(e.stringify(t))})),r.join("")}},tnode:function(){return function(){var t=function(){return this}();try{t=t||Function("return this")()||(0,eval)("this")}catch(e){"object"===("undefined"==typeof window?"undefined":o(window))&&(t=window)}return t.$ac||t.$ac__}()}}},3705:function(t,e,n){t=n.nmd(t),function(t,e,r){if(!t.$ac){try{null!=e&&null!=e.env&&(t.console=null)}catch(t){}n(4319);var o=n(9234),a=n(5472),i=n(1045),c=n(3381);o.addFilter(i),a.extend(o.env,e.env,!0),c(o),t.$ac||(t.$ac=o);try{t.$ac.$=n(8379)}catch(t){}}r.exports=t.$ac}(n(1788),"undefined"==typeof nativeModule?{}:nativeModule,t)},7556:function(t,e,n){"use strict";var r=n(9918);t.exports={get:function(t,e){r.c("$ab.get",{keypath:t},(function(t){e&&e(t)}))}}},9800:function(t,e,n){"use strict";var r=n(9918);t.exports={animate:function(t,e,n){return r.c("$animation.animate",{target:t,options:e,id:e.id},n)},transition:function(t,e){return r.c("$animation.transition",{target:t,options:e,id:e.id})}}},7008:function(t,e,n){"use strict";var r=n(9918),o=n(6021);t.exports={appear:function(t,e){r.c("$apm.enter",{target:t,args:e})},disappear:function(t){r.c("$apm.exit",{target:t})},valid:function(t){r.c("$apm.enabled",{target:t})},registerStages:function(t){o.call("NWFullTracePlugin","registerStages",t,(function(t){}),(function(t){}))},fetchTrackerId:function(t){o.call("NWFullTracePlugin","getTraceId",{},(function(e){var n=e.traceId;t&&t(n)}),(function(e){t&&t(0)}))},commit:function(t,e,n){if(t){var r={traceId:t,module:"TNode",tag:e,stages:n};o.call("NWFullTracePlugin","commitModuleTrace",r)}}}},2573:function(t,e,n){"use strict";var r=n(9918),o=n(3624),a={Message:0,Tap:1,PullRefresh:2,Favorite:3,Cart:4,Like:5,Pay:6,Publish:7};Object.freeze(a),t.exports={msgflag:o,postMessage:function(t){return r.c("$app.postMessage",t)},on:function(t,e,n){var o=this;return void 0===n&&(n=2147483647),r.c("$notify.onNotify",{name:t},(function(r,a){e&&e(r,a),--n>0&&o.on(t,e,n)}))},getNodeInfo:function(t,e,n,r){return this.getNodeInfoex({target:t,imageId:e,containerId:n},r)},getNodeInfoex:function(t,e){return r.c("$app.getNodeInfo",t,e)},getTNodeInfo:function(t,e){return r.c("$app.getTNodeInfo",t,e)},getNodeInfoInList:function(t,e,n,o,a){return r.c("$app.getNodeInfo",{target:t,imageId:e,containerId:n,listId:o},a)},setPasteboard:function(t){return r.c("$app.setPasteboard",{value:t})},getPasteboard:function(t){return r.c("$app.getPasteboard",t)},toast:function(t,e,n){return void 0===e&&(e=1500),r.c("$app.toast",{message:t,duration:e||1500,target:n})},showLoading:function(t,e){return void 0===e&&(e=null),r.c("$app.showLoading",{show:!0,title:t,target:e})},hideLoading:function(t){return r.c("$app.showLoading",{show:!1,target:t})},alert:function(t,e,n,o,a,i){return r.c("$app.alert",{title:t,message:e,confirmTitle:n,cancelable:a,cancelTitle:o},i)},actionSheet:function(t,e,n,o){return r.c("$app.actionSheet",{title:t,message:e,buttons:n},o)},SoundType:a,playSoundForType:function(t){return r.c("$app.playSound",{type:t})},playSound:function(t){return r.c("$app.playSound",{url:t})},showTips:function(t,e,n){return r.c("$app.showTips",{message:t,duration:e,target:n})},loadFont:function(t,e,n){return r.c("$app.loadFont",{url:t,fontName:e},n)},closePoplayer:function(t){return r.c("$app.closePoplayer",{target:t})},generateKey:function(t,e){return r.c("$app.generateKey",{url:t},e)},impactFeedback:function(t,e){return r.c("$app.impactFeedback",{type:t,target:e})},openTNodeTool:function(){return r.c("$app.openTNodeTool")},preloadTNodeDSL:function(t,e){var n=Array.isArray(t)?t:[t];return r.c("$app.preloadDSL",{urls:n},e)},prefetchNext:function(t){return r.c("$app.prefetchNext",t)},setLocalSetting:function(t){return r.c("$app.setLocalSetting",{setting:t})},getLocalSetting:function(t){return r.c("$app.getLocalSetting",null,t)},setABLocalSetting:function(t){return r.c("$app.setABLocalSetting",{setting:t})},getABLocalSetting:function(t){return r.c("$app.getABLocalSetting",null,t)},showQR:function(t){r.c("$app.showQR",null,t)}}},3116:function(t,e,n){"use strict";var r=n(9918);t.exports={commitSuccess:function(t,e,n){r.c("$appMonitor.commitSuccess",{pageName:t,monitorPoint:e,arg:n})},commitFail:function(t,e,n,o,a){r.c("$appMonitor.commitFail",{pageName:t,monitorPoint:e,errorCode:n,errorMsg:o,arg:a})}}},9862:function(t,e,n){"use strict";var r=n(9918);t.exports={state:function(t){r.c("$application.state",null,t)}}},9958:function(t,e,n){"use strict";var r=n(9918);t.exports={commitEnter:function(t,e,n){r.c("$behavior.commitEnter",{scene:t,bizId:e,bizArgs:n})},commitLeave:function(t,e,n){r.c("$behavior.commitLeave",{scene:t,bizId:e,bizArgs:n})},commitTap:function(t,e,n,o,a){r.c("$behavior.commitTap",{scene:t,actionName:e,actionArgs:n,bizId:o,bizArgs:a})},trackScrollStart:function(t,e,n,o,a){r.c("$behavior.trackScrollStart",{scene:t,actionName:e,currentOffsetX:n,currentOffsetY:o,bizArgs:a})},trackScrollEnd:function(t,e,n,o,a){r.c("$behavior.trackScrollEnd",{scene:t,actionName:e,currentOffsetX:n,currentOffsetY:o,bizArgs:a})},trackAppear:function(t,e,n,o,a){r.c("$behavior.trackAppear",{scene:t,actionName:e,bizId:n,target:o,bizArgs:a})},trackDisAppear:function(t,e,n,o,a){r.c("$behavior.trackDisAppear",{scene:t,actionName:e,bizId:n,target:o,bizArgs:a})},commitRequest:function(t,e,n,o){r.c("$behavior.commitRequest",{scene:t,actionName:e,requestId:n,bizArgs:o})},registerConfig:function(t,e){r.c("$behavior.registerConfig",{bizName:t,bizConfig:e})},onSolution:function(t,e,n){void 0===n&&(n=2147483647),r.c("$behavior.onSolution",t,(function(t){e&&e(t)}),n)}}},1079:function(t,e,n){"use strict";var r=n(9918);t.exports={reportBusiness:function(t){r.c("$business.reportBusiness",t)}}},6390:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){}return t.configOfTNode=function(){},t}();e.default=n},4364:function(t,e,n){"use strict";function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},o.apply(this,arguments)},a=n(9918),i=function(){function t(t){this.op=t}return t.prototype._post=function(t,e,n,r){return void 0===n&&(n=null),void 0===r&&(r=1),e=o(o({},this.op),e),a.c(t,e,n,r)},t.prototype.scrollToTop=function(t){return void 0===t&&(t=!0),this._post("$element.scrollToTop",{animated:t})},t.prototype.scrollToBottom=function(t){return void 0===t&&(t=!0),this._post("$element.scrollToBottom",{animated:t})},t.prototype.scroll=function(t,e,n){return void 0===e&&(e=0),void 0===n&&(n=!0),this.scrollex({offset:t+","+e,animated:n})},t.prototype.scrollex=function(t){return this._post("$element.scroll",t)},t.prototype.scrollby=function(t,e,n){return void 0===n&&(n=!0),this._post("$element.scrollby",{offset:t+","+e,animated:n})},t.prototype.scrollToIndex=function(t){return this._post("$element.scrollToIndex",t)},t.prototype.setZoomScale=function(t,e){return void 0===e&&(e=!0),this._post("$element.setZoomScale",{scale:t,animated:e})},t.prototype.reverseZoomScale=function(t){return void 0===t&&(t=!0),this._post("$element.reverseZoomScale",{animated:t})},t.prototype.selectTab=function(t){return this._post("$element.selectTab",{index:t})},t.prototype.setText=function(t){return this._post("$element.setText",{text:t})},t.prototype.setFocus=function(t){return void 0===t&&(t=!0),this._post("$element.setFocus",{focus:t})},t.prototype.setProgress=function(t){return this._post("$element.setProgress",{progress:t})},t.prototype.setOpacity=function(t,e){return void 0===e&&(e=!1),this._post("$element.setOpacity",{opacity:t,animated:e})},t.prototype.setHidden=function(t,e,n){return void 0===e&&(e=!1),void 0===n&&(n=300),this._post("$element.setHidden",{hidden:t,animated:e,duration:n})},t.prototype.resetTab=function(){return this._post("$element.resetTab",{})},t.prototype.getText=function(t,e,n){a.c("$element.getText",{target:t,id:e},n)},t.prototype.getSnapshot=function(t,e){return this._post("$element.getSnapshot",{target:t},e)},t.prototype.setAttributes=function(t,e,n){return Array.isArray(t)?this._post("$element.setAttributes",{list:t,options:e},n):this._post("$element.setAttributes",{attrs:t,options:e},n)},t.prototype.addEventListener=function(t,e,n){return void 0===n&&(n=2147483647),this._post("$element.addEventListener",t,e,n)},t.prototype.bindEventListener=function(t,e,n){void 0===n&&(n=2147483647);var r=(t||{}).bindattrs,a=(t||{}).event;return r&&Object.keys(r).forEach((function(t){var e=r[t];Array.isArray(e)||(r[t]=[e])})),a&&a.filter&&!Array.isArray(a.filter)&&(a.filter=[a.filter]),a&&a.exp&&!Array.isArray(a.exp)&&(a.exp=[a.exp]),a&&a.if&&!Array.isArray(a.if)&&(a.if=[a.if]),this._post("$element.bindEventListener",o(o({},t),{bindattrs:r,event:a}),e,n)},t.prototype.addAttributeListener=function(t,e,n){return void 0===n&&(n=2147483647),this._post("$element.addAttributeListener",t,e,n)},t.prototype.bindAttributeListener=function(t,e,n){void 0===n&&(n=2147483647);var r=(t||{}).bindattrs,a=(t||{}).bind;return r&&Object.keys(r).forEach((function(t){var e=r[t];Array.isArray(e)||(r[t]=[e])})),a&&a.filter&&!Array.isArray(a.filter)&&(a.filter=[a.filter]),a&&a.exp&&!Array.isArray(a.exp)&&(a.exp=[a.exp]),a&&a.if&&!Array.isArray(a.if)&&(a.if=[a.if]),this._post("$element.bindAttributeListener",o(o({},t),{bindattrs:r,bind:a}),e,n)},t.prototype.call=function(t,e,n){return this._post("$element.call",{name:t,args:e},n)},t.prototype.findComponents=function(t,e){return this._post("$element.findComponents",o({},t),e)},t}();t.exports=function(t,e,n){void 0===n&&(n=!1);var a="object"==r(t)?o({},t):{target:t,id:e,depth:n};return new i(a)}},6445:function(t,e,n){"use strict";var r=n(9918),o=n(5079);t.exports={emotions:function(){var t,e;r.env.system.isAndroid()?(t="./fc",e=".png"):(t="MPMUIComponent_Internal.bundle/",e="_new");var n=[{name:"微笑",path:"".concat(t,"001").concat(e),code:"/:^_^"},{name:"害羞",path:"".concat(t,"002").concat(e),code:"/:^$^"},{name:"吐舌头",path:"".concat(t,"003").concat(e),code:"/:Q"},{name:"偷笑",path:"".concat(t,"004").concat(e),code:"/:815"},{name:"爱慕",path:"".concat(t,"005").concat(e),code:"/:809"},{name:"大笑",path:"".concat(t,"006").concat(e),code:"/:^O^"},{name:"跳舞",path:"".concat(t,"007").concat(e),code:"/:081"},{name:"飞吻",path:"".concat(t,"008").concat(e),code:"/:087"},{name:"安慰",path:"".concat(t,"009").concat(e),code:"/:086"},{name:"抱抱",path:"".concat(t,"010").concat(e),code:"/:H"},{name:"加油",path:"".concat(t,"011").concat(e),code:"/:012"},{name:"胜利",path:"".concat(t,"012").concat(e),code:"/:806"},{name:"强",path:"".concat(t,"013").concat(e),code:"/:b"},{name:"亲亲",path:"".concat(t,"014").concat(e),code:"/:^x^"},{name:"花痴",path:"".concat(t,"015").concat(e),code:"/:814"},{name:"露齿笑",path:"".concat(t,"016").concat(e),code:"/:^W^"},{name:"查找",path:"".concat(t,"017").concat(e),code:"/:080"},{name:"呼叫",path:"".concat(t,"018").concat(e),code:"/:066"},{name:"算账",path:"".concat(t,"019").concat(e),code:"/:807"},{name:"财迷",path:"".concat(t,"020").concat(e),code:"/:805"},{name:"打call",path:"".concat(t,"021").concat(e),code:"/:071"},{name:"鬼脸",path:"".concat(t,"022").concat(e),code:"/:072"},{name:"天使",path:"".concat(t,"023").concat(e),code:"/:065"},{name:"再见",path:"".concat(t,"024").concat(e),code:"/:804"},{name:"憨笑",path:"".concat(t,"025").concat(e),code:"/:813"},{name:"享受",path:"".concat(t,"026").concat(e),code:"/:818"},{name:"期待",path:"".concat(t,"027").concat(e),code:"/:015"},{name:"呆若木鸡",path:"".concat(t,"028").concat(e),code:"/:084"},{name:"思考",path:"".concat(t,"029").concat(e),code:"/:801"},{name:"迷惑",path:"".concat(t,"030").concat(e),code:"/:811"},{name:"疑问",path:"".concat(t,"031").concat(e),code:"/:?"},{name:"剁手",path:"".concat(t,"032").concat(e),code:"/:077"},{name:"无聊",path:"".concat(t,"033").concat(e),code:"/:083"},{name:"怀疑",path:"".concat(t,"034").concat(e),code:"/:817"},{name:"嘘",path:"".concat(t,"035").concat(e),code:"/:!"},{name:"做错事",path:"".concat(t,"036").concat(e),code:"/:068"},{name:"不容易",path:"".concat(t,"037").concat(e),code:"/:079"},{name:"感冒",path:"".concat(t,"038").concat(e),code:"/:028"},{name:"尴尬",path:"".concat(t,"039").concat(e),code:"/:026"},{name:"傻笑",path:"".concat(t,"040").concat(e),code:"/:007"},{name:"不会吧",path:"".concat(t,"041").concat(e),code:"/:816"},{name:"太难了",path:"".concat(t,"042").concat(e),code:'/:\'""'},{name:"流汗",path:"".concat(t,"043").concat(e),code:"/:802"},{name:"凄凉",path:"".concat(t,"044").concat(e),code:"/:027"},{name:"困了",path:"".concat(t,"045").concat(e),code:"/:(Zz...)"},{name:"晕",path:"".concat(t,"046").concat(e),code:"/:*&*"},{name:"忧伤",path:"".concat(t,"047").concat(e),code:"/:810"},{name:"委屈",path:"".concat(t,"048").concat(e),code:"/:>_<"},{name:"悲泣",path:"".concat(t,"049").concat(e),code:"/:018"},{name:"大哭",path:"".concat(t,"050").concat(e),code:"/:>O<"},{name:"痛哭",path:"".concat(t,"051").concat(e),code:"/:020"},{name:"I服了U",path:"".concat(t,"052").concat(e),code:"/:044"},{name:"对不起",path:"".concat(t,"053").concat(e),code:"/:819"},{name:"心酸",path:"".concat(t,"054").concat(e),code:"/:085"},{name:"皱眉",path:"".concat(t,"055").concat(e),code:"/:812"},{name:"好累",path:"".concat(t,"056").concat(e),code:'/:"'},{name:"吐",path:"".concat(t,"058").concat(e),code:"/:>@<"},{name:"背",path:"".concat(t,"059").concat(e),code:"/:076"},{name:"惊讶",path:"".concat(t,"060").concat(e),code:"/:069"},{name:"惊愕",path:"".concat(t,"061").concat(e),code:"/:O"},{name:"闭嘴",path:"".concat(t,"062").concat(e),code:"/:067"},{name:"欠扁",path:"".concat(t,"063").concat(e),code:"/:043"},{name:"鄙视你",path:"".concat(t,"064").concat(e),code:"/:P"},{name:"大怒",path:"".concat(t,"065").concat(e),code:"/:808"},{name:"生气",path:"".concat(t,"066").concat(e),code:"/:>W<"},{name:"财神",path:"".concat(t,"067").concat(e),code:"/:073"},{name:"请喝茶",path:"".concat(t,"068").concat(e),code:"/:008"},{name:"恭喜发财",path:"".concat(t,"069").concat(e),code:"/:803"},{name:"小二",path:"".concat(t,"070").concat(e),code:"/:074"},{name:"老大",path:"".concat(t,"071").concat(e),code:"/:O=O"},{name:"邪恶",path:"".concat(t,"072").concat(e),code:"/:036"},{name:"单挑",path:"".concat(t,"073").concat(e),code:"/:039"},{name:"机智",path:"".concat(t,"074").concat(e),code:"/:045"},{name:"隐形人",path:"".concat(t,"075").concat(e),code:"/:046"},{name:"炸弹",path:"".concat(t,"076").concat(e),code:"/:048"},{name:"惊声尖叫",path:"".concat(t,"077").concat(e),code:"/:047"},{name:"惊艳",path:"".concat(t,"078").concat(e),code:"/:girl"},{name:"围观",path:"".concat(t,"079").concat(e),code:"/:man"},{name:"招财猫",path:"".concat(t,"080").concat(e),code:"/:052"},{name:"成交",path:"".concat(t,"081").concat(e),code:"/:(OK)"},{name:"鼓掌",path:"".concat(t,"082").concat(e),code:"/:8*8"},{name:"握手",path:"".concat(t,"083").concat(e),code:"/:)-("},{name:"红唇",path:"".concat(t,"084").concat(e),code:"/:lip"},{name:"玫瑰",path:"".concat(t,"085").concat(e),code:"/:-F"},{name:"残花",path:"".concat(t,"086").concat(e),code:"/:-W"},{name:"爱心",path:"".concat(t,"087").concat(e),code:"/:Y"},{name:"心碎",path:"".concat(t,"088").concat(e),code:"/:qp"},{name:"红包",path:"".concat(t,"089").concat(e),code:"/:$"},{name:"快递传送",path:"".concat(t,"090").concat(e),code:"/:%"},{name:"快递盒",path:"".concat(t,"091").concat(e),code:"/:(&)"},{name:"收件",path:"".concat(t,"092").concat(e),code:"/:@"},{name:"电话",path:"".concat(t,"093").concat(e),code:"/:~B"},{name:"举杯庆祝",path:"".concat(t,"094").concat(e),code:"/:U*U"},{name:"时钟",path:"".concat(t,"095").concat(e),code:"/:clock"},{name:"等待",path:"".concat(t,"096").concat(e),code:"/:R"},{name:"很晚了",path:"".concat(t,"097").concat(e),code:"/:C"},{name:"飞机",path:"".concat(t,"098").concat(e),code:"/:plane"},{name:"支付宝",path:"".concat(t,"099").concat(e),code:"/:075"}];return r.env.system.isAndroid()?n.push({name:"生病",path:"./f057.png",code:"/:>M<"}):(n.push({name:"生病",path:"TNodeFramework.bundle/057",code:"/:>M<"}),n.push({name:"赞",path:"TNodeFramework.bundle/0100",code:"/:like"}),n.push({name:"抱拳",path:"TNodeFramework.bundle/0101",code:"/:salute"})),n},taoEmotions:function(){var t,e;return r.env.system.isAndroid()?(t="./gg_",e=".png"):(t="TBGuangGuangEmoticon/",e=""),[{name:"淘小宝得意",path:"".concat(t,"2054").concat(e),code:"/:淘小宝得意"},{name:"淘小宝开心",path:"".concat(t,"2055").concat(e),code:"/:淘小宝开心"},{name:"淘小宝流泪",path:"".concat(t,"2056").concat(e),code:"/:淘小宝流泪"},{name:"淘小宝心心眼",path:"".concat(t,"2057").concat(e),code:"/:淘小宝心心眼"},{name:"淘小宝左看看",path:"".concat(t,"2058").concat(e),code:"/:淘小宝左看看"},{name:"淘小宝mua",path:"".concat(t,"2059").concat(e),code:"/:淘小宝mua"},{name:"淘小宝星星眼",path:"".concat(t,"2060").concat(e),code:"/:淘小宝星星眼"},{name:"淘小宝哈哈",path:"".concat(t,"2061").concat(e),code:"/:淘小宝哈哈"},{name:"淘小宝wink",path:"".concat(t,"2062").concat(e),code:"/:淘小宝wink"},{name:"淘小宝惊呆",path:"".concat(t,"2063").concat(e),code:"/:淘小宝惊呆"},{name:"淘小宝闭眼",path:"".concat(t,"2064").concat(e),code:"/:淘小宝闭眼"}]},parserEmoji:function(t,e,n){var a=this.emotions().concat(this.taoEmotions());n||(n=e);var i=t.split("/:");return r.env.system.isIOS()&&0==o.appVersionCompare("10.19.0")?i.forEach((function(t,r){if(r>0)for(var o=a.length-1;o>=0;o--){var c=a[o],s="/:"+t;if(s.substring(0,c.code.length)==c.code){i[r]=s.replace(c.code,'<img src="'.concat(c.path,'" style="width:').concat(e,";height:").concat(n,'" onclick=" "/>'));break}}})):i.forEach((function(t,r){if(r>0)for(var o=a.length-1;o>=0;o--){var c=a[o],s="/:"+t;if(s.substring(0,c.code.length)==c.code){i[r]=s.replace(c.code,'<img src="'.concat(c.path,'" style="width:').concat(e,";height:").concat(n,'"/>'));break}}})),i.join("")}}},2987:function(t,e,n){"use strict";var r=n(8813);t.exports=new r},8813:function(t){"use strict";var e=this&&this.__spreadArray||function(t,e,n){if(n||2===arguments.length)for(var r,o=0,a=e.length;o<a;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))};t.exports=function(){function t(){this.events={}}return t.prototype.on=function(t,e){var n=this;t&&e&&(Array.isArray(t)?t:[t]).forEach((function(t){var r=n.events[t];r||(r=[],n.events[t]=r),-1==r.indexOf(e)&&r.push(e)}))},t.prototype.once=function(t,e){var n=this;if(t&&e){var r=Array.isArray(t)?t:[t],o=this;r.forEach((function(t){n.on(t,(function n(){o.off(t,n),e&&e.apply(o,arguments)}))}))}},t.prototype.off=function(t,e){var n=this;t&&(Array.isArray(t)?t:[t]).forEach((function(t){var r=n.events[t];if(r)if(e){var o=r.indexOf(e);-1!=o&&r.splice(o,1)}else delete n.events[t]}))},t.prototype.send=function(t){for(var n=this,r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];if(t){var a=Array.isArray(t)?t:[t];a.forEach((function(t){var o=e([],n.events[t]||[],!0);o&&o.forEach((function(t){t&&t.apply(n,r)}))}))}},t}()},1126:function(t,e,n){"use strict";var r=n(9918),o=n(3872),a={NaviBarFestivalStyleNone:0,NaviBarFestivalStyleFestival:1,NaviBarFestivalStyleFestivalWithRainbowLine:2,NaviBarFestivalStyleGradient:3};Object.freeze(a),t.exports={StyleType:a,isFestivalOn:function(t){r.c("$festival.isFestivalOn",null,(function(e){t(e.isFestivalOn)}))},setFestivalStyle:function(t){r.c("$festival.setFestivalStyle",t,(function(t){}))},getColor:function(t,e,n,o){r.c("$festival.getColor",{module:t,key:e,defaultValue:n},(function(t){o(t.color)}))},getText:function(t,e,n,o){r.c("$festival.getText",{module:t,key:e,defaultValue:n},(function(t){o(t.text)}))},onFestivalChanged:function(t){o.on(o.NotifyType.FestivalChanged,t)},getFestival:function(t){r.c("$festival.getFestival",null,(function(e){t&&t(e)}))}}},8419:function(t,e,n){"use strict";var r=n(9918);t.exports={preload:function(t){r.c("$imagepreload.preload",t)}}},8379:function(t,e,n){"use strict";t.exports={component:n(6390),utils:n(5079),app:n(2573),eventbus:n(2987),net:n(8002),festival:n(1126),mtop:n(9398),navigationBar:n(2108),navigator:n(2632),notify:n(3872),orange:n(33),storage:n(7550),tracker:n(862),behavior:n(9958),user:n(2445),windvane:n(6021),appMonitor:n(3116),share:n(3750),animation:n(9800),element:n(4364),screen:n(9369),monitor:n(3353),application:n(9862),preload:n(5521),tab:n(7115),business:n(1079),imagepreload:n(8419),apm:n(7008),emotion:n(6445),abtest:n(7556)}},3353:function(t,e,n){"use strict";var r=n(9918);t.exports={report:function(t,e){r.c("$.report",{name:t||"custom_error",params:e})}}},3624:function(t){"use strict";var e={Bubble:1,Depth:2,IgnoreTrack:4,Container:8,Sibling:16,MessageHandler:32,ExceptSelf:64,IgnoreAllHandler:128};Object.freeze(e),t.exports=e},9398:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(e,n);o&&!("get"in o?!e.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,o)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return o(e,t),e},i=n(9918),c=a(n(5079));t.exports={request:function(t,e,n){this.convertDataParamsToString(t),i.c("$mtop.request",t,(function(t,r){r?n({error:r}):(t=t||{}).error?n(t.error):e(t.data)}))},requestex:function(t,e){var n=t.options,r=void 0===n?{}:n;this.convertDataParamsToString(t),i.c("$mtop.request",t,(function(t,n){e&&e(t,n)}),1==r.trace?2:1)},convertDataParamsToString:function(t){t&&t.data&&Object.keys(t.data).map((function(e,n){var r=t.data[e];c.types.isObject(r)&&(t.data[e]=JSON.stringify(r))}))}}},2108:function(t,e,n){"use strict";var r=n(9918),o={Message:0,Home:1,Help:2,FeedBack:3};Object.freeze(o),t.exports={setTitle:function(t){return r.c("$naviBar.setTitle",{title:t})},setStyle:function(t){return r.c("$naviBar.setStyle",t)},show:function(t){return void 0===t&&(t=!1),r.c("$naviBar.show",{show:!0,animated:t})},hide:function(t){return void 0===t&&(t=!1),r.c("$naviBar.show",{show:!1,animated:t})},appendNaviMenu:function(t,e,n){return void 0===n&&(n=1e3),r.c("$naviBar.appendNaviMenu",t,e,n)},NaviMenu:o,setNaviMenu:function(t){return r.c("$naviBar.setNaviMenu",{menu:t})},showNaviMenu:function(){return r.c("$naviBar.showNaviMenu")},setLeftItem:function(t,e,n){return void 0===n&&(n=1e3),r.c("$naviBar.setNaviBarLeftItem",t,e,n)},setRightItem:function(t,e,n){return void 0===n&&(n=1e3),r.c("$naviBar.setNaviBarRightItem",t,e,n)},showStatusBar:function(t,e){return void 0===t&&(t=!0),void 0===e&&(e=!1),r.c("$naviBar.showStatusBar",{show:t,animated:e})},setStatusBarStyle:function(t){return r.c("$naviBar.setStatusBarStyle",{style:t})}}},2632:function(t,e,n){"use strict";var r=n(9918);t.exports={openURL:function(t){return r.c("$navigator.openURL",{url:t})},open:function(t){return r.c("$navigator.openURL",t)},push:function(t,e){return void 0===e&&(e=!1),r.c("$navigator.push",{url:t,animated:e})},pop:function(t){return void 0===t&&(t=!1),r.c("$navigator.pop",{animated:t})}}},8002:function(t,e,n){"use strict";var r=n(9918);t.exports={fetch:function(t,e){return r.c("$net.fetch",{url:t},e)}}},3872:function(t,e,n){"use strict";var r=n(9918),o={UserChanged:"TNodeUserChanged",ApplicationState:"TNodeApplicationStateNotify",AddFollow:"TNodeAddFollowNotify",FestivalChanged:"TNodeFestivalChanged"};Object.freeze(o);var a=0;t.exports={NotifyType:o,post:function(t,e){return r.c("$notify.postNotify",{name:t,args:e})},postWhenAppear:function(t,e){return r.c("$notify.postNotify",{name:t,args:e,appear:!0})},on:function(t,e,n){return void 0===n&&(n=2147483647),++a,r.c("$notify.onNotify",{name:t,cnt:n,notifyId:a},e,n),a},off:function(t,e){return r.c("$notify.offNotify",{name:t,notifyId:e})}}},33:function(t,e,n){"use strict";var r=n(9918);t.exports={getConfig:function(t,e,n,o){r.c("$orange.getConfig",{groupName:t,key:e,defaultConfig:n},(function(t){o(t.config)}))},getConfigs:function(t,e){r.c("$orange.getConfigs",{configs:t},(function(t){e(t.configs)}))}}},5521:function(t,e,n){"use strict";var r=n(9918);t.exports={preload:function(t,e){r.c("$preload.preload",{args:t},e)}}},9369:function(t,e,n){"use strict";var r=n(9918);t.exports={setAlwaysOn:function(t){void 0===t&&(t=!0),r.c("$screen.setAlwaysOn",{on:t})},setBrightness:function(t){r.c("$screen.setBrightness",{brightness:t})},setOrientation:function(t,e){r.c("$screen.setOrientation",{orientation:t},e)}}},3750:function(t,e,n){"use strict";var r=n(9918);t.exports={share:function(t){return r.c("$share.share",t)}}},7550:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},r.apply(this,arguments)},o=n(9918),a={};t.exports={get:function(t,e){o.c("$storage.get",{key:t},e)},set:function(t,e){o.c("$storage.set",{key:t,value:e})},clear:function(t){o.c("$storage.clear","string"==typeof t?{key:t}:r({},t))},memoryGet:function(t){return a[t]},memorySet:function(t,e){a[t]=e}}},7115:function(t,e,n){"use strict";var r=n(9918);t.exports={setNumber:function(t){r.c("$tab.updateTabNumber",{number:t})},resetTab:function(){r.c("$tab.resetTab",null)},setTitle:function(t){r.c("$tab.updateTabText",{text:t})},showRedPoint:function(){r.c("$tab.showRedPoint",null)},setIcon:function(t){r.c("$tab.updateTabIcon",t)},setGuangGuangIcon:function(t){r.c("$tab.updateGuangGuangTabIcon",t)},isInTab:function(t,e){return void 0===t&&(t=1),r.c("$tab.isInTab",{index:t},e)}}},862:function(t,e,n){"use strict";var r=n(9918);function o(t){return t&&0!=t.indexOf("Page_")?"Page_"+t:t}t.exports={formatPageName:o,commit:function(t){if(t){var e=Array.isArray(t)?t:[t];e=e.map((function(t){return t.pageName=o(t.pageName),r.env.system.isIOS()&&(t.name=t.arg1,t.arg1=t.arg2,t.arg2=t.arg3),t})),r.c("$tracker.commit",{args:e})}},click:function(t){return t.pageName=o(t.pageName),t.controlName&&0!=t.controlName.indexOf("Button-")&&(t.controlName="Button-"+t.controlName),t.controlName&&0!=t.controlName.indexOf("Page_")&&(t.controlName=t.pageName+"_"+t.controlName),this.commit({pageName:t.pageName,eventID:2101,arg1:t.controlName,args:t.args})},updatePageName:function(t,e){t=o(t),r.c("$tracker.updatePageName",{pageName:t,target:e})},updatePageUtparams:function(t,e){r.c("$tracker.updatePageUtparams",{params:t,target:e})},updateNextPageUtparams:function(t){r.c("$tracker.updateNextPageUtparams",t)},updatePageUrl:function(t,e){r.c("$tracker.updatePageUrl",{url:t,target:e})},updateProperties:function(t,e){r.c("$tracker.updateProperties",{properties:t,target:e})},updateNextPageProperties:function(t){r.c("$tracker.updateNextPageProperties",t)},enter:function(t,e,n){r.c("$tracker.enter",{pageName:o(t),properties:e,target:n})},exit:function(t,e,n){r.c("$tracker.exit",{pageName:o(t),properties:e,target:n})},getSpmUrlAndSpmPre:function(t){r.c("$tracker.getSpmUrlAndSpmPre",null,(function(e){t&&t(e.spms)}))}}},2445:function(t,e,n){"use strict";var r=n(9918),o=n(3872);t.exports={login:function(t){r.c("$user.login",null,(function(e,n){r.env.system.isAndroid()&&(!e||0!=e.result&&"false"!=e.result||(e=null,n={message:"login error"})),t&&t(e,n)}))},autologin:function(t){r.c("$user.login",{auto:!0},t)},isLogin:function(t){r.c("$user.isLogin",null,t)},logout:function(t){r.c("$user.logout",null,t)},getUserInfo:function(t){r.c("$user.getUserInfo",null,t)},onUserChanged:function(t){o.on(o.NotifyType.UserChanged,t)}}},4919:function(t){"use strict";t.exports={transitionColor:function(t,e,n){n=Math.min(1,Math.max(n,0));var r=t>>24&255,o=t>>16&255,a=t>>8&255,i=255&t,c=e>>16&255,s=e>>8&255,u=255&e,f=("0"+parseInt(r+n*((e>>24&255)-r)).toString(16)).slice(-2),l=("0"+parseInt(o+n*(c-o)).toString(16)).slice(-2),p=("0"+parseInt(a+n*(s-a)).toString(16)).slice(-2),h=("0"+parseInt(i+n*(u-i)).toString(16)).slice(-2);return"#".concat(f).concat(l).concat(p).concat(h)}}},2666:function(t){"use strict";t.exports={getRate:function(t){return"★★★★★☆☆☆☆☆".slice(5-t,10-t)}}},480:function(t){"use strict";t.exports={dateFormater:function(t,e){var n=e?new Date(e):new Date,r=n.getFullYear()+"",o=n.getMonth()+1,a=n.getDate(),i=n.getHours(),c=n.getMinutes(),s=n.getSeconds();return t.replace(/YYYY|yyyy/g,r).replace(/YY|yy/g,r.substr(2,2)).replace(/MM/g,(o<10?"0":"")+o).replace(/DD/g,(a<10?"0":"")+a).replace(/HH|hh/g,(i<10?"0":"")+i).replace(/mm/g,(c<10?"0":"")+c).replace(/ss/g,(s<10?"0":"")+s)},isLeapYear:function(t){if(t%100==0){if(t%400==0)return!0}else if(t%4==0)return!0;return!1},getDaysInMonth:function(t,e){return[31,this.isLeapYear(t)?29:28,31,30,31,30,31,31,30,31,30,31][e]}}},2317:function(t,e,n){"use strict";var r=n(1950);t.exports=function(){function t(){this.list=[]}return t.prototype.addAcceptor=function(t){var e=this;Array.isArray(t)?t.forEach((function(t){e.list.push(t)})):this.list.push(t)},t.prototype.removeAcceptor=function(t){var e=this.list.indexOf(t);e>-1&&this.list.splice(e,1)},t.prototype.dispatch=function(t,e,n){for(var o=this.list.length,a=0;a<o;++a)try{var i=this.list[a];if(i)if(i.dispatch){if(1==i.dispatch(t,e,n))break}else{var c=i[e];if(r.isFunction(c)&&1==i[e](t,n))break}}catch(t){console.error("dispatch ".concat(e," error:").concat(t," args:").concat(n))}},t}()},5079:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},r.apply(this,arguments)},o=n(9918),a=n(2632);t.exports={types:n(1950),date:n(480),colors:n(4919),string:n(6269),random:n(2247),common:n(2666),poplayer:n(4185),dispatcher:n(2317),now:function(){return(new Date).getTime()},copy:function(t){try{return JSON.parse(JSON.stringify(t))}catch(e){return t}},isX:function(){var t=(o||{}).env,e=(void 0===t?{}:t).device,n=(void 0===e?{}:e).isX;return"true"==n},scale:function(){return(o.env.system.isIOS()?o.env.device.width:o.env.device.resolutionW)/750},fromPixel:function(t){return t/this.scale()},toPixel:function(t){return t*this.scale()},versionCompare:function(t,e){void 0===t&&(t=""),void 0===e&&(e="");for(var n=t.split("."),r=e.split("."),o=Math.max(n.length,r.length),a=0,i=0;i<o;i++){var c=n.length>i?n[i]:0,s=isNaN(Number(c))?c.charCodeAt():Number(c),u=r.length>i?r[i]:0,f=isNaN(Number(u))?u.charCodeAt():Number(u);if(s<f){a=-1;break}if(s>f){a=1;break}}return a},engineVersionCompare:function(t){return this.versionCompare(o.env.engine.version,t)},appVersionCompare:function(t){return this.versionCompare(o.env.app.version,t)},encodeURLQuery:function(t){return o.util.encodeURLQuery(t)},parserURL:function(t){return o.util.parserURL(t)},appendURLQuery:function(t,e,n){return void 0===n&&(n=!1),o.util.appendURLQuery(t,e,n)},urlOfTNodePage:function(t){var e=t.dsl,n=t.dslQuery,o=t.query;delete o.wh_weex,delete o._wx_statusbar_hidden,delete o.wx_navbar_hidden;var a=r(r({},o),{tnode:"".concat(e,"?").concat(this.encodeURLQuery(n))});return"https://h5.m.taobao.com/tnode/index.htm?".concat(this.encodeURLQuery(a))},openTNodePage:function(t){var e=t.animated,n=this.urlOfTNodePage(t);return n&&a.open({url:n,animated:e})}}},4185:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},r.apply(this,arguments)},o=n(9918),a=n(3872),i=n(2987);t.exports={openTaoBaoPoplayer:function(t){o.env.system.isIOS()?a.post("PopLayerPageSwitchEvent",{toVC:t,operation:1}):a.post("com.alibaba.poplayer.PopLayer.action.FRAGMENT_SWITCH",{fragment_name:t,operation:1,fragment_need_activity_param:!0})},showPoplayer:function(t,e,n,o){o&&i.once(e,(function(t){o&&o(t)})),t.postEvent("poplayer",r(r({},n),{src:"src/poplayer/".concat(e,"/index"),poplayerId:o?e:null}))}}},2247:function(t){"use strict";t.exports={uuid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})).toUpperCase()},randomAlphaNum:function(t){for(var e="";e.length<t;e+=Math.random().toString(36).substr(2));return e.substr(0,t).toUpperCase()},randNum:function(t,e){return Math.floor(t+Math.random()*(e+1-t))}}},6269:function(t){"use strict";t.exports={htmlSafeStr:function(t){return t.replace(/<[^>]+>/g,"")},firstUpperCase:function(t){return t.toLowerCase().replace(/( |^)[a-z]/g,(function(t){return t.toUpperCase()}))}}},1950:function(t){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}t.exports={isFunction:function(t){return t&&"[object Function]"=={}.toString.call(t)},isString:function(t){return t&&"string"==typeof t},isObject:function(t){return t instanceof Object},isStatic:function(t){return"string"==typeof t||"number"==typeof t||"boolean"==typeof t||null==t},isPrimitive:function(t){return this.isStatic(t)||"symbol"===e(t)},isInteger:function(t){return"number"==typeof t&&t%1==0},isFloat:function(t){return Number(t)===t&&t%1!=0},isBoolean:function(t){return t===!!t},isTrue:function(t){return"boolean"==typeof t&&t||"string"==typeof t&&"true"==t},isNull:function(t){return null==t||null==t},isEmail:function(t){return/^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(t)},isMobile:function(t){return/^1[0-9]{10}$/.test(t)},isPhone:function(t){return/^([0-9]{3,4}-)?[0-9]{7,8}$/.test(t)},isQQ:function(t){return/[1-9][0-9]{4,}/.test(t)},isURL:function(t){return/^http[s]?:\/\/.*/.test(t)},isURI:function(t){return/^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/.test(t)},isLowerCase:function(t){return/^[a-z]+$/.test(t)},isUpperCase:function(t){return/^[A-Z]+$/.test(t)},isAlphabets:function(t){return/^[A-Za-z]+$/.test(t)},isNumber:function(t){return""!=t&&/^[0-9]+$/.test(t)},isNumord:function(t,e){var n=/[^\d.]/g;if(1==e){if(!n.test(t))return!1}else if(2==e&&!(n=/[^\d.]/g).test(t))return!1;return!0},isChinese:function(t){return""!=t&&/^[\u0391-\uFFE5]+$/.test(t)},isEmoji:function(t){return new RegExp(["\ud83c[\udf00-\udfff]","\ud83d[\udc00-\ude4f]","\ud83d[\ude80-\udeff]"].join("|"),"g").test(t)},isIDCard:function(t){return/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t)},isSpecialChar:function(t){return/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]\s]/im.test(t)||/[·！#￥（——）：；“”‘、，|《。》？、【】[\]\s]/im.test(t)}}},6021:function(t,e,n){"use strict";var r=n(9918);t.exports={call:function(t,e,n,o,a){var i={};i.params=n,i.pluginName=t,i.methodName=e,r.c("$windvane.call",i,(function(t,e){"SUCCESS"==t.status?o&&o(t.result):a&&a(t.result)}))},post:function(t,e){return r.c("$windvane.postNotify",{name:t,args:e})}}},8975:function(t,e,n){var r;!function(){"use strict";var o={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^\)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[\+\-]/};function a(t){return c(u(t),arguments)}function i(t,e){return a.apply(null,[t].concat(e||[]))}function c(t,e){var n,r,i,c,s,u,f,l,p,h=1,d=t.length,g="";for(r=0;r<d;r++)if("string"==typeof t[r])g+=t[r];else if(Array.isArray(t[r])){if((c=t[r])[2])for(n=e[h],i=0;i<c[2].length;i++){if(!n.hasOwnProperty(c[2][i]))throw new Error(a('[sprintf] property "%s" does not exist',c[2][i]));n=n[c[2][i]]}else n=c[1]?e[c[1]]:e[h++];if(o.not_type.test(c[8])&&o.not_primitive.test(c[8])&&n instanceof Function&&(n=n()),o.numeric_arg.test(c[8])&&"number"!=typeof n&&isNaN(n))throw new TypeError(a("[sprintf] expecting number but found %T",n));switch(o.number.test(c[8])&&(l=n>=0),c[8]){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,c[6]?parseInt(c[6]):0);break;case"e":n=c[7]?parseFloat(n).toExponential(c[7]):parseFloat(n).toExponential();break;case"f":n=c[7]?parseFloat(n).toFixed(c[7]):parseFloat(n);break;case"g":n=c[7]?String(Number(n.toPrecision(c[7]))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=c[7]?n.substring(0,c[7]):n;break;case"t":n=String(!!n),n=c[7]?n.substring(0,c[7]):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=c[7]?n.substring(0,c[7]):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=c[7]?n.substring(0,c[7]):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}o.json.test(c[8])?g+=n:(!o.number.test(c[8])||l&&!c[3]?p="":(p=l?"+":"-",n=n.toString().replace(o.sign,"")),u=c[4]?"0"===c[4]?"0":c[4].charAt(1):" ",f=c[6]-(p+n).length,s=c[6]&&f>0?u.repeat(f):"",g+=c[5]?p+n+s:"0"===u?p+s+n:s+p+n)}return g}var s=Object.create(null);function u(t){if(s[t])return s[t];for(var e,n=t,r=[],a=0;n;){if(null!==(e=o.text.exec(n)))r.push(e[0]);else if(null!==(e=o.modulo.exec(n)))r.push("%");else{if(null===(e=o.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){a|=1;var i=[],c=e[2],u=[];if(null===(u=o.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(i.push(u[1]);""!==(c=c.substring(u[0].length));)if(null!==(u=o.key_access.exec(c)))i.push(u[1]);else{if(null===(u=o.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");i.push(u[1])}e[2]=i}else a|=2;if(3===a)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push(e)}n=n.substring(e[0].length)}return s[t]=r}e.sprintf=a,e.vsprintf=i,"undefined"!=typeof window&&(window.sprintf=a,window.vsprintf=i,void 0===(r=function(){return{sprintf:a,vsprintf:i}}.call(e,n,e,t))||(t.exports=r))}()},306:function(t){"use strict";t.exports={i8:"0.10.40"}},9918:function(t){"use strict";t.exports=$ac}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var a=e[r]={id:r,loaded:!1,exports:{}};return t[r].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t};var r=n(3705);module.exports=r}();