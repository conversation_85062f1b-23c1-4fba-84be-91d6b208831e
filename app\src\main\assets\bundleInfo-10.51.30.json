[{"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tbsearch_remote", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tbsearch_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.search.sf.remote.RemoteWidgetFactory": "com.taobao.search.sf.remote.RemoteWidgetFactoryImpl"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "afc118846e0ac10701d89c8eec871093", "priority": 5, "size": 326831, "url": "https://mtlexternal.alibabausercontent.com/feature/afc118846e0ac10701d89c8eec871093/tbsearch_remote.so", "version": "10.51.30@6.11.92", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "TBSubscribe", "bindingActivities": [], "classes": ["com.taobao.android.dinamicx.subscribe.protocol.shop.weex.ShopSubscribeWeexComponent"], "dependencies": ["ta<PERSON><PERSON>_weex_adapter"], "featureName": "TBSubscribe", "fragments": ["com.taobao.android.dinamicx.subscribe.ui.SubscribeCommentFragment"], "groupId": "com.taobao.android", "implServices": {"com.taobao.tao.topmultitab.protocol.IHomeSubTabController": "com.taobao.android.dinamicx.subscribe.protocol.SubscribeTabController"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "a9c7f3f90dc79d6fbe33d136c3a6d7cf", "priority": 8, "size": 401856, "url": "https://mtlexternal.alibabausercontent.com/feature/a9c7f3f90dc79d6fbe33d136c3a6d7cf/TBSubscribe.so", "version": "10.51.30@0.0.24.2", "views": []}, {"featureInitalClass": "com.taobao.leftsdk.LeftSDKInitializerInternal", "appVersion": "10.51.30", "artifactId": "left_sdk", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "left_sdk", "fragments": [], "groupId": "com.taobao.leftsdk", "implServices": {"com.taobao.leftsdk.triver.StepCountBridgeApi": "com.taobao.leftsdk.triver.TBStepCountBridgeExtensionInternal"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "0956eb2d16288c7d73ab6817fb5a96e9", "priority": 10, "size": 102874, "url": "https://mtlexternal.alibabausercontent.com/feature/0956eb2d16288c7d73ab6817fb5a96e9/left_sdk.so", "version": "10.51.30@1.0.3.56", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "elemeadapter_android", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "elemeadapter_android", "fragments": [], "groupId": "me.ele", "implServices": {"me.ele.bridge.PizzaApi": "me.ele.bridge.NetService"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "907e2fb61753b44ca73323317f3ed2c4", "priority": 10, "size": 45451, "url": "https://mtlexternal.alibabausercontent.com/feature/907e2fb61753b44ca73323317f3ed2c4/elemeadapter_android.so", "version": "10.51.30@1.3.7.8", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "TB3DSpace", "bindingActivities": [], "classes": [], "dependencies": ["taopai_business"], "featureName": "TB3DSpace", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "c53af269c5946884f922d8ea6df7b74f", "priority": -1, "size": 9173609, "url": "https://mtlexternal.alibabausercontent.com/feature/c53af269c5946884f922d8ea6df7b74f/TB3DSpace.so", "version": "10.51.30@1.0.189", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "industry", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "industry", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "240e6ad640dcbc32e56c426f975ef0bb", "priority": 5, "size": 228692, "url": "https://mtlexternal.alibabausercontent.com/feature/240e6ad640dcbc32e56c426f975ef0bb/industry.so", "version": "10.51.30@1.0.68", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "fm_android", "bindingActivities": [], "classes": [], "dependencies": ["industry"], "featureName": "fm_android", "fragments": [], "groupId": "com.taobao.android.fm", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "f66b371a45bd5d877feb4f5d57ce9acb", "priority": -1, "size": 99888, "url": "https://mtlexternal.alibabausercontent.com/feature/f66b371a45bd5d877feb4f5d57ce9acb/fm_android.so", "version": "10.51.30@0.0.1.0", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tmallgeniefortaobao", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tmallgeniefortaobao", "fragments": [], "groupId": "com.alibaba.coin.module", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "bcf6acdbc199b11395cac5cd646e5ee7", "priority": 10, "size": 37259, "url": "https://mtlexternal.alibabausercontent.com/feature/bcf6acdbc199b11395cac5cd646e5ee7/tmallgeniefortaobao.so", "version": "10.51.30@1.0.3", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "fliggy_vacation_ttdetail", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "fliggy_vacation_ttdetail", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.android.detail.ttdetail.platformization.business.BizLifecycle": "com.taobao.android.detail.ttdetail.platformization.business.BizContext"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "5f3b87c3aa869a9504255d0911477ace", "priority": 2, "size": 275243, "url": "https://mtlexternal.alibabausercontent.com/feature/5f3b87c3aa869a9504255d0911477ace/fliggy_vacation_ttdetail.so", "version": "10.51.30@1.0.56", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tmgdelivery", "bindingActivities": [], "classes": ["com.taobao.android.tmgdelivery.TMGDeliveryPurchaseFragment"], "dependencies": [], "featureName": "tmgdelivery", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "ac3bae2fd918330da7ca39867fa48864", "priority": 5, "size": 42292, "url": "https://mtlexternal.alibabausercontent.com/feature/ac3bae2fd918330da7ca39867fa48864/tmgdelivery.so", "version": "10.51.30@1.0.6.11", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tbsocialsdk", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tbsocialsdk", "fragments": ["com.taobao.vividsocial.dialog.CmtDialogFragment", "com.taobao.vividsocial.dialog.CmtReplyFragment", "com.taobao.android.commentpanel.container.CommentPanelFragment"], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "fa42b8fe0a71a7bf1dd9a237bd2aca00", "priority": 6, "size": 205868, "url": "https://mtlexternal.alibabausercontent.com/feature/fa42b8fe0a71a7bf1dd9a237bd2aca00/tbsocialsdk.so", "version": "10.51.30@2.1.1.98", "views": []}, {"featureInitalClass": "com.taobao.android.interactive.InteractiveApplication", "appVersion": "10.51.30", "artifactId": "tbvsvideofeature", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tbvsvideofeature", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "7e117a50586ea29efb56ba94107aacad", "priority": 5, "size": 426852, "url": "https://mtlexternal.alibabausercontent.com/feature/7e117a50586ea29efb56ba94107aacad/tbvsvideofeature.so", "version": "10.51.30@1.0.57", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ta<PERSON><PERSON>_wangxin", "bindingActivities": [], "classes": ["com.taobao.message.kit.tools.event.EventListener", "com.taobao.message.conversation.DefaultMessageTabHost", "com.taobao.message.message_open_api.core.CallManager"], "dependencies": ["messagesdkwrapper", "rxandroid"], "featureName": "ta<PERSON><PERSON>_wangxin", "fragments": ["com.taobao.message.chat.dojo.AuraFragment", "com.taobao.message.container.common.custom.lifecycle.PageLifecycleDispatchFragment"], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "2aed4cecedce139c9afac6ecf7e12048", "priority": 0, "size": 5450385, "url": "https://mtlexternal.alibabausercontent.com/feature/2aed4cecedce139c9afac6ecf7e12048/taobao_wangxin.so", "version": "10.51.30@********", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "applicationmonitor_olympic", "bindingActivities": [], "classes": ["com.taobao.monitor.olympic.OlympicLauncher", "com.taobao.monitor.olympic.OlympicOrangeLauncher"], "dependencies": [], "featureName": "applicationmonitor_olympic", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "d7d4ca6393ce2b5a66aea371b1245ca7", "priority": 10, "size": 45451, "url": "https://mtlexternal.alibabausercontent.com/feature/d7d4ca6393ce2b5a66aea371b1245ca7/applicationmonitor_olympic.so", "version": "10.51.30@1.0.4.90", "views": []}, {"featureInitalClass": "com.taobao.android.livehome.plugin.atype.flexaremote.LiveHomeInitial", "appVersion": "10.51.30", "artifactId": "LivehomeAtype", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "LivehomeAtype", "fragments": ["com.taobao.android.livehome.plugin.atype.flexaremote.homepage2.fragment.HomeSubChannelFragment"], "groupId": "com.taobao.android.flexa.remote", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "928aca40601ec01c2708996de1d1d8f2", "priority": 0, "size": 593781, "url": "https://mtlexternal.alibabausercontent.com/feature/928aca40601ec01c2708996de1d1d8f2/LivehomeAtype.so", "version": "10.51.30@1.3.99.268", "views": []}, {"featureInitalClass": "com.taobao.android.live.plugin.atype.flexaremote.ATypeInitial", "appVersion": "10.51.30", "artifactId": "liver<PERSON>_android_plugin_AType", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "liver<PERSON>_android_plugin_AType", "fragments": ["com.taobao.android.live.plugin.atype.flexaremote.tbliveinteractive.container.h5.TaoLiveWebBottomFragment"], "groupId": "com.taobao.android.live.plugin.atype.flexa.remote", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "d7ed92effc7a1be043176b36e0262af8", "priority": 7, "size": 1275459, "url": "https://mtlexternal.alibabausercontent.com/feature/d7ed92effc7a1be043176b36e0262af8/liveroom_android_plugin_AType.so", "version": "10.51.30@1.2.1391-dynamic", "views": []}, {"featureInitalClass": "com.taobao.android.live.plugin.btype.flexaremote.BTypeInitial", "appVersion": "10.51.30", "artifactId": "liver<PERSON>_android_plugin_BType", "bindingActivities": [], "classes": [], "dependencies": ["ktbiz_btype_android"], "featureName": "liver<PERSON>_android_plugin_BType", "fragments": [], "groupId": "com.taobao.android.live.plugin.btype.flexa.remote", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "4bed3abacf4f8292cdc59f0b0816719d", "priority": 4, "size": 738297, "url": "https://mtlexternal.alibabausercontent.com/feature/4bed3abacf4f8292cdc59f0b0816719d/liveroom_android_plugin_BType.so", "version": "10.51.30@1.0.4734.87", "views": []}, {"featureInitalClass": "com.taobao.android.live.plugin.btype.flexaremote.TBLiveGiftInitial", "appVersion": "10.51.30", "artifactId": "tblive-gift-android", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tblive_gift_android", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "4ef7d5bf425d23aff28d69154dc9af62", "priority": 4, "size": 58242, "url": "https://mtlexternal.alibabausercontent.com/feature/4ef7d5bf425d23aff28d69154dc9af62/tblive_gift_android.so", "version": "10.51.30@3.72.0.12", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taowise_android_aar", "bindingActivities": [], "classes": ["com.taobao.taowise.extension.TaoWiseInit"], "dependencies": [], "featureName": "taowise_android_aar", "fragments": [], "groupId": "com.taobao.taowise", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "45b66a92a1376043d0b0a615fc876be0", "priority": 5, "size": 1602563, "url": "https://mtlexternal.alibabausercontent.com/feature/45b66a92a1376043d0b0a615fc876be0/taowise_android_aar.so", "version": "10.51.30@1.6.42", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ratefeature", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "ratefeature", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "9d341693632a679a4990e5066d93a76a", "priority": 3, "size": 222877, "url": "https://mtlexternal.alibabausercontent.com/feature/9d341693632a679a4990e5066d93a76a/ratefeature.so", "version": "10.51.30@4.2.1.7081529", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tb_imagesearch_remote", "bindingActivities": [], "classes": ["com.etao.feimagesearch.capture.v3.CaptureV3Controller"], "dependencies": [], "featureName": "tb_imagesearch_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "eed54d09d7a895a089ea3ab9abf5fbf8", "priority": 3, "size": 201552, "url": "https://mtlexternal.alibabausercontent.com/feature/eed54d09d7a895a089ea3ab9abf5fbf8/tb_imagesearch_remote.so", "version": "10.51.30@2.0.39", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "voice_assistant", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "voice_assistant", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "ce8632718ffb083df938960c36ef9c9c", "priority": 5, "size": 1072952, "url": "https://mtlexternal.alibabausercontent.com/feature/ce8632718ffb083df938960c36ef9c9c/voice_assistant.so", "version": "10.51.30@1.1.0.7", "views": []}, {"featureInitalClass": "com.taobao.metrickit.honor.FlexaLauncher", "appVersion": "10.51.30", "artifactId": "metrickit-honor", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "metrickit_honor", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "307aa9f17db081b1f1c4f04cef1e5925", "priority": -1, "size": 53643, "url": "https://mtlexternal.alibabausercontent.com/feature/307aa9f17db081b1f1c4f04cef1e5925/metrickit_honor.so", "version": "10.51.30@3.0.7", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "rocket_megability_kit", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "rocket_megability_kit", "fragments": [], "groupId": "com.alibaba.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "faa94a42f2b633d541235415bee07760", "priority": 8, "size": 12683, "url": "https://mtlexternal.alibabausercontent.com/feature/faa94a42f2b633d541235415bee07760/rocket_megability_kit.so", "version": "10.51.30@0.0.1", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taobao_shake_sdk", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "taobao_shake_sdk", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.android.shake.remote.IShakeRemoteApi": "com.taobao.android.shake.ShakeRemoteApiImpl"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "3621a21c1ad67b72b1a7c211a82f2f6f", "priority": 5, "size": 41566, "url": "https://mtlexternal.alibabausercontent.com/feature/3621a21c1ad67b72b1a7c211a82f2f6f/taobao_shake_sdk.so", "version": "10.51.30@1.1.32.26", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "layoutmanager-feature", "bindingActivities": [], "classes": ["com.taobao.tao.flexbox.layoutmanager.tool.ToolManager", "com.efs.sdk.base.EfsTracker", "com.taobao.cursor.CursorManager", "com.taobao.tao.flexbox.layoutmanager.tool.Debugger", "com.taobao.tao.flexbox.layoutmanager.component.BottomSheetComponent", "com.taobao.tao.flexbox.layoutmanager.component.ImageScannerComponent", "com.taobao.tao.flexbox.layoutmanager.component.OpacityComponent", "com.taobao.tao.flexbox.layoutmanager.component.PhotoViewComponent", "com.taobao.tao.flexbox.layoutmanager.component.NavComponent", "com.taobao.tao.flexbox.layoutmanager.component.GoTopComponent", "com.taobao.tao.flexbox.layoutmanager.component.ExposureComponent", "com.taobao.tao.flexbox.layoutmanager.component.DragButtonComponent", "com.taobao.tao.flexbox.layoutmanager.component.CheckBoxComponent", "com.taobao.user.context.UCCollectEntry"], "dependencies": [], "featureName": "layoutmanager_feature", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "b3b62407edde3c16376aca812af2647a", "priority": 6, "size": 267234, "url": "https://mtlexternal.alibabausercontent.com/feature/b3b62407edde3c16376aca812af2647a/layoutmanager_feature.so", "version": "10.51.30@1.0.1575", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tmallandroid_MirrorLifeAndroid", "bindingActivities": [], "classes": ["com.tmall.wireless.mirrorlife.main.MirrorMainFragment"], "dependencies": [], "featureName": "tmallandroid_MirrorLifeAndroid", "fragments": ["com.tmall.wireless.mirrorlife.main.MirrorMainFragment"], "groupId": "com.tmall.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "1d0604a456392bc8e23e81f3dd3cfe0b", "priority": -1, "size": 144674, "url": "https://mtlexternal.alibabausercontent.com/feature/1d0604a456392bc8e23e81f3dd3cfe0b/tmallandroid_MirrorLifeAndroid.so", "version": "10.51.30@1.0.71", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tblive_opensdk", "bindingActivities": [], "classes": [], "dependencies": ["taopai_business"], "featureName": "tblive_opensdk", "fragments": [], "groupId": "com.taobao.live4anchor", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "cb63e1833ea58b581319bac2704ec9bf", "priority": -1, "size": 3461273, "url": "https://mtlexternal.alibabausercontent.com/feature/cb63e1833ea58b581319bac2704ec9bf/tblive_opensdk.so", "version": "10.51.30@0.0.0.101.60.34", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tmgbusiness4androidremote", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tmgbusiness4androidremote", "fragments": ["com.taobao.oversea.live.homepage.ContainerFragment", "com.taobao.oversea.discovery.TmgDiscoveryRemoteFragment"], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "7e6408db94e9342e81da992c80b6bbbf", "priority": 10, "size": 78946, "url": "https://mtlexternal.alibabausercontent.com/feature/7e6408db94e9342e81da992c80b6bbbf/tmgbusiness4androidremote.so", "version": "10.51.30@1.0.25", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "inside_weex_plugin", "bindingActivities": [], "classes": ["com.alipay.android.phone.inside.service.InsideOperationService"], "dependencies": [], "featureName": "inside_weex_plugin", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.ma.api.ITBInsideService": "com.taobao.android.inside.plugin.AlipayInsideServiceImpl"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "d3e20134f5b56c6b2322795c4b90ce2b", "priority": 10, "size": 288240, "url": "https://mtlexternal.alibabausercontent.com/feature/d3e20134f5b56c6b2322795c4b90ce2b/inside_weex_plugin.so", "version": "10.51.30@1.0.2", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "quality-remote-android", "bindingActivities": [], "classes": ["com.taobao.android.qualityremote.adapter.Initializer"], "dependencies": [], "featureName": "quality_remote_android", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "fbebebc570c8a9be5ea7f6e52fe73e07", "priority": -1, "size": 12683, "url": "https://mtlexternal.alibabausercontent.com/feature/fbebebc570c8a9be5ea7f6e52fe73e07/quality_remote_android.so", "version": "10.51.30@1.0.14", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "triver_taobao", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "triver_taobao", "fragments": ["com.alibaba.ariver.app.api.ui.fragment.RVFragment", "com.alibaba.triver.fragment.TRFragment"], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {"10.51.20": "f8f42ae4233fccd66c8a04f1ec867e61"}, "featuresReuseConfigMap": {"10.50.11": {"baseMd5": "2dbedce97fe3c0d7884c92e74b0d6581", "reDownload": true, "reuseAppVersion": "10.50.11"}, "10.51.20": {"baseMd5": "f8f42ae4233fccd66c8a04f1ec867e61", "reDownload": true, "reuseAppVersion": "10.51.20"}, "10.51.0": {"baseMd5": "67d47ecdb7ce510d69a62e3ee40431e8", "reDownload": true, "reuseAppVersion": "10.51.0"}, "10.50.10": {"baseMd5": "637261dca3ca9ffd4ac2f4bc2c95fba5", "reDownload": true, "reuseAppVersion": "10.50.10"}, "10.51.10": {"baseMd5": "00a5c7dae65b8df39b1b28f6888b2daf", "reDownload": true, "reuseAppVersion": "10.51.10"}, "10.50.0": {"baseMd5": "ccb404e59982a2df67a655cb48af1a08", "reDownload": true, "reuseAppVersion": "10.50.0"}}, "md5": "e32d2ca812b3484b6ff1b98c64f82563", "priority": 8, "size": 5020908, "url": "https://mtlexternal.alibabausercontent.com/feature/e32d2ca812b3484b6ff1b98c64f82563/triver_taobao.so", "version": "10.51.30@1.3.1.78", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tucaobafeature", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tucaobafeature", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "3fbf1f2413b87974a99ac553cc6f1e15", "priority": 10, "size": 88898, "url": "https://mtlexternal.alibabausercontent.com/feature/3fbf1f2413b87974a99ac553cc6f1e15/tucaobafeature.so", "version": "10.51.30@1.0.73", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "order-map", "bindingActivities": [], "classes": ["com.taobao.android.order.bundle.weex2.view.AMapPlatformView", "com.taobao.android.order.map.common.dx.DXBabelAMapViewV4WidgetNode", "com.taobao.android.order.core.dinamicX.view.DXAMapViewWidgetNode", "com.taobao.android.order.bundle.dinamicX.view.DXBabelAMapViewWidgetNode", "com.taobao.android.order.bundle.dinamicX.view.amap.utils.LogisticDetailDataUtil"], "dependencies": [], "featureName": "order_map", "fragments": [], "groupId": "com.taobao.android.order", "implServices": {}, "matchedAppFeatureMd5": {"10.51.20": "7bd891150355c65072cbf0c6671304ca"}, "featuresReuseConfigMap": {"10.50.11": {"baseMd5": "6bb889739ba875e4216aff1d4aa604a8", "reDownload": true, "reuseAppVersion": "10.50.11"}, "10.51.20": {"baseMd5": "7bd891150355c65072cbf0c6671304ca", "reDownload": true, "reuseAppVersion": "10.51.20"}, "10.51.0": {"baseMd5": "e61afd382d68d190b95d05e7c4b1968d", "reDownload": true, "reuseAppVersion": "10.51.0"}, "10.50.10": {"baseMd5": "3e0ccf8c718050f7c0dd6a450663476c", "reDownload": true, "reuseAppVersion": "10.50.10"}, "10.51.10": {"baseMd5": "ce10a8716415073cfe3d018bb5268c35", "reDownload": true, "reuseAppVersion": "10.51.10"}, "10.50.0": {"baseMd5": "24d2a63802681790824cac052b0325fe", "reDownload": true, "reuseAppVersion": "10.50.0"}}, "md5": "213842ce6534e7116ca15b16a720b122", "priority": 8, "size": 11709589, "url": "https://mtlexternal.alibabausercontent.com/feature/213842ce6534e7116ca15b16a720b122/order_map.so", "version": "10.51.30@1.2.24", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taopai_business", "bindingActivities": [], "classes": [], "dependencies": ["taopai_sdk", "rxandroid"], "featureName": "taopai_business", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "40c4625336bdfb31b3bb885567ad5280", "priority": 10, "size": 1162147, "url": "https://mtlexternal.alibabausercontent.com/feature/40c4625336bdfb31b3bb885567ad5280/taopai_business.so", "version": "10.51.30@6.21.6.25", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taopai_sdk", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "taopai_sdk", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "500a4923a8272bcea227ec2b3ea04c69", "priority": -1, "size": 7000278, "url": "https://mtlexternal.alibabausercontent.com/feature/500a4923a8272bcea227ec2b3ea04c69/taopai_sdk.so", "version": "10.51.30@**********", "views": ["com.taobao.taopai.business.view.NoGestureViewPager"]}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "litecreator", "bindingActivities": [], "classes": [], "dependencies": ["taopai_sdk", "rxandroid", "umipublish", "layoutmanager_feature"], "featureName": "litecreator", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "0ddb9b334dd949234381b10c303a6052", "priority": -1, "size": 1866527, "url": "https://mtlexternal.alibabausercontent.com/feature/0ddb9b334dd949234381b10c303a6052/litecreator.so", "version": "10.51.30@1.1.8.502", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taobao_after_buy_ai", "bindingActivities": [], "classes": [], "dependencies": ["information_flow_ai"], "featureName": "taobao_after_buy_ai", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.android.ai.api.IAfterBuyAiApi": "com.taobao.android.ai.AfterBuyAiApi"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "49d79982bd8f8f8cf8fa2c1e22903c99", "priority": 10, "size": 37259, "url": "https://mtlexternal.alibabausercontent.com/feature/49d79982bd8f8f8cf8fa2c1e22903c99/taobao_after_buy_ai.so", "version": "10.51.30@1.0.9", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "fluid-remote", "bindingActivities": [], "classes": ["com.taobao.android.fluid.remote.FluidRemoteVersion", "com.taobao.android.fluid.remote.precache.PreCacheService"], "dependencies": [], "featureName": "fluid_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "a27238be2f6b71c3dc7de391c2d55f51", "priority": 0, "size": 49547, "url": "https://mtlexternal.alibabausercontent.com/feature/a27238be2f6b71c3dc7de391c2d55f51/fluid_remote.so", "version": "10.51.30@1.0.128", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ugc_imagecontent_detailpage", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "ugc_imagecontent_detailpage", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "7f18c4efc88136d190f7ce8130e2d851", "priority": -1, "size": 604345, "url": "https://mtlexternal.alibabausercontent.com/feature/7f18c4efc88136d190f7ce8130e2d851/ugc_imagecontent_detailpage.so", "version": "10.51.30@1.0.212", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "image_search_xr_aar", "bindingActivities": [], "classes": [], "dependencies": ["TB3DSpace"], "featureName": "image_search_xr_aar", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "9391fb53d0da4a421c0f093b629c862a", "priority": -1, "size": 127534, "url": "https://mtlexternal.alibabausercontent.com/feature/9391fb53d0da4a421c0f093b629c862a/image_search_xr_aar.so", "version": "10.51.30@1.0.51", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "information_flow_ai", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "information_flow_ai", "fragments": [], "groupId": "com.taobao.android", "implServices": {"com.taobao.android.information.ai.api.IInformationFlowAiApi": "com.taobao.informationflowai.InfoFlowAiProvider"}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "f0e55dbeabe0e33b5fd56589da8d336e", "priority": 0, "size": 45451, "url": "https://mtlexternal.alibabausercontent.com/feature/f0e55dbeabe0e33b5fd56589da8d336e/information_flow_ai.so", "version": "10.51.30@********", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ugc-component-rate", "bindingActivities": [], "classes": [], "dependencies": ["ugc_core"], "featureName": "ugc_component_rate", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "3aa35d241d3ccfb0d363b64bc159967a", "priority": 0, "size": 253365, "url": "https://mtlexternal.alibabausercontent.com/feature/3aa35d241d3ccfb0d363b64bc159967a/ugc_component_rate.so", "version": "10.51.30@1.0.5.261", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ugc-core", "bindingActivities": [], "classes": [], "dependencies": ["ugc_component_rate"], "featureName": "ugc_core", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "426178287a094638e7ea26aed1e222ec", "priority": 0, "size": 391422, "url": "https://mtlexternal.alibabausercontent.com/feature/426178287a094638e7ea26aed1e222ec/ugc_core.so", "version": "10.51.30@1.2.3.261", "views": ["com.taobao.ugc.widget.ZoomImageView"]}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "rocket_mega_design", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "rocket_mega_design", "fragments": ["com.taobao.android.rocketmegadesign.imagepreview.DarkPagePreviewFragment", "com.taobao.android.rocketmegadesign.imagepreview.DarkPagePreviewFragmentV2"], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "2459a76bbec431be522a554d98b99939", "priority": 0, "size": 108195, "url": "https://mtlexternal.alibabausercontent.com/feature/2459a76bbec431be522a554d98b99939/rocket_mega_design.so", "version": "10.51.30@0.0.4.62", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "turboflow_biz-remote", "bindingActivities": [], "classes": ["com.taobao.android.turbo.flexaremote.Tab2ServiceRegistry"], "dependencies": [], "featureName": "turboflow_biz_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "e04a5c0f7a5cfee38bbad5461486522e", "priority": 6, "size": 214649, "url": "https://mtlexternal.alibabausercontent.com/feature/e04a5c0f7a5cfee38bbad5461486522e/turboflow_biz_remote.so", "version": "10.51.30@1.0.107", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "turboflow_dressup-remote", "bindingActivities": [], "classes": ["com.taobao.android.dressup.flexaremote.feeds.DressUpFeedServiceRegistry"], "dependencies": [], "featureName": "turboflow_dressup_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "1121173b1fc7a19f72d8ef52a914ec93", "priority": 6, "size": 306074, "url": "https://mtlexternal.alibabausercontent.com/feature/1121173b1fc7a19f72d8ef52a914ec93/turboflow_dressup_remote.so", "version": "10.51.30@1.0.94", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ta<PERSON><PERSON>_weex_adapter", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "ta<PERSON><PERSON>_weex_adapter", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "1bfbb2b17c2625e2c1c056acc0e892dd", "priority": -1, "size": 1106529, "url": "https://mtlexternal.alibabausercontent.com/feature/1bfbb2b17c2625e2c1c056acc0e892dd/taobao_weex_adapter.so", "version": "10.51.30@2.0.2.74", "views": []}, {"featureInitalClass": "com.taobao.taolive.crossplatformfoundation.flexaremote.TLCrossPlatformInitial", "appVersion": "10.51.30", "artifactId": "tao-live-crossplatform-foundation-remote-android", "bindingActivities": [], "classes": [], "dependencies": ["nexus_remote_android"], "featureName": "tao_live_crossplatform_foundation_remote_android", "fragments": [], "groupId": "com.taobao.live.kmp", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "023fdcf50a54bcb2b93487b9c21e2bc5", "priority": 0, "size": 151947, "url": "https://mtlexternal.alibabausercontent.com/feature/023fdcf50a54bcb2b93487b9c21e2bc5/tao_live_crossplatform_foundation_remote_android.so", "version": "<EMAIL>", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "android_ace_wrapper", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "android_ace_wrapper", "fragments": [], "groupId": "com.taobao.search", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "89c88b62fbdf8e9e79bd1f8bbaed0ee7", "priority": -1, "size": 57824, "url": "https://mtlexternal.alibabausercontent.com/feature/89c88b62fbdf8e9e79bd1f8bbaed0ee7/android_ace_wrapper.so", "version": "10.51.30@1.0.10", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "SNPE4Android", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "SNPE4Android", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "4c130992f0edc77b1837dc6d71d30e58", "priority": -1, "size": 20089025, "url": "https://mtlexternal.alibabausercontent.com/feature/4c130992f0edc77b1837dc6d71d30e58/SNPE4Android.so", "version": "10.51.30@1.0.0-android-ta<PERSON>ao", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "biometric", "bindingActivities": [], "classes": ["com.alipay.mobile.security.zim.api.ZIMFacade"], "dependencies": [], "featureName": "biometric", "fragments": [], "groupId": "com.alipay.android.phone.securitycommon", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "89983c5f3dd92359254f9e31ed1235c3", "priority": 0, "size": 2040348, "url": "https://mtlexternal.alibabausercontent.com/feature/89983c5f3dd92359254f9e31ed1235c3/biometric.so", "version": "10.51.30@3.73.7.20250331", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "information_flow_biz_extend-remote", "bindingActivities": [], "classes": ["com.taobao.infoflow.extend.flexaremote.DefaultServiceRegistry"], "dependencies": [], "featureName": "information_flow_biz_extend_remote", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "49c4e92f84bb97f9822e68a5de82f646", "priority": -1, "size": 24971, "url": "https://mtlexternal.alibabausercontent.com/feature/49c4e92f84bb97f9822e68a5de82f646/information_flow_biz_extend_remote.so", "version": "10.51.30@1.0.6", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "trade-dynamic-uikit", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "trade_dynamic_uikit", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "55b9ab60654314e9ec881548ff51c3a6", "priority": 0, "size": 74908, "url": "https://mtlexternal.alibabausercontent.com/feature/55b9ab60654314e9ec881548ff51c3a6/trade_dynamic_uikit.so", "version": "10.51.30@0.0.0.12", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "dtao-liveshop-ttdetail", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "dtao_liveshop_ttdetail", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "368784e9f72ab0fa1e9642f0dac98745", "priority": 0, "size": 67462, "url": "https://mtlexternal.alibabausercontent.com/feature/368784e9f72ab0fa1e9642f0dac98745/dtao_liveshop_ttdetail.so", "version": "10.51.30@1.2.9", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "rxandroid", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "rxandroid", "fragments": [], "groupId": "io.reactivex.rxjava2", "implServices": {}, "matchedAppFeatureMd5": {"10.51.20": "5dafac8340c7d252c5f553e5541bc179"}, "featuresReuseConfigMap": {"10.50.11": {"baseMd5": "0df9d541007863136f26742dbc5c3891", "reDownload": true, "reuseAppVersion": "10.50.11"}, "10.51.20": {"baseMd5": "5dafac8340c7d252c5f553e5541bc179", "reDownload": true, "reuseAppVersion": "10.51.20"}, "10.51.0": {"baseMd5": "febcca87515f079b573a4c5fae756ccf", "reDownload": true, "reuseAppVersion": "10.51.0"}, "10.50.10": {"baseMd5": "0232fd3341588561181d69d88cac22ef", "reDownload": true, "reuseAppVersion": "10.50.10"}, "10.51.10": {"baseMd5": "4f72d682a08bd74c182d25440c0bbfc2", "reDownload": true, "reuseAppVersion": "10.51.10"}, "10.50.0": {"baseMd5": "3a8b76233990f003fbd4aad2a8ac6c93", "reDownload": true, "reuseAppVersion": "10.50.0"}}, "md5": "532ba4730d0bd4cc2cd02f571ca1a02a", "priority": 0, "size": 373203, "url": "https://mtlexternal.alibabausercontent.com/feature/532ba4730d0bd4cc2cd02f571ca1a02a/rxandroid.so", "version": "10.51.30@2.0.2", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "tbmobilesmart", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "tbmobilesmart", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "a2f38fbe8fd2de86081aca3260d09fbf", "priority": 0, "size": 53643, "url": "https://mtlexternal.alibabausercontent.com/feature/a2f38fbe8fd2de86081aca3260d09fbf/tbmobilesmart.so", "version": "10.51.30@1.0.42", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "ktbiz-btype-android", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "ktbiz_btype_android", "fragments": [], "groupId": "com.taobao.kmp.live", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "5a22b86d725e112d38881775a27adf91", "priority": 0, "size": 12683, "url": "https://mtlexternal.alibabausercontent.com/feature/5a22b86d725e112d38881775a27adf91/ktbiz_btype_android.so", "version": "10.51.30@1.0.25.0630", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "umipublish", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "umipublish", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "94820bd4ebfd4704fb7b071d4d9d2dbb", "priority": 0, "size": 301696, "url": "https://mtlexternal.alibabausercontent.com/feature/94820bd4ebfd4704fb7b071d4d9d2dbb/umipublish.so", "version": "10.51.30@*********", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "messagesdkwrapper", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "messagesdkwrapper", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {"10.51.20": "7bfefa434dbedb0dde92f946558d014f"}, "featuresReuseConfigMap": {"10.50.11": {"baseMd5": "0ce95071f032a0e8b72d2aa87bea4956", "reDownload": true, "reuseAppVersion": "10.50.11"}, "10.51.20": {"baseMd5": "7bfefa434dbedb0dde92f946558d014f", "reDownload": true, "reuseAppVersion": "10.51.20"}, "10.51.0": {"baseMd5": "0f925e0804116935ca30395bf21b8242", "reDownload": true, "reuseAppVersion": "10.51.0"}, "10.50.10": {"baseMd5": "59bf35fd836201340e1142517642c12d", "reDownload": true, "reuseAppVersion": "10.50.10"}, "10.51.10": {"baseMd5": "f7e6b5f054f2758b7ac697eeaa5456ba", "reDownload": true, "reuseAppVersion": "10.51.10"}, "10.50.0": {"baseMd5": "5113b78b39642d0fbcfffa152b38757b", "reDownload": true, "reuseAppVersion": "10.50.0"}}, "md5": "f9e4aa3086cdf3c5f919f11975d51c3b", "priority": 0, "size": 5595758, "url": "https://mtlexternal.alibabausercontent.com/feature/f9e4aa3086cdf3c5f919f11975d51c3b/messagesdkwrapper.so", "version": "10.51.30@5.0.5.4-ANDROID", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "nexus-remote-android", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "nexus_remote_android", "fragments": [], "groupId": "com.taobao.kmp.nexus", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "8d184426c90ca48b19388a059386d689", "priority": 0, "size": 266635, "url": "https://mtlexternal.alibabausercontent.com/feature/8d184426c90ca48b19388a059386d689/nexus_remote_android.so", "version": "<EMAIL>", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "order-remote", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "order_remote", "fragments": [], "groupId": "com.taobao.android.order", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "4058a1e02962d5aa8269b49352c2dbb8", "priority": 0, "size": 557742, "url": "https://mtlexternal.alibabausercontent.com/feature/4058a1e02962d5aa8269b49352c2dbb8/order_remote.so", "version": "10.51.30@1.0.2", "views": []}, {"featureInitalClass": "", "appVersion": "10.51.30", "artifactId": "taobao-android-address-dynamic", "bindingActivities": [], "classes": [], "dependencies": [], "featureName": "taobao_android_address_dynamic", "fragments": [], "groupId": "com.taobao.android", "implServices": {}, "matchedAppFeatureMd5": {}, "featuresReuseConfigMap": {}, "md5": "621f1833984562ba7e30d9f09fb5a034", "priority": 0, "size": 83316, "url": "https://mtlexternal.alibabausercontent.com/feature/621f1833984562ba7e30d9f09fb5a034/taobao_android_address_dynamic.so", "version": "10.51.30@5.4.5", "views": []}]