{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"cancel": "取消", "noCard": "沒找到你要的卡？", "fold": "收起", "search": "搜索", "NoCardNo": "沒找到你要的卡？點此查詢卡號", "more": "查看更多", "bank_support": "支持#val#家銀行", "bind_card_tip": "免輸卡號添加", "return": "返回", "inputNo": "輸入卡號添加"}, "zh_TW": {"cancel": "取消", "noCard": "沒找到你要的卡？", "fold": "收起", "search": "搜索", "NoCardNo": "沒找到你要的卡？點此查詢卡號", "more": "查看更多", "bank_support": "支持#val#家銀行", "bind_card_tip": "免輸卡號添加", "return": "返回", "inputNo": "輸入卡號添加"}, "en_US": {"cancel": "Cancel", "noCard": "Can not find your card?", "fold": "fold", "search": "Search", "NoCardNo": "Can not find your card  Click here to check the card number", "more": "View more", "bank_support": "#val# banks supported", "bind_card_tip": "No need to add bank card no.", "return": "Back", "inputNo": "Enter card number"}, "zh_CN": {"cancel": "取消", "noCard": "没找到你要的卡？", "fold": "收起", "search": "搜索", "NoCardNo": "没找到你要的卡？点此查询卡号", "more": "查看更多", "bind_card_tip": "免输卡号添加", "bank_support": "支持#val#家银行", "return": "返回", "inputNo": "输入卡号添加"}}}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": ".amc-bg-white{background-color:#F5F5F5}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._BankCardListPage_d1gk-c-main-body{padding:16px 16px 0;margin:0}._BankCardListPage_d1gk-c-blank{height:40px;align-self:stretch}._BankCardListPage_d1gk-c-input-box{border-radius:4px;flex-direction:row;padding-left:8px;padding-right:8px;height:48px;background-color:#FFFFFF}._BankCardListPage_d1gk-c-logo{height:18px;width:18px;margin-right:8px}._BankCardListPage_d1gk-c-input-area{align-self:stretch;margin-bottom:12px}._BankCardListPage_d1gk-c-cancel-text{font-size:16px;color:#1677FF;text-align:center;margin-left:12px}._BankCardListPage_d1gk-c-input{flex:1.0;border:0;color:#333333;font-size:16px;padding:0;white-space:nowrap;placeholder-color:#999999;placeholder-font-size:16px}._BankCardListPage_d1gk-c-empty-container{height:191px;background-color:#FFFFFF;border-radius:12px;flex-direction:column;align-items:center;justify-content:center}._BankCardListPage_d1gk-c-empty-container-tip{font-size:16px;color:#999999;text-align:center}._BankCardListPage_d1gk-c-empty-container-action{margin-top:16px;border:1px solid #1677FF;border-radius:4px}._BankCardListPage_d1gk-c-empty-container-action-tip{font-size:14px;color:#1677FF;text-align:center;margin:4px 16px}._BankCardListPage_d1gk-c-end-tip{margin-top:16px;font-size:16px;color:#4B6B99;text-align:center}._QuickBindList_y1d4-c-card-list-box{border-radius:8px;background-color:#FFFFFF}._QuickBindList_y1d4-c-card-list-box-header{margin:16px;align-self:stretch;align-content:flex-end;justify-items:flex-start}._QuickBindList_y1d4-c-card-list-box-header-tip-1{font-size:18px;font-weight:bold;color:#333333}._QuickBindList_y1d4-c-card-list-box-header-tip-2{font-size:14px;color:#A8A6A6;align-self:flex-end}._QuickBindList_y1d4-c-card-list-divide{background-color:#EFEFEF;opacity:0.75;height:1px}._QuickBindList_y1d4-c-card-list-divide-mt{margin-top:12px}._QuickBindList_y1d4-c-card-list-divide-ml{margin-left:36px}._QuickBindList_y1d4-c-card-list-item{flex-direction:row}._QuickBindList_y1d4-c-card-list-item-logo{width:24px;height:24px;margin-right:12px}._QuickBindList_y1d4-c-card-list-item-content{justify-content:flex-start}._QuickBindList_y1d4-c-card-list-item-content-title{font-size:17px;color:#333333;letter-spacing:0;text-overflow:ellipsis;overflow:hidden;text-align:left;min-width:90px;flex:1.0;white-space:nowrap}._QuickBindList_y1d4-c-card-list-item-content-desc{font-size:14px;color:#FF6010;letter-spacing:0;text-align:right;flex:auto;white-space:nowrap}._QuickBindList_y1d4-c-card-list-item-tail{width:12px;height:12px;margin-left:4px}._QuickBindList_y1d4-c-card-more-content{font-size:14px;color:#999999}._QuickBindList_y1d4-c-card-item-container{padding:12px 16px 0px}._QuickBindList_y1d4-c-card-list-item-content-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;overflow:hidden;margin-top:4px;margin-left:36px}._QuickBindList_y1d4-c-card-list-item-content-label{border:0.8px solid #FFCFB7;color:#FF6010;margin-right:8px;border-radius:2px;font-size:10px;padding:2px 4px}._QuickBindList_y1d4-c-more-box{padding-bottom:12px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from d9a616eb346392367de0563fd5c3df561e57cf3d:D */!function(\nn){var i={};function o(t){if(i[t])return i[t].exports\n;var e=i[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports\n,e,e.exports,o),e.l=!0,e.exports}o.m=n,o.c=i,o.d=function(t,\ne,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,\nget:n})},o.r=function(t){\n'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(\nt,Symbol.toStringTag,{value:'Module'}),\nObject.defineProperty(t,'__esModule',{value:!0})},\no.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(\n4&t&&'object'==typeof e&&e&&e.__esModule)return e\n;var n=Object.create(null);if(o.r(n),Object.defineProperty(n\n,'default',{enumerable:!0,value:e}),2&t&&'string'!=typeof e\n)for(var i in e)o.d(n,i,function(t){return e[t]}.bind(null,i\n));return n},o.n=function(t){var e=t&&t.__esModule?function(\n){return t.default}:function(){return t};return o.d(e,'a',e)\n,e},o.o=function(t,e){\nreturn Object.prototype.hasOwnProperty.call(t,e)},o.p='',o(\no.s=6)}([function(t,e,n){'use strict';Object.defineProperty(\ne,'__esModule',{value:!0}),e.amc=window.amc},function(t,e,n\n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0});var i=n(7);e.BNComponent=i.BNComponent;var o=n(4)\n;e.ComponentRegistry=o.ComponentRegistry;var r=n(9)\n;e.Logger=r.Logger,e.logger=r.logger},function(t,e,n){\n'use strict';Object.defineProperty(e,'__esModule',{value:!0}\n);var i,c=n(0),o=n(5);function r(t,e,n,i){var o;if(\nc.amc.isAndroid)o=document.createElement('embed',e,function(\n){});else for(var r in o=document.createElement('embed'),e\n)e.hasOwnProperty(r)&&(o[r]=e[r]);return n&&(o.className=n),\ni?t.insertBefore(o,i):t.appendChild(o),o}\ne.modifyElementStyle=function(t,e,n){var i=e\n;c.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),\ni&&o.copyObj(n,i.style)},e.modifyElementAttribute=function(t\n,e,n){if(e&&n){var i=e;if(c.amc.fn.isString(e)&&(\ni=t.getViewInComponentById(e)),i)for(var o in n\n)n.hasOwnProperty(o)&&(i[o]=n[o])}},\ne.modifyElementClass=function(t,e,n,i){var o=e\n;c.amc.fn.isString(e)&&(o=t.getViewInComponentById(e)),o&&(\ni||(o.className=''),t.applyStyleTo(o,n))},\ne.visibleElement=function(t,e,n){var i;void 0===n&&(n=!0),\ne&&(i=c.amc.fn.isString(e)?t.getViewInComponentById(e):e)&&(\nn?c.amc.fn.show(i):c.amc.fn.hide(i))},\ne.modifyElementCSS=function(t,e,n){if(e){var i=e\n;c.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),\ni&&n&&(i.style.cssText=n)}},e.createEmbedViPlugin=function(t\n,e,n,i){return r(t,e,n,i)},e.createEmbedPlugin=r,\ne.getThemeColor=(i='',function(){return i||(\ni=c.amc.fn.sdkGreaterThanOrEqual('10.8.39'\n)?'#1677FF':'#108EE9'),i})},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0}),\ne.randomStr=function(){return Math.floor(61439*Math.random(\n)+4096).toString(16)},e.startsWith=function(t,e){\nreturn!!t&&0===t.indexOf(e)},e.tryJSONParse=function(t){if(\ne=t,'[object Object]'===Object.prototype.toString.call(e)\n)return t;var e;try{return JSON.parse(t)}catch(t){return{}}}\n,e.copyObj=function(t,e){for(var n in e||(e={}),t\n)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}},function(t,e,n\n){'use strict';Object.defineProperty(e,'__esModule',{\nvalue:!0});var i=n(1),o=function(){function n(){}\nreturn n.registerComponent=function(t,e){\ne?n.facts[t]?i.logger.e('CmpReg#regCmp','E0002 '+t):(\ni.logger.i('CmpReg#regCmp','I0003 '+t),n.facts[t]=e\n):i.logger.e('CmpReg#regCmp','E0001 '+t+', '+e)},\nn.getKnownComponents=function(){return n.facts},\nn.getComponentJson=function(t){return n.jsons[t]},\nn.putComponentJson=function(t,e){e||i.logger.e(\n'CmpReg#putCmpJ','E0004 '+t+', '+e),n.getComponentJson(t\n)?i.logger.e('CmpReg#putCmpJ','E0005 '+t):(i.logger.i(\n'CmpReg#putCmpJ','I0006 '+t),n.jsons[t]=e)},\nn.createComponent=function(t){i.logger.i('CmpReg#crtCmp',\n'I0007 '+t);var e=n.facts[t];return e?new e:(i.logger.e(\n'CmpReg#crtCmp','E0008 '+t),null)},n.facts={},n.jsons={},n}(\n);e.ComponentRegistry=o},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var a=n(0)\n;function i(t,e){if(t&&e){if(e===t.src)return\n;a.amc.isAndroid&&'none'===t.style.display?(a.amc.fn.show(t)\n,window.setTimeout(function(){t.src=e},20)):t.src=e}}\nfunction o(t){if(!t)return 0;t=t.replace(/<\\/?[^>]+(>|$)/g,\n'');for(var e=0,n='',i=new RegExp('[\\\\u4E00-\\\\u9FFF]+','g'),\no=0,r=t;o<r.length;o++){var c=r[o];a.amc.isIOS&&(\n' '<=c&&c<='~'&&i.test(n)||' '<=n&&n<='~'&&i.test(c))&&(\ne+=.5),e+=' '<=c&&c<='~'?.58:1,n=c}return e}\ne.mergeObject=function(){for(var t=[],\ne=0;e<arguments.length;e++)t[e]=arguments[e];var n={};if(\nt&&t.length)for(var i=0;i<t.length;i++){var o=t[i];if(\na.amc.fn.isObject(o))for(var r in o)o.hasOwnProperty(r)&&(\nn[r]=o[r])}return n},e.isFunction=function(t){\nreturn'[object Function]'===Object.prototype.toString.call(t\n)},e.isPreRender=function(t){return t&&(\nt.local&&t.local.isPrerender||t.rpcData&&t.rpcData.isPrerender\n)},e.copyObj=function(t,e){for(var n in e||(e={}),t\n)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},\ne.doNothing=function(){},e.tryJSONParse=function(t){if(\nnull==t)return{};if(a.amc.fn.isObject(t))return t;try{\nreturn JSON.parse(t)}catch(t){return{}}},\ne.checkEmptyObj=function(t){return a.amc.fn.isString(t\n)?0===t.length:!(t&&0!==Object.keys(t).length)},\ne.substrWithFontWidth=function(t,e,n){if(!t)return t;for(\nvar i='',o=0,r=t.length,c=0;c<r;c++){var a=n?t[r-c-1]:t[c]\n;if(/^[A-Za-z0-9\\(\\)]*$/.test(a)?o+=.45:o++,i+=a,e-1<o)break\n}return i},e.calculateFontWidth=function(t){if(!t)return 0\n;for(var e=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,i=0;i<t.length;i++\n)n.test(t[i])?e+=.45:e++;return Math.round(e)},\ne.deepCopy=function t(e){if(null==e||'object'!=typeof e\n)return e;var n;if(e instanceof Date)return(n=new Date\n).setTime(e.getTime()),n;if(e instanceof Array){n=[];for(\nvar i=0,o=e.length;i<o;i++)n[i]=t(e[i]);return n}if(\ne instanceof Object){for(var r in n={},e)e.hasOwnProperty(r\n)&&(n[r]=t(e[r]));return n}throw new Error(\n'Unable to copy obj! Its type isn\\'t supported.')},\ne.getConfig=function(t,e){setTimeout(function(){\ndocument.invoke('queryInfo',{queryKey:'configInfo',\nconfigKey:t},function(t){e(t.available)})},20)},\ne.showLoading=function(){setTimeout(function(){\ndocument.invoke('showLoading')},20)},e.hideLoading=function(\n){setTimeout(function(){document.invoke('hideLoading')},20)}\n,e.safeInvoke=function(t,e,n){\na.amc.isAndroid?window.setTimeout(function(){\ndocument.invoke(t,e,n)},20):document.invoke(t,e,n)},\ne.safeLoadImgSrcAndSetMode=function(t,e){t&&e&&(\nt.contentmode=e.mode,i(t,e.src))},e.safeLoadImgSrc=i,\ne.calculateStyleLineHeight=function(t,e){\nreturn a.amc.isIOS?t:(t-e)/2****},\ne.calculateLabelShowLineNumber=function(t,e,n){if(!t||n<=0\n)return 1;var i=o(t)*e;return Math.ceil(i/n)},\ne.calculateFontCount=o,e.isLowDevice=function(){return!!(\nwindow.flybird&&window.flybird.local&&window.flybird.local.isLowDevice\n)},e.safeRemoveChildNode=function(e){if(e&&e.childNodes){\nfor(var t=[],n=0,i=e.childNodes;n<i.length;n++){var o=i[n]\n;t.push(o)}t.forEach(function(t){e.removeChild(t)})}}},\nfunction(t,e,n){'use strict';var i,o=this&&this.__extends||(\ni=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[\n]}instanceof Array&&function(t,e){t.__proto__=e}||function(t\n,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var r,c=n(1),a=n(0),s=n(10),u=n(11)\n,l=n(2),d=a.amc.rpcData,p=function(t){function e(){\nvar i=null!==t&&t.apply(this,arguments)||this\n;return i.searchLock=!1,i.closeKeyBoard=function(){\ni.input.value='',setTimeout(function(){\na.amc.isIOS?a.amc.fn.hideKeyboard():(i.input.blur(),\ndocument.body.focus()),i.onInput()},20)},i.onInput=function(\n){i.checkList()},i.checkList=function(){if(!i.searchLock){\ni.searchLock=!0;var e=i.input.value\n;i.quickBindList.visibleHeader(!(e&&0<e.length)),\ni.showQuickList=d.instInfos.filter(function(t){\nreturn i.checkStr(t.instName,e)}),\ni.quickBindList.setBankList(i.showQuickList,\nd.instInfos.length,d.instInfos.length),\ni.showQuickList&&0!==i.showQuickList.length?(\ni.quickBindList.visible(!0),u.visibleElement(i,\n'empty-container',!1),u.visibleElement(i,'end-tip',!0)):(\ni.quickBindList.visible(!1),u.visibleElement(i,'end-tip',!1)\n,u.visibleElement(i,'empty-container',!0),\na.amc.fn.spmExposure('a283.b12138.c113573.d235401')),\ni.searchLock=!1}},i.checkStr=function(e,t){return t.replace(\n/\\s/g,'').split('').every(function(t){return-1!==e.indexOf(t\n)})},i.onMounted=function(){\ni.quickBindList=new s.QuickBindList({signAction:'loc:none',\nspmServerParam:{},spm:{more:'',\nbankItem:'a283.b12138.c29199.d56097'}}),\ni.input=i.getViewInComponentById('input'),\ni.input.onfocus=function(){l.modifyElementStyle(i,\n'input-box',{border:'1px solid #1677FF'}),u.visibleElement(i\n,'cancel-text',!0),a.amc.fn.spmClick(\n'a283.b12138.c113572.d235400')},i.input.onblur=function(){\nl.modifyElementStyle(i,'input-box',{border:'0 #FFFFFF'}),\nu.visibleElement(i,'cancel-text',!1)}\n;var t=i.getViewInComponentById('main-body'),\ne=i.getViewInComponentById('end-tip')\n;i.quickBindList.mountTo(t,e),\nd.instInfos=d.instInfos.filter(function(t){return!!t.enable}\n),i.showQuickList=d.instInfos,i.quickBindList.setBankList(\nd.instInfos,d.instInfos.length,d.instInfos.length\n).setBankClickListener(i.bankItemCallBack).setHeaderTipDesc(\n'（'+a.amc.fn.i18nPlaceholderReplace('{{bank_support}}',\nd.instInfos.length)+'）'),a.amc.isAndroid&&setTimeout(\nfunction(){document.setProp('focusableInTouchMode',{value:!0\n})},100),i.checkList()},i.goCardNo1=function(){\na.amc.fn.spmClick('a283.b12138.c113573.d235401'),i.goCardNo(\n)},i.goCardNo2=function(){a.amc.fn.spmClick(\n'a283.b12138.c29199.d235402'),i.goCardNo()},\ni.goCardNo=function(){document.submit({action:{\nname:'/cashier/cardinitview'}})},\ni.bankItemCallBack=function(t){if(!i.searchLock){\ni.searchLock=!0;var e,n=i.showQuickList[t]||{}\n;a.amc.fn.spmClick('a283.b12138.c29199.d56097_'+t,{\ninstId:n.instId}),e=d.expressSignSelAction?{action:{\nname:d.expressSignSelAction,params:{instId:n.instId}}}:{\naction:{name:'loc:bnvb'},param:{\ntplid:'QUICKPAY@cashier-card-type-flex',tpl:d.tpl||'',data:{\nbankInfo:n,helpURL:d.signHelpURL,\nsubmitAction:d.signApplyAction,\nbtnTxt:d.btnTxt||'{{agree_next}}'}}},document.submit(e),\ni.searchLock=!1}},i}return o(e,t),\ne.prototype.onHelp=function(){a.amc.fn.spmClick(\n'a283.b12138.c29199.d56098',void 0)\n;var t=d.helpURL||'https://csmobile.alipay.com/router.htm?scene=app_quickbindcardxyh'\n;document.submit({action:{\nname:'loc:openweb(\\''+t+'\\', \\'{{help}}\\')'}})},\ne.prototype.onBack=function(){a.amc.fn.spmClick(\n'a283.b12138.c29199.d56099',void 0),a.amc.fn.spmPageDestroy(\n'a283.b12138',void 0),a.amc.fn.back()},\ne.getComponentCSSRules=function(){return{\n'.main-body':'_BankCardListPage_d1gk-c-main-body',\n'.blank':'_BankCardListPage_d1gk-c-blank',\n'.input-box':'_BankCardListPage_d1gk-c-input-box',\n'.logo':'_BankCardListPage_d1gk-c-logo',\n'.input-area':'_BankCardListPage_d1gk-c-input-area',\n'.cancel-text':'_BankCardListPage_d1gk-c-cancel-text',\n'.input':'_BankCardListPage_d1gk-c-input',\n'.empty-container':'_BankCardListPage_d1gk-c-empty-container',\n'.empty-container-tip':'_BankCardListPage_d1gk-c-empty-container-tip',\n'.empty-container-action':'_BankCardListPage_d1gk-c-empty-container-action',\n'.empty-container-action-tip':'_BankCardListPage_d1gk-c-empty-container-action-tip',\n'.end-tip':'_BankCardListPage_d1gk-c-end-tip'}},\ne.getComponentJson=function(){return{\n_c:'amc-v-box _BankCardListPage_d1gk-c-main-body amc-scroll amc-flex-1',\n'sp-view-id':'main-body',_t:'div',_cd:[{\n_c:'amc-align-center _BankCardListPage_d1gk-c-input-area',\n_t:'div',_cd:[{'sp-view-id':'input-box',\n_c:'_BankCardListPage_d1gk-c-input-box amc-align-center amc-flex-1',\n_t:'div',_cd:[{\nsrc:'https://mdn.alipayobjects.com/portal_ahl69x/afts/img/A*MOkIQ6fwjUkAAAAAAAAAAAAAAQAAAQ/[pixelWidth]w_[pixelWidth]h',\n_c:'_BankCardListPage_d1gk-c-logo',_t:'img'},{\n'sp-view-id':'input',_c:'_BankCardListPage_d1gk-c-input',\nplaceholder:'{{search}}',oninput:'onInput',_t:'input'}]},{\n'sp-view-id':'cancel-text',\n_c:'_BankCardListPage_d1gk-c-cancel-text amc-hidden',\nonclick:'closeKeyBoard',_t:'label',_x:'{{cancel}}'}]},{\n_c:'_BankCardListPage_d1gk-c-empty-container amc-hidden',\n'sp-view-id':'empty-container',_t:'div',_cd:[{\n_c:'_BankCardListPage_d1gk-c-empty-container-tip',\n_t:'label',_x:'{{noCard}}'},{\n_c:'_BankCardListPage_d1gk-c-empty-container-action',\nonclick:'goCardNo1',_t:'div',_cd:[{\n_c:'_BankCardListPage_d1gk-c-empty-container-action-tip',\n_t:'label',_x:'{{inputNo}}'}]}]},{\n_c:'_BankCardListPage_d1gk-c-end-tip',\n'sp-view-id':'end-tip',onclick:'goCardNo2',_t:'label',\n_x:'{{NoCardNo}}'},{_c:'_BankCardListPage_d1gk-c-blank',\n_t:'div'}]}},e.componentName='BankCardListPage',\ne.componentHashName='BankCardListPage_d1gk',e}(c.BNComponent\n);e.BankCardListPage=p,r=a.amc.fn.docConfig,\na.amc.fn.docConfig=function(){var t=JSON.parse(r())\n;return t.navi={naviBarColor:'#F5F5F5',statusBarStyle:'dark'\n},JSON.stringify(t)},a.amc.fn.spmPageCreate('a283.b12138')\n;var f=!1;document.viewDidAppear=function(){\nf?a.amc.fn.spmExposureResume():f=!0},window.onload=function(\n){var t=new p\n;document.body.style.height=a.amc.isAndroid?window.innerHeight:a.amc.specs.bodyHeight\n;var e=a.amc.fn.getNav(a.amc.res.navBack,\na.amc.isAndroid?'':'{{return}}','','',a.amc.res.help,\nfunction(){t.onBack()},function(){t.onHelp()},{\nbackMode:'back'});document.body.appendChild(e),t.mountTo(\ndocument.body),\nwindow.flybird&&window.flybird.local&&window.flybird.local.agednessVersion&&(\nwindow.remScale=1.2,document.body.style.height/=1.2),\nwindow.onKeyDown=function(){4==event.which&&t.onBack()},\na.amc.fn.spmExposure('a283.b12138.c29199.d56098',void 0,!1),\na.amc.fn.spmExposure('a283.b12138.c29199.d56099',void 0,!1)}\n},function(t,e,n){'use strict'\n;var k=this&&this.__assign||function(){return(\nk=Object.assign||function(t){for(var e,n=1,\ni=arguments.length;n<i;n++)for(var o in e=arguments[n]\n)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])\n;return t}).apply(this,arguments)};Object.defineProperty(e,\n'__esModule',{value:!0});var C=n(1),L=n(3),w=n(4),A=n(8),\ni=function(){function t(){this.vueModel={data:{},compute:{}}\n,this._componentName=this.constructor.componentName,\nthis._htmlString=this.constructor.componentHTML,\nthis._componentJson=this.constructor.getComponentJson(),\nthis._componentCSSRules=this.constructor.getComponentCSSRules(\n),this._hash=L.randomStr(),this._hasRootViewBuilt=!1,\nthis._rootView=null,this._componentId='',\nthis._subComponents=[],this._subComponentsMap={},\nthis._viewsIdMap={}}return t.getComponentCSSRules=function(\n){throw new Error('E0100')},t.getComponentJson=function(){\nthrow new Error('E0101')},t.prototype.mountTo=function(t,e){\nif(t){var n=this._acquireRootView();n?(e?t.insertBefore(n,e\n):t.appendChild(n),this._triggerOnMounted()):C.logger.e(\n'Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+t)},\nObject.defineProperty(t.prototype,'debugName',{get:function(\n){\nreturn'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'\n},enumerable:!0,configurable:!0}),\nt.prototype.getMountedRootView=function(){\nreturn this._hasRootViewBuilt?this._rootView:null},\nt.prototype.getMountedParentView=function(){\nvar t=this.getMountedRootView();return t?t.parentNode:null},\nt.prototype.getSubComponentById=function(t,e){\nvar n=this.debugName+'#SCById',i=this._subComponentsMap[t]\n;if(!i)return null;var o='',r='';try{\no=i.constructor.componentName,r=e.componentName}catch(t){\nC.logger.e(n,'E0104 '+t)}return o&&o===r?i:(C.logger.e(n,\n'E0105 '+o+', '+r),null)},\nt.prototype.getViewInComponentById=function(t){\nreturn this._viewsIdMap[t]},\nt.prototype.getComponentId=function(){\nreturn this._componentId},\nt.prototype.createStyledElement=function(t,e,n){\nvar i=document.createElement(t);if(i)return t&&(\ni.className+=' '+this._css(t,2)),n&&(\ni.className+=' '+this._csses(n,1)),e&&(\ni.className+=' '+this._css('#'+e,2)),i},\nt.prototype.applyStyleTo=function(t,e){t&&(\nt.className+=' '+this._csses(e,1))},\nt.prototype.css=function(t){return this._css(t,0)},\nt.prototype.csses=function(t){return this._csses(t,0)},\nt.prototype._csses=function(t,e){var n=this;return t.split(\n' ').map(function(t){return n._css(t,e)}).join(' ')},\nt.prototype._css=function(t,e){if(!t)return''\n;var n=this._componentCSSRules;if(!n)return t;switch(\nt.charAt(0)){case'#':case'.':return n[t]||t;default:switch(e\n){case 0:return n['.'+t]||n[t]||t;case 1:return n['.'+t]||t\n;case 2:default:return t}}},\nt.prototype._triggerOnMounted=function(){new A.Observer(\nthis.vueModel.data),C.logger.i('','I0106 '+this.debugName)\n;for(var t=0,e=this._subComponents;t<e.length;t++){\nvar n=e[t];n&&n._triggerOnMounted()}\nthis.onMounted&&this.onMounted()},\nt.prototype._getMethod=function(t){var e=this[t]\n;return e instanceof Function?e:null},\nt.prototype._acquireComponentJson=function(){\nvar t=this.debugName+'#acCJ',\ne=w.ComponentRegistry.getComponentJson(this._componentName)\n;return e?(C.logger.i(t,'I0107'),e\n):void 0!==this._componentJson?(C.logger.i(t,'I0108'),\nw.ComponentRegistry.putComponentJson(this._componentName,\nthis._componentJson),this._componentJson):(C.logger.e(t,\n'E0109'),null)},t.prototype._acquireRootView=function(){\nvar t=this.debugName+'#acRV';if(this._hasRootViewBuilt\n)return C.logger.i(t,'I0110'),this._rootView\n;var e=this._acquireComponentJson();return e?(\nthis._rootView=this._convertJsonToBNNode(e,\nthis.vueModel.data||{}),this._hasRootViewBuilt=!0,\nC.logger.i(t,'I0112'),this._rootView):(C.logger.e(t,'E0111')\n,null)},t.prototype._genArrayChildNode=function(t,e,n,i,o){\nvar r=A.vueUtils.item2ArrayIndex(o,e),\nc=this._convertJsonToBNNode(t,k({},e,{item:n,index:i,\narrayName:r}));return c?(c.setAttribute('index',i),\nc.setAttribute('for_name',o),c):null},\nt.prototype._convertJsonToBNNode=function(t,u){var l=this,\ne=this.debugName+'#cJTB';if(void 0===t._t)return null\n;var d=document.createElement(t._t),p=[];if(void 0!==t._cd\n)for(var n=function(c){if(c['v-for']||c['v-for-cal']){\nvar t=!c['v-for']&&!!c['v-for-cal'],e=(\nt?f.vueModel.compute:u)||{},n=A.vueUtils.getObject(\nt?c['v-for-cal']:c['v-for'],e,t),a=t?A.vueUtils.rmSymbol(\nc['v-for-cal']):A.vueUtils.rmSymbol(c['v-for']);if(!a||!n\n)return'continue';for(var i in n)if(n.hasOwnProperty(i)){\nvar o=f._genArrayChildNode(c,u,n[i],i,a);o&&p.push(o)}\nvar s=document.createElement('div');s&&(\ns.style.display='none',s.setAttribute('for_end',a),p.push(s)\n,new A.Watcher(a,e,function(t){if(d){A.rmWatchers(a);for(\nvar e=0,n=d.childNodes;e<n.length;e++){var i=n[e]\n;i.getAttribute('for_name')===a&&d.removeChild(i)}if(t)for(\nvar o in t)if(t.hasOwnProperty(o)){\nvar r=l._genArrayChildNode(c,u,t[o],o,a);r&&d.insertBefore(r\n,s)}}},t).id=u.arrayName)}else{var r=f._convertJsonToBNNode(\nc,u);if(!r)return'continue';p.push(r)}},f=this,i=0,\no=t._cd;i<o.length;i++)n(o[i]);if(!d)return null\n;u&&u.index&&d.setAttribute('index',u.index)\n;var r=t['bn-component']||t['sp-component'];if(r){\nC.logger.i(e,'I0113 '+r)\n;var c=w.ComponentRegistry.createComponent(r);if(!c\n)return C.logger.e(e,'E0114 '+r+', '+c),null\n;var a=t['bn-component-id']||t['sp-component-id']\n;return a&&(c._componentId=a),c.onCreated&&c.onCreated(),\nC.logger.i(e,'I0115 '+c.debugName+', '+a),\nthis._subComponents.push(c),a&&!this._subComponentsMap[a]&&(\nthis._subComponentsMap[a]=c),c._acquireRootView()}\nvar s=t['bn-view-id']||t['sp-view-id'];for(var m in s&&(\nC.logger.i(e,'I0116 '+s),this._viewsIdMap[s]||(\nthis._viewsIdMap[s]=d)),t._i&&(d.id=t._i),t._c&&(\nd.className=t._c),t._s&&(d.style.cssText=t._s),t._x&&(\nd.innerText=t._x),t._y&&(d.type=t._y),t)if(t.hasOwnProperty(\nm))if(0===m.indexOf('on')){var h=this._getMethod(t[m]);h&&(\nd[m]=h.bind(this,d))}else if(0===m.indexOf('_'));else if(\n0===m.indexOf('bn-')||0===m.indexOf('sp-'));else if(\nL.startsWith(m,'v-')){var _=m.split('-');if(\n2===_.length||3===_.length){var g=_[1]\n;2===_.length?new A.NodeCompile(u).compile(g,d,t[m],t._t\n):'cal'===_[2]&&(u.arrayName&&u.index?new A.NodeCompile(k({}\n,this.vueModel.compute,{arrayName:u.arrayName,index:u.index}\n),!0).compile(g,d,t[m],t._t):new A.NodeCompile(\nthis.vueModel.compute,!0).compile(g,d,t[m],t._t))\n}else d[m]=t[m]}else d[m]=t[m];for(var v=0,\nb=p;v<b.length;v++){var y=b[v];d.appendChild(y)}return d},\nt.componentName='',t.componentHTML='',t.componentCSS='',\nt.componentHashName='',t}();e.BNComponent=i},function(module\n,exports,__webpack_require__){'use strict'\n;Object.defineProperty(exports,'__esModule',{value:!0})\n;var util_1=__webpack_require__(3),\namc_types_1=__webpack_require__(0),watchers;function notify(\n){watchers&&watchers.forEach(function(t){t.update()})}\nfunction rmWatchers(e){watchers=watchers.filter(function(t){\nreturn t.id!==e})}exports.rmWatchers=rmWatchers\n;var Watcher=function(){function t(t,e,n,i){if(this.id='',\nthis.lazy=!1,e&&'object'==typeof e){if(this.lazy=i,\nthis.callback=n,util_1.startsWith(t,'item'\n)&&e.arrayName&&e.index){var o=t.replace('item','')\n;this.expression=e.arrayName+'.'+e.index,o&&(\nthis.expression+=o)}else this.expression=t;this.data=e,\nthis.value=vueUtils.getVal(t,e,this.lazy),watchers||(\nwatchers=[]),watchers.push(this)}}\nreturn t.prototype.update=function(){if(\nthis.data&&this.expression&&this.callback){\nvar t=vueUtils.getVal(this.expression,this.data,this.lazy),\ne=this.value;vueUtils.equals(t,e)||(this.value=t,\nthis.callback(t))}},t}();exports.Watcher=Watcher\n;var Observer=function(){function t(t){this.observe(t)}\nreturn t.prototype.observe=function(e){var n=this\n;e&&'object'==typeof e&&Object.keys(e).forEach(function(t){\ntry{n.defineReactive(e,t,e[t]),n.observe(e[t])}catch(t){}})}\n,t.prototype.defineReactive=function(t,e,n){var i=this\n;Object.defineProperty(t,e,{enumerable:!0,configurable:!1,\nget:function(){return n},set:function(t){vueUtils.equals(t,n\n)||(n=t,i.observe(t),notify())}})},t}()\n;exports.Observer=Observer;var NodeCompile=function(){\nfunction t(t,e){void 0===e&&(e=!1),this.data=t||{},\nthis.lazy=e}return t.prototype.compile=function(n,t,e,i){\nvar o=this;if(t)switch(n){case'text':this.labelProcess(t,e,\nfunction(t,e){t.innerText=void 0===e?'':e});break\n;case'html':this.labelProcess(t,e,function(t,e){\nt.innerHtml=void 0===e?'':e});break;case'class':\nthis.labelProcess(t,e,function(t,e){var n=t.className,\ni=t.getAttribute('v-class-name')||'';n=n&&n.replace(i,''\n).replace(/\\s+$/,''),t.setAttribute('v-class-name',e),\nt.className=n?n+' '+e:e});break;case'style':\nthis.eventProcess(t,e,function(t,e){\nvar n=util_1.tryJSONParse(e);util_1.copyObj(n,t.style)})\n;break;case'model':this.eventProcess(t,e,function(t,e){\nt.value=e}),'input'===i?t.oninput=function(){\nvueUtils.setTextVal(e,t.value,o.data)}:'switch'===i&&(\nt.onchange=function(t){vueUtils.setTextVal(e,t||'off',o.data\n)});break;case'if':this.eventProcess(t,e,function(t,e){\n!0===e?(t.style.display='flex',spmUtils.process(t,function(t\n){amc_types_1.amc.fn.spmExposure(t.spmId,t.param4Map,\nt.doNotResume)})):t.style.display='none'});break;case'spm':\nthis.labelProcess(t,e,function(t,e){t.setAttribute('spm',\nvoid 0===e?'':e)});break;case'uep':this.labelProcess(t,e,\nfunction(t,e){e&&util_1.startsWith(e,'a283'\n)&&t.setAttribute('behaviorInfo',JSON.stringify({spm:e,\nbizCode:'pay',extParam:{}}))});break;case'click':\nthis.eventProcess(t,e,function(t,e){vueUtils.isFunction(e\n)?t.onclick=function(){e(t),spmUtils.process(t,function(t){\namc_types_1.amc.fn.spmClick(t.spmId,t.param4Map)})\n}:t.onclick=function(){}});break;case'focus':\nthis.eventProcess(t,e,function(t,e){vueUtils.isFunction(e\n)?t.onfocus=function(){e(t)}:t.onfocus=function(){}});break\n;case'blur':this.eventProcess(t,e,function(t,e){\nvueUtils.isFunction(e)?t.onblur=function(){e(t)\n}:t.onfocus=function(){}});break;default:-1===e.indexOf('@{'\n)?t.setAttribute(n,e):this.labelProcess(t,e,function(t,e){\nt.setAttribute(n,void 0===e?'':e)})}},\nt.prototype.labelProcess=function(n,i,o){var r=this,\nt=i.match(/@\\{([^}]+)\\}/g),e=i;t&&0<t.length&&(\ne=vueUtils.getTextVal(i,this.data,this.lazy),t&&t.forEach(\nfunction(t){var e=/@\\{([^}]+)\\}/g.exec(t);e&&1<e.length&&(\nnew Watcher(e[1],r.data,function(t){o(n,vueUtils.getTextVal(\ni,r.data,r.lazy))},r.lazy).id=r.data.arrayName)})),o(n,e)},\nt.prototype.eventProcess=function(e,t,n){\nvar i=/@\\{([^}]+)\\}/g.exec(t),o=vueUtils.getObject(t,\nthis.data,this.lazy);i&&1<i.length&&(new Watcher(i[1],\nthis.data,function(t){n(e,t)},this.lazy\n).id=this.data.arrayName),n(e,o)},t}()\n;exports.NodeCompile=NodeCompile;var spmUtils=function(){\nfunction t(){}return t.process=function(t,e){\nvar n=t.getAttribute('spm');if(n)try{var i=JSON.parse(n)\n;i&&i.spmId&&e(i)}catch(t){}},t}(),vueUtils=function(){\nfunction vueUtils(){}\nreturn vueUtils.item2ArrayIndex=function(t,e){var n=t;if(\nutil_1.startsWith(t,'item')&&e.arrayName&&e.index){\nvar i=t.replace('item','');n=e.arrayName+'.'+e.index,i&&(\nn+=i)}return n},vueUtils.getVal=function(expr,data,lazy){if(\nexpr){var values=expr.match(/##([^#]+)##/g);if(\nvalues&&0<values.length){for(var func_1=expr,\nindex=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(\n/##([^#]+)##/,'vueArgs['+index+']'),index++;for(\nvar _vueArgs=[],i=0;i<index;i++)_vueArgs.push(\nthis.getRealVal(values[i].replace(/##/g,''),data,lazy))\n;return function(vueArgs){return eval(func_1)}(_vueArgs)}\nreturn this.getRealVal(expr,data,lazy)}},\nvueUtils.getRealVal=function(t,e,n){if(t){var i=t.split('.')\n,o=n&&vueUtils.isFunction(e[i[0]])?e[i[0]]():e;return(\nn?i.slice(1):i).reduce(function(t,e){return t[e]},o)}},\nvueUtils.getTextVal=function(t,o,r){var c=this\n;return t.replace(/@\\{([^}]+)\\}/g,function(){for(var t,e=[],\nn=0;n<arguments.length;n++)e[n]=arguments[n]\n;var i=vueUtils.item2ArrayIndex(e[1],o);return void 0===(\nt=c.getVal(i,o,r))?'':t})},vueUtils.getObject=function(t,e,n\n){var i=/@\\{([^}]+)\\}/g.exec(t);if(i&&1<i.length\n)return this.getVal(i[1],e,n)},vueUtils.rmSymbol=function(t\n){var e=/@\\{([^}]+)\\}/g.exec(t);return e&&1<e.length?e[1]:''\n},vueUtils.setVal=function(t,i,e){var o=t.split('.')\n;return o.reduce(function(t,e,n){\nreturn n===o.length-1?t[e]=i:t[e]},e)},\nvueUtils.setTextVal=function(t,e,n){\nvar i=/@\\{([^}]+)\\}/g.exec(t);i&&1<i.length&&this.setVal(\ni[1],e,n)},vueUtils.equals=function(t,e){return this.eq(t,e,\nvoid 0,void 0)},vueUtils.eq=function(t,e,n,i){if(t===e\n)return 0!==t||1/t==1/e;if(null==t||null==e)return t===e\n;var o=toString.call(t);if(o!==toString.call(e))return!1\n;switch(o){case'[object RegExp]':case'[object String]':\nreturn''+t==''+e;case'[object Number]':\nreturn+t!=+t?+e!=+e:0==+t?1/+t==1/e:+t==+e\n;case'[object Date]':case'[object Boolean]':return+t==+e}\nvar r='[object Array]'===o;if(!r){if(\n'object'!=typeof t||'object'!=typeof e)return!1\n;var c=t.constructor,a=e.constructor;if(c!==a&&!(\nthis.isFunction(c)&&c instanceof c&&this.isFunction(a\n)&&a instanceof a)&&'constructor'in t&&'constructor'in e\n)return!1}i=i||[];for(var s=(n=n||[]).length;s--;)if(\nn[s]===t)return i[s]===e;if(n.push(t),i.push(e),r){if((\ns=t.length)!==e.length)return!1;for(;s--;)if(!this.eq(t[s],\ne[s],n,i))return!1}else{var u=Object.keys(t),l=void 0;if(\ns=u.length,Object.keys(e).length!==s)return!1;for(;s--;)if(\nl=u[s],!e.hasOwnProperty(l)||!this.eq(t[l],e[l],n,i)\n)return!1}return n.pop(),i.pop(),!0},\nvueUtils.isFunction=function(t){\nreturn'function'==typeof t||!1},vueUtils}()\n;exports.vueUtils=vueUtils},function(t,e,n){'use strict'\n;var i,o=this&&this.__extends||(i=function(t,e){return(\ni=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var r=function(){function r(){\nthis.enabled=!0}return r.fmtLine=function(t,e,n,i){var o=''\n;return i&&(\no=i instanceof Error?'- '+i.name+': '+i.message+' - '+i.stack:'- '+i\n),'['+t+']['+r.fmtTime()+']['+e+']'+n+' '+o},\nr.fmtTime=function(){var t=new Date;return t.getHours(\n)+':'+t.getMinutes()+':'+t.getSeconds(\n)+'.'+t.getMilliseconds()},r.prototype.enable=function(){\nthis.enabled=!0},r.prototype.disable=function(){\nthis.enabled=!1},r}();e.Logger=r,e.logger=new(function(t){\nfunction e(){return null!==t&&t.apply(this,arguments)||this}\nreturn o(e,t),e.prototype.e=function(t,e,n){},\ne.prototype.i=function(t,e,n){},e}(r))},function(t,e,n){\n'use strict';var i,o=this&&this.__extends||(i=function(t,e){\nreturn(i=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(t,e){t.__proto__=e}||function(t,\ne){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},\nfunction(t,e){function n(){this.constructor=t}i(t,e),\nt.prototype=null===e?Object.create(e):(\nn.prototype=e.prototype,new n)});Object.defineProperty(e,\n'__esModule',{value:!0});var r=n(1),c=n(0),a=n(2),\ns=function(e){function t(t){var i=e.call(this)||this\n;return i.onMounted=function(){},i.onCreated=function(){},\ni.vueModel={data:{selectBank:function(t){if(\ni.bankClickListener){var e=t.getAttribute('index')\n;i.bankClickListener(function(t,e){void 0===e&&(e=0);try{\nvar n=parseInt(t,10);return isNaN(n)?e:n}catch(t){return e}\n}(e||''))}},bankList:[]},compute:{}},i.fullBankList=[],\ni.defaultShowNum=0,i.supportNum=0,i.headerTipDesc='',\ni.viewMore='',i.bankListStatusListener=function(){},\ni.setBankListStatusListener=function(t){\ni.bankListStatusListener=t},i.bankClickListener=function(){}\n,i.selectMore=function(){if(c.amc.fn.spmClick(\ni.props.spm.more,i.props.spmServerParam),\ni.fullBankList.length>i.defaultShowNum)if(\ni.fullBankList.length<i.supportNum)if(\ni.vueModel.data.bankList.length===i.fullBankList.length){\nvar t={name:i.props.signAction};document.submit({action:t})\n}else i.vueModel.data.bankList=i.fullBankList,\na.visibleElement(i,'card-more-tail-img',!1),\ni.bankListStatusListener(!1);else a.visibleElement(i,\n'card-more-tail-img',!0),\ni.vueModel.data.bankList.length===i.fullBankList.length?(\ni.vueModel.data.bankList=i.fullBankList.slice(0,\ni.defaultShowNum)||[],a.modifyElementAttribute(i,\n'card-more-content',{innerText:'{{more}}'}),\na.modifyElementAttribute(i,'card-more-tail-img',{\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*wi0wQI-0ODcAAAAAAAAAAAAAARQnAQ'\n}),i.bankListStatusListener(!0)):(\ni.vueModel.data.bankList=i.fullBankList,\na.modifyElementAttribute(i,'card-more-content',{\ninnerText:'{{fold}}'}),a.modifyElementAttribute(i,\n'card-more-tail-img',{\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*C_R-RI4Srf4AAAAAAAAAAAAAARQnAQ'\n}),i.bankListStatusListener(!1));else t={\nname:i.props.signAction},document.submit({action:t})},\ni.setBankClickListener=function(t){\nreturn i.bankClickListener=t,i},i.setHeaderTipDesc=function(\nt){return a.modifyElementAttribute(i,\n'card-list-box-header-tip-2',{innerText:t}),\ni.headerTipDesc=t,i},i.setHeaderTipTitle=function(t){\nreturn a.modifyElementAttribute(i,\n'card-list-box-header-tip-1',{innerText:t}),i},\ni.setViewMore=function(t){t?(c.amc.fn.spmExposure(\ni.props.spm.more,i.props.spmServerParam),a.visibleElement(i,\n'more-box',!0),i.viewMore=t):a.visibleElement(i,'more-box',\n!1)},i.setBankList=function(t,e,n){\nreturn i.fullBankList=t||[],i.defaultShowNum=e||7,\ni.supportNum=n||i.fullBankList.length,i.setHeaderTipDesc(\ni.headerTipDesc),i.vueModel.data.bankList=t.slice(0,\ni.defaultShowNum)||[],\nt.length<=i.defaultShowNum?a.visibleElement(i,'more-box',!1\n):i.viewMore&&(c.amc.fn.spmExposure(i.props.spm.more,\ni.props.spmServerParam),a.visibleElement(i,'more-box',!0)),\nt.forEach(function(t,e){c.amc.fn.spmExposure(\ni.props.spm.bankItem+'_'+e,{instId:t.instId})}),i},\ni.visible=function(t){\n0<i.fullBankList.length&&a.visibleElement(i,'card-list',t)},\ni.visibleHeader=function(t){a.visibleElement(i,\n'card-list-box-header',t),a.visibleElement(i,\n'card-list-divide',t)},i.props=t,i}return o(t,e),\nt.getComponentCSSRules=function(){return{\n'.card-list-box':'_QuickBindList_y1d4-c-card-list-box',\n'.card-list-box-header':'_QuickBindList_y1d4-c-card-list-box-header',\n'.card-list-box-header-tip-1':'_QuickBindList_y1d4-c-card-list-box-header-tip-1',\n'.card-list-box-header-tip-2':'_QuickBindList_y1d4-c-card-list-box-header-tip-2',\n'.card-list-divide':'_QuickBindList_y1d4-c-card-list-divide',\n'.card-list-divide-mt':'_QuickBindList_y1d4-c-card-list-divide-mt',\n'.card-list-divide-ml':'_QuickBindList_y1d4-c-card-list-divide-ml',\n'.card-list-item':'_QuickBindList_y1d4-c-card-list-item',\n'.card-list-item-logo':'_QuickBindList_y1d4-c-card-list-item-logo',\n'.card-list-item-content':'_QuickBindList_y1d4-c-card-list-item-content',\n'.card-list-item-content-title':'_QuickBindList_y1d4-c-card-list-item-content-title',\n'.card-list-item-content-desc':'_QuickBindList_y1d4-c-card-list-item-content-desc',\n'.card-list-item-tail':'_QuickBindList_y1d4-c-card-list-item-tail',\n'.card-more-content':'_QuickBindList_y1d4-c-card-more-content',\n'.card-item-container':'_QuickBindList_y1d4-c-card-item-container',\n'.card-list-item-content-labels-container':'_QuickBindList_y1d4-c-card-list-item-content-labels-container',\n'.card-list-item-content-label':'_QuickBindList_y1d4-c-card-list-item-content-label',\n'.more-box':'_QuickBindList_y1d4-c-more-box'}},\nt.getComponentJson=function(){return{\n'sp-view-id':'card-list',\n'v-if':'@{!!(##bankList##.length)}',\n_c:'_QuickBindList_y1d4-c-card-list-box amc-v-box amc-align-center amc-self-stretch',\n_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-box-header',\n'sp-view-id':'card-list-box-header',_t:'div',_cd:[{\n'sp-view-id':'card-list-box-header-tip-1',\n_c:'_QuickBindList_y1d4-c-card-list-box-header-tip-1',\n_t:'label',_x:'{{bind_card_tip}}'},{\n'sp-view-id':'card-list-box-header-tip-2',\n_c:'_QuickBindList_y1d4-c-card-list-box-header-tip-2',\n_t:'label'}]},{'sp-view-id':'card-list-divide',\n_c:'_QuickBindList_y1d4-c-card-list-divide amc-self-stretch',\n_t:'div'},{\n_c:'amc-v-box amc-self-stretch _QuickBindList_y1d4-c-card-item-container',\n'v-for':'@{bankList}',_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item amc-align-center amc-self-stretch',\n'v-click':'@{selectBank}',_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item-logo',\n'v-src':'@{##item.logoUrl##||\\'\\'}',_t:'img'},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content amc-align-center amc-flex-1',\n_t:'div',_cd:[{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-title',\n_t:'label','v-text':'@{item.instName}'},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-desc',\n'v-if':'@{!##item.instActivityTags##}',_t:'label',\n'v-text':'@{##item.instActivityText##||\\'\\'}'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-item-tail',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*ZG6bSoBQYpAAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-item-content-labels-container amc-align-center',\n'v-if':'@{##item.instActivityTags## && ##item.instActivityTags##.length > 0}',\n_t:'div',_cd:[{'v-for':'@{item.instActivityTags}',\n_c:'_QuickBindList_y1d4-c-card-list-item-content-label amc-text-center',\n_t:'label','v-text':'@{item}'}]},{\n_c:'_QuickBindList_y1d4-c-card-list-divide _QuickBindList_y1d4-c-card-list-divide-ml _QuickBindList_y1d4-c-card-list-divide-mt',\n_t:'div'}]},{'sp-view-id':'more-box',\n_c:'_QuickBindList_y1d4-c-card-list-item _QuickBindList_y1d4-c-card-item-container _QuickBindList_y1d4-c-more-box amc-align-center amc-justify-center amc-self-stretch',\nonclick:'selectMore',_t:'div',_cd:[{\n'sp-view-id':'card-more-content',\n_c:'_QuickBindList_y1d4-c-card-more-content',_t:'label',\n_x:'{{more}}'},{'sp-view-id':'card-more-tail-img',\n_c:'_QuickBindList_y1d4-c-card-list-item-tail',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*wi0wQI-0ODcAAAAAAAAAAAAAARQnAQ',\n_t:'img'}]}]}},t.componentName='QuickBindList',\nt.componentHashName='QuickBindList_y1d4',t}(r.BNComponent)\n;e.QuickBindList=s},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var i=n(5)\n;e.mergeObject=i.mergeObject,e.isFunction=i.isFunction,\ne.isPreRender=i.isPreRender,e.copyObj=i.copyObj,\ne.doNothing=i.doNothing,e.tryJSONParse=i.tryJSONParse,\ne.checkEmptyObj=i.checkEmptyObj,\ne.substrWithFontWidth=i.substrWithFontWidth,\ne.calculateFontWidth=i.calculateFontWidth,\ne.deepCopy=i.deepCopy,e.getConfig=i.getConfig,\ne.showLoading=i.showLoading,e.hideLoading=i.hideLoading\n;var o=n(12)\n;e.VI_CHANNEL_MODE_FROM_TEMPLATE=o.VI_CHANNEL_MODE_FROM_TEMPLATE\n,e.SCALE_FACTOR=o.SCALE_FACTOR;var r=n(2)\n;e.modifyElementStyle=r.modifyElementStyle,\ne.modifyElementAttribute=r.modifyElementAttribute,\ne.modifyElementClass=r.modifyElementClass,\ne.visibleElement=r.visibleElement,\ne.modifyElementCSS=r.modifyElementCSS,\ne.createEmbedViPlugin=r.createEmbedViPlugin,\ne.getThemeColor=r.getThemeColor,\ne.createEmbedPlugin=r.createEmbedPlugin;var c=n(13)\n;e.logAction=c.logAction;var a=n(14);e.stEscape=a.stEscape,\ne.toString=a.toString,e.STPerf=a.STPerf,e.STAct=a.STAct,\ne.STRecord=a.STRecord;var s=n(15)\n;e.ImageLoader=s.ImageLoader;var u=n(16);e.ocr=u.ocr},\nfunction(t,e,n){'use strict';var i;Object.defineProperty(e,\n'__esModule',{value:!0}),e.VI_CHANNEL_MODE_FROM_TEMPLATE='1'\n,(i=e.SCALE_FACTOR||(e.SCALE_FACTOR={})).LEVEL_0='0',\ni.LEVEL_1='1',i.LEVEL_2='2',i.LEVEL_3='3',i.LEVEL_4='4'},\nfunction(t,e,n){'use strict';Object.defineProperty(e,\n'__esModule',{value:!0});var i=n(0);e.logAction=function(t,e\n){window.pageId||(window.pageId='|'+Math.random().toString(\n36).substr(2,3)),e=e?e+window.pageId:window.pageId,\ni.amc.fn.logAction(t,e)}},function(t,e,n){'use strict'\n;function i(t){for(var e=[],n=0;n<t.length;n+=1)e.push(\nt[n]||'-');return e.join('\\'')}Object.defineProperty(e,\n'__esModule',{value:!0}),e.stEscape=function(t){\nreturn t&&t.replace('\\'','%27').replace('`','%60').replace(\n'#','%23')},e.toString=i;var o=function(){function t(t,e,n){\nthis.prefs=[],this.initTime=Date.now(),this.cache={},\nthis.submited=!1,this.record=t,this.prefs[0]=e,\nthis.prefs[1]=n,this.prefs[2]=String(this.initTime),\nthis.prefs[7]='',this.record.addSTPref(this)}\nreturn t.prototype.toString=function(){return i(this.prefs)}\n,t.prototype.putCache=function(t,e){this.cache[t]=e},\nt.prototype.getCache=function(t,e){return this.cache[t]||e},\nt.prototype.isSubmited=function(){return this.submited},\nt.prototype.submit=function(){this.isSubmited()||(\nthis.submited=!0,this.submitInner())},\nt.prototype.submitInner=function(){},t}();e.STPerf=o\n;var r=function(){function t(t,e,n){this.acts=[],\nthis.record=t,this.acts[0]=e,this.acts[1]=n,\nthis.acts[6]=String(Date.now()),this.record.addSTAct(this)}\nreturn t.prototype.setActName=function(t){this.acts[2]=t},\nt.prototype.toString=function(){return i(this.acts)},t}()\n;e.STAct=r;var c=function(){function t(t,e,n,i,o,r){\nthis.ids=[],this.prefs=[],this.acts=[],\nthis.initTime=Date.now(),this.ids[0]=String(this.initTime),\nthis.ids[1]=t,this.ids[2]=e,this.ids[3]=n,this.ids[4]=i,\nthis.ids[5]=o,this.ids[7]='',this.logHandle=r}\nreturn t.prototype.addSTAct=function(t){this.acts.push(t)},\nt.prototype.addSTPref=function(t){this.prefs.push(t)},\nt.prototype.submit=function(t){var e=this.toString(t)\n;this.logHandle(e)},t.prototype.toString=function(e){for(\nvar t=[],n=0;n<this.ids.length;n+=1)t.push(this.ids[n]||'-')\n;var i=[];for(n=0;n<this.acts.length;n+=1)i.push(\nthis.acts[n].toString());this.acts=[];var o=[]\n;return this.prefs=this.prefs.filter(function(t){\nreturn e&&t.submit(),!t.isSubmited()||(o.push(t.toString()),\n!1)}),t.join('\\'')+'#'+i.join('`')+'#'+o.join('`')},\nt.prototype.getInitTime=function(){return this.initTime},t}(\n);e.STRecord=c},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var a=n(0)\n,i=function(){function t(){this.mUrlCache=null,\nthis.mPixelWidthCache={}}return t.getInstance=function(){\nreturn t.sImageLoader||(t.sImageLoader=new t),t.sImageLoader\n},t.prototype.loadImageHelper=function(t,e,n){var r=this;if(\n!this.mUrlCache){this.mUrlCache={}\n;var c=document.onImgLoaded;document.onImgLoaded=function(t,\ne){var n=r.mUrlCache[e];if(!n){var i='';for(\nvar o in r.mPixelWidthCache)if(\nr.mPixelWidthCache.hasOwnProperty(o)&&0===e.indexOf(o)){i=o,\nn=r.mPixelWidthCache[o];break}n&&!n.validate&&(\ndelete r.mPixelWidthCache[i],n=null)}n&&n.callback?(\nn.validate=!1,n.callback(t,e,n.img)):c&&c(t,e),\ndelete r.mUrlCache[e]}}var i={callback:n,img:t,validate:!0}\n;if(this.mUrlCache[e]=i,0<e.indexOf('[pixelWidth]')){\nvar o=e.substr(0,e.indexOf('[pixelWidth]'))\n;this.mPixelWidthCache[o]=i}t.src=e},\nt.prototype.loadImage=function(i,t,o,r){void 0===r&&(r=!0)\n;var c=!1,e=a.amc.isSDK&&a.amc.isIOS,n=function(t,e,n){\nt&&i&&r&&a.amc.fn.show(i),c=t,o&&o(t,e,i)}\n;this.loadImageHelper(i,t,e?void 0:n),e&&n(!0,t),\nr&&setTimeout(function(){c||a.amc.fn.hide(i)},10)},t}()\n;e.ImageLoader=i},function(t,e,n){'use strict'\n;Object.defineProperty(e,'__esModule',{value:!0});var c=n(0)\n;e.ocr=function(i,o,r,t){if(void 0===r&&(r=!0),void 0===t&&(\nt=500),\ni&&c.amc.isAndroid&&!c.amc.isSDK&&c.amc.fn.sdkGreaterThanOrEqual(\n'10.8.53')){var e=(new Date).getUTCMilliseconds()\n;i.className+=' '+e,setTimeout(function(){document.invoke(\n'ocr',{selector:'.'+e},function(t){if(\nt&&t.data&&t.data[0]&&t.data[0].body){var e=''\n;t.data[0].body.forEach(function(t){t.label&&(e+=t.label)})\n;var n=i.innerText.replace(/<[^>]+>([^<]+)<\\/\\w+>/g,'$1'\n).split('，').join(',').split('？').join('?').split('！').join(\n'!').split('：').join(':').split('“').join('\"').split('”'\n).join('\"');e!==n&&r&&c.amc.fn.logError(\n'render_label_exception',e),o&&o({result:e,pureInnerText:n})\n}})},t)}}}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body amc-bg-white", "tag": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-card-select-bank-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0121", "tplId": "QUICKPAY@cashier-card-select-bank-flex", "tplVersion": "5.4.6"}