{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "// 1. 别名\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var getTag = amc.fn.getById;\n        var rpc = amc.rpcData;\n        var create = amc.fn.create;\n\n        // 2. 宏定义\n        var CHECK_NORMAL = amc.path + 'alipay_msp_check';\n        var CHECK_SELECTED = amc.path + 'alipay_msp_checked';\n\n        //  图片题，未选中状态CSS\n        var CSS_NORMAL = 'item-margin-l item-normal';\n        //  图片题，选中状态CSS\n        var CSS_SELECTED = 'item-margin-l item-checked';\n\n        //  题目的类型\n        var DISPLAY_TYPE_TEXT = 'TEXT'; // 1）纯文字题目\n        var DISPLAY_TYPE_PIC = 'URL'; // 2）纯图片\n        var DISPLAY_TYPE_PIC_TEXT = 'TEXTURL'; // 3）图片加文字\n        var DISPLAY_TYPE_TEXT_ICON = 'TEXTICON'; // 3）图片加文字\n        var ITEMS_PER_LINE = 3; // 图片题每行选项个数\n\n        // 3. 全局变量\n        var answers = []; // 用户提交的所有题目的答案\n        var questions = rpc.data['privacyQuestionQueryView']; // 所有问卷\n        var currentQuestion = {}; //当前问卷\n        var correctNum = 1; // 正确答案的个数\n        var isTextIcon = false; \n        var isText = false; // 标识是否是纯文本题目\n        //  存放所有选项 {'item': 选项节点, 'checkbox': checkbox节点, 'checked': false, 'disabled': false, 'value': value}\n        var gItems = [];\n        var pageNo = 0; // 当前问卷页码号\n        var imgSize = 83;\n\n        function init() {\n            try {\n                initUI();\n\n                if (amc.fn.logPageInit) {\n                    amc.fn.logPageInit(true);\n                }\n            } catch (e) {\n                if (amc.fn.logPageInit) {\n                    amc.fn.logPageInit();\n                }\n            }\n        }\n\n        function initUI() {\n            var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', '{{id_validate}}', null, null, onBack, null);\n            getTag('bodyContainer').insertBefore(nav, getTag('mainBody'));\n            // Android平台不支持给菊花设置颜色，需要使用白色图片资源替代\n            if (amc.isAndroid) {\n                getTag('loading').src = amc.path + 'alipay_msp_indicator_white_loading';\n            }\n\n            var temp = (window.innerWidth - (amc.isPlus ? 100 : 80)) / 3;\n            // 防止计算错误\n            if (temp > 60) {\n                imgSize = (temp > 200 ? 200 : temp);\n            }\n\n            if (true !== Boolean(rpc.HAS_OTHERS)) {\n                var parentNode = document.getElementById('mainBody');\n                var othersDiv = document.getElementById('others');\n                parentNode.removeChild(othersDiv);\n            }\n\n            //初始化问卷\n            initQuestion();\n            \n            //底部留白\n            var parentNode = document.getElementById('mainBody');\n            if (amc.isIPhoneX) {\n                create('div', 'block-size-ios', parentNode);\n            }else{\n                create('div', 'block-size', parentNode);\n            }\n            \n        }\n\n        function initQuestion() {\n            getTag('submitBtn').disabled = true;\n            getTag('btnText').style.color = '#d2d2d2';\n            currentQuestion = questions && questions[pageNo];\n\n            if (currentQuestion) {\n                // 问题\n                getTag('question').innerText = currentQuestion['text'] || '';\n\n                if (currentQuestion.correctNum) {\n                    correctNum = currentQuestion.correctNum;\n                }\n\n                gItems = [];\n                // 构建选项\n                createItems(currentQuestion.options, currentQuestion['displayType']);\n            }\n            \n        }\n\n        /*\n         * 创建选项列表\n         * @param(options): 选项列表数据\n         */\n        function createItems(options, displayType) {\n            if (!options || !options.length) {\n                return;\n            }\n\n            //首先清除之前已经填充的内容\n            getTag('items').innerHTML = '';\n            getTag('mainBody').scrollTop = 0;\n\n            isText = !options[0]['displayUrl'];\n\n            if(displayType && displayType == DISPLAY_TYPE_TEXT_ICON){\n                isTextIcon = true;\n            }else {\n                isTextIcon = false;\n            }\n\n            // 1. 纯文字题目\n            if (isText || isTextIcon) {\n                var itemsContainer = getTag('items');\n                // 纯文字时背景是白色\n                itemsContainer.className = 'amc-v-box amc-bg-white amc-margin-t';\n\n                create('div', 'amc-1px-line', itemsContainer);\n                for (var i = 0; i < options.length; i++) {\n                    var item = createItem(options[i], itemsContainer, isText);\n                    if (item && i < options.length - 1) {\n                        var line = create('div', 'amc-1px-line amc-margin-l', itemsContainer);\n                    }\n                }\n                create('div', 'amc-1px-line', itemsContainer);\n            } else { // 2. 图片题\n                var itemsContainer; // 选项的一行\n                for (var i = 0; i < options.length; i++) {\n                    if (i % ITEMS_PER_LINE === 0 || !itemsContainer) {\n                        itemsContainer = create('div', 'item-margin-t amc-justify-center pd-r', getTag('items'));\n                    }\n                    var item = createItem(options[i], itemsContainer, isText);\n                }\n\n                // 没有满一行的用空白填充\n                var moreItems = (ITEMS_PER_LINE - options.length % ITEMS_PER_LINE) % ITEMS_PER_LINE;\n                for (var i = 0; i < moreItems; i++) {\n                    var box = create('div', 'item-margin-l', itemsContainer);\n                    setDimension(box, imgSize);\n                }\n            }\n        }\n\n        function setDimension(node, dimension) {\n            if (!node || !dimension) {\n                return;\n            }\n            var temp = dimension + 'px';\n            node.style.width = temp;\n            node.style.height = temp;\n\n            node.style.maxWidth = temp;\n            node.style.maxHeight = temp;\n        }\n\n        /*\n         *\n         * 创建选项(图片类型)\n         * @param(itemData): 选项的图片src及text\n         * @param(parent): 选项的父节点\n         * @param(isText): 选项的类型(纯文本/图片)\n         * @param(key): 选项的key值\n         * @return: 创建的节点,如果参数非法则返回undefined\n         *\n         */\n        function createItem(itemData, parent, isText) {\n            if (!itemData || !parent) {\n                return;\n            }\n            \n            var itemObj;\n            if(isTextIcon){\n                itemObj = createItemTextIcon(itemData, parent);\n            } else if(isText){\n                itemObj = createItemText(itemData, parent);\n            }else{\n                itemObj = createItemPic(itemData, parent);\n            }\n\n            //var createAnItem = isText ? createItemText : createItemPic;\n            //var itemObj = createAnItem(itemData, parent);\n            if (!itemObj) {\n                return;\n            }\n\n            var item = itemObj['item'];\n            var index = gItems.length;\n            item.onclick = function() {\n                toggle(index);\n            };\n\n            // 将所有选项添加到列表中\n            gItems[gItems.length] = itemObj;\n\n            return item;\n        }\n\n        /*\n         *\n         * 创建选项(图片类型)\n         * @param(itemData): 选项的图片src及text\n         * @param(parent): 选项的父节点\n         * @return: 创建的选项({'item': item, 'checkbox': checkImg, 'checked': false, 'disabled': false}),如果参数非法则返回undefined\n         *\n         */\n        function createItemPic(itemData, parent) {\n            if (!itemData['displayUrl'] || !parent) {\n                return;\n            }\n\n            var item = create('div', 'item-margin-l', parent);\n            setDimension(item, imgSize);\n\n            var img = create('img', 'amc-bg-white', item);\n            //var img = create('displayUrl', 'img-size amc-bg-white', item);\n            setDimension(img, imgSize);\n\n            var imgSrc = itemData['displayUrl'];\n\n            // 图片压缩\n            // 备注: 理论上图像应该为imgSize的2倍,为了压缩同时保证质量采用约1.5s\n            // 以下为真机测试经验值,以iPhone分辨率为例区别: 5、 6/6p、iPad\n            //            var imgCompress = '_100x100.jpg';\n            //            if (imgSize > 120) {\n            //                imgCompress = '_200x200.jpg';\n            //            } else if (imgSize > 90) {\n            //                imgCompress = '_140x140.jpg';\n            //            }\n            //\n            //            if ((imgSrc.indexOf('tfsimg.alipay.com') !== -1) || (imgSrc.indexOf('taobaocdn.com') !== -1)) {\n            //                 imgSrc += imgCompress;\n            //              }\n\n            img.src = imgSrc;\n\n            // 在图片上的浮层\n            var container = create('div', 'amc-v-box', item);\n            container.style.marginLeft = -imgSize + 'px';\n\n            setDimension(container, imgSize);\n\n            var checkImgBox = create('div', 'check-img-box amc-justify-end', container);\n            var checkImg = create('img', 'check-img amc-hidden', checkImgBox);\n            checkImg.src = CHECK_SELECTED;\n\n            if (itemData['displayText']) {\n                var space = create('div', 'amc-flex-1', container); // 填充，用于将文案置底\n                var grayBox = create('div', 'text-container amc-justify-center amc-align-center', container);\n                var labelBox = create('div', 'amc-justify-center amc-align-center label-box', container);\n                var label = create('label', 'text-on-img amc-text-center amc-ellipsis amc-flex-1', labelBox);\n                label.innerText = itemData['displayText'];\n            }\n            return {\n                'item': item,\n                'checkbox': checkImg,\n                'checked': false,\n                'disabled': false,\n                'value': itemData['value']\n            };\n        }\n\n        /*\n         *\n         * 创建选项(文字类型)\n         * @param(itemData): text\n         * @param(parent): 选项的父节点\n         * @param(key): 选项的key值\n         * @return: 创建的选项({'item': item, 'checkbox': checkImg, 'checked': false, 'disabled': false}),如果参数非法则返回undefined\n         *\n         */\n        function createItemText(itemData, parent) {\n            if (!itemData['displayText'] || !parent) {\n                return;\n            }\n\n            var item = create('div', 'amc-pd amc-align-center', parent);\n            var textBox = create('div', 'amc-flex-1', item);\n            var label = create('label', 'amc-text-dark-color amc-flex-1 amc-font-xl', textBox)\n            label.innerText = itemData['displayText'];\n\n            var checkImg = create('img', 'amc-margin-l check-img', item);\n            checkImg.src = CHECK_NORMAL;\n\n            return {\n                'item': item,\n                'checkbox': checkImg,\n                'checked': false,\n                'disabled': false,\n                'value': itemData['value']\n            };\n        }\n\n        /*\n         *\n         * 创建选项(文字类型)\n         * @param(itemData): text\n         * @param(parent): 选项的父节点\n         * @param(key): 选项的key值\n         * @return: 创建的选项({'item': item, 'checkbox': checkImg, 'checked': false, 'disabled': false}),如果参数非法则返回undefined\n         *\n         */\n        function createItemTextIcon(itemData, parent) {\n            if (!itemData['displayText'] || !parent) {\n                return;\n            }\n\n            var item = create('div', 'amc-pd amc-align-center', parent);\n            if (itemData['displayUrl']) {\n                var icons = create('img', 'icon_text', item);\n                icons.src = itemData['displayUrl'];\n            }\n            var textBox = create('div', 'amc-flex-1', item);\n            var label = create('label', 'amc-text-dark-color amc-flex-1 amc-font-xl', textBox)\n            label.innerText = itemData['displayText'];\n\n            var checkImg = create('img', 'amc-margin-l check-img', item);\n            checkImg.src = CHECK_NORMAL;\n\n            return {\n                'item': item,\n                'checkbox': checkImg,\n                'checked': false,\n                'disabled': false,\n                'value': itemData['value']\n            };\n        }\n\n        function onBack() {\n            mic.fn.onBack();\n        }\n\n        /*\n         * 更新一个图片选项的选中状态\n         * @param(item): 选项\n         * @param(checked): 选中状态\n         */\n        function updateCheckStatePic(item, checked) {\n            if (!item || !item['item'] || !item['checkbox']) {\n                return;\n            }\n\n            if (checked) {\n                item['item'].className = CSS_SELECTED;\n                show(item['checkbox']);\n            } else {\n                item['item'].className = CSS_NORMAL;\n                hide(item['checkbox']);\n            }\n\n            // 类型转换\n            item['checked'] = !!checked;\n        }\n\n        /*\n         * 更新一个纯文本选项的选中状态\n         * @param(item): 选项\n         * @param(checked): 选中状态\n         */\n        function updateCheckStateText(item, checked) {\n            if (!item || !item['item'] || !item['checkbox']) {\n                return;\n            }\n\n            item['checkbox'].src = checked ? CHECK_SELECTED : CHECK_NORMAL;\n            item['checked'] = !!checked;\n        }\n\n        /*\n         * 更新一个选项的disable状态\n         * @param(item): 选项\n         * @param(disabled): disabled状态\n         */\n        function updateDisableState(item, disabled) {\n            if (!item || !item['item'] || !item['checkbox']) {\n                return;\n            }\n\n            if (disabled) {\n                item['item'].style.opacity = 0.2;\n            } else {\n                item['item'].style.opacity = 1;\n            }\n\n            // 类型转换\n            item['disabled'] = !!disabled;\n        }\n\n        /*\n         * 将一个选项的选中状态置反\n         * @param(index): 选项的序号\n         */\n        function toggle(index) {\n\n            if (index !== undefined && gItems[index] && !gItems[index]['disabled']) {\n                var updateCheckState = (isText || isTextIcon) ? updateCheckStateText : updateCheckStatePic;\n                // 如果是单选，则取消所有已经选择的\n                if (correctNum === 1) {\n                    for (var i = 0; i < gItems.length; i++) {\n                        updateCheckState(gItems[i], false);\n                    }\n                }\n\n                updateCheckState(gItems[index], !gItems[index]['checked']);\n                updateSelectionState(gItems[index]['checked']);\n            }\n        }\n\n        /*\n         * 更新所有选项的状态以及提交按钮的状态\n         */\n        function updateSelectionState() {\n            var selectedCount = selectedOptions().length;\n            if (selectedCount >= correctNum) {\n                // 如果是多选题目，当选中项目等于最大值时，则disable其他选项\n                if (correctNum > 1) {\n                    for (var i = 0; i < gItems.length; i++) {\n                        if (!gItems[i]['checked']) {\n                            updateDisableState(gItems[i], true);\n                        }\n                    }\n                }\n\n                getTag('submitBtn').disabled = false;\n                getTag('btnText').style.color = '#fff';\n            } else {\n                for (var i = 0; i < gItems.length; i++) {\n                    updateDisableState(gItems[i], false);\n                }\n                getTag('submitBtn').disabled = true;\n                getTag('btnText').style.color = '#d2d2d2';\n            }\n        }\n\n        /*\n         * 返回用户选中的选项的值\n         * @return(数组，每个元素对应一个选中选项的key)\n         */\n        function selectedOptions() {\n\n            var result = [];\n            for (var i = 0; i < gItems.length; i++) {\n                if (gItems[i]['checked']) {\n                    result[result.length] = gItems[i]['value'];\n                }\n            }\n\n            return result;\n        }\n\n        function onSubmit() {\n            var multipleAnswer = selectedOptions();\n            var answer = {\n                'propName': currentQuestion.propName,\n                'questionId': currentQuestion.questionId,\n                'multipleAnswer': multipleAnswer\n            };\n            answers[answers.length] = answer;\n\n            // 1. 提交答案\n            if (pageNo >= questions.length - 1) {\n                // 设置用户提交的参数\n                obj = {};\n                obj['eventName'] = 'vi_rpc_validate';\n                obj['params'] = {\n                    'validateinfo': {\n                        'privacyQuestionAnswerViews': answers\n                    }\n                };\n\n                obj['moduleName'] = 'PRIVACY_SHIELD';\n                obj['actionName'] = 'VI_ACTION_SHIELD_COMMIT_ANSWERS';\n\n                amc.fn.shouldShowBtnIndicator({\n                    'pageloading': '1'\n                }, 'submitBtn', 'loadingBtn');\n                document.asyncSubmit(obj,\n                    function(data) {\n                        data['pageloading'] = '0';\n                        amc.fn.shouldShowBtnIndicator(data, 'submitBtn', 'loadingBtn');\n                        if (true === Boolean(data['verifySuccess'])) {\n                            if (data.verifyMessage) {\n                                document.toast({\n                                        text: data.verifyMessage,\n                                        type: 'success'\n                                    },\n                                    function() {\n                                        mic.fn.onBackWithResponse(data);\n                                    });\n                            } else {\n                                mic.fn.onBackWithResponse(data);\n                            }\n                        } else {\n                            var verifyMessage = data['verifyMessage'] || '人气太旺了，请稍后再试';\n                            if (data['verifyCode'] === 'RETRY') {\n                                amc.fn.viAlert({\n                                        \"title\": '',\n                                        \"message\": verifyMessage,\n                                        \"button\": '{{confirm_btn}}'\n                                    },\n                                    function() {\n                                        answerAgain(data);\n                                    });\n                            } else if (data['verifyCode'] === 'CLOSED') {\n                                amc.fn.viAlert({\n                                        \"title\": '',\n                                        \"message\": verifyMessage,\n                                        \"button\": '{{confirm_btn}}'\n                                    },\n                                    function() {\n                                        mic.fn.onBackWithResponse(data);\n                                    });\n                            } else {\n                                amc.fn.viAlert({\n                                    \"title\": '',\n                                    \"message\": verifyMessage,\n                                    \"button\": '{{confirm_btn}}'\n                                }, function() {\n                                    mic.fn.onBackWithResponse(data);\n                                });\n                            }\n\n                        }\n                    });\n            } else {\n                // 2. 进入下一页继续答题\n                //更换题目\n                pageNo++;\n\n                // 初始化问卷\n                initQuestion();\n\n            }\n        }\n\n        function answerAgain(data) {\n            //更新一批题库\n\n            rpc = JSON.parse(data['data']);\n\n            answers = []; // 用户提交的所有题目的答案\n            questions = rpc['data']['privacyQuestionQueryView']; // 所有问卷\n\n            currentQuestion = {}; //当前问卷\n            correctNum = 1; // 正确答案的个数\n            isText = false; // 标识是否是纯文本题目\n            isTextIcon = false;\n            gItems = [];\n            pageNo = 0; // 当前问卷页码号\n\n            initQuestion();\n    \n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                onBack();\n            }\n        }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".pd-r {\n            padding-right: 12px;\n        }\n\n        .label-box {\n            height: 20px;\n            max-height: 20px;\n        }\n\n        .text-on-img {\n            color: #fff;\n            font-weight: bold;\n        }\n\n        .img-size {\n            height: 83px;\n            width: 83px;\n        }\n\n        .item-checked {\n            border: 2px #1677ff;\n        }\n\n        .item-normal {\n            border: 0px;\n        }\n\n        .check-img-box {\n            padding: 5px;\n        }\n\n        .text-container {\n            opacity: 0.5;\n            background-color: #333;\n            height: 20px;\n            margin-bottom: -20px;\n        }\n\n        .check-img {\n            height: 17px;\n            width: 17px;\n        }\n\n        .icon_text {\n            height: 47px;\n            width: 47px;\n            margin-right: 15px;\n        }\n\n        .item-margin-l {\n            margin-left: 12px;\n        }\n\n        .item-margin-t {\n            margin-top: 12px;\n        }\n        \n        .block-size {\n            height: 55px;\n        }\n        \n        .block-size-ios {\n            height: 88px;\n        }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "amc-main amc-scroll-flex", "children": [{}, {"css": "amc-pd-lr", "children": [{"css": "amc-text-dark-color amc-font-m amc-flex-1", "tag": "label", "id": "question"}], "tag": "div", "id": "tipsBox"}, {}, {"css": "amc-v-box", "tag": "div", "id": "items"}, {}, {"css": "amc-pd-lr amc-margin-t", "children": [{}, {"css": "amc-btn-primary amc-justify-center amc-align-center amc-hidden", "children": [{"css": "amc-loading-img amc-text-white-clolor", "src": "indicatior", "tag": "img", "id": "loading"}], "tag": "div", "id": "loadingBtn"}, {}, {"css": "amc-btn-primary amc-justify-center amc-align-center", "children": [{"css": "amc-flex-1 amc-ellipsis amc-text-center amc-btn-disabled-color amc-font-super-l amc-btn-disabled-color", "children": [{"tag": "text", "text": "{{next}}"}], "tag": "label", "id": "btnText"}], "onclick": "onSubmit();", "disabled": "true", "tag": "div", "id": "submitBtn"}], "tag": "div"}, {}, {"css": "amc-pd-lr amc-margin-t amc-justify-center mic-other-touch-area-height", "children": [{}, {"children": [{"css": "amc-text-color-blue amc-font-m", "children": [{"tag": "text", "text": "{{changeOther<PERSON>ethod}}"}], "tag": "label", "id": "btnText"}], "onclick": "mic.fn.changeModule();", "tag": "div", "id": "othersBtn"}], "tag": "div", "id": "others"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown();", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "kba", "format": "JSON", "tag": "MOBILEIC", "time": "0040", "tplId": "MOBILEIC@kba", "tplVersion": "5.1.9"}