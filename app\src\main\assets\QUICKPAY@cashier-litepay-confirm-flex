{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"one_stpay_proc": "免密支付處理中", "one_stpay": "免密支付", "one_stpay_succ": "免密支付成功"}, "zh_TW": {"one_stpay_proc": "免密碼支付處理中", "one_stpay": "免密碼支付", "one_stpay_succ": "免密碼支付成功"}, "en_US": {"one_stpay_proc": "One-step Payment in Progress", "one_stpay": "One-step Payment", "one_stpay_succ": "One-step Payment Success"}, "zh_CN": {"one_stpay_proc": "免密支付处理中", "one_stpay": "免密支付", "one_stpay_succ": "免密支付成功"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": ".amc-i-body{display:flex;flex-direction:column;height:50%;margin:0;min-height:400px;padding:0;background-color:#fff;opacity:0.96}.amc-i-nav-m-text{font-size:18px;color:#000;text-align:center;font-weight:bold}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._PwdCheckPage_br6i-c-amount-box{margin-top:10px;height:103px}._PwdCheckPage_br6i-c-rmb-icon{height:35px;width:17px;max-width:35px;margin-top:15px}._PwdCheckPage_br6i-c-cost-label{font-size:45px;color:#333;text-align:center}._PwdCheckPage_br6i-c-info-box{padding:0 48px}._PwdCheckPage_br6i-c-info-txt{font-size:20px;color:#333;text-align:center}._PwdCheckPage_br6i-c-tips-box{margin-bottom:20px}._PwdCheckPage_br6i-c-tips-txt{font-size:13px;color:#999999;text-align:center}._PwdCheckPage_br6i-c-button-box{margin:0 16px 16px 16px}._PwdCheckPage_br6i-c-btn-primary{background-color:#1677FF;border:0;border-radius:2px;height:44px;flex:1.0}._PwdCheckPage_br6i-c-btn-primary:active{background-color:#A2C9FF}._PwdCheckPage_br6i-c-btn-primary:disabled{background-color:#A2C9FF}._PwdCheckPage_br6i-c-btn-text{color:#FFF;font-size:18px;font-weight:bold}._PwdCheckPage_br6i-c-btn-img{width:28px;height:28px;margin-right:8px}._PwdCheckPage_br6i-c-slogan-box{padding:22px 16px 16px 16px}._PwdCheckPage_br6i-c-slogan-img{height:14px}._Button_b6r6-c-text-primary{font-size:18px;color:#fff;height:49px}._Button_b6r6-c-button-primary{height:49px;max-height:49px;min-height:49px;align-self:stretch}._Button_b6r6-c-lottie-loading{width:320px;height:45px}._Button_b6r6-c-lottie-margin{margin-top:-45px}._Dialog_tc9j-c-dlg-msg{-webkit-line-clamp:4;-webkit-box-orient:vertical;display:-webkit-box;margin:8px 20px 21px 20px;font-size:17px;line-height:24px;font-family:Helvetica Neue}._Dialog_tc9j-c-btn-left-text{margin:15px 0}._Dialog_tc9j-c-btn-right-text{font-weight:bold;margin:15px 0}._Dialog_tc9j-c-title-margin-b{margin-bottom:0}._Dialog_tc9j-c-dlg-body{border-radius:8px;background-color:#FFF;width:300px}._Dialog_tc9j-c-btn-vertical-line{width:1px;align-self:stretch;margin:0;background-color:#e5e5e5}._Dialog_tc9j-c-dlg-margin-t{height:14px}._Dialog_tc9j-c-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;margin:0 11px 17px;overflow:hidden}._Dialog_tc9j-c-label{background-color:#FFECE3;color:#FF6010;margin-left:4px;margin-right:4px;margin-bottom:8px;border-radius:2px;font-size:14px;padding:2px 4px}._Dialog_tc9j-c-dlg-title{font-size:20px;line-height:28px}._Dialog_tc9j-c-dlg-button{font-size:20px;line-height:28px}._VIPlugin_y36m-c-vi-finger{height:72px;max-height:72px;min-height:72px}._VIPlugin_y36m-c-vi-finger-box{justify-content:center}._VIPlugin_y36m-c-bottom-box{justify-content:flex-end;flex:1}._ButtonGroup_gf2g-c-pay-btn-box{flex-direction:column;overflow:hidden;padding:12px 12px;min-height:74px;border-radius:2px;justify-content:flex-end}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from e7d631a26d54ef94c5b2610ef6134f28658fab87:D */!function(n){var o={};function i(t){if(o[t])return o[t].exports;var e=o[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}i.m=n,i.c=o,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(t,'__esModule',{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&'object'==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,'default',{enumerable:!0,value:e}),2&t&&'string'!=typeof e)for(var o in e)i.d(n,o,function(t){return e[t]}.bind(null,o));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,'a',e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p='',i(i.s=13)}([function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.amc=window.amc},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var s=n(0);e.mergeObject=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n={};if(t&&t.length)for(var o=0;o<t.length;o++){var i=t[o];if(s.amc.fn.isObject(i))for(var r in i)i.hasOwnProperty(r)&&(n[r]=i[r])}return n},e.isFunction=function(t){return'[object Function]'===Object.prototype.toString.call(t)},e.isPreRender=function(t){return t&&(t.local&&t.local.isPrerender||t.rpcData&&t.rpcData.isPrerender)},e.copyObj=function(t,e){for(var n in e||(e={}),t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},e.doNothing=function(){},e.tryJSONParse=function(t){if(null==t)return{};if(s.amc.fn.isObject(t))return t;try{return JSON.parse(t)}catch(t){return{}}},e.checkEmptyObj=function(t){return s.amc.fn.isString(t)?0===t.length:!(t&&0!==Object.keys(t).length)},e.substrWithFontWidth=function(t,e,n){if(!t)return t;for(var o='',i=0,r=t.length,s=0;s<r;s++){var a=n?t[r-s-1]:t[s];if(/^[A-Za-z0-9\\(\\)]*$/.test(a)?i+=.45:i++,o+=a,e-1<i)break}return o},e.calculateFontWidth=function(t){if(!t)return 0;for(var e=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<t.length;o++)n.test(t[o])?e+=.45:e++;return Math.round(e)},e.deepCopy=function t(e){if(null==e||'object'!=typeof e)return e;var n;if(e instanceof Date)return(n=new Date).setTime(e.getTime()),n;if(e instanceof Array){n=[];for(var o=0,i=e.length;o<i;o++)n[o]=t(e[o]);return n}if(e instanceof Object){for(var r in n={},e)e.hasOwnProperty(r)&&(n[r]=t(e[r]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},e.getConfig=function(t,e){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:t},function(t){e(t.available)})},20)},e.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},e.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)}},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(19);e.BNComponent=o.BNComponent;var i=n(10);e.ComponentRegistry=i.ComponentRegistry;var r=n(21);e.Logger=r.Logger,e.logger=r.logger},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o,s=n(0),i=n(1);e.modifyElementStyle=function(t,e,n){var o=e;s.amc.fn.isString(e)&&(o=t.getViewInComponentById(e)),o&&i.copyObj(n,o.style)},e.modifyElementAttribute=function(t,e,n){if(e&&n){var o=e;if(s.amc.fn.isString(e)&&(o=t.getViewInComponentById(e)),o)for(var i in n)n.hasOwnProperty(i)&&(o[i]=n[i])}},e.modifyElementClass=function(t,e,n,o){var i=e;s.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),i&&(o||(i.className=''),t.applyStyleTo(i,n))},e.visibleElement=function(t,e,n){var o;void 0===n&&(n=!0),e&&(o=s.amc.fn.isString(e)?t.getViewInComponentById(e):e)&&(n?s.amc.fn.show(o):s.amc.fn.hide(o))},e.modifyElementCSS=function(t,e,n){if(e){var o=e;s.amc.fn.isString(e)&&(o=t.getViewInComponentById(e)),o&&n&&(o.style.cssText=n)}},e.createEmbedViPlugin=function(t,e,n,o){var i;if(s.amc.isAndroid)i=document.createElement('embed',e,function(){});else for(var r in i=document.createElement('embed'),e)e.hasOwnProperty(r)&&(i[r]=e[r]);return n&&(i.className=n),o?t.insertBefore(i,o):t.appendChild(i),i},e.getThemeColor=(o='',function(){return o||(o=s.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'#1677FF':'#108EE9'),o})},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(14);e.BNComponent=o.BNComponent;var i=n(7);e.ComponentRegistry=i.ComponentRegistry;var r=n(16);e.Logger=r.Logger,e.logger=r.logger},function(t,e,n){'use strict';var o,i,r,s,a=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var c,u,p,l,f,g,d=n(2),h=n(0),m=n(3),_=n(1);(g=c=e.BUTTON_TYPE||(e.BUTTON_TYPE={})).PRIMARY='PRIMARY',g.NORMAL='NORMAL',(f=u=e.LOADING_TYPE||(e.LOADING_TYPE={}))[f.JuHua=0]='JuHua',f[f.CIRCLE=1]='CIRCLE',(l=p=e.BUTTON_STATUS||(e.BUTTON_STATUS={}))[l.NORMAL=0]='NORMAL',l[l.LOADING=1]='LOADING',l[l.SUCCESS=2]='SUCCESS',l[l.DISABLE=3]='DISABLE';var v=((i={})[c.NORMAL]=h.amc.path+'alipay_msp_loading_blue.gif',i[c.PRIMARY]=h.amc.res.loading,i),y=((r={})[c.NORMAL]='amc-loading-img amc-text-color-blue',r[c.PRIMARY]='amc-loading-img amc-text-white-clolor',r),b=((s={})[c.NORMAL]=h.amc.path+'alipay_msp_success_blue.gif',s[c.PRIMARY]=h.amc.res.success,s),I='color: '+m.getThemeColor()+';',P='background-color: #FFF;border: 2px '+m.getThemeColor()+';font-size: 18px;',C=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={type:c.PRIMARY,onClick:function(){},circleLoading:{},juHuaLoading:{},lottieImg:null},t.onCreated=function(){},t.onMounted=function(){t.props.circleLoading=m.createEmbedViPlugin(t.getViewInComponentById('buttonInnerBox'),{type:'MQPPayGifView'},'amc-hidden',t.getViewInComponentById('buttonText')),m.modifyElementCSS(t,t.props.circleLoading,'width: 24px; height: 24px;margin-right: 8px;'),t.props.juHuaLoading=t.getViewInComponentById('loadingJuHua'),h.amc.isAndroid&&m.modifyElementAttribute(t,t.props.juHuaLoading,{src:h.amc.path+'alipay_msp_indicator_white_loading'}),m.modifyElementAttribute(t,'buttonText',{accessibilityTraits:'Button'}),t.changeLoadingStatus(p.NORMAL)},t.lottieReady=!1,t.lottiePlay=!1,t}return a(t,e),t.prototype.setStyle=function(t){m.modifyElementStyle(this,'button',t)},t.prototype.setHeight=function(t){m.modifyElementStyle(this,'buttonText',{height:t+'px'}),m.modifyElementStyle(this,'button',{height:t+'px',maxHeight:t+'px',minHeight:t+'px'})},t.prototype.setType=function(t){switch(this.props.type=t,m.modifyElementClass(this,this.props.juHuaLoading,y[t],!1),t){case c.NORMAL:m.modifyElementClass(this,'button','amc-btn-secondary amc-align-center amc-justify-center button-primary',!1),m.modifyElementCSS(this,'buttonText',I),m.modifyElementCSS(this,'button',P);break;case c.PRIMARY:m.modifyElementClass(this,'button','amc-btn-primary amc-align-center amc-justify-center button-primary',!1),m.modifyElementCSS(this,'buttonText','color: #fff;')}return this},t.prototype.setLottieImg=function(t){var e=this;if(!h.amc.isSDK&&t&&h.amc.fn.sdkGreaterThanOrEqual('10.8.29')){var n=this.getViewInComponentById('buttonBackground');this.props.lottieImg=h.amc.fn.create('lottie','',n),this.applyStyleTo(this.props.lottieImg,'amc-flex-center lottie-loading'),this.props.lottieImg.djangoId=t,this.props.lottieImg.repeatCount='-1',this.props.lottieImg.invoke('pause',{}),this.props.lottieImg.dataReady=function(){e.lottieReady=!0,e.lottiePlay&&e.props.lottieImg&&e.props.lottieImg.invoke('play',{})}}return this},t.prototype.startLottie=function(){this.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke('play',{}),this.lottiePlay=!0},t.prototype.stopLottie=function(){this.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke('stop',{}),this.lottiePlay=!1},t.prototype.setVisible=function(t){return m.modifyElementStyle(this,'button',{display:t?'flex':'none'}),this},t.prototype.setOnClick=function(t){return this.props.onClick=t||_.doNothing,m.modifyElementAttribute(this,'button',{onclick:this.props.onClick}),this},t.prototype.setText=function(t){return this.props.text=t,this},t.prototype.changeTextAndOnClick=function(t,e){var n=void 0;switch(t){case p.SUCCESS:this.props.text&&(n=this.props.text[p.SUCCESS]),m.modifyElementAttribute(this,'button',{disabled:!1,onclick:_.doNothing});break;case p.NORMAL:this.props.text&&(n=this.props.text[p.NORMAL]),m.modifyElementAttribute(this,'button',{disabled:!1,onclick:this.props.onClick});break;case p.LOADING:this.props.text&&(n=this.props.text[p.LOADING]),m.modifyElementAttribute(this,'button',{disabled:!1,onclick:_.doNothing});break;case p.DISABLE:this.props.text&&(n=this.props.text[p.NORMAL]),m.modifyElementAttribute(this,'button',{disabled:!0,onclick:_.doNothing});break;default:n=''}return void 0!==e?m.modifyElementAttribute(this,'buttonText',{innerText:e}):void 0!==n&&m.modifyElementAttribute(this,'buttonText',{innerText:n}),this},t.prototype.changeLoadingStatus=function(t,e,n){switch(e){case u.CIRCLE:m.visibleElement(this,this.props.juHuaLoading,!1),m.visibleElement(this,this.props.circleLoading,!1),this.changeTextAndOnClick(t,n),this.changeCircleLoading(t),m.visibleElement(this,this.props.circleLoading,t!==p.NORMAL&&t!==p.DISABLE);break;case u.JuHua:m.visibleElement(this,this.props.juHuaLoading,!1),m.visibleElement(this,this.props.circleLoading,!1),this.changeTextAndOnClick(t,n),m.visibleElement(this,this.props.juHuaLoading,t!==p.NORMAL&&t!==p.DISABLE);break;default:this.changeLoadingStatus(t,u.CIRCLE,n)}},t.prototype.changeCircleLoading=function(t){var e=this.props.circleLoading;switch(t){case p.LOADING:m.modifyElementAttribute(this,e,{src:v[this.props.type]});break;case p.NORMAL:case p.DISABLE:m.modifyElementAttribute(this,e,{src:''});break;case p.SUCCESS:m.modifyElementAttribute(this,e,{src:b[this.props.type]});break;default:h.amc.fn.logError('Button','loading-'+(t||'status'))}},t.getComponentCSSRules=function(){return{'.text-primary':'_Button_b6r6-c-text-primary','.button-primary':'_Button_b6r6-c-button-primary','.lottie-loading':'_Button_b6r6-c-lottie-loading','.lottie-margin':'_Button_b6r6-c-lottie-margin'}},t.getComponentJson=function(){return{'sp-view-id':'button',_c:'amc-btn-primary amc-align-center amc-justify-center amc-v-box _Button_b6r6-c-button-primary',_t:'div',_cd:[{'sp-view-id':'buttonBackground',_c:'_Button_b6r6-c-lottie-loading amc-align-center amc-justify-center',_t:'div'},{'sp-view-id':'buttonInnerBox',_c:'_Button_b6r6-c-lottie-margin amc-align-center amc-justify-center',_t:'div',_cd:[{'sp-view-id':'loadingJuHua',src:'indicatior',_c:'amc-loading-img amc-text-white-clolor',alt:'',_t:'img'},{'sp-view-id':'buttonText',_c:'_Button_b6r6-c-text-primary amc-ellipsis',_t:'label',_x:'{{pay_right_now}}'}]}]}},t.componentName='Button',t.componentHashName='Button_b6r6',t}(d.BNComponent);e.Button=C},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},e.startsWith=function(t,e){return!!t&&0===t.indexOf(e)}},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(4),i=function(){function n(){}return n.registerComponent=function(t,e){e?n.facts[t]?o.logger.e('CmpReg#regCmp','E0002 '+t):(o.logger.i('CmpReg#regCmp','I0003 '+t),n.facts[t]=e):o.logger.e('CmpReg#regCmp','E0001 '+t+', '+e)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(t){return n.jsons[t]},n.putComponentJson=function(t,e){e||o.logger.e('CmpReg#putCmpJ','E0004 '+t+', '+e),n.getComponentJson(t)?o.logger.e('CmpReg#putCmpJ','E0005 '+t):(o.logger.i('CmpReg#putCmpJ','I0006 '+t),n.jsons[t]=e)},n.createComponent=function(t){o.logger.i('CmpReg#crtCmp','I0007 '+t);var e=n.facts[t];return e?new e:(o.logger.e('CmpReg#crtCmp','E0008 '+t),null)},n.facts={},n.jsons={},n}();e.ComponentRegistry=i},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.amc=window.amc},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},e.startsWith=function(t,e){return!!t&&0===t.indexOf(e)},e.tryJSONParse=function(t){if(e=t,'[object Object]'===Object.prototype.toString.call(e))return t;var e;try{return JSON.parse(t)}catch(t){return{}}},e.copyObj=function(t,e){for(var n in e||(e={}),t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(2),i=function(){function n(){}return n.registerComponent=function(t,e){e?n.facts[t]?o.logger.e('CmpReg#regCmp','E0002 '+t):(o.logger.i('CmpReg#regCmp','I0003 '+t),n.facts[t]=e):o.logger.e('CmpReg#regCmp','E0001 '+t+', '+e)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(t){return n.jsons[t]},n.putComponentJson=function(t,e){e||o.logger.e('CmpReg#putCmpJ','E0004 '+t+', '+e),n.getComponentJson(t)?o.logger.e('CmpReg#putCmpJ','E0005 '+t):(o.logger.i('CmpReg#putCmpJ','I0006 '+t),n.jsons[t]=e)},n.createComponent=function(t){o.logger.i('CmpReg#crtCmp','I0007 '+t);var e=n.facts[t];return e?new e:(o.logger.e('CmpReg#crtCmp','E0008 '+t),null)},n.facts={},n.jsons={},n}();e.ComponentRegistry=i},function(t,a,e){'use strict';var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(a,'__esModule',{value:!0});var i,c,r,u,s,p,l,f,g=e(2),d=e(12),h=e(1),m=e(0),_=e(3),v=e(5),y=e(24);g.ComponentRegistry.registerComponent(v.Button.componentName,v.Button),g.ComponentRegistry.registerComponent(y.ButtonGroup.componentName,y.ButtonGroup),(f=i=a.VI_STATUS||(a.VI_STATUS={})).NORMAL='normal',f.VERIFYING='verifying',f.PAYING='paying',f.SUCCESS='success',(l=c||(c={})).viStart='viStart',l.viStatus='viStatus',l.viRpcRequest='viRpcRequest',l.viToPWD='viToPWD',l.viResult='viResult',a.EXIT_VI_SCENE={PPW_LOCK:'PPW_LOCK',forgetPwd:'forgetPwd'},a.RETAIN_VI_SCENE={USER_CANCEL:'USER_CANCEL'},a.VI_LOG_ACTION_TYPE='VI',(p=r=a.VI_ACTION||(a.VI_ACTION={})).PRE_LOAD='viPreload',p.START='viStart',p.START_HUAWEI='viPreStart',p.RPC_RESPONSE='viRpcResponse',p.PAY_STATUS='payStatus',p.CLOSE_PAGE='viClosePage',p.FORGET_PWD_HANDLE_OTHER_PAY_WAY='viHandleOtherPayWay',a.VI_TYPE={PASSWORD:'pwd',SHORT_PASSWORD:'spwd',FINGERPRINT:'fp',BRACELET:'wl',FACE_IDENTIFICATION_CAPS:'FACEID',FACE_IDENTIFICATION_LOWER:'faceid',ZOLO_FACE_IDENTIFICATION:'ZFACE'},(s=u||(u={})).NEW='2.0',s.OLD='1.0',s.UNKNOWN='unknown';var b=function(e){function o(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={buttonGroup:new y.ButtonGroup,isPayBtnShown:!0,isPaySuccessShown:!1,processVIRpcRequest:function(){},processVIStart:function(){},processVIStatus:function(){},processVIToPWD:function(){},processVIResult:function(){},pwdTip:'',pwdType:'',rpcVIData:'',buttons:[],buttonIndex:0,supportVersion:u.UNKNOWN},t.onCreated=function(){},t.onMounted=function(){window.viCallback=t.viCallback.bind(t),t.props.viPlugin=_.createEmbedViPlugin(t.getViewInComponentById('viPwdBox'),{type:'VIPayPluginView',src:JSON.stringify({}),id:'realViPlugin'},'vi-finger amc-flex-1'),t.props.buttonGroup=t.getSubComponentById('buttonGroup',y.ButtonGroup),t.props.buttonGroup.setOnClick(t.onButtonClick.bind(t))},t}return n(o,e),o.prototype.addButton=function(t,e,n){this.props.buttons.push({dom:t,text:e}),this.props.buttonGroup.addButton(t,n),t.setText(o.VIButtonText2ButtonText(e)).changeLoadingStatus(v.BUTTON_STATUS.NORMAL,v.LOADING_TYPE.CIRCLE)},o.prototype.onButtonClick=function(t){this.props.buttonIndex=t},o.VIButtonText2ButtonText=function(t){var e;return(e={})[v.BUTTON_STATUS.NORMAL]=t.normal,e[v.BUTTON_STATUS.SUCCESS]=t.success,e[v.BUTTON_STATUS.LOADING]=t.paying,e},o.prototype.getAboveButtonBox=function(){return this.getViewInComponentById('aboveButtonBox')},o.prototype.payButtonExposure=function(t,e){var n=this;this.props.isPayBtnShown=!0,setTimeout(function(){n.props.isPayBtnShown&&m.amc.fn.spmExposure(t,e,!0)},300)},o.prototype.setRpcVIData=function(t,e){return void 0===e&&(e=!0),this.props.rpcVIData=t||'',e&&this.preLoadVIPlugin(),this},o.prototype.showButton=function(t){return void 0===t&&(t=!0),(this.props.isPayBtnShown=t)?(this.props.buttonGroup.setVisible(!0),_.visibleElement(this,'viPwdBox',!1)):(this.props.buttonGroup.setVisible(!1),_.visibleElement(this,'viPwdBox',!0)),this},o.prototype.setPwdTip=function(t){return this.props.pwdTip=t||'',this},o.prototype.setProcessVIStart=function(t){return this.props.processVIStart=t,this},o.prototype.setProcessVIStatus=function(e){var n=this;return this.props.processVIStatus=function(t){t&&t.type&&(n.props.pwdType=t.type),t&&t.version&&(n.props.version=t.version),e(t)},this},o.prototype.setProcessVIRpcRequest=function(t){return this.props.processVIRpcRequest=t,this},o.prototype.setProcessVIToPWD=function(t){return this.props.processVIToPWD=t,this},o.prototype.setProcessVIResult=function(t){return this.props.processVIResult=t,this},o.prototype.getIsPaySuccessShown=function(){return this.props.isPaySuccessShown},o.prototype.getPwdType=function(){return this.props.pwdType||''},o.prototype.getActiveButton=function(){return this.props.buttons.length>this.props.buttonIndex?this.props.buttons[this.props.buttonIndex].dom:0<this.props.buttons.length?this.props.buttons[0].dom:void 0},o.prototype.getActiveButtonText=function(){return this.props.buttons.length>this.props.buttonIndex?this.props.buttons[this.props.buttonIndex].text:0<this.props.buttons.length?this.props.buttons[0].text:void 0},o.prototype.getButtonContainer=function(){return this.props.buttonGroup},o.prototype.toggleBioProcessLoading=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex].text;switch(t){case i.NORMAL:this.stopLoadingWithText();break;case i.VERIFYING:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.VERIFYING]||'');break;case i.PAYING:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.PAYING]||'');break;case i.SUCCESS:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.SUCCESS]||'',v.BUTTON_STATUS.SUCCESS);break;default:this.stopLoadingWithText()}}},o.prototype.startLoadingWithText=function(t,e,n){this.props.buttonIndex>=this.props.buttons.length||this.props.buttons[this.props.buttonIndex].dom.changeLoadingStatus(n||v.BUTTON_STATUS.LOADING,t,e||'')},o.prototype.stopLoadingWithText=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.NORMAL,void 0,t||e.text[i.NORMAL])}},o.prototype.onPaySuccess=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.SUCCESS,v.LOADING_TYPE.CIRCLE,e.text[i.PAYING]),_.modifyElementStyle(e.dom,'buttonText',{opacity:.2}),setTimeout(function(){_.modifyElementAttribute(e.dom,'buttonText',{innerText:t||e.text[i.SUCCESS]}),_.modifyElementStyle(e.dom,'buttonText',{opacity:1})},500),this.props.isPaySuccessShown=!0}},o.prototype.onPayFailure=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.NORMAL,v.LOADING_TYPE.CIRCLE,t||e.text[i.NORMAL]),this.props.isPaySuccessShown=!0}},o.prototype.invoke=function(t,e){if(this.props.viPlugin)return this.props.viPlugin.invoke(t,e)},o.prototype.getVersion=function(){return this.props.version},o.prototype.getAuthType=function(t,e){var n=this;this.getVIConfig(function(){t(n.getAuthTypeIml(e))})},o.prototype.getAuthTypeIml=function(t){var e={};try{(e=this.invoke('getAuthType',{pwdTip:this.props.pwdTip,viData:t||this.props.rpcVIData,supportVersion:this.props.supportVersion}))&&e.version&&(this.props.version=e.version)}catch(t){m.amc.fn.logError('VI','getAuthType失败,'+t)}return d.logAction('authType-'+(this.props.supportVersion||'supportVersion')+'-'+(e&&e.version||'version'),a.VI_LOG_ACTION_TYPE),e},o.prototype.sendDataToVIPlugin=function(t){d.logAction(t&&t.action||'action',a.VI_LOG_ACTION_TYPE),_.modifyElementAttribute(this,this.props.viPlugin,{src:JSON.stringify(t)})},o.prototype.preLoadVIPlugin=function(){if(this.props.rpcVIData&&!h.checkEmptyObj(this.props.rpcVIData)){var t={action:r.PRE_LOAD,data:this.props.rpcVIData};this.sendDataToVIPlugin(t)}},o.prototype.getVIConfig=function(t){this.props.supportVersion===u.UNKNOWN&&(m.amc.isSDK?m.amc.isAndroid?m.amc.fn.sdkGreaterThanOrEqual('*********')?this.props.supportVersion=u.NEW:this.props.supportVersion=u.OLD:m.amc.fn.sdkGreaterThanOrEqual('*********')?this.props.supportVersion=u.NEW:this.props.supportVersion=u.OLD:m.amc.fn.sdkGreaterThanOrEqual('10.8.34')?this.props.supportVersion=u.NEW:this.props.supportVersion=u.OLD),t()},o.prototype.startVIPlugin=function(t,e,n){var o=this;this.getVIConfig(function(){o.startVIPluginIml(t,e,n)})},o.prototype.startVIPluginIml=function(t,e,n){var o={supportVersion:this.props.supportVersion,supportRetain:'Y'};this.props.pwdTip&&(o.pwdTip=this.props.pwdTip),n=h.copyObj(o,n),d.logAction('viLen|'+(this.props.rpcVIData||'').length+'-'+(n.supportVersion||'supportVersion')+'-'+(n.usePwd||'usePwd')+'-'+(n.costTip||'costTip'),a.VI_LOG_ACTION_TYPE);var i={action:t,data:e||this.props.rpcVIData,callbacks:{onViAction:'viCallback'},config:n};this.sendDataToVIPlugin(i)},o.prototype.sendResponseDataToVI=function(t){var e={action:r.RPC_RESPONSE,data:t};this.sendDataToVIPlugin(e)},o.prototype.showVILoadingWithPaymentResult=function(t){var e={action:r.PAY_STATUS,data:{status:t}};this.sendDataToVIPlugin(e)},o.prototype.sendForgetPwdPayWithOtherWayToVI=function(){var t={action:r.FORGET_PWD_HANDLE_OTHER_PAY_WAY,data:{from:'forgotPwd'}};this.sendDataToVIPlugin(t)},o.prototype.viCallback=function(t){var e,n;try{n=JSON.parse(t)||{action:'none',data:''}}catch(t){return void m.amc.fn.logError('VI','解析核身JSON失败'+t)}var o=n.action,i=n.data;if(m.amc.fn.isString(o)&&o===c.viToPWD){var r={};if(m.amc.fn.isString(i))try{r=JSON.parse(i)}catch(t){m.amc.fn.logError('pVI2PWD','json parse error')}else r=i;r&&(r.version?this.props.version=r.version:this.props.version=u.OLD)}var s=((e={})[c.viStart]=this.props.processVIStart,e[c.viStatus]=this.props.processVIStatus,e[c.viRpcRequest]=this.props.processVIRpcRequest,e[c.viToPWD]=this.props.processVIToPWD,e[c.viResult]=this.props.processVIResult,e.none=function(){},e);o!==c.viStatus&&o!==c.viToPWD&&o!==c.viResult&&o!==c.viStart&&d.logAction(o||'actType',a.VI_LOG_ACTION_TYPE),m.amc.fn.isString(o)&&s[o]&&s[o](i)},o.prototype.viPageCloseNotify=function(){var t={action:r.CLOSE_PAGE};this.sendDataToVIPlugin(t)},o.getComponentCSSRules=function(){return{'.vi-finger':'_VIPlugin_y36m-c-vi-finger','.vi-finger-box':'_VIPlugin_y36m-c-vi-finger-box','.bottom-box':'_VIPlugin_y36m-c-bottom-box'}},o.getComponentJson=function(){return{'sp-view-id':'viPluginBox',_c:'_VIPlugin_y36m-c-bottom-box amc-v-box',_t:'div',_cd:[{'sp-view-id':'aboveButtonBox',_t:'div'},{'sp-view-id':'buttonContainer',_c:'amc-v-box',_t:'div',_cd:[{'sp-component':'ButtonGroup','sp-component-id':'buttonGroup',_t:'div'}]},{'sp-view-id':'viPwdBox',_c:'_VIPlugin_y36m-c-vi-finger _VIPlugin_y36m-c-vi-finger-box amc-hidden',_t:'div'},{'sp-view-id':'bottomBlank',_c:'amc-iphone-x-pd-b',_t:'div'}]}},o.componentName='VIPlugin',o.componentHashName='VIPlugin_y36m',o}(g.BNComponent);a.VIPlugin=b},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(0);e.logAction=function(t,e){window.pageId||(window.pageId='|'+Math.random().toString(36).substr(2,3)),e=e?e+window.pageId:window.pageId,o.amc.fn.logAction(t,e)}},function(t,e,n){'use strict';var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var r=n(4),s=n(8),a=n(17),c=n(22),u=s.amc.rpcData,p=new(function(t){function e(){var r=null!==t&&t.apply(this,arguments)||this;return r.isPaySuccessShown=!1,r.isHidePageBody=!1,r.exitDialog=a.getExitConfirmDialog(),r.onCreated=function(){},r.onMounted=function(){r.getViewInComponentById('rmbIcon').src=s.amc.path+'alipay_msp_rmb';var t=r.getViewInComponentById('orderInfoTxt');u.exitAction?t.innerText='你正在使用淘宝100元以下免密支付':t.innerText=u.descr||'',r.getViewInComponentById('tips').innerText=u.tips||'可在我的淘宝 > 设置 > 支付设置中管理',r.gifImg=r.getViewInComponentById('gifImg'),r.gifImg.src='',r.confirmBtn=r.getViewInComponentById('confirmBtn'),r.btnText=r.getViewInComponentById('btnText'),u.amount&&'true'===u.showDisplayAmount?(r.getViewInComponentById('costLabel').innerText=u.amount,s.amc.fn.show(r.getViewInComponentById('amountBox')),t.style.fontSize='15px'):s.amc.fn.show(r.getViewInComponentById('amountBoxSpace'));var e=u.themeColor;if(e&&(r.confirmBtn.style.backgroundColor=e),!e&&u.sloganUrl){var n=function(t,e,n){t&&s.amc.fn.show(r.getViewInComponentById('sloganBox'))},o=r.getViewInComponentById('sloganImg'),i=s.amc.isSDK&&s.amc.isIOS;window.loadImageHelper(o,u.sloganUrl,i?null:n),i&&n(!0,u.sloganUrl)}u.hidePage&&(document.body.style.opacity='0',r.isHidePageBody=!0,s.amc.isIOS&&document.submit({action:{name:'loc:loading'},param:{show:!0,keeploading:!0,text:u.toastText?u.toastText:'安全免密支付'}})),'true'===u.autoCommit||u.hidePage?setTimeout(function(){r.submitAction()},20):(r.confirmBtn.onclick=function(){r.submitAction()},r.btnText.innerText='立即付款')},r.submitAction=function(){s.amc.fn.spmClick('a259.b17008.c42507.d86226',null);var n=function(){r.gifImg.src=s.amc.res.loading,s.amc.fn.show(r.gifImg),r.btnText.innerText='{{one_stpay_proc}}'},o=function(){r.gifImg.src='',s.amc.fn.hide(r.gifImg),r.btnText.innerText='立即付款'},t=u.toastText?u.toastText:'免密支付中';document.asyncSubmit({action:{name:u.buttonAction,params:u.buttonActionParams||{},loadtxt:r.isHidePageBody?t:'',neec:'6004'}},function(t){if(t=t||{},!r.isPaySuccessShown){var e=t.status;'0001'===e?(r.btnText.innerText='{{one_stpay_succ}}',r.gifImg.src=s.amc.res.success,r.isPaySuccessShown=!0):'0002'!==e&&'0006'!==e&&'0000'!==e||o(),'0'===t.pageloading?o():'1'===t.pageloading&&n()}}),n()},r.onRightBtnClick=function(){s.amc.fn.spmClick('a259.b17008.c42509.d86228',null),u.exitAction?document.submit({action:u.exitAction}):s.amc.fn.logError('LitePayRightBtn','Empty Action')},r.onClose=function(t){s.amc.fn.spmClick('a259.b17008.c42508.d86227',null),s.amc.fn.spmPageDestroy('a259.b17008',null),s.amc.fn.exit()},r.onBack=function(){var t=r.exitDialog;t.show&&t.show({title:'',message:'是否回到旧版付款',leftAction:function(){document.submit({action:{name:'loc:exit'}})},rightAction:function(){r.onRightBtnClick()},leftTxt:'<font color=\"#000000\">放弃</font>',rightTxt:'旧版付款'})},r.paymentSucc=function(){r.btnText.innerText='{{one_stpay_succ}}',r.gifImg.src=s.amc.res.success,r.isPaySuccessShown=!0},r}return i(e,t),e.getComponentCSSRules=function(){return{'.amount-box':'_PwdCheckPage_br6i-c-amount-box','.rmb-icon':'_PwdCheckPage_br6i-c-rmb-icon','.cost-label':'_PwdCheckPage_br6i-c-cost-label','.info-box':'_PwdCheckPage_br6i-c-info-box','.info-txt':'_PwdCheckPage_br6i-c-info-txt','.tips-box':'_PwdCheckPage_br6i-c-tips-box','.tips-txt':'_PwdCheckPage_br6i-c-tips-txt','.button-box':'_PwdCheckPage_br6i-c-button-box','.btn-primary':'_PwdCheckPage_br6i-c-btn-primary','.btn-text':'_PwdCheckPage_br6i-c-btn-text','.btn-img':'_PwdCheckPage_br6i-c-btn-img','.slogan-box':'_PwdCheckPage_br6i-c-slogan-box','.slogan-img':'_PwdCheckPage_br6i-c-slogan-img'}},e.getComponentJson=function(){return{'sp-view-id':'container',_c:'amc-v-box amc-flex-1',_t:'div',_cd:[{'sp-view-id':'amountBox',_c:'amc-flex-center _PwdCheckPage_br6i-c-amount-box amc-hidden',_t:'div',_cd:[{'sp-view-id':'rmbIcon',_c:'_PwdCheckPage_br6i-c-rmb-icon',_t:'img'},{'sp-view-id':'costLabel',_c:'_PwdCheckPage_br6i-c-cost-label',_t:'label'}]},{'sp-view-id':'amountBoxSpace',_c:'amc-v-box amc-flex-1 amc-hidden',_t:'div'},{_c:'amc-v-box _PwdCheckPage_br6i-c-info-box',_t:'div',_cd:[{'sp-view-id':'orderInfoTxt',_c:'amc-flex-1 _PwdCheckPage_br6i-c-info-txt amc-ellipsis-3-line',_t:'label'}]},{_c:'amc-v-box amc-flex-1',_t:'div'},{_c:'amc-v-box _PwdCheckPage_br6i-c-tips-box',_t:'div',_cd:[{'sp-view-id':'tips',_c:'amc-flex-1 _PwdCheckPage_br6i-c-tips-txt amc-ellipsis-3-line',_t:'label'}]},{_c:'_PwdCheckPage_br6i-c-button-box',_t:'div',_cd:[{_c:'_PwdCheckPage_br6i-c-btn-primary _PwdCheckPage_br6i-c-btn-primary _PwdCheckPage_br6i-c-btn-primary amc-flex-center','sp-view-id':'confirmBtn',_t:'div',_cd:[{_y:'MQPPayGifView','sp-view-id':'gifImg',_c:'_PwdCheckPage_br6i-c-btn-img amc-hidden',_t:'embed'},{'sp-view-id':'btnText',_c:'_PwdCheckPage_br6i-c-btn-text',_t:'label'}]}]},{_c:'_PwdCheckPage_br6i-c-slogan-box amc-flex-center amc-hidden','sp-view-id':'sloganBox',_t:'div',_cd:[{_c:'_PwdCheckPage_br6i-c-slogan-img','sp-view-id':'sloganImg',_t:'img'}]},{_c:'amc-iphone-x-pd-b',_t:'div'}]}},e.componentName='PwdCheckPage',e.componentHashName='PwdCheckPage_br6i',e}(r.BNComponent));document.onChannelData=function(t){var e=JSON.parse(t);e.type&&e.data&&e.data.type&&'wnd'==e.type&&'tst'==e.data.type&&e.data.img&&'succ'==e.data.img&&c.props.viPlugin.onPaySuccess()};var l,f=null;window.loadImageHelper=function(t,e,n){if(!f){f={};var o=document.onImgLoaded;document.onImgLoaded=function(t,e){var n=f[e];n&&n.callback?n.callback(t,e,n.img):o&&o(t,e),delete f[e]}}f[e]={callback:n,img:t},t.src=e},window.onKeyDown=function(){4==window.event.which&&(u.exitAction?p.onBack():p.onClose(!0))},s.amc.fn.spmPageCreate('a259.b17008',null),l=s.amc.fn.docConfig,s.amc.fn.docConfig=function(){var t=JSON.parse(l());return t.registerDataChannel=Math.random().toString(36).slice(2),JSON.stringify(t)},window.onload=function(){var t=s.amc.fn.iNav(s.amc.res.close,'','','',function(){return u.exitAction?p.onBack():p.onClose(!0),{}},function(){return p.onRightBtnClick(),{}},void 0);document.body.insertBefore(t,document.getElementById('iLine')),p.mountTo(document.body),s.amc.fn.spmExposure('a259.b17008.c42508.d86227',null,!1),s.amc.fn.spmExposure('a259.b17008.c42507.d86226',null,!1),s.amc.fn.spmExposure('a259.b17008.c42509.d86228',null,!1)}},function(t,e,n){'use strict';var s=this&&this.__assign||function(){return(s=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,'__esModule',{value:!0});var I=n(4),P=n(6),C=n(7),T=n(15),o=function(){function t(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=P.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return t.getComponentCSSRules=function(){throw new Error('E0100')},t.getComponentJson=function(){throw new Error('E0101')},t.prototype.mountTo=function(t,e){if(t){var n=this._acquireRootView();n?(e?t.insertBefore(n,e):t.appendChild(n),this._triggerOnMounted()):I.logger.e('Cmp#mT','E0103 '+n)}else I.logger.e('Cmp#mT','E0102 '+t)},Object.defineProperty(t.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),t.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},t.prototype.getMountedParentView=function(){var t=this.getMountedRootView();return t?t.parentNode:null},t.prototype.getSubComponentById=function(t,e){var n=this.debugName+'#SCById',o=this._subComponentsMap[t];if(!o)return null;var i='',r='';try{i=o.constructor.componentName,r=e.componentName}catch(t){I.logger.e(n,'E0104 '+t)}return i&&i===r?o:(I.logger.e(n,'E0105 '+i+', '+r),null)},t.prototype.getViewInComponentById=function(t){return this._viewsIdMap[t]},t.prototype.getComponentId=function(){return this._componentId},t.prototype.createStyledElement=function(t,e,n){var o=document.createElement(t);if(o)return t&&(o.className+=' '+this._css(t,2)),n&&(o.className+=' '+this._csses(n,1)),e&&(o.className+=' '+this._css('#'+e,2)),o},t.prototype.applyStyleTo=function(t,e){t&&(t.className+=' '+this._csses(e,1))},t.prototype.css=function(t){return this._css(t,0)},t.prototype.csses=function(t){return this._csses(t,0)},t.prototype._csses=function(t,e){var n=this;return t.split(' ').map(function(t){return n._css(t,e)}).join(' ')},t.prototype._css=function(t,e){if(!t)return'';var n=this._componentCSSRules;if(!n)return t;switch(t.charAt(0)){case'#':case'.':return n[t]||t;default:switch(e){case 0:return n['.'+t]||n[t]||t;case 1:return n['.'+t]||t;case 2:default:return t}}},t.prototype._triggerOnMounted=function(){new T.Observer(this.vueModel.data),I.logger.i('','I0106 '+this.debugName);for(var t=0,e=this._subComponents;t<e.length;t++){var n=e[t];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},t.prototype._getMethod=function(t){var e=this[t];return e instanceof Function?e:null},t.prototype._acquireComponentJson=function(){var t=this.debugName+'#acCJ',e=C.ComponentRegistry.getComponentJson(this._componentName);return e?(I.logger.i(t,'I0107'),e):void 0!==this._componentJson?(I.logger.i(t,'I0108'),C.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(I.logger.e(t,'E0109'),null)},t.prototype._acquireRootView=function(){var t=this.debugName+'#acRV';if(this._hasRootViewBuilt)return I.logger.i(t,'I0110'),this._rootView;var e=this._acquireComponentJson();return e?(this._rootView=this._convertJsonToBNNode(e,this.vueModel.data||{}),this._hasRootViewBuilt=!0,I.logger.i(t,'I0112'),this._rootView):(I.logger.e(t,'E0111'),null)},t.prototype._genArrayChildNode=function(t,e,n,o,i){var r=this._convertJsonToBNNode(t,s({},e,{item:n,index:o,arrayName:i}));return r?(r.setAttribute('index',o),r.setAttribute('for_name',i),r):null},t.prototype._convertJsonToBNNode=function(t,u){var p=this,e=this.debugName+'#cJTB';if(void 0===t._t)return null;var l=document.createElement(t._t),f=[];if(void 0!==t._cd)for(var n=function(s){if(s['v-for']||s['v-for-cal']){var t=!s['v-for']&&!!s['v-for-cal'],e=(t?g.vueModel.compute:u)||{},n=T.vueUtils.getObject(t?s['v-for-cal']:s['v-for'],e,t),a=t?T.vueUtils.rmSymbol(s['v-for-cal']):T.vueUtils.rmSymbol(s['v-for']);if(!a||!n)return'continue';for(var o in s['v-for']='',s['v-for-cal']='',n)if(n.hasOwnProperty(o)){var i=g._genArrayChildNode(s,u,n[o],o,a);i&&f.push(i)}var c=document.createElement('div');c&&(c.style.display='none',c.setAttribute('for_end',a),f.push(c),new T.Watcher(a,e,function(t){if(l){T.rmWatchers(a);for(var e=0,n=l.childNodes;e<n.length;e++){var o=n[e];o.getAttribute('for_name')===a&&l.removeChild(o)}if(t)for(var i in t)if(t.hasOwnProperty(i)){var r=p._genArrayChildNode(s,u,t[i],i,a);r&&l.insertBefore(r,c)}}},t).id=u.arrayName)}else{var r=g._convertJsonToBNNode(s,u);if(!r)return'continue';f.push(r)}},g=this,o=0,i=t._cd;o<i.length;o++)n(i[o]);if(!l)return null;u&&u.index&&l.setAttribute('index',u.index);var r=t['bn-component']||t['sp-component'];if(r){I.logger.i(e,'I0113 '+r);var s=C.ComponentRegistry.createComponent(r);if(!s)return I.logger.e(e,'E0114 '+r+', '+s),null;var a=t['bn-component-id']||t['sp-component-id'];return a&&(s._componentId=a),s.onCreated&&s.onCreated(),I.logger.i(e,'I0115 '+s.debugName+', '+a),this._subComponents.push(s),a&&!this._subComponentsMap[a]&&(this._subComponentsMap[a]=s),s._acquireRootView()}var c=t['bn-view-id']||t['sp-view-id'];for(var d in c&&(I.logger.i(e,'I0116 '+c),this._viewsIdMap[c]||(this._viewsIdMap[c]=l)),t._i&&(l.id=t._i),t._c&&(l.className=t._c),t._s&&(l.style.cssText=t._s),t._x&&(l.innerText=t._x),t._y&&(l.type=t._y),t)if(t.hasOwnProperty(d))if(0===d.indexOf('on')){var h=this._getMethod(t[d]);h&&(l[d]=h.bind(this,l))}else if(0===d.indexOf('_'));else if(0===d.indexOf('bn-')||0===d.indexOf('sp-'));else if(P.startsWith(d,'v-')){var m=d.split('-');if(2===m.length||3===m.length){var _=m[1];2===m.length?new T.NodeCompile(u).compile(_,l,t[d],t._t):'cal'===m[2]&&new T.NodeCompile(this.vueModel.compute,!0).compile(_,l,t[d],t._t)}else l[d]=t[d]}else l[d]=t[d];for(var v=0,y=f;v<y.length;v++){var b=y[v];l.appendChild(b)}return l},t.componentName='',t.componentHTML='',t.componentCSS='',t.componentHashName='',t}();e.BNComponent=o},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var r,s=n(6),a=n(8);e.rmWatchers=function(e){r=r.filter(function(t){return t.id!==e})};var c=function(){function t(t,e,n,o){if(this.id='',this.lazy=!1,e&&'object'==typeof e){if(this.lazy=o,this.callback=n,s.startsWith(t,'item')&&e.arrayName&&e.index){var i=t.replace('item','');this.expression=e.arrayName+'.'+e.index,i&&(this.expression+=i)}else this.expression=t;this.data=e,this.value=p.getVal(t,e,this.lazy),r||(r=[]),r.push(this)}}return t.prototype.update=function(){if(this.data&&this.expression&&this.callback){var t=p.getVal(this.expression,this.data,this.lazy),e=this.value;p.equals(t,e)||(this.value=t,this.callback(t))}},t}();e.Watcher=c;var o=function(){function t(t){this.observe(t)}return t.prototype.observe=function(e){var n=this;e&&'object'==typeof e&&Object.keys(e).forEach(function(t){try{n.defineReactive(e,t,e[t]),n.observe(e[t])}catch(t){}})},t.prototype.defineReactive=function(t,e,n){var o=this;Object.defineProperty(t,e,{enumerable:!0,configurable:!1,get:function(){return n},set:function(t){p.equals(t,n)||(n=t,o.observe(t),r&&r.forEach(function(t){t.update()}))}})},t}();e.Observer=o;var i=function(){function t(t,e){void 0===e&&(e=!1),this.data=t||{},this.lazy=e}return t.prototype.compile=function(n,t,e,o){var i=this;if(t)switch(n){case'text':this.labelProcess(t,e,function(t,e){t.innerText=void 0===e?'':e});break;case'html':this.labelProcess(t,e,function(t,e){t.innerHtml=void 0===e?'':e});break;case'class':this.labelProcess(t,e,function(t,e){var n=t.className,o=(n=n.replace(e,'').replace(/\\s$/,''))&&String(e)?' ':'';t.className=n+o+e});break;case'model':this.eventProcess(t,e,function(t,e){t.value=e}),'input'===o?t.oninput=function(){p.setTextVal(e,t.value,i.data)}:'switch'===o&&(t.onchange=function(t){p.setTextVal(e,t||'off',i.data)});break;case'if':this.eventProcess(t,e,function(t,e){!0===e?(t.style.display='flex',u.process(t,function(t){a.amc.fn.spmExposure(t.spmId,t.param4Map,t.doNotResume)})):t.style.display='none'});break;case'spm':this.labelProcess(t,e,function(t,e){t.setAttribute('spm',void 0===e?'':e)});break;case'click':this.eventProcess(t,e,function(t,e){p.isFunction(e)?t.onclick=function(){e(t),u.process(t,function(t){a.amc.fn.spmClick(t.spmId,t.param4Map)})}:t.onclick=function(){}});break;default:this.labelProcess(t,e,function(t,e){t[n]=void 0===e?'':e})}},t.prototype.labelProcess=function(n,o,i){var r=this,t=o.match(/@\\{([^}]+)\\}/g),e=p.getTextVal(o,this.data,this.lazy);t&&t.forEach(function(t){var e=/@\\{([^}]+)\\}/g.exec(t);e&&1<e.length&&(new c(e[1],r.data,function(t){i(n,p.getTextVal(o,r.data,r.lazy))},r.lazy).id=r.data.arrayName)}),i(n,e)},t.prototype.eventProcess=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(t),i=p.getObject(t,this.data,this.lazy);o&&1<o.length&&(new c(o[1],this.data,function(t){n(e,t)},this.lazy).id=this.data.arrayName),n(e,i)},t}();e.NodeCompile=i;var u=function(){function t(){}return t.process=function(t,e){var n=t.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&e(o)}catch(t){}},t}(),p=function(){function a(){}return a.item2ArrayIndex=function(t,e){var n=t;if(s.startsWith(t,'item')&&e.arrayName&&e.index){var o=t.replace('item','');n=e.arrayName+'.'+e.index,o&&(n+=o)}return n},a.getVal=function(t,e,n){if(t){var o=t.split('.').reduce(function(t,e){return t[e]},e);return n?a.isFunction(o)?o():void 0:o}},a.getTextVal=function(t,i,r){var s=this;return t.replace(/@\\{([^}]+)\\}/g,function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(r)t=s.getVal(e[1],i,r);else{var o=a.item2ArrayIndex(e[1],i);t=s.getVal(o,i,!1)}return void 0===t?'':t})},a.getObject=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(t);if(o&&1<o.length)return this.getVal(o[1],e,n)},a.rmSymbol=function(t){var e=/@\\{([^}]+)\\}/g.exec(t);return e&&1<e.length?e[1]:''},a.setVal=function(t,o,e){var i=t.split('.');return i.reduce(function(t,e,n){return n===i.length-1?t[e]=o:t[e]},e)},a.setTextVal=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(t);o&&1<o.length&&this.setVal(o[1],e,n)},a.equals=function(t,e){return this.eq(t,e,void 0,void 0)},a.eq=function(t,e,n,o){if(t===e)return 0!==t||1/t==1/e;if(null==t||null==e)return t===e;var i=toString.call(t);if(i!==toString.call(e))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+t==''+e;case'[object Number]':return+t!=+t?+e!=+e:0==+t?1/+t==1/e:+t==+e;case'[object Date]':case'[object Boolean]':return+t==+e}var r='[object Array]'===i;if(!r){if('object'!=typeof t||'object'!=typeof e)return!1;var s=t.constructor,a=e.constructor;if(s!==a&&!(this.isFunction(s)&&s instanceof s&&this.isFunction(a)&&a instanceof a)&&'constructor'in t&&'constructor'in e)return!1}o=o||[];for(var c=(n=n||[]).length;c--;)if(n[c]===t)return o[c]===e;if(n.push(t),o.push(e),r){if((c=t.length)!==e.length)return!1;for(;c--;)if(!this.eq(t[c],e[c],n,o))return!1}else{var u=Object.keys(t),p=void 0;if(c=u.length,Object.keys(e).length!==c)return!1;for(;c--;)if(p=u[c],!e.hasOwnProperty(p)||!this.eq(t[p],e[p],n,o))return!1}return n.pop(),o.pop(),!0},a.isFunction=function(t){return'function'==typeof t||!1},a}();e.vueUtils=p},function(t,e,n){'use strict';var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var r=function(){function r(){this.enabled=!0}return r.fmtLine=function(t,e,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+t+']['+r.fmtTime()+']['+e+']'+n+' '+i},r.fmtTime=function(){var t=new Date;return t.getHours()+':'+t.getMinutes()+':'+t.getSeconds()+'.'+t.getMilliseconds()},r.prototype.enable=function(){this.enabled=!0},r.prototype.disable=function(){this.enabled=!1},r}();e.Logger=r,e.logger=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.e=function(t,e,n){},e.prototype.i=function(t,e,n){},e}(r))},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(0),i=n(1),r=n(18),s=void 0;e.getExitConfirmDialog=function(){return s||(s=new a),s};var a=function(){function e(){this.birdnestDialog=void 0,this.antUISwitch=!1,this.getBirdnestDialog(),this.getAntUIGray()}return e.prototype.getBirdnestDialog=function(){return this.birdnestDialog||(this.birdnestDialog=new r.Dialog,this.birdnestDialog.mountTo(document.body)),this.birdnestDialog},e.prototype.show=function(e){e&&e.message&&(this.getAntuiEnable(e)?document.confirm({title:e.title,message:e.message,okButton:e.rightTxt,cancelButton:e.leftTxt},function(t){t.ok?r.getBtnAction(e.rightAction)():r.getBtnAction(e.leftAction)()},!0):this.getBirdnestDialog().show(e))},e.prototype.getAntuiEnable=function(t){return!(t.labels&&0<t.labels.length)&&!o.amc.isSDK&&!!o.amc.fn.sdkGreaterThanOrEqual('10.8.41')&&!t.timeout&&(o.amc.isAndroid?21<=e.getOsVersion()&&this.antUISwitch:this.antUISwitch)},e.prototype.getAntUIGray=function(){var e=this;i.getConfig('msp_antui_dialog_gray',function(t){e.antUISwitch=t})},e.getOsVersion=function(){return window.flybird.local.osVersion},e}();e.ExitConfirmDialog=a},function(t,e,n){'use strict';var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var u,p,r=n(2),l=n(0),f=n(3),s=n(1),a=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.onCreated=function(){},t.onMounted=function(){},t.props={dialogInfo:{message:'',title:void 0,leftTxt:'',rightTxt:'',leftAction:void 0,rightAction:void 0,labels:void 0,timeout:void 0}},t}return i(t,e),t.prototype.setMessage=function(t){return this.props.dialogInfo.message=t,this},t.prototype.setRightTxt=function(t){return this.props.dialogInfo.rightTxt=t,this},t.prototype.setLeftTxt=function(t){return this.props.dialogInfo.leftTxt=t,this},t.prototype.setTitle=function(t){return this.props.dialogInfo.title=t,this},t.prototype.setLeftAction=function(t){return this.props.dialogInfo.leftAction=t,this},t.prototype.setRightAction=function(t){return this.props.dialogInfo.rightAction=t,this},t.prototype.setLabels=function(t){return this.props.dialogInfo.labels=t,this},t.prototype.setTimeout=function(t){return this.props.dialogInfo.timeout=t,this},t.prototype.show=function(t){var e=this;if(t&&this.setMessage(t.message).setLeftTxt(t.leftTxt).setLeftAction(t.leftAction).setRightTxt(t.rightTxt).setRightAction(t.rightAction).setTitle(t.title).setLabels(t.labels).setTimeout(t.timeout),this.props.dialogInfo.message&&this.props.dialogInfo.leftTxt&&this.props.dialogInfo.rightTxt){if(this.props.dialogInfo.timeout&&'string'==typeof this.props.dialogInfo.timeout)try{var n=Number(this.props.dialogInfo.timeout);if(n<0)return;p=this.props.dialogInfo.message,this.props.dialogInfo.message=this.props.dialogInfo.message.replace(new RegExp('#timeout#','g'),g(n)),u=window.setInterval(function(){!function(t,e){if(e<=0){window.clearInterval(u);var n=p;return n=n.replace(new RegExp('#timeout#','g'),'0:0.0'),f.modifyElementAttribute(t,'dlgMsg',{innerText:n})}var o=p;o=o.replace(new RegExp('#timeout#','g'),g(e)),f.modifyElementAttribute(t,'dlgMsg',{innerText:o})}(e,n-=100)},100)}catch(t){l.amc.fn.logError('dialog','number parse error')}this.props.dialogInfo.title?(f.visibleElement(this,'dlgTopMargin',!1),f.visibleElement(this,'dlgTitle',!0),f.modifyElementAttribute(this,'dlgTitle',{innerText:this.props.dialogInfo.title})):(f.visibleElement(this,'dlgTopMargin',!0),f.visibleElement(this,'dlgTitle',!1)),f.modifyElementAttribute(this,'dlgMsg',{innerText:this.props.dialogInfo.message});var o=this.getViewInComponentById('dlg');f.modifyElementAttribute(this,'btnLeft',{innerText:this.props.dialogInfo.leftTxt,onclick:function(){d(e.props.dialogInfo.leftAction)(),u&&window.clearInterval(u),o.close()}}),f.modifyElementAttribute(this,'btnRight',{innerText:this.props.dialogInfo.rightTxt,onclick:function(){d(e.props.dialogInfo.rightAction)(),u&&window.clearInterval(u),o.close()}});var i=this.getViewInComponentById('labelsContainer'),r=!!(this.props.dialogInfo.labels&&0<this.props.dialogInfo.labels.length);f.visibleElement(this,i,r);try{var s=i.childNodes;if(s&&0<s.length)for(var a=0;a<s.length;a++)i.removeChild(s[a]);if(r){for(a=0;a<this.props.dialogInfo.labels.length&&a<6;a++){var c=this.createStyledElement('label','label_'+a,'label amc-text-center');c.innerText=this.props.dialogInfo.labels[a],i.appendChild(c)}f.modifyElementStyle(this,'dlgMsg',{marginBottom:'16px'})}}catch(t){}l.amc.isIOS&&l.amc.fn.sdkGreaterThanOrEqual('10.8.35')?o.show():o.showModal()}},t.getComponentCSSRules=function(){return{'.dlg-msg':'_Dialog_tc9j-c-dlg-msg','.btn-left-text':'_Dialog_tc9j-c-btn-left-text','.btn-right-text':'_Dialog_tc9j-c-btn-right-text','.title-margin-b':'_Dialog_tc9j-c-title-margin-b','.dlg-body':'_Dialog_tc9j-c-dlg-body','.btn-vertical-line':'_Dialog_tc9j-c-btn-vertical-line','.dlg-margin-t':'_Dialog_tc9j-c-dlg-margin-t','.labels-container':'_Dialog_tc9j-c-labels-container','.label':'_Dialog_tc9j-c-label','.dlg-title':'_Dialog_tc9j-c-dlg-title','.dlg-button':'_Dialog_tc9j-c-dlg-button'}},t.getComponentJson=function(){return{'sp-view-id':'dlg',_c:'_Dialog_tc9j-c-dlg-body amc-v-box',_t:'dialog',_cd:[{'sp-view-id':'dlgTopMargin',_c:'_Dialog_tc9j-c-dlg-margin-t',_t:'div'},{'sp-view-id':'dlgTitle',_c:'amc-dlg-title _Dialog_tc9j-c-dlg-title _Dialog_tc9j-c-title-margin-b',_t:'label'},{'sp-view-id':'dlgMsg',_c:'amc-text-center _Dialog_tc9j-c-dlg-msg',_t:'label'},{'sp-view-id':'labelsContainer',_c:'_Dialog_tc9j-c-labels-container amc-hidden amc-align-center amc-justify-center',_t:'div'},{_c:'amc-v-box amc-justify-center',_t:'div',_cd:[{_c:'amc-adapt-line',_t:'div'},{_c:'amc-align-center amc-justify-center',_t:'div',_cd:[{'sp-view-id':'btnLeft',_c:'_Dialog_tc9j-c-btn-left-text amc-theme-color _Dialog_tc9j-c-dlg-button amc-flex-1 amc-text-center amc-ellipsis-2-line',_t:'label'},{_c:'_Dialog_tc9j-c-btn-vertical-line',_t:'div'},{'sp-view-id':'btnRight',_c:'_Dialog_tc9j-c-btn-right-text amc-theme-color _Dialog_tc9j-c-dlg-button amc-flex-1 amc-text-center amc-ellipsis-2-line',_t:'label'}]}]}]}},t.componentName='Dialog',t.componentHashName='Dialog_tc9j',t}(r.BNComponent);function g(t){var e=Math.floor(t/6e4),n=Math.floor((t-6e4*e)/1e3);return e+':'+n+'.'+(t-1e3*n-6e4*e).toString().substring(0,1)}function d(e){return function(){if(s.isFunction(e))e();else if(l.amc.fn.isString(e)){var t={action:{name:e}};document.submit(t)}else l.amc.fn.isObject(e)&&(t={action:e},document.submit(t))}}e.Dialog=a,e.getBtnAction=d},function(t,e,n){'use strict';var a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,'__esModule',{value:!0});var I=n(2),P=n(9),C=n(10),T=n(20),o=function(){function t(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=P.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return t.getComponentCSSRules=function(){throw new Error('E0100')},t.getComponentJson=function(){throw new Error('E0101')},t.prototype.mountTo=function(t,e){if(t){var n=this._acquireRootView();n?(e?t.insertBefore(n,e):t.appendChild(n),this._triggerOnMounted()):I.logger.e('Cmp#mT','E0103 '+n)}else I.logger.e('Cmp#mT','E0102 '+t)},Object.defineProperty(t.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),t.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},t.prototype.getMountedParentView=function(){var t=this.getMountedRootView();return t?t.parentNode:null},t.prototype.getSubComponentById=function(t,e){var n=this.debugName+'#SCById',o=this._subComponentsMap[t];if(!o)return null;var i='',r='';try{i=o.constructor.componentName,r=e.componentName}catch(t){I.logger.e(n,'E0104 '+t)}return i&&i===r?o:(I.logger.e(n,'E0105 '+i+', '+r),null)},t.prototype.getViewInComponentById=function(t){return this._viewsIdMap[t]},t.prototype.getComponentId=function(){return this._componentId},t.prototype.createStyledElement=function(t,e,n){var o=document.createElement(t);if(o)return t&&(o.className+=' '+this._css(t,2)),n&&(o.className+=' '+this._csses(n,1)),e&&(o.className+=' '+this._css('#'+e,2)),o},t.prototype.applyStyleTo=function(t,e){t&&(t.className+=' '+this._csses(e,1))},t.prototype.css=function(t){return this._css(t,0)},t.prototype.csses=function(t){return this._csses(t,0)},t.prototype._csses=function(t,e){var n=this;return t.split(' ').map(function(t){return n._css(t,e)}).join(' ')},t.prototype._css=function(t,e){if(!t)return'';var n=this._componentCSSRules;if(!n)return t;switch(t.charAt(0)){case'#':case'.':return n[t]||t;default:switch(e){case 0:return n['.'+t]||n[t]||t;case 1:return n['.'+t]||t;case 2:default:return t}}},t.prototype._triggerOnMounted=function(){new T.Observer(this.vueModel.data),I.logger.i('','I0106 '+this.debugName);for(var t=0,e=this._subComponents;t<e.length;t++){var n=e[t];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},t.prototype._getMethod=function(t){var e=this[t];return e instanceof Function?e:null},t.prototype._acquireComponentJson=function(){var t=this.debugName+'#acCJ',e=C.ComponentRegistry.getComponentJson(this._componentName);return e?(I.logger.i(t,'I0107'),e):void 0!==this._componentJson?(I.logger.i(t,'I0108'),C.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(I.logger.e(t,'E0109'),null)},t.prototype._acquireRootView=function(){var t=this.debugName+'#acRV';if(this._hasRootViewBuilt)return I.logger.i(t,'I0110'),this._rootView;var e=this._acquireComponentJson();return e?(this._rootView=this._convertJsonToBNNode(e,this.vueModel.data||{}),this._hasRootViewBuilt=!0,I.logger.i(t,'I0112'),this._rootView):(I.logger.e(t,'E0111'),null)},t.prototype._genArrayChildNode=function(t,e,n,o,i){var r=T.vueUtils.item2ArrayIndex(i,e),s=this._convertJsonToBNNode(t,a({},e,{item:n,index:o,arrayName:r}));return s?(s.setAttribute('index',o),s.setAttribute('for_name',i),s):null},t.prototype._convertJsonToBNNode=function(t,u){var p=this,e=this.debugName+'#cJTB';if(void 0===t._t)return null;var l=document.createElement(t._t),f=[];if(void 0!==t._cd)for(var n=function(s){if(s['v-for']||s['v-for-cal']){var t=!s['v-for']&&!!s['v-for-cal'],e=(t?g.vueModel.compute:u)||{},n=T.vueUtils.getObject(t?s['v-for-cal']:s['v-for'],e,t),a=t?T.vueUtils.rmSymbol(s['v-for-cal']):T.vueUtils.rmSymbol(s['v-for']);if(!a||!n)return'continue';for(var o in n)if(n.hasOwnProperty(o)){var i=g._genArrayChildNode(s,u,n[o],o,a);i&&f.push(i)}var c=document.createElement('div');c&&(c.style.display='none',c.setAttribute('for_end',a),f.push(c),new T.Watcher(a,e,function(t){if(l){T.rmWatchers(a);for(var e=0,n=l.childNodes;e<n.length;e++){var o=n[e];o.getAttribute('for_name')===a&&l.removeChild(o)}if(t)for(var i in t)if(t.hasOwnProperty(i)){var r=p._genArrayChildNode(s,u,t[i],i,a);r&&l.insertBefore(r,c)}}},t).id=u.arrayName)}else{var r=g._convertJsonToBNNode(s,u);if(!r)return'continue';f.push(r)}},g=this,o=0,i=t._cd;o<i.length;o++)n(i[o]);if(!l)return null;u&&u.index&&l.setAttribute('index',u.index);var r=t['bn-component']||t['sp-component'];if(r){I.logger.i(e,'I0113 '+r);var s=C.ComponentRegistry.createComponent(r);if(!s)return I.logger.e(e,'E0114 '+r+', '+s),null;var a=t['bn-component-id']||t['sp-component-id'];return a&&(s._componentId=a),s.onCreated&&s.onCreated(),I.logger.i(e,'I0115 '+s.debugName+', '+a),this._subComponents.push(s),a&&!this._subComponentsMap[a]&&(this._subComponentsMap[a]=s),s._acquireRootView()}var c=t['bn-view-id']||t['sp-view-id'];for(var d in c&&(I.logger.i(e,'I0116 '+c),this._viewsIdMap[c]||(this._viewsIdMap[c]=l)),t._i&&(l.id=t._i),t._c&&(l.className=t._c),t._s&&(l.style.cssText=t._s),t._x&&(l.innerText=t._x),t._y&&(l.type=t._y),t)if(t.hasOwnProperty(d))if(0===d.indexOf('on')){var h=this._getMethod(t[d]);h&&(l[d]=h.bind(this,l))}else if(0===d.indexOf('_'));else if(0===d.indexOf('bn-')||0===d.indexOf('sp-'));else if(P.startsWith(d,'v-')){var m=d.split('-');if(2===m.length||3===m.length){var _=m[1];2===m.length?new T.NodeCompile(u).compile(_,l,t[d],t._t):'cal'===m[2]&&new T.NodeCompile(this.vueModel.compute,!0).compile(_,l,t[d],t._t)}else l[d]=t[d]}else l[d]=t[d];for(var v=0,y=f;v<y.length;v++){var b=y[v];l.appendChild(b)}return l},t.componentName='',t.componentHTML='',t.componentCSS='',t.componentHashName='',t}();e.BNComponent=o},function(module,exports,__webpack_require__){'use strict';Object.defineProperty(exports,'__esModule',{value:!0});var util_1=__webpack_require__(9),amc_types_1=__webpack_require__(0),watchers;function notify(){watchers&&watchers.forEach(function(t){t.update()})}function rmWatchers(e){watchers=watchers.filter(function(t){return t.id!==e})}exports.rmWatchers=rmWatchers;var Watcher=function(){function t(t,e,n,o){if(this.id='',this.lazy=!1,e&&'object'==typeof e){if(this.lazy=o,this.callback=n,util_1.startsWith(t,'item')&&e.arrayName&&e.index){var i=t.replace('item','');this.expression=e.arrayName+'.'+e.index,i&&(this.expression+=i)}else this.expression=t;this.data=e,this.value=vueUtils.getVal(t,e,this.lazy),watchers||(watchers=[]),watchers.push(this)}}return t.prototype.update=function(){if(this.data&&this.expression&&this.callback){var t=vueUtils.getVal(this.expression,this.data,this.lazy),e=this.value;vueUtils.equals(t,e)||(this.value=t,this.callback(t))}},t}();exports.Watcher=Watcher;var Observer=function(){function t(t){this.observe(t)}return t.prototype.observe=function(e){var n=this;e&&'object'==typeof e&&Object.keys(e).forEach(function(t){try{n.defineReactive(e,t,e[t]),n.observe(e[t])}catch(t){}})},t.prototype.defineReactive=function(t,e,n){var o=this;Object.defineProperty(t,e,{enumerable:!0,configurable:!1,get:function(){return n},set:function(t){vueUtils.equals(t,n)||(n=t,o.observe(t),notify())}})},t}();exports.Observer=Observer;var NodeCompile=function(){function t(t,e){void 0===e&&(e=!1),this.data=t||{},this.lazy=e}return t.prototype.compile=function(n,t,e,o){var i=this;if(t)switch(n){case'text':this.labelProcess(t,e,function(t,e){t.innerText=void 0===e?'':e});break;case'html':this.labelProcess(t,e,function(t,e){t.innerHtml=void 0===e?'':e});break;case'class':this.labelProcess(t,e,function(t,e){var n=t.className,o=t.getAttribute('v-class-name')||'';n=n&&n.replace(o,'').replace(/\\s+$/,''),t.setAttribute('v-class-name',e),t.className=n?n+' '+e:e});break;case'style':this.eventProcess(t,e,function(t,e){var n=util_1.tryJSONParse(e);util_1.copyObj(n,t.style)});break;case'model':this.eventProcess(t,e,function(t,e){t.value=e}),'input'===o?t.oninput=function(){vueUtils.setTextVal(e,t.value,i.data)}:'switch'===o&&(t.onchange=function(t){vueUtils.setTextVal(e,t||'off',i.data)});break;case'if':this.eventProcess(t,e,function(t,e){!0===e?(t.style.display='flex',spmUtils.process(t,function(t){amc_types_1.amc.fn.spmExposure(t.spmId,t.param4Map,t.doNotResume)})):t.style.display='none'});break;case'spm':this.labelProcess(t,e,function(t,e){t.setAttribute('spm',void 0===e?'':e)});break;case'uep':this.labelProcess(t,e,function(t,e){e&&util_1.startsWith(e,'a283')&&t.setAttribute('behaviorInfo',JSON.stringify({spm:e,bizCode:'pay',extParam:{}}))});break;case'click':this.eventProcess(t,e,function(t,e){vueUtils.isFunction(e)?t.onclick=function(){e(t),spmUtils.process(t,function(t){amc_types_1.amc.fn.spmClick(t.spmId,t.param4Map)})}:t.onclick=function(){}});break;case'focus':this.eventProcess(t,e,function(t,e){vueUtils.isFunction(e)?t.onfocus=function(){e(t)}:t.onfocus=function(){}});break;case'blur':this.eventProcess(t,e,function(t,e){vueUtils.isFunction(e)?t.onblur=function(){e(t)}:t.onfocus=function(){}});break;default:-1===e.indexOf('@{')?t.setAttribute(n,e):this.labelProcess(t,e,function(t,e){t.setAttribute(n,void 0===e?'':e)})}},t.prototype.labelProcess=function(n,o,i){var r=this,t=o.match(/@\\{([^}]+)\\}/g),e=o;t&&0<t.length&&(e=vueUtils.getTextVal(o,this.data,this.lazy),t&&t.forEach(function(t){var e=/@\\{([^}]+)\\}/g.exec(t);e&&1<e.length&&(new Watcher(e[1],r.data,function(t){i(n,vueUtils.getTextVal(o,r.data,r.lazy))},r.lazy).id=r.data.arrayName)})),i(n,e)},t.prototype.eventProcess=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(t),i=vueUtils.getObject(t,this.data,this.lazy);o&&1<o.length&&(new Watcher(o[1],this.data,function(t){n(e,t)},this.lazy).id=this.data.arrayName),n(e,i)},t}();exports.NodeCompile=NodeCompile;var spmUtils=function(){function t(){}return t.process=function(t,e){var n=t.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&e(o)}catch(t){}},t}(),vueUtils=function(){function vueUtils(){}return vueUtils.item2ArrayIndex=function(t,e){var n=t;if(util_1.startsWith(t,'item')&&e.arrayName&&e.index){var o=t.replace('item','');n=e.arrayName+'.'+e.index,o&&(n+=o)}return n},vueUtils.getVal=function(expr,data,lazy){if(expr){var values=expr.match(/##([^#]+)##/g);if(values&&0<values.length){for(var func_1=expr,index=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(/##([^#]+)##/,'vueArgs['+index+']'),index++;for(var _vueArgs=[],i=0;i<index;i++)_vueArgs.push(this.getRealVal(values[i].replace(/##/g,''),data,lazy));return function(vueArgs){return eval(func_1)}(_vueArgs)}return this.getRealVal(expr,data,lazy)}},vueUtils.getRealVal=function(t,e,n){if(t){var o=t.split('.'),i=n&&vueUtils.isFunction(e[o[0]])?e[o[0]]():e;return(n?o.slice(1):o).reduce(function(t,e){return t[e]},i)}},vueUtils.getTextVal=function(t,i,r){var s=this;return t.replace(/@\\{([^}]+)\\}/g,function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(r)t=s.getVal(e[1],i,r);else{var o=vueUtils.item2ArrayIndex(e[1],i);t=s.getVal(o,i,!1)}return void 0===t?'':t})},vueUtils.getObject=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(t);if(o&&1<o.length)return this.getVal(o[1],e,n)},vueUtils.rmSymbol=function(t){var e=/@\\{([^}]+)\\}/g.exec(t);return e&&1<e.length?e[1]:''},vueUtils.setVal=function(t,o,e){var i=t.split('.');return i.reduce(function(t,e,n){return n===i.length-1?t[e]=o:t[e]},e)},vueUtils.setTextVal=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(t);o&&1<o.length&&this.setVal(o[1],e,n)},vueUtils.equals=function(t,e){return this.eq(t,e,void 0,void 0)},vueUtils.eq=function(t,e,n,o){if(t===e)return 0!==t||1/t==1/e;if(null==t||null==e)return t===e;var i=toString.call(t);if(i!==toString.call(e))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+t==''+e;case'[object Number]':return+t!=+t?+e!=+e:0==+t?1/+t==1/e:+t==+e;case'[object Date]':case'[object Boolean]':return+t==+e}var r='[object Array]'===i;if(!r){if('object'!=typeof t||'object'!=typeof e)return!1;var s=t.constructor,a=e.constructor;if(s!==a&&!(this.isFunction(s)&&s instanceof s&&this.isFunction(a)&&a instanceof a)&&'constructor'in t&&'constructor'in e)return!1}o=o||[];for(var c=(n=n||[]).length;c--;)if(n[c]===t)return o[c]===e;if(n.push(t),o.push(e),r){if((c=t.length)!==e.length)return!1;for(;c--;)if(!this.eq(t[c],e[c],n,o))return!1}else{var u=Object.keys(t),p=void 0;if(c=u.length,Object.keys(e).length!==c)return!1;for(;c--;)if(p=u[c],!e.hasOwnProperty(p)||!this.eq(t[p],e[p],n,o))return!1}return n.pop(),o.pop(),!0},vueUtils.isFunction=function(t){return'function'==typeof t||!1},vueUtils}();exports.vueUtils=vueUtils},function(t,e,n){'use strict';var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var r=function(){function r(){this.enabled=!0}return r.fmtLine=function(t,e,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+t+']['+r.fmtTime()+']['+e+']'+n+' '+i},r.fmtTime=function(){var t=new Date;return t.getHours()+':'+t.getMinutes()+':'+t.getSeconds()+'.'+t.getMilliseconds()},r.prototype.enable=function(){this.enabled=!0},r.prototype.disable=function(){this.enabled=!1},r}();e.Logger=r,e.logger=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.e=function(t,e,n){},e.prototype.i=function(t,e,n){},e}(r))},function(t,n,e){'use strict';Object.defineProperty(n,'__esModule',{value:!0});var o=e(23),i=e(5),r=e(1);n.props={viPlugin:new o.VIPlugin,dialog:{},store:new function(){this.spmServerParam4Str={}},getButton:function(){return n.props.viPlugin.getActiveButton()},getAboveButtonBox:function(){return n.props.viPlugin.getAboveButtonBox()},modifyButtonNormalText:function(t){var e=n.props.viPlugin.getActiveButton();e&&e.setText(o.VIPlugin.VIButtonText2ButtonText({normal:t,verifying:'{{verifying}}',paying:'{{paying}}',success:'{{pay_ok}}   '})).changeLoadingStatus(i.BUTTON_STATUS.NORMAL,i.LOADING_TYPE.CIRCLE,t)},modifyConfirmActionParams:function(t){n.props.store.confirmAction.params=r.mergeObject(n.props.store.confirmAction.params,t)}}},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(11);e.VI_STATUS=o.VI_STATUS,e.EXIT_VI_SCENE=o.EXIT_VI_SCENE,e.RETAIN_VI_SCENE=o.RETAIN_VI_SCENE,e.VI_LOG_ACTION_TYPE=o.VI_LOG_ACTION_TYPE,e.VI_ACTION=o.VI_ACTION,e.VI_TYPE=o.VI_TYPE,e.VIPlugin=o.VIPlugin;var i=n(25);e.buildConfirmActionWithAct=i.buildConfirmActionWithAct,e.commonStartVIPlugin=i.commonStartVIPlugin,e.commonProcessVIToPWD=i.commonProcessVIToPWD,e.gotoPwdPage=i.gotoPwdPage,e.processVIRpcRequest=i.processVIRpcRequest,e.commonProcessVIResult=i.commonProcessVIResult,e.processVIStatus=i.processVIStatus,e.pwdFreePay=i.pwdFreePay},function(t,e,n){'use strict';var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,'__esModule',{value:!0});var r=n(5),s=n(2),a=n(3);s.ComponentRegistry.registerComponent(r.Button.componentName,r.Button);var c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={buttonsNum:0,container:{},buttons:[],onClick:function(t){}},t.onCreated=function(){},t.onMounted=function(){t.props.container=t.getViewInComponentById('buttonContainer')},t}return i(t,e),t.prototype.addButton=function(t,e){var n=this;0===this.props.buttons.length&&this.initDom(),this.props.buttons.push(t),t.mountTo(this.props.container);var o=this.props.buttons.length-1;t.setOnClick(function(){n.props.onClick(o),e&&e()})},t.prototype.setOnClick=function(t){this.props.onClick=t},t.prototype.setVisible=function(t){return a.modifyElementStyle(this,'buttonContainer',{display:t?'flex':'none'}),this},t.prototype.affectAllButtons=function(t){for(var e=0;e<this.props.buttons.length;e++)this.props.buttons[e].changeLoadingStatus(t)},t.prototype.initDom=function(t){void 0===t&&(t=!1),t?a.modifyElementClass(this,this.props.container,'',!1):this.applyStyleTo(this.props.container,'pay-btn-box')},t.getComponentCSSRules=function(){return{'.pay-btn-box':'_ButtonGroup_gf2g-c-pay-btn-box'}},t.getComponentJson=function(){return{'sp-view-id':'buttonContainer',_t:'div'}},t.componentName='ButtonGroup',t.componentHashName='ButtonGroup_gf2g',t}(s.BNComponent);e.ButtonGroup=c},function(t,e,n){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var o=n(26),s=n(11),a=n(12),c=n(0);function r(t,e,n,o,i){void 0===i&&(i=!1);var r={action:{name:'loc:bnvb'},param:{tplid:'QUICKPAY@cashier-pwd-check-flex',tpl:e.viPwdTpl,data:{payOrder:n,pwdTip:e.pwdTip,logon_id:e.logon_id,VIData:t,confirmAct:o}}};document.submit(r)}e.buildConfirmActionWithAct=function(t,e){var n={name:t};return e&&e.name&&(n=e),n.loadtxt='',n.viChannelMode=o.VI_CHANNEL_MODE_FROM_TEMPLATE,n},e.commonStartVIPlugin=function(t,e){var n=e.showAgreement?'{{agree_auth_fp}}':'{{auth_finger_or}}',o=e.isHuaWeiFinger?s.VI_ACTION.START_HUAWEI:s.VI_ACTION.START,i=e.isHuaWeiFinger?{hwPaySuccessText:'{{pay_success}}',hwAuthingText:'{{auth_fingering}}',hwPayingText:'{{paying}}',hwRetryText:'{{retry_finger}}',hwAuthTip:n,hwInputPwdTip:'{{input_pwd_s}}'}:{fpAlertMsg:'{{confirm_exit}}',fpAlertCancelBtn:'{{confirm_btn}}',fpAlertToPwdBtn:'{{input_pwd_s}}',shouldDismissAfterAuth:'Y',pwdInputBtn:'{{confirm_btn}}',pwdPlaceHolder:'{{alipaypwd}}',pwdInputTip:e.rpcData.pwdTip,loadingPayingText:e.rpcData.doingText||'{{paying}}',loadingPaySuccessText:e.rpcData.successText||'{{pay_ok}}',LoginId:e.rpcData.logon_id,costTip:e.rpcData.costTip||'',usePwd:e.usePwd?'Y':'N',pwdBtnShow:e.rpcData.rightBtn?'Y':'N',kVIDisableForgetPwdKey:e.kVIDisableForgetPwdKey?'Y':'N',viSourcePage:e.viSourcePage||''};e.setViCallBack&&(e.isHuaWeiFinger?e.setViCallBack.HuaWei&&e.setViCallBack.HuaWei():e.setViCallBack.standard&&e.setViCallBack.standard()),e.viPlugin.startVIPlugin(o,t,i)},e.commonProcessVIToPWD=function(t,e){var n={};if(c.amc.fn.isString(t))try{n=JSON.parse(t)}catch(t){c.amc.fn.logError('pVI2PWD','json parse error')}else n=t;var o=n.version,i=n.usePwd;a.logAction('VIToPwD-'+(o||'version-'+(i||'usePwd')),s.VI_LOG_ACTION_TYPE),'2.0'!==o?(e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL),r(t,e.rpcData,e.canGoBack,e.confirmAction,'Y'===i)):e.startVIPlugin(t,!1,'Y'===i)},e.gotoPwdPage=r,e.processVIRpcRequest=function(t,e){e.submitPay(t,function(t){'0001'===t.status&&e.viPlugin.onPaySuccess(),e.viPlugin.getIsPaySuccessShown()||('1'===t.pageloading?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.PAYING):'0'===t.pageloading&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL))})&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.PAYING)},e.commonProcessVIResult=function(t,e){var n={};if(c.amc.fn.isString(t))try{n=JSON.parse(t)}catch(t){c.amc.fn.logError('processVIResult','json parse error')}else n=t;a.logAction('viResult-'+(n.code||'code')+'-'+(e.viPlugin.getVersion()||'version')+'-'+(e.viPlugin.getPwdType()||'VIType'),s.VI_LOG_ACTION_TYPE),'2.0'!==e.viPlugin.getVersion()||'1000'!==n.code||e.viPlugin.getPwdType()!==s.VI_TYPE.PASSWORD&&e.viPlugin.getPwdType()!==s.VI_TYPE.SHORT_PASSWORD||(c.amc.isIOS?c.amc.fn.showLoading(!0,!0):setTimeout(function(){document.invoke('showLoading')},10))},e.processVIStatus=function(t,e){if(t){var n=t.status,o=t.type,i=t.version,r=t.scene;a.logAction((o||'type')+'-'+(n||'status')+'-'+(i||'version')+'-'+(r||'scene'),s.VI_LOG_ACTION_TYPE),'start'===n?(e.setCanGoBack('wl'!==o),o!==s.VI_TYPE.PASSWORD&&o!==s.VI_TYPE.SHORT_PASSWORD||'2.0'===i?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.VERIFYING):e.processVIToPWD()):'end'===n?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):'abort'===n?s.RETAIN_VI_SCENE.hasOwnProperty(r)?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):s.EXIT_VI_SCENE.hasOwnProperty(r)?document.submit({action:{name:'loc:exit'}}):o===s.VI_TYPE.SHORT_PASSWORD||o===s.VI_TYPE.PASSWORD?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):c.amc.fn.exit():'awaitUser'===n&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL)}},e.pwdFreePay=function(t,e){var n={action:t};n.action.neec='6004',e.toggleBioProcessLoading(s.VI_STATUS.VERIFYING),document.asyncSubmit(n,function(t){if(!e.getIsPaySuccessShown())switch(t.status){case'0001':e.onPaySuccess();break;case'0000':case'0002':case'0006':e.onPayFailure()}})}},function(t,e,n){'use strict';var o;Object.defineProperty(e,'__esModule',{value:!0}),e.VI_CHANNEL_MODE_FROM_TEMPLATE='1',(o=e.SCALE_FACTOR||(e.SCALE_FACTOR={})).LEVEL_0='0',o.LEVEL_1='1',o.LEVEL_2='2',o.LEVEL_3='3',o.LEVEL_4='4'}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-i-body", "children": [{"css": "amc-1px-title-line", "tag": "div", "id": "iLine"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-litepay-confirm-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0072", "tplId": "QUICKPAY@cashier-litepay-confirm-flex", "tplVersion": "5.4.1"}