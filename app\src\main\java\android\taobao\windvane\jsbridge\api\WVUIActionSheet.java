package android.taobao.windvane.jsbridge.api;

import android.taobao.windvane.jsbridge.WVCallBackContext;
import android.taobao.windvane.view.PopupWindowController;
import android.text.TextUtils;
import android.view.View;
import com.android.alibaba.p078ip.runtime.InstantReloadException;
import com.android.alibaba.p078ip.runtime.IpChange;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import p277tb.enx;
import p277tb.kpw;
import p277tb.nsw;
import p277tb.t2o;
import p277tb.v7t;

/* compiled from: Taobao */
/* loaded from: classes.dex */
public class WVUIActionSheet extends kpw {
    public static volatile transient /* synthetic */ IpChange $ipChange = null;
    private static final String TAG = "WVUIActionSheet";
    private String _index;
    private PopupWindowController mPopupWindowController;
    private WVCallBackContext mCallback = null;
    private View.OnClickListener popupClickListener = new ViewOnClickListenerC0151a();

    /* compiled from: Taobao */
    /* renamed from: android.taobao.windvane.jsbridge.api.WVUIActionSheet$a */
    public class ViewOnClickListenerC0151a implements View.OnClickListener {
        public static volatile transient /* synthetic */ IpChange $ipChange;

        public ViewOnClickListenerC0151a() {
        }

        @Override // android.view.View.OnClickListener
        public void onClick(View view) {
            IpChange ipChange = $ipChange;
            if (ipChange instanceof IpChange) {
                ipChange.ipc$dispatch("8dfcefe2", new Object[]{this, view});
                return;
            }
            nsw nswVar = new nsw();
            nswVar.m130775b("type", (String) view.getTag());
            nswVar.m130775b(enx.INDEX_KEY, WVUIActionSheet.access$000(WVUIActionSheet.this));
            if (v7t.m160253h()) {
                v7t.m160246a(WVUIActionSheet.TAG, "ActionSheet: click: 8.5.0");
            }
            WVUIActionSheet.access$100(WVUIActionSheet.this).m179f(true);
            nswVar.m130784k();
            if (WVUIActionSheet.access$200(WVUIActionSheet.this) != null) {
                WVUIActionSheet.access$200(WVUIActionSheet.this).success(nswVar);
                WVUIActionSheet.access$200(WVUIActionSheet.this).fireEvent("wv.actionsheet", nswVar.m130786m());
            }
        }
    }

    static {
        t2o.m151972a(1031799364);
    }

    public static /* synthetic */ String access$000(WVUIActionSheet wVUIActionSheet) {
        IpChange ipChange = $ipChange;
        return ipChange instanceof IpChange ? (String) ipChange.ipc$dispatch("11abb722", new Object[]{wVUIActionSheet}) : wVUIActionSheet._index;
    }

    public static /* synthetic */ PopupWindowController access$100(WVUIActionSheet wVUIActionSheet) {
        IpChange ipChange = $ipChange;
        return ipChange instanceof IpChange ? (PopupWindowController) ipChange.ipc$dispatch("26ad8f74", new Object[]{wVUIActionSheet}) : wVUIActionSheet.mPopupWindowController;
    }

    public static /* synthetic */ WVCallBackContext access$200(WVUIActionSheet wVUIActionSheet) {
        IpChange ipChange = $ipChange;
        return ipChange instanceof IpChange ? (WVCallBackContext) ipChange.ipc$dispatch("b892efd3", new Object[]{wVUIActionSheet}) : wVUIActionSheet.mCallback;
    }

    public static /* synthetic */ Object ipc$super(WVUIActionSheet wVUIActionSheet, String str, Object... objArr) {
        str.hashCode();
        throw new InstantReloadException("String switch could not find '" + str + "' with hashcode " + str.hashCode() + " in android/taobao/windvane/jsbridge/api/WVUIActionSheet");
    }

    @Override // p277tb.kpw, com.uc.webview.export.extension.IEmbedViewContainer.OnStateChangedListener
    public void onDestroy() {
        IpChange ipChange = $ipChange;
        if (ipChange instanceof IpChange) {
            ipChange.ipc$dispatch("a6532022", new Object[]{this});
        } else {
            this.mCallback = null;
        }
    }

    @Override // p277tb.kpw
    public boolean execute(String str, String str2, WVCallBackContext wVCallBackContext) {
        IpChange ipChange = $ipChange;
        if (ipChange instanceof IpChange) {
            return ((Boolean) ipChange.ipc$dispatch("bcd41fd1", new Object[]{this, str, str2, wVCallBackContext})).booleanValue();
        }
        if (!"show".equals(str)) {
            return false;
        }
        show(wVCallBackContext, str2);
        return true;
    }

    public synchronized void show(WVCallBackContext wVCallBackContext, String str) {
        String[] strArr;
        synchronized (this) {
            IpChange ipChange = $ipChange;
            if (ipChange instanceof IpChange) {
                ipChange.ipc$dispatch("1a2dc1cb", new Object[]{this, wVCallBackContext, str});
                return;
            }
            String[] strArr2 = null;
            String str2 = null;
            if (TextUtils.isEmpty(str)) {
                strArr = null;
            } else {
                try {
                    JSONObject jSONObject = new JSONObject(str);
                    String optString = jSONObject.optString("title");
                    this._index = jSONObject.optString(enx.INDEX_KEY);
                    JSONArray optJSONArray = jSONObject.optJSONArray("buttons");
                    if (optJSONArray != null) {
                        if (optJSONArray.length() > 8) {
                            v7t.m160259n(TAG, "WVUIDialog: ActionSheet is too long, limit 8");
                            nsw nswVar = new nsw();
                            nswVar.m130783j("HY_PARAM_ERR");
                            nswVar.m130775b("msg", "ActionSheet is too long. limit 8");
                            wVCallBackContext.error(nswVar);
                            return;
                        }
                        strArr2 = new String[optJSONArray.length()];
                        for (int i = 0; i < optJSONArray.length(); i++) {
                            strArr2[i] = optJSONArray.optString(i);
                        }
                    }
                    strArr = strArr2;
                    str2 = optString;
                } catch (JSONException unused) {
                    v7t.m160249d(TAG, "WVUIDialog: param parse to JSON error, param=" + str);
                    nsw nswVar2 = new nsw();
                    nswVar2.m130783j("HY_PARAM_ERR");
                    wVCallBackContext.error(nswVar2);
                    return;
                }
            }
            this.mCallback = wVCallBackContext;
            try {
                PopupWindowController popupWindowController = new PopupWindowController(this.mContext, this.mWebView.getView(), str2, strArr, this.popupClickListener);
                this.mPopupWindowController = popupWindowController;
                popupWindowController.m182i();
                v7t.m160246a(TAG, "ActionSheet: show");
            } catch (Exception e) {
                v7t.m160259n(TAG, e.getMessage());
                nsw nswVar3 = new nsw();
                nswVar3.m130775b("errMsg", e.getMessage());
                wVCallBackContext.error(nswVar3);
            }
        }
    }
}
