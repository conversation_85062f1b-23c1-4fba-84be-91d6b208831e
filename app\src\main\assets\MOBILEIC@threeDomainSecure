{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/vi-amc-meta.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc-meta.js", "tag": "script"}, {"children": [{"tag": "text", "text": "//入口参数\n        var G_enterParams = amc.rpcData || {};\n        // var G_moduleData = JSON.parse(G_enterParams[\"moduleData\"] || \"{}\") || {};\n        var G_initParams = G_enterParams['InitParams'] || {};\n        var G_moduleInfo = G_enterParams[\"moduleInfo\"] || {};\n        //全局变量\n        var g_verifyId = G_initParams['verifyId'] || '';\n        var g_token = G_initParams['token'] || '';\n        // var g_sceneId = G_initParams['sceneId'] || '';\n        var g_moduleName = G_moduleInfo['moduleName'] || '';\n        var g_callWebViewUrl = G_enterParams.callWebViewUrl || '';\n\n        function init() {\n            if (isParamsValid()) {\n                logWithUcid('UC-MobileIC-221031-1', { \"init\": \"success\" });\n                loadWebViewUrl();\n            } else {\n                logWithUcid('UC-MobileIC-221031-1', { \"init\": \"fail\" });\n                notifyParamsInvalid();\n            }\n        }\n\n        function notifyParamsInvalid() {\n            var obj = {\n                \"eventName\": \"vi_direct_jump_failed\",\n                \"moduleName\": g_moduleName\n            };\n            document.asyncSubmit(obj, function (data) {\n            });\n        }\n\n        function loadWebViewUrl() {\n            var params = {};\n            params['action_name'] = 'openUrl';\n            params['useViWebView'] = 'Y';\n            params['url'] = g_callWebViewUrl || '';\n            var obj = {};\n            obj['eventName'] = 'vi_external_action';\n            obj['module'] = g_moduleName;\n            obj['params'] = params || {};\n            document.asyncSubmit(obj, function (data) {\n            });\n        }\n\n        function isParamsValid() {\n            return isValidStr(g_callWebViewUrl);\n        }\n\n        function isValidStr(str) {\n            return (amc.fn.isString(str) && str.length > 0);\n        }\n\n        function logWithUcid(ucId, extParams) {\n            if (!isValidStr(ucId)) {\n                return;\n            }\n            var params = {\n                \"action_name\": \"recordLog\",\n                \"caseId\": ucId || '',\n            }\n            extParams = extParams || {};\n            for (var i in extParams) {\n                params[i] = extParams[i];\n            }\n            var obj = {\n                \"eventName\": \"vi_external_action\",\n                \"moduleName\": g_moduleName,\n                \"params\": params || {}\n            };\n            document.asyncSubmit(obj, function (data) {\n            });\n        }\n\n        function onViWebViewExit(paramStr) {\n            var params = JSON.parse(paramStr) || {}\n            var doVerify = params['doVerify'] || '';\n            logWithUcid('UC-MobileIC-221031-2', { \"doVerify\": doVerify });\n            if ('true' !== doVerify) {\n                var finalResult = {};\n                finalResult['eventName'] = 'vi_quit_module';\n                document.submit(finalResult);\n                return\n            }\n\n            obj = {};\n            obj['showLoading'] = 'true';\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['params'] = {};\n            obj['moduleName'] = g_moduleName;\n            obj['actionName'] = 'VERIFY_THREE_DOMAIN_SECURE';\n\n            document.asyncSubmit(obj, function (data) {\n                if (Boolean(data[\"verifySuccess\"])) {\n                    //验证成功\n                    document.toast({\n                        text: \"{{verify_success}}\",\n                    }, function () { });\n                    mic.fn.onBackWithResponse(data);\n                    return;\n                } else {\n                    var dialogMsg = data[\"verifyMessage\"] || \"人气太旺了，请稍后再试\";\n                    amc.fn.viAlert({\n                        \"title\": \"\",\n                        \"message\": dialogMsg,\n                        \"button\": '{{got_it}}'\n                    }, function () {\n                        mic.fn.onBackWithResponse(data);\n                    });\n                }\n            });\n        }"}], "tag": "script"}, {"tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "tag": "body", "id": "body", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "threeDomainSecure", "format": "JSON", "tag": "MOBILEIC", "time": "0011", "tplId": "MOBILEIC@threeDomainSecure", "tplVersion": "5.8.4"}