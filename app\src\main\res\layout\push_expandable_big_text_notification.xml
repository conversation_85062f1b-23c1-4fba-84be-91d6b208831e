<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <RelativeLayout
        android:id="@+id/push_big_pic_default_Content"
        android:background="@android:color/transparent"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:id="@+id/push_big_defaultView"
            android:layout_width="match_parent"
            android:layout_height="65dp">
            <ImageView
                android:id="@+id/push_big_notification_icon"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_marginLeft="10dp"
                android:scaleType="centerInside"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"/>
            <ImageView
                android:id="@+id/push_big_notification_icon2"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginRight="5dp"
                android:scaleType="centerInside"
                android:layout_alignBottom="@+id/push_big_notification_icon"
                android:layout_alignParentRight="true"/>
            <RelativeLayout
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:id="@+id/push_big_notification"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="0dp"
                android:layout_toRightOf="@+id/push_big_notification_icon">
                <DateTimeView
                    android:textSize="12sp"
                    android:textColor="#ffffff"
                    android:id="@+id/push_big_notification_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:layout_marginRight="10dp"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"
                    android:alpha="0.6"
                    style="@android:style/TextAppearance.StatusBar.EventContent"/>
                <TextView
                    android:textSize="18sp"
                    android:textStyle="normal"
                    android:textColor="#ffffff"
                    android:id="@+id/push_big_notification_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:layout_toLeftOf="@+id/push_big_notification_date"
                    style="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
                <TextView
                    android:textSize="14sp"
                    android:textStyle="normal"
                    android:textColor="#ffffff"
                    android:id="@+id/push_big_notification_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:layout_marginRight="10dp"
                    android:singleLine="true"
                    android:layout_below="@+id/push_big_notification_title"
                    style="@android:style/TextAppearance.StatusBar.EventContent"/>
            </RelativeLayout>
        </RelativeLayout>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:id="@+id/push_big_text_notification_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="69dp"
            android:layout_marginTop="34dp"
            android:layout_marginRight="13dp"
            android:layout_marginBottom="10dp">
            <TextView
                android:textSize="14sp"
                android:textStyle="normal"
                android:textColor="#ffffff"
                android:ellipsize="end"
                android:id="@+id/push_big_bigtext_defaultView"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="192dp"
                android:alpha="0.9"
                style="@android:style/TextAppearance.StatusBar.EventContent"/>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
