{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"modify_phone_num": "修改", "card_owner_name": "持卡人姓名", "next_step": "同意協議並下一步", "phone_tip": "手機號是你在辦理該銀行卡時所填寫的手機號或該銀行認可的協力廠商機构（如電信運營商等）辦理的本人手機號碼。", "enter_phone": "與銀行記錄相同", "phone_tip_5": "為了提升您的服務體驗，避免重複填寫信息，將為您智能推薦填寫您名下支付寶賬號近期綁定銀行卡時已通過驗證的手機號。", "bank_card_type": "銀行卡類型", "reserved_phone_no": "銀行預留手機號"}, "zh_TW": {"modify_phone_num": "修改", "card_owner_name": "持卡人姓名", "next_step": "同意協議並下一步", "phone_tip": "手機號是你在辦理該銀行卡時所填寫的手機號或該銀行認可的協力廠商機构（如電信運營商等）辦理的本人手機號碼。", "enter_phone": "與銀行記錄相同", "phone_tip_5": "為了提升您的服務體驗，避免重複填寫信息，將為您智能推薦填寫您名下支付寶賬號近期綁定銀行卡時已通過驗證的手機號。", "bank_card_type": "銀行卡類型", "reserved_phone_no": "銀行預留手機號"}, "en_US": {"modify_phone_num": "Modify", "phone_tip": "The mobile number is the one you provided for card application or applied from the telecom operator authorized by the bank.", "enter_phone": "Same as bank records", "phone_tip_4": "For further help, call us at +*************.", "reserved_phone_no": "Bank Reserved Phone Number", "phone_tip_3": "If your phone no. is not a Chinese number，please enter “country code-phone no.”.", "phone_tip_2": "If the phone no. is not registered in your bank，forgotten or out of use, please contact the bank for help.", "phone_tip_1": "Phone no. should be the same as the number you left in the bank. It is recommended to fill in the form according to the bank document.", "user_name_title": "Cardholder&apos;s name", "card_owner_name": "Card owner name", "next_step": "Agree and Next", "phone": "Phone Number Description", "phone_tip_5": "To improve your service experience and avoid filling in repeated information, you will be intellegetly recommended the phone no. that hads passed the verification when you added the lastest bank card.", "bank_card_type": "Bank Card Type", "name_tip_2": "If you forget the name format or you changed your name, please contact the bank for help.", "name_tip_3": "For further help, call us at +*************.", "name_tip_1": "Cardholder&apos;s name should be exactly the same as the name you left in the bank, including: the case sensitivity, name order, spaces, special symbols, etc. It is recommended to fill in the form according to the name on the bank document."}, "zh_CN": {"modify_phone_num": "修改", "card_owner_name": "持卡人姓名", "next_step": "同意协议并下一步", "phone_tip": "手机号是你在办理该银行卡时所填写的手机号或该银行认可的第三方机构（如电信运行商等）办理的本人手机号码。", "enter_phone": "请输入手机号", "phone_tip_5": "为了提升您的服务体验，避免重复填写信息，将为您智能推荐填写您名下支付宝账号近期绑定银行卡时已通过验证的手机号。", "bank_card_type": "银行卡类型", "reserved_phone_no": "银行预留手机号"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": ".amc-nav-container-android{display:flex;height:48px;flex-direction:column}.amc-nav-box-android{align-items:center;display:flex;flex:1.0;justify-content:center;height:47px;background-color:#f5f5f5}.amc-nav-r-box-android:active{opacity:0.4}.amc-nav-l-img-box-android:active{opacity:0.4}.amc-nav-horiz-line-android{background-color:#f5f5f5;height:0PX}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._CardDetailPage_jorn-c-amc-nav-r-box{display:none}._CardDetailPage_jorn-c-amc-cell-l-box{width:85px;margin-right:5px}._CardDetailPage_jorn-c-card-name{max-width:150px}._CardDetailPage_jorn-c-protocol-agree{max-width:100px;color:#999}._CardDetailPage_jorn-c-protocol-font-size{font-size:16px}._CardDetailPage_jorn-c-align-start{align-items:flex-start}._CardDetailPage_jorn-c-l-label-ellipsis{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box}._CardDetailPage_jorn-c-detail-cell{align-items:center;background-color:#fff;padding-left:11px;padding-right:4px;height:60px;margin-top:8px}._CardDetailPage_jorn-c-detail-cell-left-width{justify-content:flex-start;align-items:center}._CardDetailPage_jorn-c-detail-cell-right-width{justify-content:flex-end;align-items:center}._CardDetailPage_jorn-c-dlg-txt{-webkit-line-clamp:9;-webkit-box-orient:vertical;display:-webkit-box}._CardDetailPage_jorn-c-tip-info-box{padding-bottom:8px}._CardDetailPage_jorn-c-tip-info-text{font-size:13px;color:#999}._CardDetailPage_jorn-c-dlg-title-box{margin-top:21px;margin-bottom:6px;padding:0 15px}._CardDetailPage_jorn-c-dlg-title{font-size:18px;font-weight:bold;text-align:center;flex:1}._CardDetailPage_jorn-c-dlg-dot{margin-top:7px;width:4px;height:4px;background-color:#777;border-radius:2px}._CardDetailPage_jorn-c-dlg-list{font-size:15px;margin-left:6px}._CardDetailPage_jorn-c-tip-box{padding:9px 15px 0 15px}._CardDetailPage_jorn-c-dlg-btn-text{font-size:17px;text-align:center;margin:11.5px 15px}._CardDetailPage_jorn-c-dlg-line{margin-top:18px}._CardDetailPage_jorn-c-bottom-box{justify-content:flex-end;height:40px;margin-top:15px;padding-top:20px}._CardDetailPage_jorn-c-sticky-bottom-box{justify-content:flex-end;height:47px;padding-bottom:15px;padding-top:15px}._CardDetailPage_jorn-c-bottom-text{font-size:16px;color:#999}._CardDetailPage_jorn-c-bottom-text-area{align-items:center;justify-content:center}._CardDetailPage_jorn-c-margin-l{margin-left:16px}._CardDetailPage_jorn-c-amc-dlg-width{width:270px;max-width:270px;min-width:270px}._CardDetailPage_jorn-c-amc-dlg-box{border:0;background-color:#fff;border-radius:8px;width:270px;align-items:center}._CardDetailPage_jorn-c-local-input{flex:1.0;border:0;color:#333;font-size:17px;padding:0px;white-space:nowrap}._CardDetailPage_jorn-c-amc-input-box-height{height:36px;max-height:36px}._CardDetailPage_jorn-c-btn-img-box{width:24px;height:24px;justify-content:flex-end;align-items:center}._CardDetailPage_jorn-c-info-img{height:19px;width:19px;margin-right:1.5px}._CardDetailPage_jorn-c-margin-l-xs{margin-left:5px}._CardDetailPage_jorn-c-amc-1px-line{height:1PX;background-color:#eee}._CardDetailPage_jorn-c-amc-cell-l-text{font-size:18px;color:#333333}._CardDetailPage_jorn-c-cell-box-desc{font-size:16px;color:#666666;margin-left:5px;margin-right:3px}._CardDetailPage_jorn-c-margin-t-xl{margin-top:13px}._CardDetailPage_jorn-c-cert-type{font-size:18px;color:#333}._CardDetailPage_jorn-c-protocol-box{margin-top:15px}._CardDetailPage_jorn-c-button-box{margin-top:30px}._CardDetailPage_jorn-c-amc-btn-disabled-color{color:#C5E4FA;font-size:18px;text-align:center}._CardDetailPage_jorn-c-amc-btn-primary{background-color:#1677FF;border:0;border-radius:2px;font-size:18px;height:47px;max-height:47px;min-height:47px;flex:1.0;color:#FFF}._CardDetailPage_jorn-c-amc-btn-primary:active{background-color:#136BE6;color:#9ABEF4}._CardDetailPage_jorn-c-amc-btn-primary:disabled{background-color:#9DC3FB;color:#C4DBFC}._CardDetailPage_jorn-c-large-label{height:90px}._CardDetailPage_jorn-c-large-label-box{height:37px}._CardDetailPage_jorn-c-main-box{padding-left:16px;padding-right:16px;padding-top:16px;padding-bottom:16px}._CardDetailPage_jorn-c-margin-top-box{margin-top:18px}._CardDetailPage_jorn-c-margin-left-box{margin-left:10px}._CardDetailPage_jorn-c-ant-blue-border{border-radius:2px;border:1px solid #108EE9}._CardDetailPage_jorn-c-ant-gray-border{border-radius:2px;border:0.5px solid #c6c5c5}._CardDetailPage_jorn-c-margin-m-right{margin-right:5px}._CardDetailPage_jorn-c-btn-right-label{justify-content:flex-end;align-items:center;font-size:18px;margin-right:12px}._CardDetailPage_jorn-c-divide-zone-lr{width:10px;height:1px;background-color:#f5f5f5}._CardDetailPage_jorn-c-asi-img{width:17px;height:17px;margin-right:2px;margin-top:2px}._CardDetailPage_jorn-c-securityInfoBox{margin-bottom:20px}._CardDetailPage_jorn-c-bottom-sticky-box{background-color:#fff;padding-left:16px;padding-right:16px;padding-top:12px}._CardDetailPage_jorn-c-sticky-button-box{margin-top:12px;margin-bottom:12px}._CardDetailPage_jorn-c-card-1px-line{height:0.5px;background-color:#e5e5e5}._CardDetailPage_jorn-c-font-super{font-size:18px}._CardDetailPage_jorn-c-tmp{border:#4cd864 4px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 3e251bcbbb526db180a39876a4a7edfcd17191b7:D */!function(\nn){var a={};function o(e){if(a[e])return a[e].exports\n;var t=a[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports\n,t,t.exports,o),t.l=!0,t.exports}o.m=n,o.c=a,o.d=function(e,\nt,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,\nget:n})},o.r=function(e){\n'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(\ne,Symbol.toStringTag,{value:'Module'}),\nObject.defineProperty(e,'__esModule',{value:!0})},\no.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(\n4&e&&'object'==typeof t&&t&&t.__esModule)return t\n;var n=Object.create(null);if(o.r(n),Object.defineProperty(n\n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t\n)for(var a in t)o.d(n,a,function(e){return t[e]}.bind(null,a\n));return n},o.n=function(e){var t=e&&e.__esModule?function(\n){return e.default}:function(){return e};return o.d(t,'a',t)\n,t},o.o=function(e,t){\nreturn Object.prototype.hasOwnProperty.call(e,t)},o.p='',o(\no.s=4)}([function(e,t,n){'use strict';Object.defineProperty(\nt,'__esModule',{value:!0});var a=n(5)\n;t.BNComponent=a.BNComponent;var o=n(2)\n;t.ComponentRegistry=o.ComponentRegistry;var i=n(7)\n;t.Logger=i.Logger,t.logger=i.logger},function(e,t,n){\n'use strict';Object.defineProperty(t,'__esModule',{value:!0}\n),t.randomStr=function(){return Math.floor(\n61439*Math.random()+4096).toString(16)},\nt.startsWith=function(e,t){return!!e&&0===e.indexOf(t)},\nt.tryJSONParse=function(e){if(t=e,\n'[object Object]'===Object.prototype.toString.call(t)\n)return e;var t;try{return JSON.parse(e)}catch(e){return{}}}\n,t.copyObj=function(e,t){for(var n in t||(t={}),e\n)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},function(e,t,n\n){'use strict';Object.defineProperty(t,'__esModule',{\nvalue:!0});var a=n(0),o=function(){function n(){}\nreturn n.registerComponent=function(e,t){\nt?n.facts[e]?a.logger.e('CmpReg#regCmp','E0002 '+e):(\na.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t\n):a.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},\nn.getKnownComponents=function(){return n.facts},\nn.getComponentJson=function(e){return n.jsons[e]},\nn.putComponentJson=function(e,t){t||a.logger.e(\n'CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e\n)?a.logger.e('CmpReg#putCmpJ','E0005 '+e):(a.logger.i(\n'CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},\nn.createComponent=function(e){a.logger.i('CmpReg#crtCmp',\n'I0007 '+e);var t=n.facts[e];return t?new t:(a.logger.e(\n'CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}(\n);t.ComponentRegistry=o},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0}),\nt.amc=window.amc},function(e,t,n){'use strict';var a,\no=this&&this.__extends||(a=function(e,t){return(\na=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}a(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var i=n(0),s=n(3),_=s.amc.rpcData,\nr=s.amc.fn.isObject(_.spmObj)?JSON.stringify(_.spmObj):'{}',\nc=s.amc.path+'alipay_msp_',p=s.amc.fn.spmExposure,\nl=s.amc.fn.spmClick,u=c+'info',d=c+'cvv',m=c+'validate',\ng=new Array,v=new Array,f='',h=_.needinput,b=!1,y='',C={},\nI={},x=!1,w='+*************',P=[{name:'cvv2',id:'cvv2'},{\nname:'validate',id:'validate'},{name:'username',\nid:'userName'},{name:'certno',id:'certNo'},{\nname:'bankmobile',id:'bankMobile'}],j={cvv2:{\nregex:'^\\\\d{0,4}$',lastValue:''},validate:{\nregex:'^\\\\d{2}/\\\\d{2}$',lastValue:''},bankMobile:{\nregex:'^[0-9-+]*$',lastValue:''},certNoId:{\nregex:'(^\\\\d{0,18}$)|(^\\\\d{17}([X|x]{0,1}))$',\nformatter:function(e){var t;if(!(t=/^\\d{6}[12]/.test(e\n)?/(\\d{6})(\\d{8})(\\d*[xX]{0,1})|(\\d{6})(\\d*)|(\\d*)/.exec(e\n):/(\\d{6})(\\d{6})(\\d*[xX]{0,1})|(\\d{6})(\\d*)|(\\d*)/.exec(e))\n)return'';for(var n=[],a=1;a<t.length;a+=1)t[a]&&n.push(t[a]\n);return n.join(' ')},lastValue:''}},D={},B={ICBC:1},V={}\n;function k(e){e&&(e.style.display=s.amc.VISIBLE)}\nfunction N(e){e&&(e.style.display='none')}V.cvv={\ntext:'{{cvv_info}}',title:'{{cvv}}',img:d},V.validate={\ntext:'{{validity_info}}',title:'{{validity}}',img:m};var A,\nT=function(e){function t(){var d=null!==e&&e.apply(this,\narguments)||this;return d.onMounted=function(){\nd.hasScrolled=!1,d.validateInput=d.getViewInComponentById(\n'validate'),d.validateAreaBox=d.getViewInComponentById(\n'validateAreaBox'),d.cvv2Input=d.getViewInComponentById(\n'cvv2'),d.cvv2AreaBox=d.getViewInComponentById('cvv2AreaBox'\n),d.userNameInput=d.getViewInComponentById('userName'),\nd.userNameAreaBox=d.getViewInComponentById('userNameAreaBox'\n),d.certNoAreBox=d.getViewInComponentById('certNoAreaBox'),\nd.certNoInput=d.getViewInComponentById('certNo'),\nd.certNoIdInput=d.getViewInComponentById('certNoId'),\nd.bankMobile=d.getViewInComponentById('bankMobile'),\nd.bankMobileAreaBox=d.getViewInComponentById(\n'bankMobileAreaBox'),d.totalHeight=79,(\nvoid 0!==_.isCredit||_.bankname)&&(void 0!==_.isCredit&&(\nd.getViewInComponentById('cardType'\n).innerText=_.isCredit?'{{credit_card}}':'{{store_card}}'),\nd.getViewInComponentById('cardName'\n).innerText=_.bankname||'',k(d.getViewInComponentById(\n'cardTypeArea')),d.totalHeight+=84),h.cvv2=_.cvv2,\nh.validate=_.validate,window.innerWidth;for(\nvar e=0;e<P.length;e++){var t=P[e].name,n=P[e].id\n;!h[t]||h[t].disable&&!h[t].value?(d.getViewInComponentById(\nn).disabled=!0,'certNo'===n&&(b=!0)):(\nd.getViewInComponentById(n).value=h[t].value||'',\n'certNo'===n&&(y=h[t].value||'',C.certNoId=h[t].value||''),\nC[n]=h[t].value||'',h[t].disable&&('certNo'===n&&(b=!0),\nd.getViewInComponentById(n).disabled=!0,\nd.getViewInComponentById(n).style.color='#a5a5a5'),k(\nd.getViewInComponentById(n+'Area')),D[n]=!0)}\nD.cvv2&&D.validate||(\nD.cvv2&&!D.validate||!D.cvv2&&D.validate)&&(\nd.getViewInComponentById('cvvValidateZone'\n).style.display='none'),d.initCertTypePicker(),(\nD.cvv2||D.validate)&&(d.totalHeight+=102),(\nD.userName||D.certType)&&(d.totalHeight+=102),D.certNo&&(\nd.totalHeight+=102),D.bankMobile&&(d.totalHeight+=102)\n;var a=!1,o=118;_.protocols&&_.protocols.length&&(\nd.totalHeight+=31,a=!0,o+=32),d.totalHeight+=132,\nd.overScreen?(N(d.getViewInComponentById('protocolsArea')),\nN(d.getViewInComponentById('buttonArea')),k(\nd.getViewInComponentById('stickyButtonArea')),\nd.getViewInComponentById('stickyButtonArea').style.height=o,\nd.getViewInComponentById('mainBody'\n).style.height=s.amc.specs.bodyHeight-o,\nd.getViewInComponentById('mainBody'\n).style.maxHeight=s.amc.specs.bodyHeight-o,k(\nd.getViewInComponentById('fillBottomBox')),k(\nd.getViewInComponentById('divideLine')),\nd.getViewInComponentById('fillBottomBox').style.height=o+130\n,a&&(k(d.getViewInComponentById('stickyProtocolsArea')),p(\n'a283.b5788.c12704.d23266',{},!1)),s.amc.fn.logAction(\nd.totalHeight+'-'+window.innerHeight+'-'+o+'-'+s.amc.specs.bodyHeight\n,'card-detail')):(d.getViewInComponentById('mainBody'\n).style.height=s.amc.specs.bodyHeight,a&&(k(\nd.getViewInComponentById('protocolsArea')),p(\n'a283.b5788.c12704.d23266',{},!1)));var i=d;if(\nD.validate||D.cvv2||N(d.getViewInComponentById(\n'cvv2ValidateSpace')),D.userName||D.certNo||D.certType||N(\nd.getViewInComponentById('nameCertSpace')),\nd.getViewInComponentById('cvvInfoImg').src=u,\nd.getViewInComponentById('validateInfo').src=u,\nd.getViewInComponentById('userImg').src=u,\nd.getViewInComponentById('phoneInfoUp').src=u,\ns.amc.isAndroid&&(d.getViewInComponentById('loading'\n).src=s.amc.path+'alipay_msp_indicator_white_loading',\nd.getViewInComponentById('stickyLoading'\n).src=s.amc.path+'alipay_msp_indicator_white_loading'),\nd.checkInput('cvv2',d.getViewInComponentById('cvvInfoLayout'\n)),d.checkInput('validate',d.getViewInComponentById(\n'validateInfoLayout')),d.checkInput('userName',\nd.getViewInComponentById('userInfoLayout')),d.checkInput(\n'certNo',null),d.checkInput('certNoId',null),\nd.updateSubmitState(),d.overScreen?d.getViewInComponentById(\n'stickyBtnText'\n).innerText=_.btnName||'{{next_step}}':d.getViewInComponentById(\n'btnText').innerText=_.btnName||'{{next_step}}',p(\n'a283.b5788.c12704.d23264',{},!1),B[_.inst_id]&&(\nd.getViewInComponentById('phoneTip1'\n).innerText='{{phone_tip}}',d.getViewInComponentById(\n'bankMobile').placeholder='{{enter_phone}}'),\ns.amc.fn.sdkGreaterThanOrEqual('10.8.33')){var r={\nname:'MSP_CASHIER_ERROR_GUIDE'},c=function(){\ndocument.invoke('meta-onNotification',r,function(e){\ni.getViewInComponentById('modifyPhoneNumBtn'\n).style.display=s.amc.VISIBLE})};s.amc.isIOS?c():setTimeout(\nc,30)}if(d.getViewInComponentById('modifyPhoneNumBtn'\n).onclick=function(){d.getViewInComponentById('bankMobile'\n).value='',d.getViewInComponentById('modifyPhoneNumBtn'\n).style.display='none',d.applyStyleTo(d.bankMobileAreaBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border'),\nd.updateSubmitState(),d.getViewInComponentById('bankMobile'\n).focus()},_.channelContextId&&setTimeout(function(){\ndocument.invoke('rpc',{\noperationType:'alipay.mobilechannel.cardbindingprod.cardsavepropagatequery',\nrequestData:{channelContextId:_.channelContextId}},function(\ne){\ne&&e.data&&e.data.content&&e.data.leftText&&e.data.rightText&&(\ni.saveCardDlg={},i.saveCardDlg.content=e.data.content,\ni.saveCardDlg.leftText=e.data.leftText,\ni.saveCardDlg.rightText=e.data.rightText)})},30),\n_.alipayLogoUrl){var l=d.getViewInComponentById('asiImg')\n;'none'===_.alipayLogoUrl?s.amc.fn.hide(l\n):l.src=_.alipayLogoUrl}},d.onClickNameInfo=function(){\nd.getViewInComponentById('userDlg').showModal()\n;var e=d.getViewInComponentById('nameTip3Label'),\nt=e.innerText||'';0<=t.indexOf(w)&&(t=t.replace(w,\n'<font color=\"#108ee9\">'+w+'</font>')),e.innerText=t},\nd.onClickCVVInfo=function(){d.onClickInfo('cvv')},\nd.onClickValidateInfo=function(){d.onClickInfo('validate')},\nd.onClickPhonenum=function(){d.getViewInComponentById(\n'phoneDlg').showModal();var e=d.getViewInComponentById(\n'phoneTip4Label'),t=e.innerText||'';0<=t.indexOf(w)&&(\nt=t.replace(w,'<font color=\"#108ee9\">'+w+'</font>')),\ne.innerText=t},d.onClickDlgClose=function(){\nd.getViewInComponentById('dlg').close()},\nd.onClickNameClose=function(){d.getViewInComponentById(\n'userDlg').close()},d.onCheckUserNameInput=function(){\nd.checkInput('userName',d.getViewInComponentById(\n'userInfoLayout'))},d.onCVV2Input=function(){d.onInput(\n'cvv2','cvvInfoLayout')},d.onValidateInput=function(){\nd.onInput('validate','validateInfoLayout')},\nd.onCertNoInput=function(){d.onInput('certNo',null)},\nd.onCertNoIdInput=function(){d.onInput('certNoId',null)},\nd.onBankMobileFocus=function(){d.getViewInComponentById(\n'modifyPhoneNumBtn').style.display='none',d.overScreen||(\nd.getViewInComponentById('fillBottomBox'\n).style.display=s.amc.VISIBLE,d.getViewInComponentById(\n'fillBottomBox').style.height=300)},\nd.onBankMobileInput=function(){d.getViewInComponentById(\n'modifyPhoneNumBtn').style.display='none',d.onInput(\n'bankMobile',null)},d.onClickPhonenumClose=function(){\nd.getViewInComponentById('phoneDlg').close()},\nd.onInputBlur=function(){d.overScreen||(\nd.getViewInComponentById('fillBottomBox'\n).style.display='none'),d.hasShownIDKeyboard=!1},\nd.onCertNoFocus=function(){d.hasScrolled||(\nd.getViewInComponentById('mainBody').scrollTop=300,\nd.hasScrolled=!0),d.overScreen||(d.getViewInComponentById(\n'fillBottomBox').style.display=s.amc.VISIBLE,\nd.getViewInComponentById('fillBottomBox').style.height=300),\nd.hasShownIDKeyboard=!0},d.dial=function(){var e='95188'\n;0<=(s.amc.fn.i18nValueForKey('{{phone_tip_4}}')||''\n).indexOf(w)&&(e=w);var t={action:{\nname:'loc:tel(\\''+e+'\\', \\'1\\')'}};document.submit(t),\nd.getViewInComponentById('phoneDlg').close()},\nd.onSubmit=function(){l('a283.b5788.c12704.d23264',{})\n;var e={};_.isCredit?e.action={\nname:_.nextAction||'/card/sign',neec:'6004',host:_.pci_url,\nhttps:!0,enctype:'application/x-www-form-urlencoded',\nrequest_param:'requestData'}:e.action={\nname:_.nextAction||'/card/sign',neec:'6004'},\ne.action.params={cardtype:_.cardtype,bankcode:_.bankcode,\nccdctoken:_.ccdctoken,hidden_card_no:_.hidden_card_no},\ne.param={};var t=d.getViewInComponentById('userName').value\n;D.userName&&t!=h.username.value&&(e.param.username=t)\n;var n=d.getCertInpuValue();if(D.certNo&&n!=h.certno.value){\nif('A'==f){var a=n;if(!a.match(\n'(^\\\\d{15}$)|(^\\\\d{17}([0-9]|X|x)$)'))return void d.toast(\n'{{input_right_id_card}}','certNo')}e.param.certno=n}\nD.certType&&f!=h.certtype.default&&(e.param.certtype=f)\n;var o=d.getViewInComponentById('bankMobile').value;if(\nD.bankMobile&&o!=h.bankmobile.value){if(!(a=o).match(\n'(^([+]?[0]{0,2}86|([+]?0{0,2}86-))?1\\\\d{10}$)|(^[+]?[0]{0,2}[1-9]{1}\\\\d{0,7}[-]{0,1}\\\\d{7,11}$)'\n))return void d.toast('{{input_right_phone}}','bankMobile')\n;e.param.bankmobile=o}if(D.cvv2){\nvar i=d.getViewInComponentById('cvv2').value||'';if(\n!i.match('^\\\\d{3,4}$'))return void d.toast(\n'{{input_valid_cvv2}}','cvv2');e.param.cvv2=i}if(D.validate\n){var r=d.getViewInComponentById('validate').value||''\n;r=r.replace('/',''),e.param.validate=r||''}\ns.amc.fn.hideKeyboard(),e.action.loadtxt='';var c=d\n;document.asyncSubmit(e,function(e){\n'0'===e.pageloading?c.overScreen?(k(\nc.getViewInComponentById('stickyBtnText')),N(\nc.getViewInComponentById('stickyLoading'))):(k(\nc.getViewInComponentById('btnText')),N(\nc.getViewInComponentById('loading'))):'1'===e.pageloading&&(\nc.overScreen?(N(c.getViewInComponentById('stickyBtnText')),\nk(c.getViewInComponentById('stickyLoading'))):(N(\nc.getViewInComponentById('btnText')),k(\nc.getViewInComponentById('loading'))))}),c.overScreen?(N(\nd.getViewInComponentById('stickyBtnText')),k(\nd.getViewInComponentById('stickyLoading'))):(N(\nd.getViewInComponentById('btnText')),k(\nd.getViewInComponentById('loading')))},\nd.onClickInfo=function(e){'user'===e||(\nd.getViewInComponentById('dlgTitle').innerText=V[e].title,\nd.getViewInComponentById('dlgText').innerText=V[e].text,\nd.getViewInComponentById('dlgImg').src=V[e].img,\nd.getViewInComponentById('dlg').showModal())},\nd.onClickCertType=function(){if(l('a283.b5788.c12704.d23265'\n,{}),!h.certno.disable&&!h.certtype.disable&&1!==g.length){\nvar e={btns:g,default:v.indexOf(f),title:'{{sel_id_no}}'}\n;s.amc.isAndroid&&document.submit({action:{\nname:'loc:hideKeyboard'}});var i=d;document.picker(e,\nfunction(e){var t=e.index,n=g[t];if(\nn&&n!=i.getViewInComponentById('certType').innerText&&(\ni.getViewInComponentById('certType').innerText=n,f=v[t],\ni.updateCertNoKeyboardType('',!1),i.updateSubmitState()),\nD.certNo){var a=i.getViewInComponentById('certNo'),\no=i.getViewInComponentById('certNoId');'A'===f&&o?o.focus(\n):a&&a.focus()}})}},d.showProtocol=function(){l(\n'a283.b5788.c12704.d23266',{}),s.amc.fn.showProtocolList(\n_.protocols)},d}return o(t,e),\nt.prototype.updateInputState=function(e,t){\nvar n=this.getViewInComponentById(e);switch(\nn&&n.value&&n.value.trim().length&&t?N(t):t&&k(t),e){\ncase'validate':this.validateInput.value?this.applyStyleTo(\nthis.validateAreaBox,\n'detail-cell ant-gray-border detail-cell-left-width'\n):this.applyStyleTo(this.validateAreaBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border detail-cell-left-width'\n);break;case'cvv2':this.cvv2Input.value?this.applyStyleTo(\nthis.cvv2AreaBox,\n'detail-cell ant-gray-border detail-cell-right-width'\n):this.applyStyleTo(this.cvv2AreaBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border detail-cell-right-width'\n);break;case'userName':\nthis.userNameInput.value?this.applyStyleTo(\nthis.userNameAreaBox,\n'detail-cell ant-gray-border detail-cell-left-width'\n):this.applyStyleTo(this.userNameAreaBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border detail-cell-right-width'\n);break;case'certNo':case'certNoId':\nthis.certNoInput.style.display==s.amc.VISIBLE&&this.certNoInput.value||this.certNoIdInput.style.display==s.amc.VISIBLE&&this.certNoIdInput.value?this.applyStyleTo(\nthis.certNoAreBox,'detail-cell ant-gray-border'\n):this.applyStyleTo(this.certNoAreBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border')\n;break;case'bankMobile':\nthis.bankMobile.value?this.applyStyleTo(\nthis.bankMobileAreaBox,'detail-cell ant-gray-border'\n):this.applyStyleTo(this.bankMobileAreaBox,\n'detail-cell ant-blue-border amc-1px-theme-color-border')}},\nt.prototype.checkInput=function(e,t){this.updateInputState(e\n,t),this.updateSubmitState()},t.prototype.onBack=function(){\nif(this.hasShownIDKeyboard\n)return this.getViewInComponentById('certNo').blur(),\nvoid this.getViewInComponentById('certNoId').blur();if(l(\n'a283.b5788.c12704.d23270',{}),s.amc.fn.spmPageDestroy(\n'a283.b5788',{}),s.amc.fn.hideKeyboard(),\ns.amc.fn.sdkGreaterThanOrEqual('10.8.33')&&document.invoke(\n'meta-offNotification',{name:'MSP_CASHIER_ERROR_GUIDE'},\nfunction(e){}),this.saveCardDlg){var e={\nmessage:this.saveCardDlg.content||'{{confirm_exit}}',\nokButton:this.saveCardDlg.rightText||'{{confirm_btn}}',\ncancelButton:this.saveCardDlg.leftText||'{{cancel}}'}\n;return document.confirm(e,function(e){e.ok?(\ndocument.invoke('rpc',{\noperationType:'alipay.mobilechannel.cardbindingprod.cardsave',\nrequestData:{channelContextId:_.channelContextId}},function(\ne){}),s.amc.fn.spmClick('a283.b14494.c41193.d83368',S(r))\n):s.amc.fn.spmClick('a283.b14494.c41193.d83369',S(r)),\ns.amc.fn.back()}),s.amc.fn.spmExposure(\n'a283.b14494.c41193.d83368',S(r),!1),\nvoid s.amc.fn.spmExposure('a283.b14494.c41193.d83369',S(r),\n!1)}s.amc.fn.back()},t.prototype.onHelp=function(){\nvar e=_.helpURL||'https://csmobile.alipay.com/router.htm?scene=app_addcard'\n;document.submit({action:{\nname:'loc:openweb(\\''+e+'\\', \\'{{help}}\\')'}}),\ns.amc.fn.spmClick('a283.b4032.c9686.d24461',S(r))},\nt.prototype.onInput=function(e,t){if('certNo'!==e){\nvar n=this.getViewInComponentById(e).value,a=j[e].formatter\n;a&&(n=O(n));var o=this;if(n.match(j[e].regex)){if(a){n=a(n)\n;var i=function(){o.getViewInComponentById(e).value=n};i(),\ns.amc.isAndroid&&window.setTimeout(i,1)}j[e].lastValue=n\n}else{if(('certNoId'==e||'bankMobile'==e)&&!I[e]&&C[e]){\nvar r=function(e,t){e=e||'',t=t||'';for(var n=0;n<t.length;\n){if(n>=e.length||e[n]!==t[n])return t[n];n++}return''}(C[e]\n,this.getViewInComponentById(e).value);(n=r).match(\nj[e].regex)&&(j[e].lastValue=r)}var c=n&&n[n.length-1];(\n'certNoId'==e&&'x'==c||'X'==c)&&this.toast('{{id_card_tip}}'\n,'certNoId'),this.getViewInComponentById(e\n).value=j[e].lastValue}}I[e]=!0,t&&this.updateInputState(e,\nthis.getViewInComponentById(t)),this.updateInputState(e,null\n),this.updateSubmitState()},\nt.prototype.updateSubmitState=function(){\nvar e=document.getElementsByTagName('input');if(e){for(\nvar t=!1,n=0;n<e.length;n++){var a=e[n];if(!a.disabled&&(\na.value||(t=!0),this.cvv2Input!==a||a.value.match(\n'^\\\\d{3,4}$')||(t=!0),this.validateInput!==a||a.value.match(\n'^\\\\d{2}/\\\\d{2}$')||(t=!0),t))break}this.overScreen?(\nthis.getViewInComponentById('stickySubmit').disabled=t,\nthis.getViewInComponentById('stickyBtnText'\n).style.color=t?'#C5E4FA':'#fff'):(\nthis.getViewInComponentById('submit').disabled=t,\nthis.getViewInComponentById('btnText'\n).style.color=t?'#C5E4FA':'#fff')}},\nt.prototype.toast=function(e,t){if(\nthis.getViewInComponentById(t)&&e){var n=this\n;document.toast({text:e,type:'fail'},function(){\nn.getViewInComponentById(t).focus()})}},\nt.prototype.getCertInpuValue=function(){return'A'===f?O(\nthis.getViewInComponentById('certNoId').value\n):this.getViewInComponentById('certNo').value},\nt.prototype.updateCertNoKeyboardType=function(e,t){\nvar n=this.getViewInComponentById('certNo'),\na=this.getViewInComponentById('certNoId');if(n.value=e||'',\na.value=e||'',!0===t||!D.certNo\n)return a.style.color='#a5a5a5',void(n.style.color='#a5a5a5'\n);'A'===f?(n.disabled=!0,a.disabled=!1,N(n),k(a)):(\na.disabled=!0,n.disabled=!1,N(a),k(n))},\nt.prototype.initCertTypePicker=function(){if(\nD.certNo&&h.certtype){var e=h.certtype.value,t=0;for(\nvar n in e){var a=e[n];g.push(n),v.push(a),\na===h.certtype.default&&(t=g.length-1)}var o=g[t];f=v[t]||''\n,o&&(this.getViewInComponentById('certType').innerText=o)}\ng.length&&v.length&&(\nh.certno.disable||h.certtype.disable||1===g.length||(\nthis.getViewInComponentById('arrowRight'\n).src=s.amc.res.arrowRight),D.certType=!0,k(\nthis.getViewInComponentById('certTypeArea')),p(\n'a283.b5788.c12704.d23265',{},!1)),D.userName&&D.certType||(\nD.userName&&!D.certType||!D.userName&&D.certType)&&(\nthis.getViewInComponentById('nameCertTypeZone'\n).style.display='none'),this.updateCertNoKeyboardType(y,b)},\nt.getComponentCSSRules=function(){return{\n'.amc-nav-r-box':'_CardDetailPage_jorn-c-amc-nav-r-box',\n'.amc-cell-l-box':'_CardDetailPage_jorn-c-amc-cell-l-box',\n'.card-name':'_CardDetailPage_jorn-c-card-name',\n'.protocol-agree':'_CardDetailPage_jorn-c-protocol-agree',\n'.protocol-font-size':'_CardDetailPage_jorn-c-protocol-font-size',\n'.align-start':'_CardDetailPage_jorn-c-align-start',\n'.l-label-ellipsis':'_CardDetailPage_jorn-c-l-label-ellipsis',\n'.detail-cell':'_CardDetailPage_jorn-c-detail-cell',\n'.detail-cell-left-width':'_CardDetailPage_jorn-c-detail-cell-left-width',\n'.detail-cell-right-width':'_CardDetailPage_jorn-c-detail-cell-right-width',\n'.dlg-txt':'_CardDetailPage_jorn-c-dlg-txt',\n'.tip-info-box':'_CardDetailPage_jorn-c-tip-info-box',\n'.tip-info-text':'_CardDetailPage_jorn-c-tip-info-text',\n'.dlg-title-box':'_CardDetailPage_jorn-c-dlg-title-box',\n'.dlg-title':'_CardDetailPage_jorn-c-dlg-title',\n'.dlg-dot':'_CardDetailPage_jorn-c-dlg-dot',\n'.dlg-list':'_CardDetailPage_jorn-c-dlg-list',\n'.tip-box':'_CardDetailPage_jorn-c-tip-box',\n'.dlg-btn-text':'_CardDetailPage_jorn-c-dlg-btn-text',\n'.dlg-line':'_CardDetailPage_jorn-c-dlg-line',\n'.bottom-box':'_CardDetailPage_jorn-c-bottom-box',\n'.sticky-bottom-box':'_CardDetailPage_jorn-c-sticky-bottom-box',\n'.bottom-text':'_CardDetailPage_jorn-c-bottom-text',\n'.bottom-text-area':'_CardDetailPage_jorn-c-bottom-text-area',\n'.margin-l':'_CardDetailPage_jorn-c-margin-l',\n'.amc-dlg-width':'_CardDetailPage_jorn-c-amc-dlg-width',\n'.amc-dlg-box':'_CardDetailPage_jorn-c-amc-dlg-box',\n'.local-input':'_CardDetailPage_jorn-c-local-input',\n'.amc-input-box-height':'_CardDetailPage_jorn-c-amc-input-box-height',\n'.btn-img-box':'_CardDetailPage_jorn-c-btn-img-box',\n'.info-img':'_CardDetailPage_jorn-c-info-img',\n'.margin-l-xs':'_CardDetailPage_jorn-c-margin-l-xs',\n'.amc-1px-line':'_CardDetailPage_jorn-c-amc-1px-line',\n'.amc-cell-l-text':'_CardDetailPage_jorn-c-amc-cell-l-text',\n'.cell-box-desc':'_CardDetailPage_jorn-c-cell-box-desc',\n'.margin-t-xl':'_CardDetailPage_jorn-c-margin-t-xl',\n'.cert-type':'_CardDetailPage_jorn-c-cert-type',\n'.protocol-box':'_CardDetailPage_jorn-c-protocol-box',\n'.button-box':'_CardDetailPage_jorn-c-button-box',\n'.amc-btn-disabled-color':'_CardDetailPage_jorn-c-amc-btn-disabled-color',\n'.amc-btn-primary':'_CardDetailPage_jorn-c-amc-btn-primary',\n'.large-label':'_CardDetailPage_jorn-c-large-label',\n'.large-label-box':'_CardDetailPage_jorn-c-large-label-box',\n'.main-box':'_CardDetailPage_jorn-c-main-box',\n'.margin-top-box':'_CardDetailPage_jorn-c-margin-top-box',\n'.margin-left-box':'_CardDetailPage_jorn-c-margin-left-box',\n'.ant-blue-border':'_CardDetailPage_jorn-c-ant-blue-border',\n'.ant-gray-border':'_CardDetailPage_jorn-c-ant-gray-border',\n'.margin-m-right':'_CardDetailPage_jorn-c-margin-m-right',\n'.btn-right-label':'_CardDetailPage_jorn-c-btn-right-label',\n'.divide-zone-lr':'_CardDetailPage_jorn-c-divide-zone-lr',\n'.asi-img':'_CardDetailPage_jorn-c-asi-img',\n'.securityInfoBox':'_CardDetailPage_jorn-c-securityInfoBox',\n'.bottom-sticky-box':'_CardDetailPage_jorn-c-bottom-sticky-box',\n'.sticky-button-box':'_CardDetailPage_jorn-c-sticky-button-box',\n'.card-1px-line':'_CardDetailPage_jorn-c-card-1px-line',\n'.font-super':'_CardDetailPage_jorn-c-font-super',\n'.tmp':'_CardDetailPage_jorn-c-tmp'}},\nt.getComponentJson=function(){return{_c:'amc-v-box',\n_t:'div',_cd:[{'sp-view-id':'mainBody',\n_c:'amc-v-box-center amc-scroll _CardDetailPage_jorn-c-main-box',\n_t:'div',_cd:[{'sp-view-id':'cardTypeArea',\n_c:'amc-v-box amc-hidden',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc amc-flex-1 _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{bank_card_type}}'},{\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'cardName',\n_c:'_CardDetailPage_jorn-c-amc-cell-l-text _CardDetailPage_jorn-c-card-name amc-ellipsis',\n_t:'label'},{'sp-view-id':'cardType',\n_c:'_CardDetailPage_jorn-c-amc-cell-l-text _CardDetailPage_jorn-c-margin-l-xs amc-flex-1 amc-ellipsis',\n_t:'label'}]}]}]},{'sp-view-id':'cvv2ValidateSpace',\n_c:'_CardDetailPage_jorn-c-margin-top-box amc-align-center',\n_t:'div',_cd:[{'sp-view-id':'validateArea',\n_c:'amc-v-box amc-flex-1 amc-hidden',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{validity_text}}'},{\n'sp-view-id':'validateAreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border _CardDetailPage_jorn-c-detail-cell-left-width',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'validate',_y:'month',\noninput:'onValidateInput',\n_c:'_CardDetailPage_jorn-c-local-input',\nplaceholder:'{{month_year}}',value:'',_t:'input'}]},{\n'sp-view-id':'validateInfoLayout',\n_c:'_CardDetailPage_jorn-c-btn-img-box',_t:'div',_cd:[{\n'sp-view-id':'validateInfo',onclick:'onClickValidateInfo',\n_c:'_CardDetailPage_jorn-c-info-img',alt:'{{info}}',_t:'img'\n}]}]}]},{_c:'_CardDetailPage_jorn-c-divide-zone-lr',\n'sp-view-id':'cvvValidateZone',_t:'div'},{\n'sp-view-id':'cvv2Area',\n_c:'amc-v-box amc-flex-1 amc-hidden',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{cvv_text}}'},{'sp-view-id':'cvv2AreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border _CardDetailPage_jorn-c-detail-cell-right-width',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'cvv2',_y:'number',\noninput:'onCVV2Input',maxlength:'4',\n_c:'_CardDetailPage_jorn-c-local-input',\nplaceholder:'{{card_three_num}}',value:'',_t:'input'}]},{\n_c:'_CardDetailPage_jorn-c-btn-img-box',\n'sp-view-id':'cvvInfoLayout',_t:'div',_cd:[{\n'sp-view-id':'cvvInfoImg',onclick:'onClickCVVInfo',\n_c:'_CardDetailPage_jorn-c-info-img',alt:'{{info}}',_t:'img'\n}]}]}]}]},{'sp-view-id':'nameCertSpace',\n_c:'_CardDetailPage_jorn-c-margin-top-box',_t:'div',_cd:[{\n'sp-view-id':'userNameArea',\n_c:'amc-v-box amc-flex-1 amc-hidden',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{name}}'},{'sp-view-id':'userNameAreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border _CardDetailPage_jorn-c-detail-cell-left-width',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'userName',\noninput:'onCheckUserNameInput',\n_c:'_CardDetailPage_jorn-c-local-input',\nplaceholder:'{{card_owner_name}}',value:'',_t:'input'}]},{\n_c:'_CardDetailPage_jorn-c-btn-img-box',\n'sp-view-id':'userInfoLayout',_t:'div',_cd:[{\n'sp-view-id':'userImg',onclick:'onClickNameInfo',\n_c:'_CardDetailPage_jorn-c-info-img',alt:'{{info}}',_t:'img'\n}]}]}]},{_c:'_CardDetailPage_jorn-c-divide-zone-lr',\n'sp-view-id':'nameCertTypeZone',_t:'div'},{\n'sp-view-id':'certTypeArea',\n_c:'amc-v-box amc-flex-1 amc-hidden',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{papers_type}}'},{\n'sp-view-id':'certTypeAreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border _CardDetailPage_jorn-c-detail-cell-right-width',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\nonclick:'onClickCertType',_t:'div',_cd:[{\n'sp-view-id':'certType',\n_c:'_CardDetailPage_jorn-c-cert-type amc-flex-1 amc-ellipsis',\n_t:'label',_x:'{{papers_type}}'},{'sp-view-id':'arrowRight',\n_c:'_CardDetailPage_jorn-c-margin-m-right',_t:'img'}]}]}]}]}\n,{'sp-view-id':'certNoArea',\n_c:'_CardDetailPage_jorn-c-margin-top-box amc-v-box amc-hidden',\n_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc amc-flex-1 _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{id_no}}'},{'sp-view-id':'certNoAreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'certNo',\noninput:'onCertNoInput',onfocus:'onCertNoFocus',\nonblur:'onInputBlur',\n_c:'_CardDetailPage_jorn-c-local-input',\nplaceholder:'{{id_info_tip}}',disabled:'true',value:'',\n_t:'input'},{'sp-view-id':'certNoId',\noninput:'onCertNoIdInput',onfocus:'onCertNoFocus',\nonblur:'onInputBlur',\n_c:'_CardDetailPage_jorn-c-local-input amc-hidden',\ndisabled:'true',placeholder:'{{id_info_tip}}',value:'',\n_y:'idcard',_t:'input'}]}]}]},{\n'sp-view-id':'bankMobileArea',\n_c:'_CardDetailPage_jorn-c-margin-top-box amc-v-box amc-hidden',\n_t:'div',_cd:[{_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-cell-box-desc _CardDetailPage_jorn-c-l-label-ellipsis',\n_t:'label',_x:'{{reserved_phone_no}}'},{\n'sp-view-id':'phoneInfoLayoutUp',\n_c:'_CardDetailPage_jorn-c-btn-img-box',_t:'div',_cd:[{\n'sp-view-id':'phoneInfoUp',\n_c:'_CardDetailPage_jorn-c-info-img',\nonclick:'onClickPhonenum',alt:'{{info}}',_t:'img'}]}]},{\n'sp-view-id':'bankMobileAreaBox',\n_c:'_CardDetailPage_jorn-c-detail-cell _CardDetailPage_jorn-c-ant-gray-border',\n_t:'div',_cd:[{\n_c:'amc-cell-m-box _CardDetailPage_jorn-c-amc-input-box-height',\n_t:'div',_cd:[{'sp-view-id':'bankMobile',_y:'phone',\noninput:'onBankMobileInput',onblur:'onInputBlur',\nonfocus:'onBankMobileFocus',\n_c:'_CardDetailPage_jorn-c-local-input',\nplaceholder:'{{input_bank_phone}}',value:'',_t:'input'}]},{\n'sp-view-id':'modifyPhoneNumBtn',\n_c:'_CardDetailPage_jorn-c-btn-right-label amc-theme-color amc-hidden',\n_t:'label',_x:'{{modify_phone_num}}'}]}]},{\n'sp-view-id':'protocolsArea',\n_c:'amc-align-center _CardDetailPage_jorn-c-protocol-box amc-hidden',\n_t:'div',_cd:[{_c:'amc-align-center',_t:'div',_cd:[{\n_c:'amc-ellipsis _CardDetailPage_jorn-c-protocol-agree _CardDetailPage_jorn-c-protocol-font-size',\n_t:'label',_x:'{{look}}'}]},{_c:'amc-flex-1',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-protocol-font-size amc-theme-color amc-flex-1',\nonclick:'showProtocol',_t:'label',\n_x:'{{serve_protocol_full}}'}]}]},{\n'sp-view-id':'buttonArea',\n_c:'_CardDetailPage_jorn-c-button-box',_t:'div',_cd:[{\n'sp-view-id':'submit',\n_c:'_CardDetailPage_jorn-c-amc-btn-primary _CardDetailPage_jorn-c-amc-btn-primary _CardDetailPage_jorn-c-amc-btn-primary amc-justify-center amc-align-center',\ndisabled:'true',onclick:'onSubmit',_t:'div',_cd:[{\n'sp-view-id':'loading',src:'indicatior',\n_c:'amc-loading-img amc-hidden amc-text-white-clolor',\n_t:'img'},{'sp-view-id':'btnText',\n_c:'_CardDetailPage_jorn-c-amc-btn-disabled-color _CardDetailPage_jorn-c-font-super amc-ellipsis amc-flex-1 amc-text-center',\n_t:'label'}]}]},{\n_c:'amc-flex-1 _CardDetailPage_jorn-c-bottom-box amc-v-box',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-bottom-text-area',\n_t:'div',_cd:[{'sp-view-id':'asiImg',\n_c:'_CardDetailPage_jorn-c-asi-img',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*Am_ZQq-lR_cAAAAAAAAAAABkARQnAQ',\n_t:'img'},{'sp-view-id':'securityInfo',\n_c:'_CardDetailPage_jorn-c-bottom-text amc-ellipsis',\n_t:'label',_x:'{{info_sec}}'}]}]},{\n'sp-view-id':'fillBottomBox',_c:'amc-hidden',_t:'div'}]},{\n'sp-view-id':'divideLine',\n_c:'_CardDetailPage_jorn-c-card-1px-line amc-hidden',\n_t:'div'},{'sp-view-id':'stickyButtonArea',\n_c:'amc-hidden amc-v-box _CardDetailPage_jorn-c-bottom-sticky-box',\n_t:'div',_cd:[{'sp-view-id':'stickyProtocolsArea',\n_c:'amc-align-center amc-hidden',_t:'div',_cd:[{\n_c:'amc-align-center',_t:'div',_cd:[{\n_c:'amc-ellipsis _CardDetailPage_jorn-c-protocol-agree _CardDetailPage_jorn-c-protocol-font-size',\n_t:'label',_x:'{{look}}'}]},{_c:'amc-flex-1',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-protocol-font-size amc-theme-color amc-flex-1',\nonclick:'showProtocol',_t:'label',\n_x:'{{serve_protocol_full}}'}]}]},{\n_c:'_CardDetailPage_jorn-c-sticky-button-box',_t:'div',_cd:[\n{'sp-view-id':'stickySubmit',\n_c:'_CardDetailPage_jorn-c-amc-btn-primary _CardDetailPage_jorn-c-amc-btn-primary _CardDetailPage_jorn-c-amc-btn-primary amc-justify-center amc-align-center',\ndisabled:'true',onclick:'onSubmit',_t:'div',_cd:[{\n'sp-view-id':'stickyLoading',src:'indicatior',\n_c:'amc-loading-img amc-hidden amc-text-white-clolor',\n_t:'img'},{'sp-view-id':'stickyBtnText',\n_c:'_CardDetailPage_jorn-c-amc-btn-disabled-color _CardDetailPage_jorn-c-font-super amc-ellipsis amc-flex-1 amc-text-center',\n_t:'label'}]}]},{\n_c:'amc-flex-1 _CardDetailPage_jorn-c-sticky-bottom-box amc-v-box',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-bottom-text-area',\n_t:'div',_cd:[{'sp-view-id':'asiImg',\n_c:'_CardDetailPage_jorn-c-asi-img',\nsrc:'https://gw.alipayobjects.com/mdn/rms_33ea73/afts/img/A*Am_ZQq-lR_cAAAAAAAAAAABkARQnAQ',\n_t:'img'},{'sp-view-id':'securityInfo',\n_c:'_CardDetailPage_jorn-c-bottom-text amc-ellipsis',\n_t:'label',_x:'{{info_sec}}'}]}]}]},{'sp-view-id':'dlg',\n_c:'_CardDetailPage_jorn-c-amc-dlg-box amc-v-box',\n_t:'dialog',_cd:[{_c:'amc-self-stretch',_t:'div',_cd:[{\n'sp-view-id':'dlgTitle',\n_c:'amc-dlg-title amc-ellipsis-3-line amc-flex-1 amc-text-center',\n_t:'label'}]},{_c:'amc-justify-center',_t:'div',_cd:[{\n'sp-view-id':'dlgImg',_t:'img'}]},{_c:'amc-self-stretch',\n_t:'div',_cd:[{'sp-view-id':'dlgText',\n_c:'amc-dlg-txt _CardDetailPage_jorn-c-dlg-txt amc-flex-1 amc-text-center',\n_t:'label'}]},{\n_c:'amc-adapt-line _CardDetailPage_jorn-c-amc-dlg-width',\n_t:'div'},{onclick:'onClickDlgClose',\n_c:'amc-theme-color amc-dlg-btn',_t:'button',\n_x:'{{i_know_it}}'}]},{'sp-view-id':'userDlg',\n_c:'_CardDetailPage_jorn-c-amc-dlg-box amc-v-box',\n_t:'dialog',_cd:[{_c:'_CardDetailPage_jorn-c-dlg-title-box',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-dlg-title',\n_t:'label',_x:'{{user_name_title}}'}]},{\n_c:'amc-v-box _CardDetailPage_jorn-c-amc-dlg-width',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',\n_cd:[{_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{name_tip_1}}'}]},{_c:'_CardDetailPage_jorn-c-tip-box',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'}\n,{_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',\n_t:'label',_x:'{{name_tip_2}}'}]},{\n_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n'sp-view-id':'nameTip3Label',\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',\nonclick:'dial',_t:'label',_x:'{{name_tip_3}}'}]},{\n_c:'amc-adapt-line _CardDetailPage_jorn-c-dlg-line',_t:'div'\n},{_c:'amc-align-center amc-justify-center',\nonclick:'onClickNameClose',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-btn-text amc-flex-1 amc-ellipsis amc-theme-color',\n_t:'label',_x:'{{cashier_no_dlg_btn}}'}]}]}]},{\n'sp-view-id':'phoneDlg',\n_c:'_CardDetailPage_jorn-c-amc-dlg-box amc-v-box',\n_t:'dialog',_cd:[{_c:'_CardDetailPage_jorn-c-dlg-title-box',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-dlg-title',\n_t:'label',_x:'{{phone}}'}]},{\n_c:'amc-v-box _CardDetailPage_jorn-c-amc-dlg-width',\n_t:'div',_cd:[{_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',\n_cd:[{_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n'sp-view-id':'phoneTip1',\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_1}}'}]},{\n_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_2}}'}]},{\n_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_3}}'}]},{\n_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n'sp-view-id':'phoneTip4Label',\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',\nonclick:'dial',_t:'label',_x:'{{phone_tip_4}}'}]},{\n_c:'_CardDetailPage_jorn-c-tip-box',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-dot',_t:'div'},{\n_c:'_CardDetailPage_jorn-c-dlg-list amc-flex-1',_t:'label',\n_x:'{{phone_tip_5}}'}]},{\n_c:'amc-adapt-line _CardDetailPage_jorn-c-dlg-line',_t:'div'\n},{_c:'amc-align-center amc-justify-center',\nonclick:'onClickPhonenumClose',_t:'div',_cd:[{\n_c:'_CardDetailPage_jorn-c-dlg-btn-text amc-flex-1 amc-ellipsis amc-theme-color',\n_t:'label',_x:'{{cashier_no_dlg_btn}}'}]}]}]}]}},\nt.componentName='CardDetailPage',\nt.componentHashName='CardDetailPage_jorn',t}(i.BNComponent)\n;function S(e){if(s.amc.fn.isObject(e))return e;try{\nreturn JSON.parse(e)}catch(e){return{}}}function O(e){if(\ns.amc.fn.isString(e))return e.replace(/\\s/g,'')}\nt.CardDetailPage=T,x=!1,document.viewDidAppear=function(){\nx?s.amc.fn.spmExposureResume():x=!0},A=s.amc.fn.docConfig,\ns.amc.fn.docConfig=function(){var e=JSON.parse(A())\n;return e.navi={naviBarColor:'#f5f5f5',statusBarStyle:'dark'\n},JSON.stringify(e)},window.onload=function(){try{\ns.amc.fn.spmPageCreate('a283.b5788',S(r));var e=new T,\nt=s.amc.fn.getNav(s.amc.res.navBack,\ns.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'':'{{return}}',\n'{{write_card_info}}','','',function(){e.onBack()},function(\n){},{backMode:'back'})\n;document.body.style.height=s.amc.isAndroid?window.innerHeight:s.amc.specs.bodyHeight\n,document.body.appendChild(t),\nwindow.flybird&&window.flybird.local&&window.flybird.local.agednessVersion&&(\nwindow.remScale=1.2,document.body.style.height/=1.2),\ns.amc.fn.spmExposure('a283.b5788.c12704.d23270',S(r),!1),\ne.mountTo(document.body),window.onKeyDown=function(){\n4==event.which&&e.onBack()},document.setProp(\n'focusableInTouchMode',{value:!0})}catch(e){\ns.amc.fn.logPageInit&&s.amc.fn.logPageInit(!1)}}},function(e\n,t,n){'use strict';var c=this&&this.__assign||function(){\nreturn(c=Object.assign||function(e){for(var t,n=1,\na=arguments.length;n<a;n++)for(var o in t=arguments[n]\n)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])\n;return e}).apply(this,arguments)};Object.defineProperty(t,\n'__esModule',{value:!0});var C=n(0),I=n(1),x=n(2),w=n(6),\na=function(){function e(){this.vueModel={data:{},compute:{}}\n,this._componentName=this.constructor.componentName,\nthis._htmlString=this.constructor.componentHTML,\nthis._componentJson=this.constructor.getComponentJson(),\nthis._componentCSSRules=this.constructor.getComponentCSSRules(\n),this._hash=I.randomStr(),this._hasRootViewBuilt=!1,\nthis._rootView=null,this._componentId='',\nthis._subComponents=[],this._subComponentsMap={},\nthis._viewsIdMap={}}return e.getComponentCSSRules=function(\n){throw new Error('E0100')},e.getComponentJson=function(){\nthrow new Error('E0101')},e.prototype.mountTo=function(e,t){\nif(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t\n):e.appendChild(n),this._triggerOnMounted()):C.logger.e(\n'Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},\nObject.defineProperty(e.prototype,'debugName',{get:function(\n){\nreturn'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'\n},enumerable:!0,configurable:!0}),\ne.prototype.getMountedRootView=function(){\nreturn this._hasRootViewBuilt?this._rootView:null},\ne.prototype.getMountedParentView=function(){\nvar e=this.getMountedRootView();return e?e.parentNode:null},\ne.prototype.getSubComponentById=function(e,t){\nvar n=this.debugName+'#SCById',a=this._subComponentsMap[e]\n;if(!a)return null;var o='',i='';try{\no=a.constructor.componentName,i=t.componentName}catch(e){\nC.logger.e(n,'E0104 '+e)}return o&&o===i?a:(C.logger.e(n,\n'E0105 '+o+', '+i),null)},\ne.prototype.getViewInComponentById=function(e){\nreturn this._viewsIdMap[e]},\ne.prototype.getComponentId=function(){\nreturn this._componentId},\ne.prototype.createStyledElement=function(e,t,n){\nvar a=document.createElement(e);if(a)return e&&(\na.className+=' '+this._css(e,2)),n&&(\na.className+=' '+this._csses(n,1)),t&&(\na.className+=' '+this._css('#'+t,2)),a},\ne.prototype.applyStyleTo=function(e,t){e&&(\ne.className+=' '+this._csses(t,1))},\ne.prototype.css=function(e){return this._css(e,0)},\ne.prototype.csses=function(e){return this._csses(e,0)},\ne.prototype._csses=function(e,t){var n=this;return e.split(\n' ').map(function(e){return n._css(e,t)}).join(' ')},\ne.prototype._css=function(e,t){if(!e)return''\n;var n=this._componentCSSRules;if(!n)return e;switch(\ne.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t\n){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e\n;case 2:default:return e}}},\ne.prototype._triggerOnMounted=function(){new w.Observer(\nthis.vueModel.data),C.logger.i('','I0106 '+this.debugName)\n;for(var e=0,t=this._subComponents;e<t.length;e++){\nvar n=t[e];n&&n._triggerOnMounted()}\nthis.onMounted&&this.onMounted()},\ne.prototype._getMethod=function(e){var t=this[e]\n;return t instanceof Function?t:null},\ne.prototype._acquireComponentJson=function(){\nvar e=this.debugName+'#acCJ',\nt=x.ComponentRegistry.getComponentJson(this._componentName)\n;return t?(C.logger.i(e,'I0107'),t\n):void 0!==this._componentJson?(C.logger.i(e,'I0108'),\nx.ComponentRegistry.putComponentJson(this._componentName,\nthis._componentJson),this._componentJson):(C.logger.e(e,\n'E0109'),null)},e.prototype._acquireRootView=function(){\nvar e=this.debugName+'#acRV';if(this._hasRootViewBuilt\n)return C.logger.i(e,'I0110'),this._rootView\n;var t=this._acquireComponentJson();return t?(\nthis._rootView=this._convertJsonToBNNode(t,\nthis.vueModel.data||{}),this._hasRootViewBuilt=!0,\nC.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111')\n,null)},e.prototype._genArrayChildNode=function(e,t,n,a,o){\nvar i=w.vueUtils.item2ArrayIndex(o,t),\nr=this._convertJsonToBNNode(e,c({},t,{item:n,index:a,\narrayName:i}));return r?(r.setAttribute('index',a),\nr.setAttribute('for_name',o),r):null},\ne.prototype._convertJsonToBNNode=function(e,d){var s=this,\nt=this.debugName+'#cJTB';if(void 0===e._t)return null\n;var _=document.createElement(e._t),p=[];if(void 0!==e._cd\n)for(var n=function(r){if(r['v-for']||r['v-for-cal']){\nvar e=!r['v-for']&&!!r['v-for-cal'],t=(\ne?u.vueModel.compute:d)||{},n=w.vueUtils.getObject(\ne?r['v-for-cal']:r['v-for'],t,e),c=e?w.vueUtils.rmSymbol(\nr['v-for-cal']):w.vueUtils.rmSymbol(r['v-for']);if(!c||!n\n)return'continue';for(var a in n)if(n.hasOwnProperty(a)){\nvar o=u._genArrayChildNode(r,d,n[a],a,c);o&&p.push(o)}\nvar l=document.createElement('div');l&&(\nl.style.display='none',l.setAttribute('for_end',c),p.push(l)\n,new w.Watcher(c,t,function(e){if(_){w.rmWatchers(c);for(\nvar t=0,n=_.childNodes;t<n.length;t++){var a=n[t]\n;a.getAttribute('for_name')===c&&_.removeChild(a)}if(e)for(\nvar o in e)if(e.hasOwnProperty(o)){\nvar i=s._genArrayChildNode(r,d,e[o],o,c);i&&_.insertBefore(i\n,l)}}},e).id=d.arrayName)}else{var i=u._convertJsonToBNNode(\nr,d);if(!i)return'continue';p.push(i)}},u=this,a=0,\no=e._cd;a<o.length;a++)n(o[a]);if(!_)return null\n;d&&d.index&&_.setAttribute('index',d.index)\n;var i=e['bn-component']||e['sp-component'];if(i){\nC.logger.i(t,'I0113 '+i)\n;var r=x.ComponentRegistry.createComponent(i);if(!r\n)return C.logger.e(t,'E0114 '+i+', '+r),null\n;var c=e['bn-component-id']||e['sp-component-id']\n;return c&&(r._componentId=c),r.onCreated&&r.onCreated(),\nC.logger.i(t,'I0115 '+r.debugName+', '+c),\nthis._subComponents.push(r),c&&!this._subComponentsMap[c]&&(\nthis._subComponentsMap[c]=r),r._acquireRootView()}\nvar l=e['bn-view-id']||e['sp-view-id'];for(var m in l&&(\nC.logger.i(t,'I0116 '+l),this._viewsIdMap[l]||(\nthis._viewsIdMap[l]=_)),e._i&&(_.id=e._i),e._c&&(\n_.className=e._c),e._s&&(_.style.cssText=e._s),e._x&&(\n_.innerText=e._x),e._y&&(_.type=e._y),e)if(e.hasOwnProperty(\nm))if(0===m.indexOf('on')){var g=this._getMethod(e[m]);g&&(\n_[m]=g.bind(this,_))}else if(0===m.indexOf('_'));else if(\n0===m.indexOf('bn-')||0===m.indexOf('sp-'));else if(\nI.startsWith(m,'v-')){var v=m.split('-');if(\n2===v.length||3===v.length){var f=v[1]\n;2===v.length?new w.NodeCompile(d).compile(f,_,e[m],e._t\n):'cal'===v[2]&&new w.NodeCompile(this.vueModel.compute,!0\n).compile(f,_,e[m],e._t)}else _[m]=e[m]}else _[m]=e[m];for(\nvar h=0,b=p;h<b.length;h++){var y=b[h];_.appendChild(y)}\nreturn _},e.componentName='',e.componentHTML='',\ne.componentCSS='',e.componentHashName='',e}()\n;t.BNComponent=a},function(module,exports,\n__webpack_require__){'use strict';Object.defineProperty(\nexports,'__esModule',{value:!0})\n;var util_1=__webpack_require__(1),\namc_types_1=__webpack_require__(3),watchers;function notify(\n){watchers&&watchers.forEach(function(e){e.update()})}\nfunction rmWatchers(t){watchers=watchers.filter(function(e){\nreturn e.id!==t})}exports.rmWatchers=rmWatchers\n;var Watcher=function(){function e(e,t,n,a){if(this.id='',\nthis.lazy=!1,t&&'object'==typeof t){if(this.lazy=a,\nthis.callback=n,util_1.startsWith(e,'item'\n)&&t.arrayName&&t.index){var o=e.replace('item','')\n;this.expression=t.arrayName+'.'+t.index,o&&(\nthis.expression+=o)}else this.expression=e;this.data=t,\nthis.value=vueUtils.getVal(e,t,this.lazy),watchers||(\nwatchers=[]),watchers.push(this)}}\nreturn e.prototype.update=function(){if(\nthis.data&&this.expression&&this.callback){\nvar e=vueUtils.getVal(this.expression,this.data,this.lazy),\nt=this.value;vueUtils.equals(e,t)||(this.value=e,\nthis.callback(e))}},e}();exports.Watcher=Watcher\n;var Observer=function(){function e(e){this.observe(e)}\nreturn e.prototype.observe=function(t){var n=this\n;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){\ntry{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})}\n,e.prototype.defineReactive=function(e,t,n){var a=this\n;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,\nget:function(){return n},set:function(e){vueUtils.equals(e,n\n)||(n=e,a.observe(e),notify())}})},e}()\n;exports.Observer=Observer;var NodeCompile=function(){\nfunction e(e,t){void 0===t&&(t=!1),this.data=e||{},\nthis.lazy=t}return e.prototype.compile=function(n,e,t,a){\nvar o=this;if(e)switch(n){case'text':this.labelProcess(e,t,\nfunction(e,t){e.innerText=void 0===t?'':t});break\n;case'html':this.labelProcess(e,t,function(e,t){\ne.innerHtml=void 0===t?'':t});break;case'class':\nthis.labelProcess(e,t,function(e,t){var n=e.className,\na=e.getAttribute('v-class-name')||'';n=n&&n.replace(a,''\n).replace(/\\s+$/,''),e.setAttribute('v-class-name',t),\ne.className=n?n+' '+t:t});break;case'style':\nthis.eventProcess(e,t,function(e,t){\nvar n=util_1.tryJSONParse(t);util_1.copyObj(n,e.style)})\n;break;case'model':this.eventProcess(e,t,function(e,t){\ne.value=t}),'input'===a?e.oninput=function(){\nvueUtils.setTextVal(t,e.value,o.data)}:'switch'===a&&(\ne.onchange=function(e){vueUtils.setTextVal(t,e||'off',o.data\n)});break;case'if':this.eventProcess(e,t,function(e,t){\n!0===t?(e.style.display='flex',spmUtils.process(e,function(e\n){amc_types_1.amc.fn.spmExposure(e.spmId,e.param4Map,\ne.doNotResume)})):e.style.display='none'});break;case'spm':\nthis.labelProcess(e,t,function(e,t){e.setAttribute('spm',\nvoid 0===t?'':t)});break;case'uep':this.labelProcess(e,t,\nfunction(e,t){t&&util_1.startsWith(t,'a283'\n)&&e.setAttribute('behaviorInfo',JSON.stringify({spm:t,\nbizCode:'pay',extParam:{}}))});break;case'click':\nthis.eventProcess(e,t,function(e,t){vueUtils.isFunction(t\n)?e.onclick=function(){t(e),spmUtils.process(e,function(e){\namc_types_1.amc.fn.spmClick(e.spmId,e.param4Map)})\n}:e.onclick=function(){}});break;case'focus':\nthis.eventProcess(e,t,function(e,t){vueUtils.isFunction(t\n)?e.onfocus=function(){t(e)}:e.onfocus=function(){}});break\n;case'blur':this.eventProcess(e,t,function(e,t){\nvueUtils.isFunction(t)?e.onblur=function(){t(e)\n}:e.onfocus=function(){}});break;default:-1===t.indexOf('@{'\n)?e.setAttribute(n,t):this.labelProcess(e,t,function(e,t){\ne.setAttribute(n,void 0===t?'':t)})}},\ne.prototype.labelProcess=function(n,a,o){var i=this,\ne=a.match(/@\\{([^}]+)\\}/g),t=a;e&&0<e.length&&(\nt=vueUtils.getTextVal(a,this.data,this.lazy),e&&e.forEach(\nfunction(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(\nnew Watcher(t[1],i.data,function(e){o(n,vueUtils.getTextVal(\na,i.data,i.lazy))},i.lazy).id=i.data.arrayName)})),o(n,t)},\ne.prototype.eventProcess=function(t,e,n){\nvar a=/@\\{([^}]+)\\}/g.exec(e),o=vueUtils.getObject(e,\nthis.data,this.lazy);a&&1<a.length&&(new Watcher(a[1],\nthis.data,function(e){n(t,e)},this.lazy\n).id=this.data.arrayName),n(t,o)},e}()\n;exports.NodeCompile=NodeCompile;var spmUtils=function(){\nfunction e(){}return e.process=function(e,t){\nvar n=e.getAttribute('spm');if(n)try{var a=JSON.parse(n)\n;a&&a.spmId&&t(a)}catch(e){}},e}(),vueUtils=function(){\nfunction vueUtils(){}\nreturn vueUtils.item2ArrayIndex=function(e,t){var n=e;if(\nutil_1.startsWith(e,'item')&&t.arrayName&&t.index){\nvar a=e.replace('item','');n=t.arrayName+'.'+t.index,a&&(\nn+=a)}return n},vueUtils.getVal=function(expr,data,lazy){if(\nexpr){var values=expr.match(/##([^#]+)##/g);if(\nvalues&&0<values.length){for(var func_1=expr,\nindex=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(\n/##([^#]+)##/,'vueArgs['+index+']'),index++;for(\nvar _vueArgs=[],i=0;i<index;i++)_vueArgs.push(\nthis.getRealVal(values[i].replace(/##/g,''),data,lazy))\n;return function(vueArgs){return eval(func_1)}(_vueArgs)}\nreturn this.getRealVal(expr,data,lazy)}},\nvueUtils.getRealVal=function(e,t,n){if(e){var a=e.split('.')\n,o=n&&vueUtils.isFunction(t[a[0]])?t[a[0]]():t;return(\nn?a.slice(1):a).reduce(function(e,t){return e[t]},o)}},\nvueUtils.getTextVal=function(e,o,i){var r=this\n;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],\nn=0;n<arguments.length;n++)t[n]=arguments[n];if(i\n)e=r.getVal(t[1],o,i);else{var a=vueUtils.item2ArrayIndex(\nt[1],o);e=r.getVal(a,o,!1)}return void 0===e?'':e})},\nvueUtils.getObject=function(e,t,n){\nvar a=/@\\{([^}]+)\\}/g.exec(e);if(a&&1<a.length\n)return this.getVal(a[1],t,n)},vueUtils.rmSymbol=function(e\n){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''\n},vueUtils.setVal=function(e,a,t){var o=e.split('.')\n;return o.reduce(function(e,t,n){\nreturn n===o.length-1?e[t]=a:e[t]},t)},\nvueUtils.setTextVal=function(e,t,n){\nvar a=/@\\{([^}]+)\\}/g.exec(e);a&&1<a.length&&this.setVal(\na[1],t,n)},vueUtils.equals=function(e,t){return this.eq(e,t,\nvoid 0,void 0)},vueUtils.eq=function(e,t,n,a){if(e===t\n)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t\n;var o=toString.call(e);if(o!==toString.call(t))return!1\n;switch(o){case'[object RegExp]':case'[object String]':\nreturn''+e==''+t;case'[object Number]':\nreturn+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t\n;case'[object Date]':case'[object Boolean]':return+e==+t}\nvar i='[object Array]'===o;if(!i){if(\n'object'!=typeof e||'object'!=typeof t)return!1\n;var r=e.constructor,c=t.constructor;if(r!==c&&!(\nthis.isFunction(r)&&r instanceof r&&this.isFunction(c\n)&&c instanceof c)&&'constructor'in e&&'constructor'in t\n)return!1}a=a||[];for(var l=(n=n||[]).length;l--;)if(\nn[l]===e)return a[l]===t;if(n.push(e),a.push(t),i){if((\nl=e.length)!==t.length)return!1;for(;l--;)if(!this.eq(e[l],\nt[l],n,a))return!1}else{var d=Object.keys(e),s=void 0;if(\nl=d.length,Object.keys(t).length!==l)return!1;for(;l--;)if(\ns=d[l],!t.hasOwnProperty(s)||!this.eq(e[s],t[s],n,a)\n)return!1}return n.pop(),a.pop(),!0},\nvueUtils.isFunction=function(e){\nreturn'function'==typeof e||!1},vueUtils}()\n;exports.vueUtils=vueUtils},function(e,t,n){'use strict'\n;var a,o=this&&this.__extends||(a=function(e,t){return(\na=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}a(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var i=function(){function i(){\nthis.enabled=!0}return i.fmtLine=function(e,t,n,a){var o=''\n;return a&&(\no=a instanceof Error?'- '+a.name+': '+a.message+' - '+a.stack:'- '+a\n),'['+e+']['+i.fmtTime()+']['+t+']'+n+' '+o},\ni.fmtTime=function(){var e=new Date;return e.getHours(\n)+':'+e.getMinutes()+':'+e.getSeconds(\n)+'.'+e.getMilliseconds()},i.prototype.enable=function(){\nthis.enabled=!0},i.prototype.disable=function(){\nthis.enabled=!1},i}();t.Logger=i,t.logger=new(function(e){\nfunction t(){return null!==e&&e.apply(this,arguments)||this}\nreturn o(t,e),t.prototype.e=function(e,t,n){},\nt.prototype.i=function(e,t,n){},t}(i))}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body", "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-card-detail-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0240", "tplId": "QUICKPAY@cashier-card-detail-flex", "tplVersion": "5.4.1"}