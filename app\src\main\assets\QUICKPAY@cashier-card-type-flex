{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"tag": "meta", "type": "i18n", "locale": {"zh_HK": {"username_no_pure_num": "姓名不能為純數字，請重新輸入", "to_vi": "開始身份驗證", "username_no_star_after_edit": "姓名中不能帶有*號，請重新輸入", "title": "添加銀行卡", "switch": "切換"}, "zh_TW": {"username_no_pure_num": "姓名不能為純數字，請重新輸入", "to_vi": "開始身份驗證", "username_no_star_after_edit": "姓名中不能帶有*號，請重新輸入", "title": "添加銀行卡", "switch": "切換"}, "en_US": {"username_no_pure_num": "The name cannot be pure numbers, please re-enter", "to_vi": "Authenticating", "username_no_star_after_edit": "The name cannot contain *, please re-enter", "title": "Add Bank Card", "switch": "Switch "}, "zh_CN": {"username_no_pure_num": "姓名不能为纯数字，请重新输入", "to_vi": "开始身份验证", "username_no_star_after_edit": "姓名中不能带有*号，请重新输入", "title": "添加银行卡", "switch": "切换"}}}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"src": "AlipaySDK.bundle/amc-meta.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": ".amc-nav-horiz-line-android{background-color:#F5F5F5;height:0}.amc-nav-container-android{display:flex;background-color:#F5F5F5;height:48px;opacity:0.96;flex-direction:column}.amc-nav-box-android{align-items:center;display:flex;flex:1.0;justify-content:center;height:47px;background-color:#F5F5F5}.card-bg-default{background-image:linear-gradient(to right bottom, #c4ced5 0%, #a6adb7 100%)}.card-bg-blue{background-image:linear-gradient(to right bottom, #2d8ed7 0%, #2e61d0 100%)}.card-bg-red{background-image:linear-gradient(to right bottom, #FF7666 0%, #FE5162 100%)}.card-bg-green{background-image:linear-gradient(to right bottom, #01b4a2 0%, #0389ac 100%)}.card-bg-yellow{background-image:linear-gradient(to right bottom, #ffb557 0%, #ff8f1f 100%)}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._BankTypePage_laeb-c-main-body{padding:0 12px;flex:1.0;background-color:#F5F5F5}._BankTypePage_laeb-c-selection-tip{color:#333333;font-size:22px}._BankTypePage_laeb-c-card-type-box{margin:0 0 12px 0;display:flex;justify-content:flex-start;align-items:center;flex-direction:column;align-self:stretch;height:161px;background-image:linear-gradient(to right, #7FFFFFFF 0%, #FFFFFF 100%);border-radius:8px}._BankTypePage_laeb-c-current-card-type-box{flex-direction:column;align-self:stretch;height:131px;border-radius:8px;padding:20px 12px;background-image:linear-gradient(to right bottom, #FF7666 0%, #FE5162 100%)}._BankTypePage_laeb-c-current-card-type-content-box{display:flex;justify-content:flex-start;align-items:center;flex-direction:row;align-self:stretch}._BankTypePage_laeb-c-card-type-logo-box{background-color:#FFFFFF;width:40px;height:40px;border-radius:20px;align-items:center;justify-content:center}._BankTypePage_laeb-c-card-type-logo{width:26px;height:26px;border-radius:13px}._BankTypePage_laeb-c-card-type-text-box{flex-direction:column;justify-content:flex-start;align-items:flex-start;margin-left:12px}._BankTypePage_laeb-c-card-type-title{font-size:15px;color:#FFFFFF;font-weight:bold}._BankTypePage_laeb-c-card-type-desc{font-size:12px;color:#FEFAFA;line-height:15px;margin-top:4px}._BankTypePage_laeb-c-card-type-tail-box{font-size:17px;color:#FFFFFF;text-align:center}._BankTypePage_laeb-c-card-type-background-logo{width:125px;height:125px;opacity:0.06;align-self:flex-end}._BankTypePage_laeb-c-card-type-end-box{align-self:flex-end;flex-direction:row;align-items:center;justify-content:center;margin-right:8px}._BankTypePage_laeb-c-card-type-end-box-img{width:11px;height:11px}._BankTypePage_laeb-c-card-type-end-box-text{margin-left:4px;font-size:14px;color:#1677FF}._BankTypePage_laeb-c-cert-detail-box{border-radius:8px;background-color:#FFFFFF}._BankTypePage_laeb-c-cert-tip{font-size:16px;color:#333333;font-weight:bold;margin:16px}._BankTypePage_laeb-c-cert-input-box{height:47px;align-items:center;padding:0 16px}._BankTypePage_laeb-c-cert-item-title{font-size:17px;color:#333;text-align:left;width:90px;max-width:90px}._BankTypePage_laeb-c-margin-l{margin-left:16px}._BankTypePage_laeb-c-cert-item-input{flex:1.0;border:0;color:#333;font-size:17px;padding:0px;white-space:nowrap;placeholder-color:#CCC}._BankTypePage_laeb-c-protocol-box{text-align:left;margin-top:26px}._BankTypePage_laeb-c-protocol-title{font-size:14px;color:#000}._BankTypePage_laeb-c-protocol{font-size:14px;color:#1677FF}._BankTypePage_laeb-c-amc-primary-btn{height:47px;max-height:47px;min-height:47px;border-radius:2px;background-color:#1677FF;font-size:18px;color:#FFF;margin-top:16px}._BankTypePage_laeb-c-amc-primary-btn:active{background-color:#136BE6;color:#9ABEF4}._BankTypePage_laeb-c-amc-primary-btn:disabled{background-color:#9DC3FB;color:#C4DBFC}._BankTypePage_laeb-c-primary-btn-text{color:#FFF;font-size:18px;text-align:center}._BankTypePage_laeb-c-loading-color{color:#FFF}._BankTypePage_laeb-c-arrow-right{width:8px;height:13px}._BankTypePage_laeb-c-bottom-tip{font-size:14px;color:#999}._BankTypePage_laeb-c-bottom-tip-box{margin:16px 0}._BankTypePage_laeb-c-header-box{margin-bottom:40px;margin-top:16px;flex-direction:column}._BankTypePage_laeb-c-sub-title-box{flex-direction:row;margin-top:12px}._BankTypePage_laeb-c-sub-title{font-size:14px;color:#333333;margin-left:4px}._BankTypePage_laeb-c-sub-title-logo{width:13px;height:13px}._BankTypePage_laeb-c-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;align-self:stretch;justify-content:flex-start;overflow:hidden;margin-top:4px}._BankTypePage_laeb-c-tag{margin-right:3px;padding:2px;border-radius:2px;background-color:rgba(0, 0, 0, 0.06)}._BankTypePage_laeb-c-label-tag{font-size:11px;color:#FFFFFF;line-height:11px}._CommonDialog_mbaf-c-dlg-box{border:0;background-color:#fff;border-radius:8px;padding:25px 0 0;width:280px}._CommonDialog_mbaf-c-margin-box-lr{margin-left:16px;margin-right:16px}._CommonDialog_mbaf-c-title-box{margin-bottom:12px}._CommonDialog_mbaf-c-msg{flex:1.0;text-align:center;color:#333333;font-size:15px;line-height:21px}._CommonDialog_mbaf-c-title{font-size:18px;color:#333333;text-align:center;font-weight:bold}._CommonDialog_mbaf-c-btn-text{font-size:18px}._CommonDialog_mbaf-c-btn-div{padding:12px 0;background-color:#fff;align-self:stretch}._CommonDialog_mbaf-c-btn-div:active{background-color:#ddd}._CommonDialog_mbaf-c-align-self-box{align-self:stretch}._CommonDialog_mbaf-c-btn-line{background-color:#e5e5e5}._CommonDialog_mbaf-c-labels-container{display:flex;flex-wrap:wrap;flex-direction:row;justify-content:center;margin:12px 16px 20px;align-self:stretch;overflow:hidden}._CommonDialog_mbaf-c-label{background-color:#FFECE3;color:#FF6010;margin-left:4px;margin-right:4px;margin-top:6px;border-radius:2px;font-size:14px;padding:2px 4px}._CommonDialog_mbaf-c-close-btn-container{margin:-12px 13px 13px 13px;align-self:stretch}._CommonDialog_mbaf-c-close-btn{width:18px;height:18px}._CommonDialog_mbaf-c-content-img-container{margin:0px 0px 25px 0}._CommonDialog_mbaf-c-protocol-box{margin-bottom:8px}._CommonDialog_mbaf-c-single-protocol-label{font-size:15px;line-height:21px;color:#4B6B99}._CommonDialog_mbaf-c-addition{text-align:left;color:#666666;margin:16px 0;font-size:15px;line-height:21px}._Button_b6r6-c-text-primary{font-size:18px;color:#fff;height:49px}._Button_b6r6-c-button-primary{height:49px;max-height:49px;min-height:49px;align-self:stretch}._Button_b6r6-c-lottie-loading{width:320px;height:45px}._Button_b6r6-c-lottie-margin{margin-top:-45px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 4c9118abc661c88b18a7e3053f0777cf517e7f88:D */!function(\nn){var o={};function i(e){if(o[e])return o[e].exports\n;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports\n,t,t.exports,i),t.l=!0,t.exports}i.m=n,i.c=o,i.d=function(e,\nt,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,\nget:n})},i.r=function(e){\n'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(\ne,Symbol.toStringTag,{value:'Module'}),\nObject.defineProperty(e,'__esModule',{value:!0})},\ni.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(\n4&e&&'object'==typeof t&&t&&t.__esModule)return t\n;var n=Object.create(null);if(i.r(n),Object.defineProperty(n\n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t\n)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o\n));return n},i.n=function(e){var t=e&&e.__esModule?function(\n){return e.default}:function(){return e};return i.d(t,'a',t)\n,t},i.o=function(e,t){\nreturn Object.prototype.hasOwnProperty.call(e,t)},i.p='',i(\ni.s=7)}([function(e,t,n){'use strict';Object.defineProperty(\nt,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n\n){'use strict';Object.defineProperty(t,'__esModule',{\nvalue:!0});var c=n(0);function o(e,t){if(e&&t){if(t===e.src\n)return;c.amc.isAndroid&&'none'===e.style.display?(\nc.amc.fn.show(e),window.setTimeout(function(){e.src=t},20)\n):e.src=t}}function i(e){if(!e)return 0;e=e.replace(\n/<\\/?[^>]+(>|$)/g,'');for(var t=0,n='',o=new RegExp(\n'[\\\\u4E00-\\\\u9FFF]+','g'),i=0,a=e;i<a.length;i++){var r=a[i]\n;c.amc.isIOS&&(' '<=r&&r<='~'&&o.test(n\n)||' '<=n&&n<='~'&&o.test(r))&&(t+=.5),\nt+=' '<=r&&r<='~'?.58:1,n=r}return t}t.mergeObject=function(\n){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]\n;var n={};if(e&&e.length)for(var o=0;o<e.length;o++){\nvar i=e[o];if(c.amc.fn.isObject(i))for(var a in i\n)i.hasOwnProperty(a)&&(n[a]=i[a])}return n},\nt.isFunction=function(e){\nreturn'[object Function]'===Object.prototype.toString.call(e\n)},t.isPreRender=function(e){return e&&(\ne.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender\n)},t.copyObj=function(e,t){for(var n in t||(t={}),e\n)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},\nt.doNothing=function(){},t.tryJSONParse=function(e){if(\nnull==e)return{};if(c.amc.fn.isObject(e))return e;try{\nreturn JSON.parse(e)}catch(e){return{}}},\nt.checkEmptyObj=function(e){return c.amc.fn.isString(e\n)?0===e.length:!(e&&0!==Object.keys(e).length)},\nt.substrWithFontWidth=function(e,t,n){if(!e)return e;for(\nvar o='',i=0,a=e.length,r=0;r<a;r++){var c=n?e[a-r-1]:e[r]\n;if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break\n}return o},t.calculateFontWidth=function(e){if(!e)return 0\n;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++\n)n.test(e[o])?t+=.45:t++;return Math.round(t)},\nt.deepCopy=function e(t){if(null==t||'object'!=typeof t\n)return t;var n;if(t instanceof Date)return(n=new Date\n).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(\nvar o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(\nt instanceof Object){for(var a in n={},t)t.hasOwnProperty(a\n)&&(n[a]=e(t[a]));return n}throw new Error(\n'Unable to copy obj! Its type isn\\'t supported.')},\nt.getConfig=function(e,t){setTimeout(function(){\ndocument.invoke('queryInfo',{queryKey:'configInfo',\nconfigKey:e},function(e){t(e.available)})},20)},\nt.showLoading=function(){setTimeout(function(){\ndocument.invoke('showLoading')},20)},t.hideLoading=function(\n){setTimeout(function(){document.invoke('hideLoading')},20)}\n,t.safeInvoke=function(e,t,n){\nc.amc.isAndroid?window.setTimeout(function(){\ndocument.invoke(e,t,n)},20):document.invoke(e,t,n)},\nt.safeLoadImgSrcAndSetMode=function(e,t){e&&t&&(\ne.contentmode=t.mode,o(e,t.src))},t.safeLoadImgSrc=o,\nt.calculateStyleLineHeight=function(e,t){\nreturn c.amc.isIOS?e:(e-t)/2****},\nt.calculateLabelShowLineNumber=function(e,t,n){if(!e||n<=0\n)return 1;var o=i(e)*t;return Math.ceil(o/n)},\nt.calculateFontCount=i,t.isLowDevice=function(){return!!(\nwindow.flybird&&window.flybird.local&&window.flybird.local.isLowDevice\n)},t.safeRemoveChildNode=function(t){if(t&&t.childNodes){\nfor(var e=[],n=0,o=t.childNodes;n<o.length;n++){var i=o[n]\n;e.push(i)}e.forEach(function(e){t.removeChild(e)})}}},\nfunction(e,t,n){'use strict';Object.defineProperty(t,\n'__esModule',{value:!0});var o,r=n(0),i=n(1);function a(e,t,\nn,o){var i;if(r.amc.isAndroid)i=document.createElement(\n'embed',t,function(){});else for(\nvar a in i=document.createElement('embed'),t\n)t.hasOwnProperty(a)&&(i[a]=t[a]);return n&&(i.className=n),\no?e.insertBefore(i,o):e.appendChild(i),i}\nt.modifyElementStyle=function(e,t,n){var o=t\n;r.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),\no&&i.copyObj(n,o.style)},t.modifyElementAttribute=function(e\n,t,n){if(t&&n){var o=t;if(r.amc.fn.isString(t)&&(\no=e.getViewInComponentById(t)),o)for(var i in n\n)n.hasOwnProperty(i)&&(o[i]=n[i])}},\nt.modifyElementClass=function(e,t,n,o){var i=t\n;r.amc.fn.isString(t)&&(i=e.getViewInComponentById(t)),i&&(\no||(i.className=''),e.applyStyleTo(i,n))},\nt.visibleElement=function(e,t,n){var o;void 0===n&&(n=!0),\nt&&(o=r.amc.fn.isString(t)?e.getViewInComponentById(t):t)&&(\nn?r.amc.fn.show(o):r.amc.fn.hide(o))},\nt.modifyElementCSS=function(e,t,n){if(t){var o=t\n;r.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),\no&&n&&(o.style.cssText=n)}},t.createEmbedViPlugin=function(e\n,t,n,o){return a(e,t,n,o)},t.createEmbedPlugin=a,\nt.getThemeColor=(o='',function(){return o||(\no=r.amc.fn.sdkGreaterThanOrEqual('10.8.39'\n)?'#1677FF':'#108EE9'),o})},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0});var o=n(4)\n;t.BNComponent=o.BNComponent;var i=n(5)\n;t.ComponentRegistry=i.ComponentRegistry;var a=n(9)\n;t.Logger=a.Logger,t.logger=a.logger},function(e,t,n){\n'use strict';var x=this&&this.__assign||function(){return(\nx=Object.assign||function(e){for(var t,n=1,\no=arguments.length;n<o;n++)for(var i in t=arguments[n]\n)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])\n;return e}).apply(this,arguments)};Object.defineProperty(t,\n'__esModule',{value:!0});var C=n(3),T=n(6),w=n(5),I=n(8),\no=function(){function e(){this.vueModel={data:{},compute:{}}\n,this._componentName=this.constructor.componentName,\nthis._htmlString=this.constructor.componentHTML,\nthis._componentJson=this.constructor.getComponentJson(),\nthis._componentCSSRules=this.constructor.getComponentCSSRules(\n),this._hash=T.randomStr(),this._hasRootViewBuilt=!1,\nthis._rootView=null,this._componentId='',\nthis._subComponents=[],this._subComponentsMap={},\nthis._viewsIdMap={}}return e.getComponentCSSRules=function(\n){throw new Error('E0100')},e.getComponentJson=function(){\nthrow new Error('E0101')},e.prototype.mountTo=function(e,t){\nif(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t\n):e.appendChild(n),this._triggerOnMounted()):C.logger.e(\n'Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},\nObject.defineProperty(e.prototype,'debugName',{get:function(\n){\nreturn'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'\n},enumerable:!0,configurable:!0}),\ne.prototype.getMountedRootView=function(){\nreturn this._hasRootViewBuilt?this._rootView:null},\ne.prototype.getMountedParentView=function(){\nvar e=this.getMountedRootView();return e?e.parentNode:null},\ne.prototype.getSubComponentById=function(e,t){\nvar n=this.debugName+'#SCById',o=this._subComponentsMap[e]\n;if(!o)return null;var i='',a='';try{\ni=o.constructor.componentName,a=t.componentName}catch(e){\nC.logger.e(n,'E0104 '+e)}return i&&i===a?o:(C.logger.e(n,\n'E0105 '+i+', '+a),null)},\ne.prototype.getViewInComponentById=function(e){\nreturn this._viewsIdMap[e]},\ne.prototype.getComponentId=function(){\nreturn this._componentId},\ne.prototype.createStyledElement=function(e,t,n){\nvar o=document.createElement(e);if(o)return e&&(\no.className+=' '+this._css(e,2)),n&&(\no.className+=' '+this._csses(n,1)),t&&(\no.className+=' '+this._css('#'+t,2)),o},\ne.prototype.applyStyleTo=function(e,t){e&&(\ne.className+=' '+this._csses(t,1))},\ne.prototype.css=function(e){return this._css(e,0)},\ne.prototype.csses=function(e){return this._csses(e,0)},\ne.prototype._csses=function(e,t){var n=this;return e.split(\n' ').map(function(e){return n._css(e,t)}).join(' ')},\ne.prototype._css=function(e,t){if(!e)return''\n;var n=this._componentCSSRules;if(!n)return e;switch(\ne.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t\n){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e\n;case 2:default:return e}}},\ne.prototype._triggerOnMounted=function(){new I.Observer(\nthis.vueModel.data),C.logger.i('','I0106 '+this.debugName)\n;for(var e=0,t=this._subComponents;e<t.length;e++){\nvar n=t[e];n&&n._triggerOnMounted()}\nthis.onMounted&&this.onMounted()},\ne.prototype._getMethod=function(e){var t=this[e]\n;return t instanceof Function?t:null},\ne.prototype._acquireComponentJson=function(){\nvar e=this.debugName+'#acCJ',\nt=w.ComponentRegistry.getComponentJson(this._componentName)\n;return t?(C.logger.i(e,'I0107'),t\n):void 0!==this._componentJson?(C.logger.i(e,'I0108'),\nw.ComponentRegistry.putComponentJson(this._componentName,\nthis._componentJson),this._componentJson):(C.logger.e(e,\n'E0109'),null)},e.prototype._acquireRootView=function(){\nvar e=this.debugName+'#acRV';if(this._hasRootViewBuilt\n)return C.logger.i(e,'I0110'),this._rootView\n;var t=this._acquireComponentJson();return t?(\nthis._rootView=this._convertJsonToBNNode(t,\nthis.vueModel.data||{}),this._hasRootViewBuilt=!0,\nC.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111')\n,null)},e.prototype._genArrayChildNode=function(e,t,n,o,i){\nvar a=I.vueUtils.item2ArrayIndex(i,t),\nr=this._convertJsonToBNNode(e,x({},t,{item:n,index:o,\narrayName:a}));return r?(r.setAttribute('index',o),\nr.setAttribute('for_name',i),r):null},\ne.prototype._convertJsonToBNNode=function(e,l){var d=this,\nt=this.debugName+'#cJTB';if(void 0===e._t)return null\n;var u=document.createElement(e._t),p=[];if(void 0!==e._cd\n)for(var n=function(r){if(r['v-for']||r['v-for-cal']){\nvar e=!r['v-for']&&!!r['v-for-cal'],t=(\ne?m.vueModel.compute:l)||{},n=I.vueUtils.getObject(\ne?r['v-for-cal']:r['v-for'],t,e),c=e?I.vueUtils.rmSymbol(\nr['v-for-cal']):I.vueUtils.rmSymbol(r['v-for']);if(!c||!n\n)return'continue';for(var o in n)if(n.hasOwnProperty(o)){\nvar i=m._genArrayChildNode(r,l,n[o],o,c);i&&p.push(i)}\nvar s=document.createElement('div');s&&(\ns.style.display='none',s.setAttribute('for_end',c),p.push(s)\n,new I.Watcher(c,t,function(e){if(u){I.rmWatchers(c);for(\nvar t=0,n=u.childNodes;t<n.length;t++){var o=n[t]\n;o.getAttribute('for_name')===c&&u.removeChild(o)}if(e)for(\nvar i in e)if(e.hasOwnProperty(i)){\nvar a=d._genArrayChildNode(r,l,e[i],i,c);a&&u.insertBefore(a\n,s)}}},e).id=l.arrayName)}else{var a=m._convertJsonToBNNode(\nr,l);if(!a)return'continue';p.push(a)}},m=this,o=0,\ni=e._cd;o<i.length;o++)n(i[o]);if(!u)return null\n;l&&l.index&&u.setAttribute('index',l.index)\n;var a=e['bn-component']||e['sp-component'];if(a){\nC.logger.i(t,'I0113 '+a)\n;var r=w.ComponentRegistry.createComponent(a);if(!r\n)return C.logger.e(t,'E0114 '+a+', '+r),null\n;var c=e['bn-component-id']||e['sp-component-id']\n;return c&&(r._componentId=c),r.onCreated&&r.onCreated(),\nC.logger.i(t,'I0115 '+r.debugName+', '+c),\nthis._subComponents.push(r),c&&!this._subComponentsMap[c]&&(\nthis._subComponentsMap[c]=r),r._acquireRootView()}\nvar s=e['bn-view-id']||e['sp-view-id'];for(var f in s&&(\nC.logger.i(t,'I0116 '+s),this._viewsIdMap[s]||(\nthis._viewsIdMap[s]=u)),e._i&&(u.id=e._i),e._c&&(\nu.className=e._c),e._s&&(u.style.cssText=e._s),e._x&&(\nu.innerText=e._x),e._y&&(u.type=e._y),e)if(e.hasOwnProperty(\nf))if(0===f.indexOf('on')){var h=this._getMethod(e[f]);h&&(\nu[f]=h.bind(this,u))}else if(0===f.indexOf('_'));else if(\n0===f.indexOf('bn-')||0===f.indexOf('sp-'));else if(\nT.startsWith(f,'v-')){var g=f.split('-');if(\n2===g.length||3===g.length){var b=g[1]\n;2===g.length?new I.NodeCompile(l).compile(b,u,e[f],e._t\n):'cal'===g[2]&&(l.arrayName&&l.index?new I.NodeCompile(x({}\n,this.vueModel.compute,{arrayName:l.arrayName,index:l.index}\n),!0).compile(b,u,e[f],e._t):new I.NodeCompile(\nthis.vueModel.compute,!0).compile(b,u,e[f],e._t))\n}else u[f]=e[f]}else u[f]=e[f];for(var y=0,\n_=p;y<_.length;y++){var v=_[y];u.appendChild(v)}return u},\ne.componentName='',e.componentHTML='',e.componentCSS='',\ne.componentHashName='',e}();t.BNComponent=o},function(e,t,n\n){'use strict';Object.defineProperty(t,'__esModule',{\nvalue:!0});var o=n(3),i=function(){function n(){}\nreturn n.registerComponent=function(e,t){\nt?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(\no.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t\n):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},\nn.getKnownComponents=function(){return n.facts},\nn.getComponentJson=function(e){return n.jsons[e]},\nn.putComponentJson=function(e,t){t||o.logger.e(\n'CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e\n)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i(\n'CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},\nn.createComponent=function(e){o.logger.i('CmpReg#crtCmp',\n'I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e(\n'CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}(\n);t.ComponentRegistry=i},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0}),\nt.randomStr=function(){return Math.floor(61439*Math.random(\n)+4096).toString(16)},t.startsWith=function(e,t){\nreturn!!e&&0===e.indexOf(t)},t.tryJSONParse=function(e){if(\nt=e,'[object Object]'===Object.prototype.toString.call(t)\n)return e;var t;try{return JSON.parse(e)}catch(e){return{}}}\n,t.copyObj=function(e,t){for(var n in t||(t={}),e\n)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},function(e,t,n\n){'use strict';var o,i=this&&this.__extends||(o=function(e,t\n){return(o=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}o(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var a,r=n(3),C=n(0),m=n(2),T=n(1),\nf=n(10),s=n(16),l=n(19),w=C.amc.rpcData,\nc=C.amc.fn.sdkGreaterThanOrEqual('10.8.39'),d=!1,u=function(\ne){function t(){var c=null!==e&&e.apply(this,arguments\n)||this;return c.certNames=[],c.certIds=[],\nc.certTypeDisabled=!1,c.certIdDisabled=!1,c.spmServer4={},\nc.h5BackDlgTxt=[],c.switchCardType=function(){\nvar e=c.vueModel.compute.errorGuide();if(e.action.name){if(\nm.modifyElementAttribute(c,'card-type-end-box',{\naccessibilityTraits:'Button'}),'bn:switch'!==e.action.name\n)return C.amc.fn.spmClick('a283.b12139.c29200.d178654',\nf.mergeObject({cardType:c.vueModel.data.checked},\nc.spmServer4)),void document.submit({action:e.action})\n;var t='CC'===c.vueModel.data.checked?'DC':'CC';(\nw.bankInfo&&w.bankInfo.signElementMap&&w.bankInfo.signElementMap[t]||{}\n).enable&&'bn:switch'===e.action.name&&c.switchCard(t)\n}else m.modifyElementAttribute(c,'card-type-end-box',{\naccessibilityTraits:'Text'})},c.switchCard=function(e){\nC.amc.fn.spmClick('a283.b12139.c29200.d178654',\nf.mergeObject({cardType:c.vueModel.data.checked},\nc.spmServer4)),c.switchCardTypeAnimation(),\nc.vueModel.data.checked=e,c.refreshCertInfo(\nc.vueModel.data.checked)},\nc.switchCardTypeAnimation=function(){\nvar e=c.getViewInComponentById('current-card-type-box')\n;e.animation=[{type:'alpha',fromValue:1,toValue:.5,\nduration:200},{type:'translate',fromValue:{x:0,y:0},\ntoValue:{x:0,y:-40},duration:200}],setTimeout(function(){\ne.animation=[{type:'alpha',fromValue:.5,toValue:.9,\nduration:200},{type:'translate',fromValue:{x:0,y:-40},\ntoValue:{x:0,y:20},duration:200}]},200),setTimeout(function(\n){e.animation=[{type:'alpha',fromValue:.9,toValue:1,\nduration:100},{type:'translate',fromValue:{x:0,y:20},\ntoValue:{x:0,y:0},duration:100}]},400)},c.vueModel={data:{\ninstLogo:'',instBackgroundLogo:'',instName:'',checked:'DC',\nsubTitle:'',subTitleLogo:'',activityTags:[],\ntopBoxVisible:!0,\ntopBoxBackground:w.bankInfo&&w.bankInfo.bgStyle||'card-bg-default'\n},compute:{cardTypeName:function(){\nreturn C.amc.fn.i18nValueForKey(\n'CC'===c.vueModel.data.checked?'{{credit_card}}':'{{store_card}}'\n)},switchTip:function(){return C.amc.fn.i18nValueForKey(\n'{{switch}}')+C.amc.fn.i18nValueForKey(\n'DC'===c.vueModel.data.checked?'{{credit_card}}':'{{store_card}}'\n)},errorGuide:function(){\nvar e='DC'===c.vueModel.data.checked?'CC':'DC',t=(\nw.bankInfo&&w.bankInfo.signElementMap&&w.bankInfo.signElementMap[e]||{}\n).switchAction||{};return{icon:t.icon||'',tip:t.tips||'',\naction:t.action||{}}},activityText:function(){\nvar e=c.vueModel.data.checked,\nt=w.bankInfo&&w.bankInfo.signElementMap&&w.bankInfo.signElementMap[e]||{}\n;return t.activityTags&&0<t.activityTags.length&&(\nt.activityText=''),t.activityText||''}}},c.onBack=function(\n){if(C.amc.fn.spmClick('a283.b12139.c29200.d56104',void 0),\n!c.vueModel.data.topBoxVisible){if(C.amc.isIOS\n)C.amc.fn.hideKeyboard();else try{setTimeout(function(){\nc.curCertNode.blur(),document.body.focus()},20)}catch(e){}\nreturn f.modifyElementStyle(c,'cert-detail',{marginTop:'0'})\n,void(c.vueModel.data.topBoxVisible=!0)}\nC.amc.fn.spmPageDestroy('a283.b12139',{\ninstId:c.pageData&&c.pageData.instId||''}),C.amc.fn.back()},\nc.inputNodeOnFocus=function(e){c.curCertNode=e,\nc.vueModel.data.topBoxVisible&&(\nc.vueModel.data.topBoxVisible=!1,f.modifyElementStyle(c,\n'cert-detail',{marginTop:'16px'}))},c.onMounted=function(){\nvar e=w.bankInfo,t=w.cardTypePropagate,n=T.tryJSONParse(\nt||{});if(e&&e.instId){var o=e.signElementMap;if(o){\nvar i=o.CC,a=o.DC;if(i&&a){c.pageData={instId:e.instId,\ncardTypeMap:{CC:i,DC:a}},c.dialog=s.getDialog(),\nC.amc.isAndroid&&(c.getViewInComponentById('loading'\n).src=C.amc.path+'alipay_msp_indicator_white_loading'),t&&(\nc.vueModel.data.subTitle=n.primaryText||'',\nc.vueModel.data.subTitleLogo=n.iconUrl||'',\nc.spmServer4=T.tryJSONParse(n.spmObj||{})||{}),\nc.usernameNode=c.getViewInComponentById('username'),\nc.usernameNode.oninput=function(){c.onInput('username',\nc.usernameNode.value)},c.usernameNode.onfocus=function(){\nc.inputNodeOnFocus(c.usernameNode)},\nc.phoneNoNode=c.getViewInComponentById('phone-no'),\nc.phoneNoNode.oninput=function(){c.onInput('phoneno',\nc.phoneNoNode.value)},c.phoneNoNode.onfocus=function(){\nc.inputNodeOnFocus(c.phoneNoNode)},\nc.certNoNode=c.getViewInComponentById('cert-no'),\nc.certNoIdNode=c.getViewInComponentById('cert-no-id'),\nc.certNoNode.oninput=function(){c.onInput('certno',\nc.certNoNode.value||'')},c.certNoNode.onfocus=function(){\nc.inputNodeOnFocus(c.certNoNode)},\nc.certNoIdNode.oninput=function(){c.onInput('certidno',\nc.certNoIdNode.value||'')},c.certNoIdNode.onfocus=function(\n){c.inputNodeOnFocus(c.certNoIdNode)},\nc.certTypeLabel=c.getViewInComponentById('cert-type-label'),\nc.certTypeLabel.onclick=function(){\n1<c.certNames.length&&!c.certTypeDisabled&&c.showCertTypePicker(\n)},c.getViewInComponentById('protocol').onclick=function(){\nc.showProtocol()},c.nextBtn=c.getViewInComponentById(\n'next-btn'),c.nextBtn.disabled=!0,\nc.nextBtn.onclick=function(){c.submitDataCertData()},\nc.getViewInComponentById('btn-text'\n).innerText=w.btnTxt||'{{agree_next}}',\nc.getViewInComponentById('arrow-right'\n).src=C.amc.res.arrowRight,c.showViewById('protocol-box'),\nC.amc.fn.spmExposure('a283.b12139.c29200.d56109',\nc.spmServer4,!1),c.showViewById('next-btn'),\nC.amc.fn.spmExposure('a283.b12139.c29200.d56108',{\ninstId:c.pageData.instId},!1),c.showViewById('bottom-tip'),\nc.vueModel.data.instName=e.instName||'',\nc.vueModel.data.instLogo=e.logoUrl||'',\nc.vueModel.data.instBackgroundLogo=e.wmLogoUrl||'',\n'DC'===e.selectedCardType?c.vueModel.data.checked='DC':'CC'===e.selectedCardType?c.vueModel.data.checked='CC':a.enable?c.vueModel.data.checked='DC':c.vueModel.data.checked='CC'\n;var r=c.vueModel.compute.errorGuide()\n;r.tip&&r.action.name&&C.amc.fn.spmExposure(\n'a283.b12139.c29200.d178654',f.mergeObject({\ncardType:c.vueModel.data.checked},c.spmServer4)),\nc.refreshCertInfo(c.vueModel.data.checked),\nC.amc.isAndroid&&setTimeout(function(){document.setProp(\n'focusableInTouchMode',{value:!0})},100),c.autoDlg(\nw.cardBindingInitDlg),window.mp&&window.mp.onNotification(\n'MQP_WEBVIEW_FIRST_LOADED',function(e){if(\ne&&!l.checkEmptyObj(e)&&!C.amc.isSDK){\nvar t=w&&w.bankInfo&&w.bankInfo.instId,\nn=w&&w.bankInfo&&w.bankInfo.instName,o={bankID:t||'',\ncardType:c.vueModel.data.checked||'',bankName:n||''}\n;e.url&&0<e.url.length&&(o.url=e.url),\ne.loadTime&&0<e.loadTime.length&&(o.loadTime=e.loadTime)\n;var i=c.getLocalInfo();i&&!l.checkEmptyObj(i)&&(\no=f.mergeObject(o,i)),setTimeout(function(){\nC.amc.fn.feedback('antevent',{eventId:'101248',data:o||{}})}\n,1),C.amc.fn.postNotification(\n'kCashierCardBindingWebPageLoadedNotification',{\ntplId:'cashier-card-type-flex',curPageShowType:'cashierweb',\npageVersion:'0001',extra:JSON.stringify(o)})}}),\nwindow.mp&&window.mp.onNotification(\n'MQP_LOC_H5_TIP_ACTION_NOTIFY',function(e){var t={\nbankID:w&&w.bankInfo&&w.bankInfo.instId||'',\ncardType:c.vueModel.data.checked||''};C.amc.fn.spmClick(\n'a283.b55002.c302667.d305107',t)}),\nwindow.mp&&window.mp.onNotification(\n'MQP_LOC_H5_BACK_DLG_NOTIFY',function(e){var t={\nbankID:w&&w.bankInfo&&w.bankInfo.instId||'',\ncardType:c.vueModel.data.checked||''};if(\ne.type&&'string'==typeof e.type){var n=e.type;if('exp'===n\n)C.amc.fn.spmExposure('a283.b55002.c302669',t),\nc.h5BackDlgTxt.forEach(function(e){C.amc.fn.spmExposure(\n'a283.b55002.c302669.d311859',f.mergeObject(t,{txt:e||''}))}\n),setTimeout(function(){document.invoke('rpc',{\noperationType:'alipay.mobilechannel.cardbindingprod.hitStrategy',\nrequestData:{\nchannelContextId:c.h5NotifyStrategChannelContextId,\nstrategyId:c.h5NotifyStrategyId,scene:'EXPRESS_INST_VIEW'}},\nfunction(){})},1);else if('click'===n){var o=e.txt\n;C.amc.fn.spmClick('a283.b55002.c302669.d311859',\nf.mergeObject(t,{txt:o||''}))}}})}}}},\nc.showCertTypePicker=function(){var n=c,o=c.pageData,e={\nbtns:c.certNames,default:o.certType&&c.certIds.indexOf(\no.certType)||0,title:'{{sel_id_no}}'}\n;C.amc.isAndroid&&document.submit({action:{\nname:'loc:hideKeyboard'}}),document.picker(e,function(e){\nvar t=e&&e.index\n;0<=t&&t<n.certIds.length&&o.certType!==n.certIds[t]&&(\no.certType=n.certIds[t],o.certNo='',\nn.certTypeLabel.innerText=n.certNames[t]||'',\nn.updateCertNoInput(),n.curCertNoNode.value=o.certNo,\nsetTimeout(function(){n.curCertNoNode.focus()},10))}),\nC.amc.fn.spmClick('a283.b12139.c29202.d56113',{\ncardType:c.vueModel.data.checked})},\nc.submitDataCertData=function(){var e=c,t=c.pageData,n={\ncardType:c.vueModel.data.checked,instId:t.instId},o={\nname:w.submitAction,params:n,loadtxt:''};if(\nt.needCertType&&t.certNo&&(n.certType=t.certType,\nn.certNo=t.certNo),t.needPhoneNo&&t.phoneNo&&(\nn.bankmobile=t.phoneNo),\n!n.certNo||'A'!==n.certType||n.certNo.match(\n'(^\\\\d{15}$)|(^\\\\d{17}([0-9]|X|x)$)')&&e.isCertIdNoValid(\nn.certNo))if(!n.bankmobile||n.bankmobile.match(\n'(^([+]?[0]{0,2}86|([+]?0{0,2}86-))?1\\\\d{10}$)|(^[+]?[0]{0,2}[1-9]{1}\\\\d{0,7}[-]{0,1}\\\\d{7,11}$)'\n)){if(t.needUserName){if(t.username&&t.username.match(\n'^\\\\d{1,}$'))return void document.toast({\ntext:'{{username_no_pure_num}}',type:'fail'},function(){\ne.usernameNode.focus()});if(\nt.username&&-1!==t.username.indexOf('*')){var i=(\n'CC'===t.cardType?t.cardTypeMap.CC:t.cardTypeMap.DC\n).needInput,a=void 0===i?{}:i,r=a.username;if(a.certtype,\na.certno,a.bankmobile,\nr&&r.display&&r.value&&r.value!==t.username\n)return void document.toast({\ntext:'{{username_no_star_after_edit}}',type:'fail'},\nfunction(){e.usernameNode.focus()})}n.userName=t.username}\nC.amc.fn.spmClick('a283.b12139.c29200.d56108',{\ncardType:c.vueModel.data.checked,instId:t.instId}),\nc.submitData(o)}else document.toast({\ntext:'{{input_right_phone}}',type:'fail'},function(){\ne.phoneNoNode.focus()});else document.toast({\ntext:'{{input_right_id_card}}',type:'fail'},function(){\ne.curCertNoNode.focus()})},c.submitData=function(e){\ndocument.asyncSubmit({action:e},function(e){\n'1'===e.pageloading?c.showLoading(\n):'0'===e.pageloading?c.stopLoading(\n):e.verifyId?c.processVI(e.verifyId,e.nextAct):e.bankUrl&&(\nc.processUrl(e),c.stopLoading())}),c.showLoading(),\nC.amc.fn.hideKeyboard()},c.autoDlg=function(e){if(e){\nvar n=e.buttons||[{act:{name:'loc:dismiss'},txt:'{{ok}}'}]\n;C.amc.fn.spmExposure('a283.b12139.c114332'),n.forEach(\nfunction(e,t){C.amc.fn.spmExposure(\n'a283.b12139.c114332.d236931_'+t,e)}),c.dialog.show({\ntitle:e.title,message:e.msg,btns:n.map(function(e){\nreturn e.txt||''}),clickCallBack:function(e){var t=n[e]\n;C.amc.fn.spmClick('a283.b12139.c114332.d236931_'+e,t),\nt&&t.act&&document.submit({action:t.act})}})}},\nc.refreshNextBtn=function(){var e=c.pageData\n;e.needUserName&&''===e.username||e.needCertType&&''===e.certNo?c.nextBtn.disabled=!0:c.nextBtn.disabled=!1\n},c}return i(t,e),t.prototype.onHelp=function(){\nC.amc.fn.spmClick('a283.b12139.c29200.d56105',void 0)\n;var e=w.helpURL||'https://csmobile.alipay.com/router.htm?scene=app_quickbindcardxyh'\n;document.submit({action:{\nname:'loc:openweb(\\''+e+'\\', \\'{{help}}\\')'}})},\nt.prototype.hideViewById=function(e){m.visibleElement(this,e\n,!1)},t.prototype.showViewById=function(e){m.visibleElement(\nthis,e,!0)},t.prototype.onInput=function(e,t){var n=this,\no=this.pageData,\ni=o.cardTypeMap[this.vueModel.data.checked].needInput,\na=void 0===i?{}:i,r=a.certno,c=a.bankmobile;if(t=t.trim(),\n'username'===e)o.username=t;else if('certno'===e\n)o.certNo=t;else if('certidno'===e)if(\no.certNo||''===o.certNo||(t=this.getFirtDifferent(r.value,t)\n),t.match('(^\\\\d{0,18}$)|(^\\\\d{17}([X|x]{0,1}))$')\n)o.certNo=t,(l=function(){n.curCertNoNode.value=t})(),\nC.amc.isAndroid&&window.setTimeout(l,1);else{\nvar s=t&&t[t.length-1];'x'!=s&&'X'!=s||document.toast({\ntext:'{{id_card_tip}}',type:'fail'},function(){\nn.curCertNoNode.focus()}),(l=function(){\nn.curCertNoNode.value=o.certNo||''})(),\nC.amc.isAndroid&&window.setTimeout(l,1)}else if(\n'phoneno'===e){var l;o.phoneNo||''===o.phoneNo||(\nt=this.getFirtDifferent(c.value,t)),o.phoneNo=t,(l=function(\n){n.phoneNoNode.value=t})(),\nC.amc.isAndroid&&window.setTimeout(l,1)}this.refreshNextBtn(\n)},t.prototype.getFirtDifferent=function(e,t){e=e||'',\nt=t||'';for(var n=0;n<t.length;){if(n>=e.length||e[n]!==t[n]\n)return t[n];n++}return''},\nt.prototype.showProtocol=function(){for(\nvar e=this.pageData.cardTypeMap[this.vueModel.data.checked].signAgreementList\n,t=void 0===e?[]:e,n=[],o=0;o<t.length;o+=1){var i=t[o]\n;n.push({text:i.agreementTitle,url:i.agreementUrl})}\nC.amc.fn.showProtocolList(n),C.amc.fn.spmClick(\n'a283.b12139.c29200.d56109',{\ncardType:this.vueModel.data.checked})},\nt.prototype.updateCertNoInput=function(){\n'A'===this.pageData.certType?(\nthis.certNoIdNode.style.display='flex',\nthis.certNoNode.style.display='none',\nthis.curCertNoNode=this.certNoIdNode):(\nthis.certNoIdNode.style.display='none',\nthis.certNoNode.style.display='flex',\nthis.curCertNoNode=this.certNoNode)},\nt.prototype.isCertIdNoValid=function(e){if(!e||18!==e.length\n)return!1;for(var t=0,n=e.substr(17),o=0;o<e.length;o+=1)if(\n17!==o){var i=0;try{i=parseInt(e[o])}catch(e){}t+=i*(\nMath.pow(2,17-o)%11)}return['1','0','X','9','8','7','6','5',\n'4','3','2'][t%11]===n.toUpperCase()},\nt.prototype.showLoading=function(){this.showViewById(\n'loading'),this.hideViewById('btn-text')},\nt.prototype.stopLoading=function(){this.showViewById(\n'btn-text'),this.hideViewById('loading')},\nt.prototype.processVI=function(e,n){var o=this,\nt=C.amc.fn.i18nValueForKey('{{to_vi}}')||'';setTimeout(\nfunction(){document.invoke('showLoading',{text:t})},1),\nsetTimeout(function(){document.invoke('hideLoading'),\ndocument.invoke('verifyIdentity',{verifyId:e,\nverifyType:'verify'},function(e){if('1000'===e.code){var t={\nname:n,params:{code:'1000'},loadtxt:''};o.submitData(t)\n}else o.stopLoading(),C.amc.fn.hideKeyboard()})},1e3)},\nt.prototype.processUrl=function(e){var n=this,t=e.bankUrl,\no=e.title,i=e.nextAct,a=e.exitUrl,r=e.method,c=e.postData,\ns=e.loadtxt,l=e.helpUrl,d=e.offscreenLoadTime,\nu=e.nativeLoadingText,p=e.tips,m=e.backAction,\nf=e.channelContextId,h={name:'loc:openweb',params:{url:t,\ntitle:o||'',extras:{exitact:{name:'loc:none'},succact:{\nname:i,loadtxt:s},exiturl:a,method:r,postData:c,\nhelpUrl:l||'https://csmobile.alipay.com/router.htm?scene=app_quickbindcardsqy',\noffscreenLoadTime:d||1500}}};if(m){var g=m.actions,b=[]\n;this.h5BackDlgTxt=[],\nthis.h5NotifyStrategyId=m.strategyId||'',\nthis.h5NotifyStrategChannelContextId=f,g.forEach(function(e\n){var t={act:e.act,txt:e.text}\n;e.act&&e.act.params&&'string'==typeof e.act.params&&(\nt.act.params=T.tryJSONParse(e.act.params)),\nn.h5BackDlgTxt.push(e.text),b.push(t)});var y={name:m.name,\nstrategyId:m.strategyId,msg:m.message,title:m.title,\nbtns:b.reverse()};h.params.extras.backDlg=y}p&&(\nh.params.extras.tips=p),u&&(h.params.extras.loadtxt=u)\n;var _=C.amc.fn.i18nValueForKey('{{open_bank}}')||'';if(\nC.amc.fn.sdkGreaterThanOrEqual('10.8.30')){var v=function(){\ndocument.submit({action:h})};setTimeout(v,1)\n}else setTimeout(function(){document.invoke('showLoading',{\ntext:_})},1),v=function(){document.invoke('hideLoading'),\ndocument.submit({action:h})},setTimeout(v,1e3);var x={\nbankID:w&&w.bankInfo&&w.bankInfo.instId||'',\ncardType:this.vueModel.data.checked||''}\n;C.amc.fn.spmPageCreate('a283.b55002',x),\nC.amc.fn.spmExposure('a283.b55002',x),\np&&C.amc.fn.spmExposure('a283.b55002.c302667',x)},\nt.prototype.refreshCertInfo=function(e){var t=this.pageData,\nn=t.cardTypeMap[e],o=n.needInput,i=void 0===o?{}:o,\na=i.username,r=i.certtype,c=i.certno,s=i.bankmobile;if(\n'CC'===e?C.amc.fn.spmExposure('a283.b12139.c29200.d56106',{\ninstId:this.pageData.instId},!1):C.amc.fn.spmExposure(\n'a283.b12139.c29200.d56107',{instId:this.pageData.instId},!1\n),r&&r.display&&c&&c.display){for(var l in this.certIds=[],\nthis.certNames=[],this.certTypeDisabled=!!r.disable,\nthis.certIdDisabled=!!c.disable,this.certIdDisabled?(\nthis.certNoIdNode.disabled=!0,this.certNoNode.disabled=!0):(\nthis.certNoIdNode.disabled=!1,this.certNoNode.disabled=!1),\nr.value){var d=r.value[l];this.certIds.push(d),\nthis.certNames.push(l)}if(1<=this.certIds.length){var u=0,\np=t.certNo||'';t.needCertType=!0,this.showViewById(\n'certtype-box'),this.certTypeLabel.style.color='#333',\nt.certType&&0<=this.certIds.indexOf(t.certType)?(\nu=this.certIds.indexOf(t.certType),\nt.certNo?p=t.certNo:c.value&&t.certType===r.default?(\nt.certNo=void 0,p=c.value):(p='',t.certNo='')\n):p=0<=this.certIds.indexOf(r.default)?(\nu=this.certIds.indexOf(r.default),t.certNo=void 0,\nc.value||''):(t.certNo=void 0,''),\nt.certType=this.certIds[u]||'',\nthis.certTypeLabel.innerText=this.certNames[u]||'',\n1===this.certIds.length?this.hideViewById('arrow-right'):(\nthis.showViewById('arrow-right'),C.amc.fn.spmExposure(\n'a283.b12139.c29202.d56113',{cardType:e},!0)),\nthis.updateCertNoInput(),this.curCertNoNode.value=p\n}else t.needCertType=!1,this.hideViewById('certtype-box')\n}else t.needCertType=!1,this.hideViewById('certtype-box')\n;a&&a.display?(t.needUserName=!0,this.showViewById(\n'username-box'),m.modifyElementAttribute(this,'username',{\ndisabled:!!a.disable}),t.username||(\nthis.usernameNode.value=a.value||'',\na.value?t.username=void 0:t.username=''),\nt.needCertType?this.showViewById('username-line'\n):this.hideViewById('username-line')):(t.needUserName=!1,\nthis.hideViewById('username-box'),this.hideViewById(\n'username-line')),s&&s.display?(t.needPhoneNo=!0,(\nt.needCertType||t.needUserName)&&this.showViewById(\n'phone-line'),this.showViewById('phone-no-box'),\nm.modifyElementAttribute(this,'phone-no',{\ndisabled:!!s.disable}),t.phoneNo||(\nthis.phoneNoNode.value=s.value||'',\ns.value?t.phoneNo=void 0:t.phoneNo='')):(t.needPhoneNo=!1,\nthis.hideViewById('phone-no-box'),this.hideViewById(\n'phone-line')),\nt.needPhoneNo||t.needCertType||t.needUserName?(\nthis.showViewById('cert-detail'),f.modifyElementStyle(this,\n'card-type-box',{height:'123px'}),f.modifyElementStyle(this,\n'current-card-type-box',{height:'90px'})):(\nthis.hideViewById('cert-detail'),f.modifyElementStyle(this,\n'card-type-box',{height:'161px'}),f.modifyElementStyle(this,\n'current-card-type-box',{height:'131px'})),\nthis.vueModel.data.activityTags=n.activityTags||[],\nthis.refreshNextBtn()},t.prototype.getLocalInfo=function(){\nvar e={};if(window.flybird&&window.flybird.local&&(\nwindow.flybird.local.apLinkToken&&(\ne.apLinkToken=window.flybird.local.apLinkToken),\nwindow.flybird.local.clientBizType&&(\ne.clientBizType=window.flybird.local.clientBizType),\nwindow.flybird.local.clientLogData)){var t=T.tryJSONParse(\nwindow.flybird.local.clientLogData);t&&(e.bizType=t.bizType,\ne.salesProductCode=t.salesProductCode)}return e},\nt.getComponentCSSRules=function(){return{\n'.main-body':'_BankTypePage_laeb-c-main-body',\n'.selection-tip':'_BankTypePage_laeb-c-selection-tip',\n'.card-type-box':'_BankTypePage_laeb-c-card-type-box',\n'.current-card-type-box':'_BankTypePage_laeb-c-current-card-type-box',\n'.current-card-type-content-box':'_BankTypePage_laeb-c-current-card-type-content-box',\n'.card-type-logo-box':'_BankTypePage_laeb-c-card-type-logo-box',\n'.card-type-logo':'_BankTypePage_laeb-c-card-type-logo',\n'.card-type-text-box':'_BankTypePage_laeb-c-card-type-text-box',\n'.card-type-title':'_BankTypePage_laeb-c-card-type-title',\n'.card-type-desc':'_BankTypePage_laeb-c-card-type-desc',\n'.card-type-tail-box':'_BankTypePage_laeb-c-card-type-tail-box',\n'.card-type-background-logo':'_BankTypePage_laeb-c-card-type-background-logo',\n'.card-type-end-box':'_BankTypePage_laeb-c-card-type-end-box',\n'.card-type-end-box-img':'_BankTypePage_laeb-c-card-type-end-box-img',\n'.card-type-end-box-text':'_BankTypePage_laeb-c-card-type-end-box-text',\n'.cert-detail-box':'_BankTypePage_laeb-c-cert-detail-box',\n'.cert-tip':'_BankTypePage_laeb-c-cert-tip',\n'.cert-input-box':'_BankTypePage_laeb-c-cert-input-box',\n'.cert-item-title':'_BankTypePage_laeb-c-cert-item-title',\n'.margin-l':'_BankTypePage_laeb-c-margin-l',\n'.cert-item-input':'_BankTypePage_laeb-c-cert-item-input',\n'.protocol-box':'_BankTypePage_laeb-c-protocol-box',\n'.protocol-title':'_BankTypePage_laeb-c-protocol-title',\n'.protocol':'_BankTypePage_laeb-c-protocol',\n'.amc-primary-btn':'_BankTypePage_laeb-c-amc-primary-btn',\n'.primary-btn-text':'_BankTypePage_laeb-c-primary-btn-text',\n'.loading-color':'_BankTypePage_laeb-c-loading-color',\n'.arrow-right':'_BankTypePage_laeb-c-arrow-right',\n'.bottom-tip':'_BankTypePage_laeb-c-bottom-tip',\n'.bottom-tip-box':'_BankTypePage_laeb-c-bottom-tip-box',\n'.header-box':'_BankTypePage_laeb-c-header-box',\n'.sub-title-box':'_BankTypePage_laeb-c-sub-title-box',\n'.sub-title':'_BankTypePage_laeb-c-sub-title',\n'.sub-title-logo':'_BankTypePage_laeb-c-sub-title-logo',\n'.labels-container':'_BankTypePage_laeb-c-labels-container',\n'.tag':'_BankTypePage_laeb-c-tag',\n'.label-tag':'_BankTypePage_laeb-c-label-tag'}},\nt.getComponentJson=function(){return{\n_c:'amc-v-box _BankTypePage_laeb-c-main-body amc-scroll',\n_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-header-box amc-align-center amc-justify-center',\n'v-if':'@{topBoxVisible}',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-selection-tip',_t:'label',\n_x:'{{title}}'},{\n_c:'_BankTypePage_laeb-c-sub-title-box amc-align-center amc-justify-center',\n'v-if':'@{!!##subTitle##}',_t:'div',_cd:[{\n'v-src':'@{subTitleLogo}',\n_c:'_BankTypePage_laeb-c-sub-title-logo',\n'v-if':'@{!!##subTitleLogo##}',_t:'img'},{\n_c:'_BankTypePage_laeb-c-sub-title',_t:'label',\n'v-text':'@{subTitle}'}]}]},{'sp-view-id':'card-type-box',\n_c:'_BankTypePage_laeb-c-card-type-box',\n'v-if':'@{topBoxVisible}',_t:'div',_cd:[{\n'sp-view-id':'current-card-type-box',\n_c:'_BankTypePage_laeb-c-current-card-type-box',\n'v-class':'@{topBoxBackground}',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-current-card-type-content-box',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-card-type-logo-box',\n_t:'div',_cd:[{'v-src':'@{instLogo}',\n_c:'_BankTypePage_laeb-c-card-type-logo',_t:'img'}]},{\n_c:'_BankTypePage_laeb-c-card-type-text-box amc-flex-1',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-card-type-title',\n_t:'label','v-text':'@{instName}'},{\n_c:'_BankTypePage_laeb-c-card-type-desc',\n'v-text-cal':'@{activityText}',\n'v-if-cal':'@{!!##activityText##}',_t:'label'},{\n_c:'_BankTypePage_laeb-c-labels-container',\n'v-if':'@{##activityTags## && ##activityTags##.length > 0}',\n_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-tag amc-align-center',\n'v-for':'@{activityTags}',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-label-tag amc-ellipsis-1-line amc-text-center',\n'v-text':'@{item}',_t:'label'}]}]}]},{\n_c:'_BankTypePage_laeb-c-card-type-tail-box',\n'v-text-cal':'@{cardTypeName}',_t:'label'}]},{\n_c:'_BankTypePage_laeb-c-card-type-background-logo',\n'v-src':'@{instBackgroundLogo}',_t:'img'}]},{\n'sp-view-id':'card-type-end-box',\n_c:'amc-align-center _BankTypePage_laeb-c-card-type-end-box amc-flex-1',\nonclick:'switchCardType',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-card-type-end-box-img',\n'v-if-cal':'@{!!##errorGuide.icon##}',\n'v-src-cal':'@{errorGuide.icon}',_t:'img'},{\n'sp-view-id':'card-type-end-box-text',\n_c:'_BankTypePage_laeb-c-card-type-end-box-text',\n'v-text-cal':'@{errorGuide.tip}',_t:'label'}]}]},{\n'sp-view-id':'cert-detail',\n_c:'amc-v-box _BankTypePage_laeb-c-cert-detail-box amc-hidden',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-cert-tip',\n_t:'label',_x:'{{enter_card_info}}'},{_c:'amc-1px-line',\n_t:'div'},{'sp-view-id':'username-box',\n_c:'_BankTypePage_laeb-c-cert-input-box amc-hidden',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-cert-item-title',\n_t:'label',_x:'{{name}}'},{'sp-view-id':'username',\n_c:'_BankTypePage_laeb-c-cert-item-input',\nplaceholder:'{{enter_real_name}}',_t:'input'}]},{\n'sp-view-id':'username-line',\n_c:'amc-1px-line _BankTypePage_laeb-c-margin-l amc-hidden',\n_t:'div'},{'sp-view-id':'certtype-box',\n_c:'amc-v-box amc-hidden',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-cert-input-box',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-cert-item-title',_t:'label',\n_x:'{{papers_type}}'},{'sp-view-id':'cert-type-label',\n_c:'_BankTypePage_laeb-c-cert-item-input amc-flex-1 amc-ellipsis',\n_t:'label'},{'sp-view-id':'arrow-right',\n_c:'_BankTypePage_laeb-c-arrow-right',_t:'img'}]},{\n_c:'amc-1px-line _BankTypePage_laeb-c-margin-l',_t:'div'},{\n_c:'_BankTypePage_laeb-c-cert-input-box',_t:'div',_cd:[{\n_c:'_BankTypePage_laeb-c-cert-item-title',_t:'label',\n_x:'{{id_no}}'},{'sp-view-id':'cert-no',\n_c:'_BankTypePage_laeb-c-cert-item-input',\nplaceholder:'{{enter_cert_id}}',_t:'input'},{\n'sp-view-id':'cert-no-id',\n_c:'_BankTypePage_laeb-c-cert-item-input amc-hidden',\nplaceholder:'{{enter_cert_id}}',_y:'idcard',_t:'input'}]}]},\n{'sp-view-id':'phone-line',\n_c:'amc-1px-line _BankTypePage_laeb-c-margin-l amc-hidden',\n_t:'div'},{'sp-view-id':'phone-no-box',\n_c:'_BankTypePage_laeb-c-cert-input-box amc-hidden',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-cert-item-title',\n_t:'label',_x:'{{phone_no}}'},{'sp-view-id':'phone-no',\n_c:'_BankTypePage_laeb-c-cert-item-input',_y:'phone',\nplaceholder:'{{input_bank_phone}}',_t:'input'}]},{\n_c:'amc-1px-line',_t:'div'}]},{'sp-view-id':'protocol-box',\n_c:'_BankTypePage_laeb-c-protocol-box amc-hidden',_t:'div',\n_cd:[{_c:'_BankTypePage_laeb-c-protocol-title',_t:'label',\n_x:'{{look}}'},{'sp-view-id':'protocol',\n_c:'_BankTypePage_laeb-c-protocol',_t:'label',\n_x:'{{quickpay_protocol}}'}]},{'sp-view-id':'next-btn',\n_c:'_BankTypePage_laeb-c-amc-primary-btn _BankTypePage_laeb-c-amc-primary-btn _BankTypePage_laeb-c-amc-primary-btn amc-v-box amc-flex-center margin-lr amc-hidden',\n_t:'div',_cd:[{'sp-view-id':'btn-text',\n_c:'_BankTypePage_laeb-c-primary-btn-text',_t:'label'},{\n'sp-view-id':'loading',src:'indicatior',\n_c:'amc-loading-img _BankTypePage_laeb-c-loading-color amc-hidden',\n_t:'img'}]},{'sp-view-id':'bottom-tip',\n_c:'_BankTypePage_laeb-c-bottom-tip-box amc-flex-center amc-hidden',\n_t:'div',_cd:[{_c:'_BankTypePage_laeb-c-bottom-tip',\n_t:'label',_x:'{{bank_page_tip}}'}]},{\n_c:'amc-iphone-x-pd-b',_t:'div'}]}},\nt.componentName='BankTypePage',\nt.componentHashName='BankTypePage_laeb',t}(r.BNComponent)\n;t.BankTypePage=u,a=C.amc.fn.docConfig,\nC.amc.fn.docConfig=function(){var e=JSON.parse(a())\n;return e.navi={naviBarColor:'#F5F5F5',statusBarStyle:'dark'\n},JSON.stringify(e)},C.amc.fn.spmPageCreate('a283.b12139',{\ninstId:w.bankInfo&&w.bankInfo.instId||''}),\ndocument.viewDidAppear=function(){\nd?C.amc.fn.spmExposureResume():d=!0},window.onload=function(\n){var e=new u\n;document.body.style.height=C.amc.isAndroid?window.innerHeight:C.amc.specs.bodyHeight\n;var t=C.amc.fn.getNav(C.amc.res.navBack,\nC.amc.isAndroid||c?'':'{{return}}',\nC.amc.isAndroid&&!c?'{{return}}':'','',C.amc.res.help,\nfunction(){e.onBack()},function(){e.onHelp()},{\nbackMode:'back'});document.body.appendChild(t),e.mountTo(\ndocument.body),\nwindow.flybird&&window.flybird.local&&window.flybird.local.agednessVersion&&(\nwindow.remScale=1.2,document.body.style.height/=1.2),\nwindow.onKeyDown=function(){4==event.which&&e.onBack()},\nC.amc.fn.spmExposure('a283.b12139.c29200.d56105',void 0,!1),\nC.amc.fn.spmExposure('a283.b12139.c29200.d56104',void 0,!1)}\n},function(module,exports,__webpack_require__){'use strict'\n;Object.defineProperty(exports,'__esModule',{value:!0})\n;var util_1=__webpack_require__(6),\namc_types_1=__webpack_require__(0),watchers;function notify(\n){watchers&&watchers.forEach(function(e){e.update()})}\nfunction rmWatchers(t){watchers=watchers.filter(function(e){\nreturn e.id!==t})}exports.rmWatchers=rmWatchers\n;var Watcher=function(){function e(e,t,n,o){if(this.id='',\nthis.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,\nthis.callback=n,util_1.startsWith(e,'item'\n)&&t.arrayName&&t.index){var i=e.replace('item','')\n;this.expression=t.arrayName+'.'+t.index,i&&(\nthis.expression+=i)}else this.expression=e;this.data=t,\nthis.value=vueUtils.getVal(e,t,this.lazy),watchers||(\nwatchers=[]),watchers.push(this)}}\nreturn e.prototype.update=function(){if(\nthis.data&&this.expression&&this.callback){\nvar e=vueUtils.getVal(this.expression,this.data,this.lazy),\nt=this.value;vueUtils.equals(e,t)||(this.value=e,\nthis.callback(e))}},e}();exports.Watcher=Watcher\n;var Observer=function(){function e(e){this.observe(e)}\nreturn e.prototype.observe=function(t){var n=this\n;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){\ntry{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})}\n,e.prototype.defineReactive=function(e,t,n){var o=this\n;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,\nget:function(){return n},set:function(e){vueUtils.equals(e,n\n)||(n=e,o.observe(e),notify())}})},e}()\n;exports.Observer=Observer;var NodeCompile=function(){\nfunction e(e,t){void 0===t&&(t=!1),this.data=e||{},\nthis.lazy=t}return e.prototype.compile=function(n,e,t,o){\nvar i=this;if(e)switch(n){case'text':this.labelProcess(e,t,\nfunction(e,t){e.innerText=void 0===t?'':t});break\n;case'html':this.labelProcess(e,t,function(e,t){\ne.innerHtml=void 0===t?'':t});break;case'class':\nthis.labelProcess(e,t,function(e,t){var n=e.className,\no=e.getAttribute('v-class-name')||'';n=n&&n.replace(o,''\n).replace(/\\s+$/,''),e.setAttribute('v-class-name',t),\ne.className=n?n+' '+t:t});break;case'style':\nthis.eventProcess(e,t,function(e,t){\nvar n=util_1.tryJSONParse(t);util_1.copyObj(n,e.style)})\n;break;case'model':this.eventProcess(e,t,function(e,t){\ne.value=t}),'input'===o?e.oninput=function(){\nvueUtils.setTextVal(t,e.value,i.data)}:'switch'===o&&(\ne.onchange=function(e){vueUtils.setTextVal(t,e||'off',i.data\n)});break;case'if':this.eventProcess(e,t,function(e,t){\n!0===t?(e.style.display='flex',spmUtils.process(e,function(e\n){amc_types_1.amc.fn.spmExposure(e.spmId,e.param4Map,\ne.doNotResume)})):e.style.display='none'});break;case'spm':\nthis.labelProcess(e,t,function(e,t){e.setAttribute('spm',\nvoid 0===t?'':t)});break;case'uep':this.labelProcess(e,t,\nfunction(e,t){t&&util_1.startsWith(t,'a283'\n)&&e.setAttribute('behaviorInfo',JSON.stringify({spm:t,\nbizCode:'pay',extParam:{}}))});break;case'click':\nthis.eventProcess(e,t,function(e,t){vueUtils.isFunction(t\n)?e.onclick=function(){t(e),spmUtils.process(e,function(e){\namc_types_1.amc.fn.spmClick(e.spmId,e.param4Map)})\n}:e.onclick=function(){}});break;case'focus':\nthis.eventProcess(e,t,function(e,t){vueUtils.isFunction(t\n)?e.onfocus=function(){t(e)}:e.onfocus=function(){}});break\n;case'blur':this.eventProcess(e,t,function(e,t){\nvueUtils.isFunction(t)?e.onblur=function(){t(e)\n}:e.onfocus=function(){}});break;default:-1===t.indexOf('@{'\n)?e.setAttribute(n,t):this.labelProcess(e,t,function(e,t){\ne.setAttribute(n,void 0===t?'':t)})}},\ne.prototype.labelProcess=function(n,o,i){var a=this,\ne=o.match(/@\\{([^}]+)\\}/g),t=o;e&&0<e.length&&(\nt=vueUtils.getTextVal(o,this.data,this.lazy),e&&e.forEach(\nfunction(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(\nnew Watcher(t[1],a.data,function(e){i(n,vueUtils.getTextVal(\no,a.data,a.lazy))},a.lazy).id=a.data.arrayName)})),i(n,t)},\ne.prototype.eventProcess=function(t,e,n){\nvar o=/@\\{([^}]+)\\}/g.exec(e),i=vueUtils.getObject(e,\nthis.data,this.lazy);o&&1<o.length&&(new Watcher(o[1],\nthis.data,function(e){n(t,e)},this.lazy\n).id=this.data.arrayName),n(t,i)},e}()\n;exports.NodeCompile=NodeCompile;var spmUtils=function(){\nfunction e(){}return e.process=function(e,t){\nvar n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n)\n;o&&o.spmId&&t(o)}catch(e){}},e}(),vueUtils=function(){\nfunction vueUtils(){}\nreturn vueUtils.item2ArrayIndex=function(e,t){var n=e;if(\nutil_1.startsWith(e,'item')&&t.arrayName&&t.index){\nvar o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(\nn+=o)}return n},vueUtils.getVal=function(expr,data,lazy){if(\nexpr){var values=expr.match(/##([^#]+)##/g);if(\nvalues&&0<values.length){for(var func_1=expr,\nindex=0;/##([^#]+)##/g.test(func_1);)func_1=func_1.replace(\n/##([^#]+)##/,'vueArgs['+index+']'),index++;for(\nvar _vueArgs=[],i=0;i<index;i++)_vueArgs.push(\nthis.getRealVal(values[i].replace(/##/g,''),data,lazy))\n;return function(vueArgs){return eval(func_1)}(_vueArgs)}\nreturn this.getRealVal(expr,data,lazy)}},\nvueUtils.getRealVal=function(e,t,n){if(e){var o=e.split('.')\n,i=n&&vueUtils.isFunction(t[o[0]])?t[o[0]]():t;return(\nn?o.slice(1):o).reduce(function(e,t){return e[t]},i)}},\nvueUtils.getTextVal=function(e,i,a){var r=this\n;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],\nn=0;n<arguments.length;n++)t[n]=arguments[n]\n;var o=vueUtils.item2ArrayIndex(t[1],i);return void 0===(\ne=r.getVal(o,i,a))?'':e})},vueUtils.getObject=function(e,t,n\n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length\n)return this.getVal(o[1],t,n)},vueUtils.rmSymbol=function(e\n){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''\n},vueUtils.setVal=function(e,o,t){var i=e.split('.')\n;return i.reduce(function(e,t,n){\nreturn n===i.length-1?e[t]=o:e[t]},t)},\nvueUtils.setTextVal=function(e,t,n){\nvar o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(\no[1],t,n)},vueUtils.equals=function(e,t){return this.eq(e,t,\nvoid 0,void 0)},vueUtils.eq=function(e,t,n,o){if(e===t\n)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t\n;var i=toString.call(e);if(i!==toString.call(t))return!1\n;switch(i){case'[object RegExp]':case'[object String]':\nreturn''+e==''+t;case'[object Number]':\nreturn+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t\n;case'[object Date]':case'[object Boolean]':return+e==+t}\nvar a='[object Array]'===i;if(!a){if(\n'object'!=typeof e||'object'!=typeof t)return!1\n;var r=e.constructor,c=t.constructor;if(r!==c&&!(\nthis.isFunction(r)&&r instanceof r&&this.isFunction(c\n)&&c instanceof c)&&'constructor'in e&&'constructor'in t\n)return!1}o=o||[];for(var s=(n=n||[]).length;s--;)if(\nn[s]===e)return o[s]===t;if(n.push(e),o.push(t),a){if((\ns=e.length)!==t.length)return!1;for(;s--;)if(!this.eq(e[s],\nt[s],n,o))return!1}else{var l=Object.keys(e),d=void 0;if(\ns=l.length,Object.keys(t).length!==s)return!1;for(;s--;)if(\nd=l[s],!t.hasOwnProperty(d)||!this.eq(e[d],t[d],n,o)\n)return!1}return n.pop(),o.pop(),!0},\nvueUtils.isFunction=function(e){\nreturn'function'==typeof e||!1},vueUtils}()\n;exports.vueUtils=vueUtils},function(e,t,n){'use strict'\n;var o,i=this&&this.__extends||(o=function(e,t){return(\no=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}o(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var a=function(){function a(){\nthis.enabled=!0}return a.fmtLine=function(e,t,n,o){var i=''\n;return o&&(\ni=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o\n),'['+e+']['+a.fmtTime()+']['+t+']'+n+' '+i},\na.fmtTime=function(){var e=new Date;return e.getHours(\n)+':'+e.getMinutes()+':'+e.getSeconds(\n)+'.'+e.getMilliseconds()},a.prototype.enable=function(){\nthis.enabled=!0},a.prototype.disable=function(){\nthis.enabled=!1},a}();t.Logger=a,t.logger=new(function(e){\nfunction t(){return null!==e&&e.apply(this,arguments)||this}\nreturn i(t,e),t.prototype.e=function(e,t,n){},\nt.prototype.i=function(e,t,n){},t}(a))},function(e,t,n){\n'use strict';Object.defineProperty(t,'__esModule',{value:!0}\n);var o=n(1);t.mergeObject=o.mergeObject,\nt.isFunction=o.isFunction,t.isPreRender=o.isPreRender,\nt.copyObj=o.copyObj,t.doNothing=o.doNothing,\nt.tryJSONParse=o.tryJSONParse,\nt.checkEmptyObj=o.checkEmptyObj,\nt.substrWithFontWidth=o.substrWithFontWidth,\nt.calculateFontWidth=o.calculateFontWidth,\nt.deepCopy=o.deepCopy,t.getConfig=o.getConfig,\nt.showLoading=o.showLoading,t.hideLoading=o.hideLoading\n;var i=n(11)\n;t.VI_CHANNEL_MODE_FROM_TEMPLATE=i.VI_CHANNEL_MODE_FROM_TEMPLATE\n,t.SCALE_FACTOR=i.SCALE_FACTOR;var a=n(2)\n;t.modifyElementStyle=a.modifyElementStyle,\nt.modifyElementAttribute=a.modifyElementAttribute,\nt.modifyElementClass=a.modifyElementClass,\nt.visibleElement=a.visibleElement,\nt.modifyElementCSS=a.modifyElementCSS,\nt.createEmbedViPlugin=a.createEmbedViPlugin,\nt.getThemeColor=a.getThemeColor,\nt.createEmbedPlugin=a.createEmbedPlugin;var r=n(12)\n;t.logAction=r.logAction;var c=n(13);t.stEscape=c.stEscape,\nt.toString=c.toString,t.STPerf=c.STPerf,t.STAct=c.STAct,\nt.STRecord=c.STRecord;var s=n(14)\n;t.ImageLoader=s.ImageLoader;var l=n(15);t.ocr=l.ocr},\nfunction(e,t,n){'use strict';var o;Object.defineProperty(t,\n'__esModule',{value:!0}),t.VI_CHANNEL_MODE_FROM_TEMPLATE='1'\n,(o=t.SCALE_FACTOR||(t.SCALE_FACTOR={})).LEVEL_0='0',\no.LEVEL_1='1',o.LEVEL_2='2',o.LEVEL_3='3',o.LEVEL_4='4'},\nfunction(e,t,n){'use strict';Object.defineProperty(t,\n'__esModule',{value:!0});var o=n(0);t.logAction=function(e,t\n){window.pageId||(window.pageId='|'+Math.random().toString(\n36).substr(2,3)),t=t?t+window.pageId:window.pageId,\no.amc.fn.logAction(e,t)}},function(e,t,n){'use strict'\n;function o(e){for(var t=[],n=0;n<e.length;n+=1)t.push(\ne[n]||'-');return t.join('\\'')}Object.defineProperty(t,\n'__esModule',{value:!0}),t.stEscape=function(e){\nreturn e&&e.replace('\\'','%27').replace('`','%60').replace(\n'#','%23')},t.toString=o;var i=function(){function e(e,t,n){\nthis.prefs=[],this.initTime=Date.now(),this.cache={},\nthis.submited=!1,this.record=e,this.prefs[0]=t,\nthis.prefs[1]=n,this.prefs[2]=String(this.initTime),\nthis.prefs[7]='',this.record.addSTPref(this)}\nreturn e.prototype.toString=function(){return o(this.prefs)}\n,e.prototype.putCache=function(e,t){this.cache[e]=t},\ne.prototype.getCache=function(e,t){return this.cache[e]||t},\ne.prototype.isSubmited=function(){return this.submited},\ne.prototype.submit=function(){this.isSubmited()||(\nthis.submited=!0,this.submitInner())},\ne.prototype.submitInner=function(){},e}();t.STPerf=i\n;var a=function(){function e(e,t,n){this.acts=[],\nthis.record=e,this.acts[0]=t,this.acts[1]=n,\nthis.acts[6]=String(Date.now()),this.record.addSTAct(this)}\nreturn e.prototype.setActName=function(e){this.acts[2]=e},\ne.prototype.toString=function(){return o(this.acts)},e}()\n;t.STAct=a;var r=function(){function e(e,t,n,o,i,a){\nthis.ids=[],this.prefs=[],this.acts=[],\nthis.initTime=Date.now(),this.ids[0]=String(this.initTime),\nthis.ids[1]=e,this.ids[2]=t,this.ids[3]=n,this.ids[4]=o,\nthis.ids[5]=i,this.ids[7]='',this.logHandle=a}\nreturn e.prototype.addSTAct=function(e){this.acts.push(e)},\ne.prototype.addSTPref=function(e){this.prefs.push(e)},\ne.prototype.submit=function(e){var t=this.toString(e)\n;this.logHandle(t)},e.prototype.toString=function(t){for(\nvar e=[],n=0;n<this.ids.length;n+=1)e.push(this.ids[n]||'-')\n;var o=[];for(n=0;n<this.acts.length;n+=1)o.push(\nthis.acts[n].toString());this.acts=[];var i=[]\n;return this.prefs=this.prefs.filter(function(e){\nreturn t&&e.submit(),!e.isSubmited()||(i.push(e.toString()),\n!1)}),e.join('\\'')+'#'+o.join('`')+'#'+i.join('`')},\ne.prototype.getInitTime=function(){return this.initTime},e}(\n);t.STRecord=r},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0});var c=n(0)\n,o=function(){function e(){this.mUrlCache=null,\nthis.mPixelWidthCache={}}return e.getInstance=function(){\nreturn e.sImageLoader||(e.sImageLoader=new e),e.sImageLoader\n},e.prototype.loadImageHelper=function(e,t,n){var a=this;if(\n!this.mUrlCache){this.mUrlCache={}\n;var r=document.onImgLoaded;document.onImgLoaded=function(e,\nt){var n=a.mUrlCache[t];if(!n){var o='';for(\nvar i in a.mPixelWidthCache)if(\na.mPixelWidthCache.hasOwnProperty(i)&&0===t.indexOf(i)){o=i,\nn=a.mPixelWidthCache[i];break}n&&!n.validate&&(\ndelete a.mPixelWidthCache[o],n=null)}n&&n.callback?(\nn.validate=!1,n.callback(e,t,n.img)):r&&r(e,t),\ndelete a.mUrlCache[t]}}var o={callback:n,img:e,validate:!0}\n;if(this.mUrlCache[t]=o,0<t.indexOf('[pixelWidth]')){\nvar i=t.substr(0,t.indexOf('[pixelWidth]'))\n;this.mPixelWidthCache[i]=o}e.src=t},\ne.prototype.loadImage=function(o,e,i,a){void 0===a&&(a=!0)\n;var r=!1,t=c.amc.isSDK&&c.amc.isIOS,n=function(e,t,n){\ne&&o&&a&&c.amc.fn.show(o),r=e,i&&i(e,t,o)}\n;this.loadImageHelper(o,e,t?void 0:n),t&&n(!0,e),\na&&setTimeout(function(){r||c.amc.fn.hide(o)},10)},e}()\n;t.ImageLoader=o},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0});var r=n(0)\n;t.ocr=function(o,i,a,e){if(void 0===a&&(a=!0),void 0===e&&(\ne=500),\no&&r.amc.isAndroid&&!r.amc.isSDK&&r.amc.fn.sdkGreaterThanOrEqual(\n'10.8.53')){var t=(new Date).getUTCMilliseconds()\n;o.className+=' '+t,setTimeout(function(){document.invoke(\n'ocr',{selector:'.'+t},function(e){if(\ne&&e.data&&e.data[0]&&e.data[0].body){var t=''\n;e.data[0].body.forEach(function(e){e.label&&(t+=e.label)})\n;var n=o.innerText.replace(/<[^>]+>([^<]+)<\\/\\w+>/g,'$1'\n).split('，').join(',').split('？').join('?').split('！').join(\n'!').split('：').join(':').split('“').join('\"').split('”'\n).join('\"');t!==n&&a&&r.amc.fn.logError(\n'render_label_exception',t),i&&i({result:t,pureInnerText:n})\n}})},e)}}},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0});var o=n(0)\n,i=n(1),a=n(17),r=void 0;t.getDialog=function(){return r||(\nr=new c),r};var c=function(){function e(){\nthis.birdnestDialog=void 0,this.antUISwitch=!1,\nthis.getBirdnestDialog(),this.getAntUIGray()}\nreturn e.prototype.getBirdnestDialog=function(){\nreturn this.birdnestDialog||(\nthis.birdnestDialog=new a.CommonDialog,\nthis.birdnestDialog.mountTo(document.body)),\nthis.birdnestDialog},e.prototype.show=function(n,e){if(n)if(\nvoid 0!==e&&(this.antUISwitch=e),this.getAntuiEnable(n)){\nvar t={title:n.title,message:n.message,antui:!0},o=n.btns,\ni=void 0,a=1;o&&0<o.length&&(a=o.length,i=o[o.length-1],\no=1<o.length?o.slice(0,o.length-1):void 0),t.btnsText=o,\nt.cancelButton=i,'column'===n.buttonDirection&&(\nt.buttonDirection='column'),document.confirm(t,function(e){\nvar t=0;try{t=parseInt(e.index||'0')}catch(e){}\n0===t?n.clickCallBack&&n.clickCallBack(a-1\n):n.clickCallBack&&n.clickCallBack(t-1)},!0)}else{\nvar r=this.getBirdnestDialog();n.textAlign&&(\nn.msgExtraCss?n.msgExtraCss='text-align:'+n.textAlign+';'+n.msgExtraCss:n.msgExtraCss='text-align:'+n.textAlign+';'\n),r.show(n)}},e.prototype.getAntuiEnable=function(e){if(\ne.labels&&0<e.labels.length)return!1;if(e.strongBtn)return!1\n;if(e.contentImg)return!1;if(e.msgExtraCss)return!1;if(\no.amc.isSDK)return!1;if(!o.amc.fn.sdkGreaterThanOrEqual(\n'10.8.41'))return!1;var t=String.fromCharCode(60),\nn=String.fromCharCode(62);return!(\n!e.message||e.message&&-1!==e.message.indexOf(t+'/a'+n))&&!(\ne.protocols&&1<=e.protocols.length)&&!e.addition&&(\no.amc.isAndroid?21<=window.flybird.local.osVersion&&this.antUISwitch:this.antUISwitch\n)},e.prototype.getAntUIGray=function(){var t=this\n;i.getConfig('msp_antui_dialog_gray',function(e){\nt.antUISwitch=e})},e}();t.Dialog=c},function(e,t,n){\n'use strict';var o,i=this&&this.__extends||(o=function(e,t){\nreturn(o=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}o(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var a=n(5),r=n(0),c=n(4),s=n(18),\nl=n(2);a.ComponentRegistry.registerComponent(\ns.Button.componentName,s.Button);var d=function(e){\nfunction t(){var o=null!==e&&e.apply(this,arguments)||this\n;return o.onCreated=function(){},o.onMounted=function(){},\no.onDialogKeyDown=function(){},o.buttonClick=function(e){\no.getViewInComponentById('dlg').close(),\no.buttonClickListener&&o.buttonClickListener(e)},\no.vueModel={data:{btns:[],msg:'',title:'',addition:'',\nstrongBtn:'',labels:[],btnBoxStyle:{flexDirection:'column'},\nonBtnClick:function(e){var t=e.getAttribute('index'),n=0\n;try{n=parseInt(t||'0')}catch(e){}\n2===o.vueModel.data.btns.length&&o.isHorizontal&&(\nn=Math.abs(n-1)),o.buttonClick(n)},showCloseBtn:!1},\ncompute:{msgBoxStyle:function(){\nreturn o.vueModel.data.labels&&0<o.vueModel.data.labels.length||o.vueModel.data.addition?{\nmarginBottom:'0px'}:{marginBottom:'28px'}}}},\no.isHorizontal=!1,o.onCloseDlg=function(){\no.getViewInComponentById('dlg').close(),\no.onCloseListener&&o.onCloseListener()},\no.showContentImg=function(e){var t=o.getViewInComponentById(\n'dlg');if(o.imgBox&&t.removeChild(o.imgBox),e.contentImg){\no.imgBox=o.createStyledElement('div','contentImgBox',\n'content-img-container amc-align-center amc-justify-center')\n,e.contentImgBoxStyle&&l.modifyElementStyle(o,o.imgBox,\ne.contentImgBoxStyle);var n=o.createStyledElement('img',\n'contentImg');switch(o.imgBox.appendChild(n),\ne.contentImgAnchor){case'title':t.insertBefore(o.imgBox,\no.getViewInComponentById('titleBox'));break;case'message':\nt.insertBefore(o.imgBox,o.getViewInComponentById('msgBox'))\n;break;case'labels':t.insertBefore(o.imgBox,\no.getViewInComponentById('labelsContainer'));break;default:\nt.insertBefore(o.imgBox,o.getViewInComponentById('mainBody')\n)}e.contentImgStyle?l.modifyElementStyle(o,n,\ne.contentImgStyle):l.modifyElementStyle(o,n,{width:'220px'})\n,n.src=e.contentImg}},o}return i(t,e),\nt.prototype.setData=function(e){var t,n=this\n;this.vueModel.data.title=e.title||'',\nthis.vueModel.data.msg=e.message||'',\nthis.vueModel.data.addition=e.addition||'',\nthis.buttonClickListener=e.clickCallBack,\nthis.onCloseListener=e.onCloseCallBack,\ne.labels&&0<e.labels.length&&(\nthis.vueModel.data.labels=e.labels)\n;var o=e.btns&&0<e.btns.length?e.btns:e.strongBtn?[]:[\n'{{confirm_btn}}'];if(\nthis.isHorizontal='column'!==e.buttonDirection&&2===o.length&&!e.strongBtn\n,this.isHorizontal&&(o=o.reverse()),\nthis.vueModel.data.btnBoxStyle=e.btnBoxStyle||{\nflexDirection:this.isHorizontal?'row':'column',\nmargin:e.strongBtn&&o.length<=1?'12px 0':''},\nthis.vueModel.data.strongBtn=e.strongBtn||'',\nthis.vueModel.data.strongBtn){\nvar i=this.getSubComponentById('strongButton',s.Button);i&&(\ni.setText(((t={}\n)[s.BUTTON_STATUS.NORMAL]=this.vueModel.data.strongBtn,\nt[s.BUTTON_STATUS.SUCCESS]=this.vueModel.data.strongBtn,\nt[s.BUTTON_STATUS.LOADING]=this.vueModel.data.strongBtn,t)),\ni.setOnClick(function(){n.buttonClick(-1)}),\ni.changeLoadingStatus(s.BUTTON_STATUS.NORMAL))}\nthis.vueModel.data.btns=o.map(function(e,t){return{\nlineStyle:0===t?{display:'none'}:n.isHorizontal?{width:'1px'\n}:{height:'1px'},text:e||''}}),\nthis.vueModel.data.onMsgLink=e.onMsgLink,e.msgExtraCss&&(\nthis.getViewInComponentById('msg'\n).style.cssText=e.msgExtraCss),e.additionExtraCss&&(\nthis.getViewInComponentById('addition'\n).style.cssText=e.additionExtraCss),\nthis.protocolNode=this.getViewInComponentById(\n'singleProtocolLabel'),e.protocolsExtraCss&&(\nthis.protocolNode.style.cssText=e.protocolsExtraCss)},\nt.prototype.initViews=function(e){\nvar t=this.vueModel.data.onMsgLink;if(t&&(\nthis.getViewInComponentById('msg').onlink=function(e){t(e)})\n,e&&e.protocols&&r.amc.fn.isArray(e.protocols\n)&&1<=e.protocols.length){r.amc.fn.show(this.protocolNode)\n;var n=e.protocols[0]\n;this.protocolNode.innerText=n.innerText||'《'+n.text+'》',\nthis.protocolNode.onclick=function(){\ne.protocolsSpmInfo&&1<=e.protocolsSpmInfo.length&&r.amc.fn.spmClick(\ne.protocolsSpmInfo[0].spmId,e.protocolsSpmInfo[0].spmObj),\ndocument.submit({action:{\nname:'loc:openweb(\\''+n.url+'\\', \\'\\')'}})}}},\nt.prototype.show=function(e){this.setData(e),this.initViews(\ne),this.showContentImg(e);var t=this.getViewInComponentById(\n'dlg');r.amc.isIOS&&r.amc.fn.sdkGreaterThanOrEqual('10.8.35'\n)?t.show():t.showModal(),e.showCloseBtn&&(r.amc.fn.show(\nthis.getViewInComponentById('closeBtnBox')),\nthis.getViewInComponentById('closeBtn').src=r.amc.res.close)\n},t.getComponentCSSRules=function(){return{\n'.dlg-box':'_CommonDialog_mbaf-c-dlg-box',\n'.margin-box-lr':'_CommonDialog_mbaf-c-margin-box-lr',\n'.title-box':'_CommonDialog_mbaf-c-title-box',\n'.msg':'_CommonDialog_mbaf-c-msg',\n'.title':'_CommonDialog_mbaf-c-title',\n'.btn-text':'_CommonDialog_mbaf-c-btn-text',\n'.btn-div':'_CommonDialog_mbaf-c-btn-div',\n'.align-self-box':'_CommonDialog_mbaf-c-align-self-box',\n'.btn-line':'_CommonDialog_mbaf-c-btn-line',\n'.labels-container':'_CommonDialog_mbaf-c-labels-container',\n'.label':'_CommonDialog_mbaf-c-label',\n'.close-btn-container':'_CommonDialog_mbaf-c-close-btn-container',\n'.close-btn':'_CommonDialog_mbaf-c-close-btn',\n'.content-img-container':'_CommonDialog_mbaf-c-content-img-container',\n'.protocol-box':'_CommonDialog_mbaf-c-protocol-box',\n'.single-protocol-label':'_CommonDialog_mbaf-c-single-protocol-label',\n'.addition':'_CommonDialog_mbaf-c-addition'}},\nt.getComponentJson=function(){return{'sp-view-id':'dlg',\nonkeydown:'onDialogKeyDown',\n_c:'_CommonDialog_mbaf-c-dlg-box amc-v-box amc-justify-center amc-align-center',\n_t:'dialog',_cd:[{'sp-view-id':'closeBtnBox',\n_c:'_CommonDialog_mbaf-c-close-btn-container amc-hidden',\n_t:'div',_cd:[{'sp-view-id':'closeBtn',\n_c:'_CommonDialog_mbaf-c-close-btn',onclick:'onCloseDlg',\n_t:'img'}]},{'v-if':'@{##msg## && ##msg##.length > 0}',\n'sp-view-id':'titleBox',\n_c:'_CommonDialog_mbaf-c-align-self-box _CommonDialog_mbaf-c-title-box _CommonDialog_mbaf-c-margin-box-lr',\n_t:'div',_cd:[{'sp-view-id':'title',\n_c:'amc-ellipsis _CommonDialog_mbaf-c-title amc-flex-1 amc-text-center',\n_t:'label','v-text':'@{title}'}]},{'sp-view-id':'msgBox',\n_c:'amc-flex-center _CommonDialog_mbaf-c-align-self-box _CommonDialog_mbaf-c-margin-box-lr',\n'v-style-cal':'@{msgBoxStyle}',_t:'div',_cd:[{\n'sp-view-id':'msg',_c:'_CommonDialog_mbaf-c-msg',_t:'label',\n'v-text':'@{msg}'}]},{'sp-view-id':'addition',\n_c:'_CommonDialog_mbaf-c-addition _CommonDialog_mbaf-c-margin-box-lr',\n'v-if':'@{!!##addition##}',_t:'label','v-text':'@{addition}'\n},{'v-if':'@{##labels## && ##labels##.length > 0}',\n'sp-view-id':'labelsContainer',\n_c:'_CommonDialog_mbaf-c-labels-container amc-align-center',\n_t:'div',_cd:[{'v-for':'@{labels}',\n_c:'_CommonDialog_mbaf-c-label amc-text-center',_t:'label',\n'v-text':'@{item}'}]},{'sp-view-id':'mainBody',\n_c:'_CommonDialog_mbaf-c-align-self-box amc-v-box amc-justify-center',\n_t:'div',_cd:[{'sp-view-id':'singleProtocolLabel',\n_c:'_CommonDialog_mbaf-c-single-protocol-label amc-flex-1 amc-ellipsis-2-line _CommonDialog_mbaf-c-protocol-box _CommonDialog_mbaf-c-margin-box-lr amc-hidden',\n_t:'label'},{'v-if':'@{!!##strongBtn##}',\n_c:'_CommonDialog_mbaf-c-margin-box-lr',_t:'div',_cd:[{\n'sp-component':'Button','sp-component-id':'strongButton',\n_t:'div'}]},{'v-if':'@{!##strongBtn##}',_c:'amc-adapt-line',\n_t:'div'},{'sp-view-id':'btnBox',_c:'amc-align-center',\n'v-style':'@{btnBoxStyle}',_t:'div',_cd:[{'v-for':'@{btns}',\n'v-style':'@{btnBoxStyle}',\n_c:'amc-align-center amc-flex-1 _CommonDialog_mbaf-c-align-self-box',\n_t:'div',_cd:[{'v-style':'@{item.lineStyle}',\n_c:'_CommonDialog_mbaf-c-btn-line _CommonDialog_mbaf-c-align-self-box',\n_t:'div'},{'v-click':'@{onBtnClick}',\n_c:'_CommonDialog_mbaf-c-btn-div _CommonDialog_mbaf-c-btn-div amc-flex-1',\n_t:'div',_cd:[{\n_c:'_CommonDialog_mbaf-c-btn-text amc-theme-color amc-flex-1 amc-text-center amc-ellipsis',\n_t:'label','v-text':'@{item.text}'}]}]}]}]}]}},\nt.componentName='CommonDialog',\nt.componentHashName='CommonDialog_mbaf',t}(c.BNComponent)\n;t.CommonDialog=d},function(e,t,n){'use strict';var o,i,a,r,\nc=this&&this.__extends||(o=function(e,t){return(\no=Object.setPrototypeOf||{__proto__:[]\n}instanceof Array&&function(e,t){e.__proto__=t}||function(e,\nt){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},\nfunction(e,t){function n(){this.constructor=e}o(e,t),\ne.prototype=null===t?Object.create(t):(\nn.prototype=t.prototype,new n)});Object.defineProperty(t,\n'__esModule',{value:!0});var s,l,d,u,p,m,f=n(0),h=n(4),g=n(2\n),b=n(1);(m=s=t.BUTTON_TYPE||(t.BUTTON_TYPE={})\n).PRIMARY='PRIMARY',m.NORMAL='NORMAL',(p=l=t.LOADING_TYPE||(\nt.LOADING_TYPE={}))[p.JuHua=0]='JuHua',\np[p.CIRCLE=1]='CIRCLE',(u=d=t.BUTTON_STATUS||(\nt.BUTTON_STATUS={}))[u.NORMAL=0]='NORMAL',\nu[u.LOADING=1]='LOADING',u[u.SUCCESS=2]='SUCCESS',\nu[u.DISABLE=3]='DISABLE';var y=((i={}\n)[s.NORMAL]=f.amc.path+'alipay_msp_loading_blue.gif',\ni[s.PRIMARY]=f.amc.res.loading,i),_=((a={}\n)[s.NORMAL]='amc-loading-img amc-text-color-blue',\na[s.PRIMARY]='amc-loading-img amc-text-white-clolor',a),v=((\nr={})[s.NORMAL]=f.amc.path+'alipay_msp_success_blue.gif',\nr[s.PRIMARY]=f.amc.res.success,r),\nx='color: '+g.getThemeColor()+';',\nC='background-color: #FFF;border: 2px '+g.getThemeColor(\n)+';font-size: 18px;',T=function(t){function e(){\nvar e=null!==t&&t.apply(this,arguments)||this\n;return e.props={type:s.PRIMARY,onClick:function(){},\ncircleLoading:{},juHuaLoading:{},lottieImg:null},\ne.onCreated=function(){},e.onMounted=function(){\ne.props.circleLoading=g.createEmbedViPlugin(\ne.getViewInComponentById('buttonInnerBox'),{\ntype:'MQPPayGifView'},'amc-hidden',e.getViewInComponentById(\n'buttonText')),g.modifyElementCSS(e,e.props.circleLoading,\n'width: 24px; height: 24px;margin-right: 8px;'),\ne.props.juHuaLoading=e.getViewInComponentById('loadingJuHua'\n),f.amc.isAndroid&&g.modifyElementAttribute(e,\ne.props.juHuaLoading,{\nsrc:f.amc.path+'alipay_msp_indicator_white_loading'}),\ng.modifyElementAttribute(e,'buttonText',{\naccessibilityTraits:'Button'}),e.changeLoadingStatus(\nd.NORMAL)},e.lottieReady=!1,e.lottiePlay=!1,e}return c(e,t),\ne.prototype.setStyle=function(e){g.modifyElementStyle(this,\n'button',e)},e.prototype.setHeight=function(e){\ng.modifyElementStyle(this,'buttonText',{height:e+'px'}),\ng.modifyElementStyle(this,'button',{height:e+'px',\nmaxHeight:e+'px',minHeight:e+'px'})},\ne.prototype.setType=function(e){switch(this.props.type=e,\ng.modifyElementClass(this,this.props.juHuaLoading,_[e],!1),e\n){case s.NORMAL:g.modifyElementClass(this,'button',\n'amc-btn-secondary amc-align-center amc-justify-center button-primary'\n,!1),g.modifyElementCSS(this,'buttonText',x),\ng.modifyElementCSS(this,'button',C);break;case s.PRIMARY:\ng.modifyElementClass(this,'button',\n'amc-btn-primary amc-align-center amc-justify-center button-primary'\n,!1),g.modifyElementCSS(this,'buttonText','color: #fff;')}\nreturn this},e.prototype.setLottieImg=function(e){var t=this\n;if(!f.amc.isSDK&&e&&f.amc.fn.sdkGreaterThanOrEqual(\n'10.8.29')){var n=this.getViewInComponentById(\n'buttonBackground');this.props.lottieImg=f.amc.fn.create(\n'lottie','',n),this.applyStyleTo(this.props.lottieImg,\n'amc-flex-center lottie-loading'),\nthis.props.lottieImg.djangoId=e,\nthis.props.lottieImg.repeatCount='-1',\nthis.props.lottieImg.invoke('pause',{}),\nthis.props.lottieImg.dataReady=function(){t.lottieReady=!0,\nt.lottiePlay&&t.props.lottieImg&&t.props.lottieImg.invoke(\n'play',{})}}return this},e.prototype.startLottie=function(){\nthis.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke(\n'play',{}),this.lottiePlay=!0},\ne.prototype.stopLottie=function(){\nthis.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke(\n'stop',{}),this.lottiePlay=!1},\ne.prototype.setVisible=function(e){\nreturn g.modifyElementStyle(this,'button',{\ndisplay:e?'flex':'none'}),this},\ne.prototype.setOnClick=function(e){\nreturn this.props.onClick=e||b.doNothing,\ng.modifyElementAttribute(this,'button',{\nonclick:this.props.onClick}),this},\ne.prototype.setText=function(e){return this.props.text=e,\nthis},e.prototype.changeTextAndOnClick=function(e,t){\nvar n=void 0;switch(e){case d.SUCCESS:this.props.text&&(\nn=this.props.text[d.SUCCESS]),g.modifyElementAttribute(this,\n'button',{disabled:!1,onclick:b.doNothing});break\n;case d.NORMAL:this.props.text&&(n=this.props.text[d.NORMAL]\n),g.modifyElementAttribute(this,'button',{disabled:!1,\nonclick:this.props.onClick});break;case d.LOADING:\nthis.props.text&&(n=this.props.text[d.LOADING]),\ng.modifyElementAttribute(this,'button',{disabled:!1,\nonclick:b.doNothing});break;case d.DISABLE:\nthis.props.text&&(n=this.props.text[d.NORMAL]),\ng.modifyElementAttribute(this,'button',{disabled:!0,\nonclick:b.doNothing});break;default:n=''}\nreturn void 0!==t?g.modifyElementAttribute(this,'buttonText'\n,{innerText:t}):void 0!==n&&g.modifyElementAttribute(this,\n'buttonText',{innerText:n}),this},\ne.prototype.changeLoadingStatus=function(e,t,n){switch(t){\ncase l.CIRCLE:g.visibleElement(this,this.props.juHuaLoading,\n!1),g.visibleElement(this,this.props.circleLoading,!1),\nthis.changeTextAndOnClick(e,n),this.changeCircleLoading(e),\ng.visibleElement(this,this.props.circleLoading,\ne!==d.NORMAL&&e!==d.DISABLE);break;case l.JuHua:\ng.visibleElement(this,this.props.juHuaLoading,!1),\ng.visibleElement(this,this.props.circleLoading,!1),\nthis.changeTextAndOnClick(e,n),g.visibleElement(this,\nthis.props.juHuaLoading,e!==d.NORMAL&&e!==d.DISABLE);break\n;default:this.changeLoadingStatus(e,l.CIRCLE,n)}},\ne.prototype.changeCircleLoading=function(e){\nvar t=this.props.circleLoading;switch(e){case d.LOADING:\ng.modifyElementAttribute(this,t,{src:y[this.props.type]})\n;break;case d.NORMAL:case d.DISABLE:\ng.modifyElementAttribute(this,t,{src:''});break\n;case d.SUCCESS:g.modifyElementAttribute(this,t,{\nsrc:v[this.props.type]});break;default:f.amc.fn.logError(\n'Button','loading-'+(e||'status'))}},\ne.getComponentCSSRules=function(){return{\n'.text-primary':'_Button_b6r6-c-text-primary',\n'.button-primary':'_Button_b6r6-c-button-primary',\n'.lottie-loading':'_Button_b6r6-c-lottie-loading',\n'.lottie-margin':'_Button_b6r6-c-lottie-margin'}},\ne.getComponentJson=function(){return{'sp-view-id':'button',\n_c:'amc-btn-primary amc-align-center amc-justify-center amc-v-box _Button_b6r6-c-button-primary',\n_t:'div',_cd:[{'sp-view-id':'buttonBackground',\n_c:'_Button_b6r6-c-lottie-loading amc-align-center amc-justify-center',\n_t:'div'},{'sp-view-id':'buttonInnerBox',\n_c:'_Button_b6r6-c-lottie-margin amc-align-center amc-justify-center',\n_t:'div',_cd:[{'sp-view-id':'loadingJuHua',src:'indicatior',\n_c:'amc-loading-img amc-text-white-clolor',alt:'',_t:'img'},\n{'sp-view-id':'buttonText',\n_c:'_Button_b6r6-c-text-primary amc-ellipsis',_t:'label',\n_x:'{{pay_right_now}}'}]}]}},e.componentName='Button',\ne.componentHashName='Button_b6r6',e}(h.BNComponent)\n;t.Button=T},function(e,t,n){'use strict'\n;Object.defineProperty(t,'__esModule',{value:!0});var r=n(0)\n;t.mergeObject=function(){for(var e=[],\nt=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(\ne&&e.length)for(var o=0;o<e.length;o++){var i=e[o];if(\nr.amc.fn.isObject(i))for(var a in i)i.hasOwnProperty(a)&&(\nn[a]=i[a])}return n},t.isFunction=function(e){\nreturn'[object Function]'===Object.prototype.toString.call(e\n)},t.isPreRender=function(e){return e&&(\ne.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender\n)},t.copyObj=function(e,t){for(var n in t||(t={}),e\n)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},\nt.doNothing=function(){},t.tryJSONParse=function(e){if(\nr.amc.fn.isObject(e))return e;try{return JSON.parse(e)\n}catch(e){return{}}},t.checkEmptyObj=function(e){\nreturn r.amc.fn.isString(e)?0===e.length:!(\ne&&0!==Object.keys(e).length)},\nt.substrWithFontWidth=function(e,t,n){if(!e)return e;for(\nvar o='',i=0,a=e.length,r=0;r<a;r++){var c=n?e[a-r-1]:e[r]\n;if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break\n}return o},t.calculateFontWidth=function(e){if(!e)return 0\n;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++\n)n.test(e[o])?t+=.45:t++;return Math.round(t)},\nt.deepCopy=function e(t){if(null==t||'object'!=typeof t\n)return t;var n;if(t instanceof Date)return(n=new Date\n).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(\nvar o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(\nt instanceof Object){for(var a in n={},t)t.hasOwnProperty(a\n)&&(n[a]=e(t[a]));return n}throw new Error(\n'Unable to copy obj! Its type isn\\'t supported.')},\nt.getConfig=function(e,t){setTimeout(function(){\ndocument.invoke('queryInfo',{queryKey:'configInfo',\nconfigKey:e},function(e){t(e.available)})},20)},\nt.showLoading=function(){setTimeout(function(){\ndocument.invoke('showLoading')},20)},t.hideLoading=function(\n){setTimeout(function(){document.invoke('hideLoading')},20)}\n}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body amc-bg-white", "tag": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-card-type-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0301", "tplId": "QUICKPAY@cashier-card-type-flex", "tplVersion": "5.4.6"}