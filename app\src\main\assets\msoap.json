[{"bizName": "msoa.taobao.cart", "services": [{"result": [{"name": "msg", "type": "java.lang.String"}, {"name": "resultCode", "type": "java.lang.String"}], "targetPlatforms": ["native", "weex", "windvane"], "provider": {"needTimeout": "false", "func": "addBag", "action": "", "className": "com.taobao.cart.api.ITBCartService", "type": "serviceHub"}, "serviceId": "msoa.taobao.cart.add", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "quantity", "type": "long"}, {"name": "skuId", "type": "java.lang.String"}, {"name": "exParams", "type": "java.lang.String"}, {"name": "from", "type": "java.lang.String"}, {"name": "context", "type": "android.content.Context"}], "version": "1.0"}]}, {"bizName": "msoa.taobao.cart.sdk", "services": [{"result": [{"name": "requestType", "type": "int"}, {"name": "response", "type": "mtopsdk.mtop.domain.MtopResponse"}, {"name": "data", "type": "mtopsdk.mtop.domain.BaseOutDo"}, {"name": "context", "type": "java.lang.Object"}], "targetPlatforms": ["native", "weex", "windvane"], "provider": {"needTimeout": "false", "func": "addCartBag", "implClassName": "com.taobao.android.trade.cart.provider.msoa.MsoaCartServiceImpl", "bundleName": "com.taobao.android.newtrade", "action": "", "className": "com.taobao.android.trade.cart.provider.msoa.MsoaCartService", "type": "bundleInterface"}, "serviceId": "msoa.taobao.cart.sdk.add", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "skuId", "type": "java.lang.String"}, {"name": "quantity", "type": "java.lang.String"}, {"name": "exParams", "type": "java.lang.String"}, {"name": "feature", "type": "java.lang.String"}, {"name": "from", "type": "java.lang.String"}, {"name": "ttid", "type": "java.lang.String"}, {"name": "bizId", "type": "java.lang.String"}], "version": "2.0"}]}, {"bizName": "msoa.taobao.cart.open", "services": [{"result": [{"name": "msg", "type": "java.lang.String"}, {"name": "resultCode", "type": "java.lang.String"}], "targetPlatforms": ["native", "weex", "windvane"], "provider": {"needTimeout": "false", "func": "addBag", "implClassName": "com.taobao.android.opencart.msoa.AddBagServiceImpl", "bundleName": "com.taobao.android.newtrade", "action": "", "className": "com.taobao.android.opencart.msoa.IAddBagService", "type": "bundleInterface"}, "serviceId": "msoa.taobao.cart.open.add", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "quantity", "type": "java.lang.Integer"}, {"name": "skuId", "type": "java.lang.String"}, {"name": "exParams", "type": "java.lang.String"}, {"name": "cartFrom", "type": "java.lang.String"}, {"name": "showSKU", "type": "java.lang.Bo<PERSON>an"}, {"name": "clientExParams", "type": "java.util.Map"}, {"name": "context", "type": "android.content.Context"}], "version": "1.0"}]}, {"bizName": "msoa.taobao.favorite", "services": [{"result": [{"name": "isFavorite", "type": "boolean"}], "provider": {"func": "isFavoriteItem", "action": "com.taobao.favorite.good.service", "className": "com.taobao.favorites.service.FavGoodService", "type": "bindService"}, "serviceId": "msoa.taobao.favorite.isFavoriteItem", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}], "version": "1.0"}, {"result": [{"name": "addResult", "type": "boolean"}], "provider": {"func": "addFavoriteItem", "action": "com.taobao.favorite.good.service", "className": "com.taobao.favorites.service.FavGoodService", "type": "bindService"}, "serviceId": "msoa.taobao.favorite.addFavoriteItem", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}], "version": "1.0"}, {"result": [{"name": "addResult", "type": "boolean"}], "provider": {"func": "addFavoriteItemV2", "action": "com.taobao.favorite.good.service", "className": "com.taobao.favorites.service.FavGoodService", "type": "bindService"}, "serviceId": "msoa.taobao.favorite.addFavoriteItemV2", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "ext", "type": "java.util.Map"}], "version": "1.0"}, {"result": [{"name": "deleteResult", "type": "boolean"}], "provider": {"func": "deleteFavoriteItem", "action": "com.taobao.favorite.good.service", "className": "com.taobao.favorites.service.FavGoodService", "type": "bindService"}, "serviceId": "msoa.taobao.favorite.deleteFavoriteItem", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}], "version": "1.0"}, {"result": [{"name": "activityUrl", "type": "java.lang.String"}], "provider": {"func": "showCategoryList", "action": "com.taobao.favorite.good.service", "className": "com.taobao.favorites.service.FavGoodService", "type": "bindService"}, "serviceId": "msoa.taobao.favorite.showCategoryList", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "x", "type": "int"}, {"name": "y", "type": "int"}], "version": "1.0"}]}, {"bizName": "msoa.taobao.detail", "services": [{"serviceType": 0, "provider": {"needTimeout": false, "func": "showSku", "implClassName": "com.taobao.android.detail.msoa.DetailMSOAImpl", "action": "", "bundleName": "com.taobao.android.detail", "className": "com.taobao.android.detail.msoa.DetailMSOAInterface", "type": "bundleInterface"}, "serviceId": "msoa.taobao.detail.showsku", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "actionType", "type": "java.lang.String"}], "version": "1.0", "platform": 7}, {"serviceType": 0, "provider": {"needTimeout": false, "func": "showSku", "implClassName": "com.taobao.android.detail.msoa.DetailMSOAImpl", "action": "", "bundleName": "com.taobao.android.detail", "className": "com.taobao.android.detail.msoa.DetailMSOAInterface", "type": "bundleInterface"}, "serviceId": "msoa.taobao.detail.showsku", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "sourceType", "type": "java.lang.String"}, {"name": "exParams", "type": "com.alibaba.fastjson.JSONObject"}], "version": "2.0", "platform": 7}, {"serviceType": 0, "provider": {"needTimeout": false, "func": "showSku3", "implClassName": "com.taobao.android.detail.msoa.DetailMSOAImpl", "action": "", "bundleName": "com.taobao.android.detail", "className": "com.taobao.android.detail.msoa.DetailMSOAInterface", "type": "bundleInterface"}, "serviceId": "msoa.taobao.detail.showsku", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "sourceType", "type": "java.lang.String"}, {"name": "exParams", "type": "com.alibaba.fastjson.JSONObject"}], "version": "3.0", "platform": 7}, {"serviceType": 0, "provider": {"needTimeout": false, "func": "addSMCart", "implClassName": "com.taobao.android.detail.msoa.DetailMSOAImpl", "action": "", "bundleName": "com.taobao.android.detail", "className": "com.taobao.android.detail.msoa.DetailMSOAInterface", "type": "bundleInterface"}, "serviceId": "msoa.taobao.detail.add2smcart", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "itemId", "type": "java.lang.String"}, {"name": "token", "type": "java.lang.String"}, {"name": "bizData", "type": "java.lang.String"}], "version": "1.0", "platform": 7}, {"serviceType": 0, "provider": {"needTimeout": false, "func": "showFloatPage", "implClassName": "com.taobao.android.detail.msoa.DetailMSOAImpl", "action": "", "bundleName": "com.taobao.android.detail", "className": "com.taobao.android.detail.msoa.DetailMSOAInterface", "type": "bundleInterface"}, "serviceId": "msoa.taobao.detail.pageshow", "params": [{"name": "requestId", "type": "java.lang.String"}, {"name": "type", "type": "java.lang.String"}, {"name": "bizData", "type": "java.lang.String"}], "version": "1.0", "platform": 7}]}]