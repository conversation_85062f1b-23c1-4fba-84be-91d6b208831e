#[[item]]
#name = "yourlibname"
## 可省略不写，默认为 "lib${name}.so"
#fileNme = "libyourlibname.so"
#allowPreFetch = true
#allowLastIndex = false
#syncTimeoutSeconds = 5
#[[item.process]]
#abi = ["v8a", "v7a"]
#type = "remote"
#action = "RemoveFromApk"
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]

[[item]]
name = "nnrruntime"
allowPreFetch = false
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"

[[item]]
name = "kernelu4_7z_uc"
allowLastIndex = true
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"
advancedProcess = [
    { type = "diffpatch-uc", target = "remote", versionCount = 6 }]

[[item]]
name = "themis_gfx"
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"
advancedProcess = [
    { type = "remotecompressed" }]

[[item]]
name = "artc_engine"
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"
advancedProcess = [
    { type = "diffpatch-latest", target = "inapk", versionCount = 1 },
    { type = "diffpatch-latest", target = "remote", versionCount = 5 },
    { type = "remotecompressed" }]

#[[item]]
#name = "AliNNPython"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "mnnkitcore"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "MNN_Express"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "MNN_CL"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "MNN"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "MNNOpenCV"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "mnnpybridge"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "mnncv"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "mrt"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "walle_base"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "mnnface"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]

[[item]]
name = "bhx_cxx"
syncTimeoutSeconds = 2
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"
advancedProcess = [
    { type = "diffpatch-latest", target = "inapk", versionCount = 1 },
    { type = "diffpatch-latest", target = "remote", versionCount = 5 },
    { type = "remotecompressed" }]

#[[item]]
#name = "mnnqjs"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "mnn_jsi"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "wbdebug"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "fcanvas_v8_jsi"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "tbuprofen-engine"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "tbuprofen-plugin"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "tbuprofen-agent-v1"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "ALBiometricsJni"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "PailitaoCUtil"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "common_lib_uc"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "tmsonic"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]

[[item]]
name = "avcodec"
[[item.process]]
abi = ["v8a", "v7a"]
type = "remote"
action = "RemoveFromApk"
advancedProcess = [
    { type = "diffpatch-latest", target = "inapk", versionCount = 1 },
    { type = "diffpatch-latest", target = "remote", versionCount = 5 },
    { type = "remotecompressed" }]

#[[item]]
#name = "zcachecore"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "wasm_runtime"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "algo_wrapper"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "s266dec"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "zstd"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "ztcodec2"
#process = [
#    { abi = "v8a", type = "inapk", action = "KeepInApk" },
#    { abi = "v7a", type = "inapk", action = "KeepInApk" }]
#
#[[item]]
#name = "pexwebp"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "dwebp"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "xquic"
#process = [
#    { abi = "v8a", type = "inapk", action = "KeepInApk" },
#    { abi = "v7a", type = "inapk", action = "KeepInApk" }]
#
#[[item]]
#name = "VPM"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "lrc_core"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "latest", target = "inapk", versionCount = 1 },
#    { type = "diffpatch", base = "latest", target = "remote", versionCount = 5 },
#    { type = "remotecompressed" }]
#
#[[item]]
#name = "perfKnife"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "cro_python_kit"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "iifaa_did_security"
#process = [
#    { abi = "v8a", type = "compressed", action = "RemoveFromApk" },
#    { abi = "v7a", type = "compressed", action = "RemoveFromApk" }]
#
#[[item]]
#name = "jniutils"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "rtqpixelstreamingandroid"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "quickjs"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "diffpatch", action = "KeepInApk" },
#    { abi = "v7a", type = "diffpatch", action = "KeepInApk" }]
#advancedProcess = [
#    { type = "diffpatch", base = "abtest", target = "remote", abFileName = "quickjs_abtest" }]
#
#[[item]]
#name = "tbreakpad"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "tunwindstack"
#allowPreFetch = false
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#
#[[item]]
#name = "ucheif_alpha1"
#process = [
#    { abi = "v8a", type = "remote", action = "RemoveFromApk" },
#    { abi = "v7a", type = "remote", action = "RemoveFromApk" }]
#advancedProcess = [
#    { type = "remotecompressed" }]
