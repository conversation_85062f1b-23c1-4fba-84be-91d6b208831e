{"data": {"children": [{"children": [{}, {"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {}, {"children": [{"tag": "text", "text": "var getTag=amc.fn.getById;var rpc=amc.rpcData;var show=amc.fn.show;var hide=amc.fn.hide;var create=amc.fn.create;var spmExposure=amc.fn.spmExposure;var spmClick=amc.fn.spmClick;var gAccountInput=null;var gAccountInputValue=\"\";var NOTIFY_LIST_RETURN=\"SHAREPAY_LIST_RETURN\";var NOTIFY_USER_INFO=\"SHAREPAY_USER_INFO\";var CALLBACK_NAME=\"navtiveNotifyCallback\";var gSectionDisabled4LoadControl=false;var gSection4AlipayMessage=false;var gToastDisabled=false;var gAlipayDlgConfirmAction={};var loaded=false;var gChannelsConfig={code:{spmId:\"a283.b5823.c12843.d23533\",title:\"当面扫码\",icon:\"local:sharepay_channel_paycode\",action:\"loc:shareppaysel\",channel:\"code\",needSubmitParams:true},shareToken:{spmId:\"a283.b5823.c12843.d23531\",title:\"微信好友\",action:\"/shareppay/shareToken\",needSubmitParams:false,icon:\"local:sharepay_channel_sharetoken\"},phone:{spmId:\"a283.b5823.c12843.d23532\",title:\"通讯录好友\",icon:\"local:sharepay_channel_phone\",action:\"loc:shareppaysel\",channel:\"phone\",needSubmitParams:true,failact:{name:\"loc:openurl\",params:{url:\"https://ds.alipay.com/?nojump=true\",endflag:\"2\"}}},friends:{spmId:\"a283.b5823.c12843.d23530\",title:\"支付宝朋友\",needSubmitParams:true,icon:\"local:sharepay_channel_friend\",action:\"loc:shareppaysel\",channel:\"friends\",failact:{name:\"loc:openurl\",params:{url:\"https://ds.alipay.com/?nojump=true\",endflag:\"2\"}}}};function onload(){if(isPrerender(flybird)){return}loadData(amc.rpcData)}document.onreload=function(a){if(isPrerender(a)){return}if(a&&a.local){flybird.local=a.local}if(a&&a.rpcData){loadData(a.rpcData)}};function loadData(a){if(loaded){return}loaded=true;initGlobalVariable(a);amc.fn.spmPageCreate(\"a283.b5823\");init()}function isPrerender(a){return a&&(a.local&&a.local.isPrerender||a.rpcData&&a.rpcData.isPrerender)}function initGlobalVariable(a){rpc=a}function init(){try{initUI();if(amc.fn.logPageInit){amc.fn.logPageInit(true)}}catch(a){if(amc.fn.logPageInit){amc.fn.logPageInit()}}}function initUI(){var c=amc.fn.navBack(true,\"发起代付\",null,null,onBack,null);getTag(\"body\").insertBefore(c,getTag(\"mainBody\"));spmExposure(\"a283.b5823.c12845\");gAccountInput=getTag(\"accountInput\");spmExposure(\"a283.b5823.c12841\");getTag(\"rmbIcon\").src=amc.path+\"alipay_msp_rmb_white\";getTag(\"amount\").innerText=rpc.amount||\"\";getTag(\"desc\").innerText=rpc.desc||\"\";getTag(\"mainBody\").style.height=amc.specs.bodyHeight;getTag(\"btnIcon\").src=amc.path+\"sharepay_code_guide_heart\";if(amc.isAndroid){getTag(\"topPart\").className=\"top-part amc-v-box top-part-android\";getTag(\"sendBtn\").className=\"btn-div amc-align-center amc-justify-center btn-android\"}createChannels();var a=false;if(rpc.switches){var b=amc.fn.isObject(rpc.switches)?rpc.switches:JSON.parse(rpc.switches);a=!!b.extContactDegrade;var d=b.extContactAction;gSection4AlipayMessage=(d==\"/shareppay/sendMsgToUser\")}registerNotification();if(!a&&amc.fn.sdkGreaterThanOrEqual(\"********\")){setTimeout(function(){document.submit({action:{name:\"loc:shareppayproxy\",params:{type:\"getList\",notifyName:NOTIFY_LIST_RETURN}}})},0)}}function createFriends(g){if(!g||!g.list){return}var f=g.list;if(amc.fn.isString(g.list)){f=JSON.parse(g.list)}if(gSection4AlipayMessage){var b=[];var a=f.length;for(var c=0;c<a;c++){var e=f[c];if(e.friendAlipayUserId){b.push(e)}}f=b}var d=f.length;if(d==0){return}else{show(\"friendsTip\");spmExposure(\"a283.b5823.c12842\")}for(var c=0;c<d;c++){var e=f[c];createFriend(e,c)}}function createFriend(e,b){if(!e||!e.friendNickName){return}var f=amc.path+\"alipay_msp_user\";var d=create(\"div\",\"friend-box amc-align-center\",getTag(\"friends\"));var c=create(\"img\",\"friend-icon\",d);c.src=e.friendIconUrl||f;c.failureValue=f;c.defaultValue=f;var a=create(\"label\",\"item-title amc-ellipsis amc-text-center\",d);a.innerText=e.friendNickName||\"\";d.onclick=function(){spmClick(\"a283.b5823.c12842.d23538\");submitWithLoadControl(function(){if(amc.fn.sdkGreaterThanOrEqual(\"********\")){if(gSection4AlipayMessage){var i={action:{name:\"/shareppay/sendMsgToUser\",params:{receiverUserId:e.friendAlipayUserId}}};document.submit(i)}else{var g=e.friendTaobaoId||e.userId||\"\";if(g){var k=(amc.fn.isObject(rpc.params)?JSON.stringify(rpc.params):rpc.params)||\"\";amc.fn.setResult({resultStatus:\"9000\",result:'{\"biz_type\":\"share_pp\"}',memo:\"代付申请成功\",sharepayData:k,payerUserId:g})}}}else{if(amc.fn.sdkGreaterThanOrEqual(\"********\")){amc.fn.setResult({resultStatus:\"9000\",result:'{\"isJumpUrl\":false}',memo:\"代付申请成功\",doNotExit:true})}var j=undefined;if(rpc.params){var h=amc.fn.isObject(rpc.params)?rpc.params:JSON.parse(rpc.params);j=h.payurl}var i={action:{name:\"loc:shareppayproxy\",params:{type:\"sendMsg\",index:b,sharepayAmount:rpc.amount,sharepayUrl:j}}};document.asyncSubmit(i,function(l){l=l||{};if(l.action==\"failAlert\"){showAlert()}})}})}}function createChannels(){if(!rpc.channels||!rpc.channels.length){hide(\"channelsTip\");return}var b=rpc.channels;var a=b.length;for(var d=0;d<a;d++){var c=b[d];createChannel(gChannelsConfig[c])}spmExposure(\"a283.b5823.c12843\")}function createChannel(c){if(!c){return}var b=create(\"div\",\"channel amc-align-center\",getTag(\"channels\"));var a=create(\"img\",\"channel-icon\",b);a.src=getImgPath(c.icon);var d=create(\"label\",\"item-title amc-ellipsis amc-text-center\",b);d.innerText=c.title;spmExposure(c.spmId);b.onclick=function(){spmClick(c.spmId);submitWithLoadControl(function(){var e={action:{name:c.action}};if(c.needSubmitParams&&rpc.params){e.action.params=amc.fn.isObject(rpc.params)?rpc.params:JSON.parse(rpc.params)}if(c.channel){e.action.params=e.action.params||{};e.action.params.channel=c.channel}if(c.succact){e.action.params.succact=c.succact}if(c.failact){e.action.params.failact=c.failact}document.asyncSubmit(e,function(f){f=f||{};if(f.action==\"exitAlert\"){showDlg()}})})}}function registerNotification(){var a={callbacks:{onNotify:CALLBACK_NAME}};getTag(\"notification\").src=JSON.stringify(a)}function navtiveNotifyCallback(a){if(!a){return}var c=null;try{c=(amc.fn.isObject(a)?a:JSON.parse(a))||{}}catch(d){amc.fn.logError(\"SHAREPAY\",\"native回调参数解析错误, \"+d);return}var b=c.notifyName;if(b==NOTIFY_LIST_RETURN){createFriends(c)}else{if(b==NOTIFY_USER_INFO){showAlipayDlg(c)}}}function showAlipayDlg(b){gAlipayDlgConfirmAction=b.confirmAction||{};var a=(b.title||\"\")+\"\\n\"+(b.subtitle||\"\");if(amc.isAndroid){getTag(\"androidSubMsg\").innerText=a;getTag(\"androidAlipayDlg\").showModal()}else{getTag(\"iosSubMsg\").innerText=a;getTag(\"iosAlipayDlg\").showModal()}spmExposure(\"a283.b5823.c12846\")}function onAlipayDlgClose(){if(amc.isAndroid){getTag(\"androidAlipayDlg\").close()}else{getTag(\"iosAlipayDlg\").close()}}function onAlipayDlgCancel(){spmClick(\"a283.b5823.c12846.d23539\");onAlipayDlgClose()}function onAlipayDlgConfirm(){spmClick(\"a283.b5823.c12846.d23540\");onAlipayDlgClose();onClearInput();document.asyncSubmit({action:gAlipayDlgConfirmAction},function(a){if(a.action==\"failAlert\"){showAlert()}})}function showDlg(){if(amc.isAndroid){getTag(\"androidDlg\").showModal()}else{getTag(\"iosDlg\").showModal()}}function showAlert(){if(amc.isAndroid){getTag(\"androidAlert\").showModal()}else{getTag(\"iosAlert\").showModal()}}function submitWithLoadControl(a){if(gSectionDisabled4LoadControl){return}gSectionDisabled4LoadControl=true;setTimeout(function(){gSectionDisabled4LoadControl=false},600);a()}function onClearInput(){gAccountInput.value=\"\";gAccountInputValue=\"\"}function onSend(){spmClick(\"a283.b5823.c12841.d23536\");submitWithLoadControl(function(){var a=gAccountInputValue||\"\";a=a.trim();if(!a.length){toast(\"请输入正确的账号\",null,true);return}if(amc.fn.sdkGreaterThanOrEqual(\"********\")){document.submit({action:{name:\"/shareppay/validateUser\",params:{logonId:a}}})}else{document.asyncSubmit({action:{name:\"/shareppay/sendMsgToUser\",params:{logonId:a}}},function(b){b=b||{};if(b.action==\"clearInput\"){gAccountInput.value=\"\";gAccountInputValue=\"\"}})}})}function toast(c,a,b){if(!c){return}if(amc.isAndroid){if(b&&gToastDisabled){return}else{setTimeout(function(){gToastDisabled=false},2000);gToastDisabled=true}}document.toast({text:c,type:a||\"none\"},function(){})}function onAccountInput(){var a=gAccountInput.value||\"\";a=a.trim();gAccountInputValue=a}function onAccountInputReturn(){if(event.which==\"keycode_confirm\"||event.which==10){if(amc.isIOS){amc.fn.hideKeyboard()}else{getTag(\"rmbIcon\").focus()}}else{if(event.which==4){amc.fn.exitConfirm()}}}function onInputFocus(){if(amc.isAndroid){document.setProp(\"focusableInTouchMode\",{value:true})}}function getImgPath(a){return(a&&a.indexOf(\"local:\")===0)?(amc.path+a.substr(\"local:\".length)):a}function onBack(){spmClick(\"a283.b5823.c12845.d23537\");amc.fn.spmPageDestroy(\"a283.b5823\");amc.fn.exit()}function onKeyDown(){if(event.which==4){amc.fn.exitConfirm()}};"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".log-info {\n        font-size: 14px;\n        color: #000;\n        margin-top: 15px;\n        margin-bottom: 15px;\n        margin-left: 15px;\n        margin-right: 15px;\n        word-break: break-all;\n        flex: 1.0;\n    }\n\n    .channel-label {\n        font-size: 17px;\n    }\n\n    .icon-box {\n        align-items: center;\n        justify-content: center;\n        margin-right: 15px;\n        width: 25px;\n    }\n\n    .channel-icon {\n        width: 32px;\n        max-width: 32px;\n    }\n\n    .pd-lr {\n        padding-left: 15px;\n        padding-right: 15px;\n    }\n\n    .arrow-right {\n        max-height: 12px;\n        margin-left: 5px;\n    }\n\n    .gap-bg {\n        height: 16px;\n        background-color: #f5f5f5;\n    }\n\n    .channel {\n        width: 72px;\n        margin-right: 16px;\n        flex-direction: column;\n    }\n\n    .section-info {\n        font-size: 14px;\n        color: #888;\n        margin-top: 15px;\n        margin-bottom: 15px;\n        margin-left: 15px;\n        margin-right: 15px;\n    }\n\n    .input-box {\n        height: 47px;\n        justify-content: center;\n    }\n\n    .account-input {\n        font-size: 17px;\n        height: 37px;\n        border-radius: 0px;\n        padding: 0px;\n        white-space: nowrap;\n        color: #000;\n    }\n\n    .btn-box {\n        border-radius: 4px;\n        margin-top: 20px;\n        height: 42px;\n    }\n\n    .btn-div {\n        height: 42px;\n        border-radius: 4px;\n    }\n\n    .btn-ios {\n        background-image: url(AlipaySDK.bundle/sharepay_code_guide_bg);\n    }\n\n    .btn-android {\n        background-image: url(com.alipay.android.app/sharepay_code_guide_bg);\n    }\n\n    .btn-icon {\n        width: 19px;\n        height: 19px;\n    }\n\n    .btn-label {\n        font-size: 18px;\n        color: #fff;\n        margin-left: 6px;\n    }\n\n    .top-part {\n        padding: 30px 15px 20px 15px;\n    }\n\n    .top-part-ios {\n        background-image: url(AlipaySDK.bundle/alipay_msp_sharepay_bg);\n    }\n\n    .top-part-android {\n        background-image: url(com.alipay.android.app/alipay_msp_sharepay_bg);\n    }\n\n    .rmb-icon-box {\n        padding-top: 18px;\n        margin-right: 2px;\n    }\n\n    .rmb-icon {\n        width: 15px;\n        height: 20px;\n        max-width: 15px;\n        max-height: 20px;\n    }\n\n    .lines-box {\n        padding: 0 50px;\n        margin-top: 9px;\n        background-color: rgba(0, 0, 0, 0)\n    }\n\n    .gray-line {\n        height: 1PX;\n        background-color: #dedee2;\n        margin-top: 3px;\n        opacity: 0.3;\n    }\n\n    .gray-line-first {\n        margin-top: 0;\n    }\n\n    .amount-box {\n        margin-top: -67px;\n        height: 67px;\n        max-height: 67px;\n    }\n\n    .amount-info-label {\n        font-size: 14px;\n        color: #fff;\n        text-align: center;\n    }\n\n    .amount-label {\n        font-size: 45px;\n        color: #fff;\n    }\n\n    .desc {\n        opacity: 0.8;\n        font-size: 14px;\n        color: #fff;\n    }\n\n    .desc-box {\n        margin-top: 30px;\n    }\n\n    .middle-part {\n        padding-bottom: 15px;\n    }\n\n    .margin-lr {\n        margin-left: 15px;\n        margin-right: 15px;\n    }\n\n    .btn-text {\n        color: #108ee9;\n        margin: 12px 0;\n        font-size: 16px;\n    }\n\n    .ios-cfm-rbtn-text {\n        color: #108ee9;\n        margin: 12px 0;\n        font-size: 18px;\n        font-weight: bold;\n    }\n\n    .ios-cfm-lbtn-text {\n        color: #999;\n        margin: 12px 0;\n        font-size: 18px;\n    }\n\n    .ios-cfm-btn-div {\n        padding: 0 5px;\n        background-color: #fff;\n    }\n\n    .ios-cfm-btn-div:active {\n        background-color: #f6f6f6;\n    }\n\n    .android-cfm-rbtn-text {\n        color: #1284D6;\n        margin: 4px 0;\n        font-size: 16px;\n    }\n\n    .android-cfm-lbtn-text {\n        color: #999;\n        margin: 4px 0;\n        font-size: 16px;\n    }\n\n    .android-cfm-btn-div {\n        padding: 0 16px;\n        border-radius: 2px;\n        background-color: #fff;\n    }\n\n    .android-cfm-btn-div:active {\n        background-color: #f6f6f6;\n    }\n\n    .btn-vertical-line {\n        width: 1px;\n        align-self: stretch;\n        background-color: #e5e5e5;\n    }\n\n    .amc-font-m {\n        font-size: 13px;\n        line-height: 16px;\n    }\n\n    .ios-title {\n        flex: 1.0;\n        margin: 10px 15px 5px 15px;\n        text-align: center;\n        font-weight: bold;\n    }\n\n    .ios-msg {\n        flex: 1.0;\n        margin: 5px 15px 21px 15px;\n        text-align: center;\n    }\n\n    .ios-btn-div {\n        padding: 0 5px;\n        background-color: #fff;\n    }\n\n    .ios-btn-div:active {\n        background-color: #ddd;\n    }\n\n    .ios-dlg-box {\n        border: 0;\n        background-color: #fff;\n        border-radius: 2px;\n        width: 270px;\n        padding-top: 12px;\n    }\n\n    .android-title {\n        flex: 1.0;\n        text-align: left;\n        margin: 10px 10px 5px 10px;\n        font-weight: bold;\n    }\n\n    .android-msg {\n        flex: 1.0;\n        text-align: left;\n        margin: 5px 10px 15px 10px;\n    }\n\n    .android-btn-div-vert {\n        padding: 0 10px;\n        background-color: #fff;\n        justify-content: flex-end;\n    }\n\n    .android-btn-div-vert:active {\n        background-color: #ddd;\n    }\n\n    .android-btn-div {\n        padding: 0 10px;\n        border-radius: 2px;\n        background-color: #fff;\n    }\n\n    .android-btn-div:active {\n        background-color: #ddd;\n    }\n\n    .android-btn-text {\n        max-width: 110px;\n    }\n\n    .android-dlg-box {\n        padding: 10px 10px;\n        border: 0;\n        background-color: #fff;\n        border-radius: 3px;\n    }\n\n    .android-btn-box {\n        align-items: center;\n        justify-content: flex-end;\n    }\n\n    .title {\n        font-size: 20px;\n        margin: 5px 0;\n    }\n\n    .title-box {\n        padding-top: 5px;\n    }\n\n    .dlg-title-font {\n        font-size: 18px;\n    }\n\n    .dlg-msg-font {\n        font-size: 14px;\n    }\n\n    .dlg-content-width {\n        width: 274px;\n    }\n\n    .items-box {\n        padding-top: 15px;\n        padding-bottom: 15px;\n        overflow-x: scroll;\n    }\n\n    .friend-box {\n        width: 72px;\n        margin-right: 12px;\n        flex-direction: column;\n    }\n\n    .friend-icon {\n        height: 50px;\n        width: 50px;\n        max-width: 50px;\n        max-width: 50px;\n        border-radius: 2px;\n        border: 0.5px #ddd solid;\n    }\n\n    .item-title {\n        font-size: 14px;\n        color: #333;\n        margin-top: 8px;\n    }\n\n    .main-body {\n        background-color: #f5f5f5;\n    }"}], "tag": "style"}], "tag": "head"}, {"css": "amc-body", "children": [{}, {"css": "amc-v-box amc-flex-1 amc-scroll main-body", "children": [{}, {"css": "top-part amc-v-box top-part-ios", "children": [{}, {"css": "amc-justify-center amc-align-center", "children": [{"css": "amc-flex-1 amc-ellipsis amount-info-label", "children": [{"tag": "text", "text": "代付金额"}], "tag": "label"}], "tag": "div"}, {}, {"css": "amc-v-box", "children": [{"css": "lines-box amc-v-box", "children": [{"css": "gray-line gray-line-first", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}, {"css": "gray-line", "tag": "div"}], "tag": "div"}, {"css": "amc-justify-center amount-box", "children": [{"css": "amc-v-box rmb-icon-box", "children": [{"css": "rmb-icon", "tag": "img", "id": "rmbIcon"}], "tag": "div"}, {"css": "amc-align-center", "children": [{"css": "amount-label", "tag": "label", "id": "amount"}], "tag": "div"}], "tag": "div"}], "tag": "div"}, {}, {"css": "pd-lr desc-box", "children": [{"css": "amc-text-center desc amc-flex-1 amc-ellipsis", "tag": "label", "id": "desc"}], "tag": "div"}], "tag": "div", "id": "topPart"}, {}, {"css": "amc-v-box middle-part amc-bg-white", "children": [{"css": "pd-lr amc-v-box input-box", "children": [{"css": "account-input", "onfocus": "onInputFocus();", "maxlength": "64", "tag": "input", "id": "accountInput", "placeholder": "请输入对方的支付宝账号或手机号码", "onkeydown": "onAccountInputReturn();", "value": "", "oninput": "onAccountInput();"}], "tag": "div"}, {"css": "amc-1px-line", "tag": "div"}, {"css": "btn-box amc-v-box margin-lr", "children": [{"css": "btn-div amc-align-center amc-justify-center btn-ios", "children": [{"css": "btn-icon", "tag": "img", "id": "btnIcon"}, {"css": "btn-label", "children": [{"tag": "text", "text": "发送"}], "tag": "label"}], "tag": "div", "id": "sendBtn"}], "onclick": "onSend();", "tag": "div"}], "tag": "div", "id": "middle<PERSON><PERSON>"}, {"css": "amc-1px-line", "tag": "div"}, {}, {"css": "amc-bg-white amc-v-box amc-hidden", "children": [{"css": "amc-v-box", "children": [{"css": "gap-bg", "tag": "div"}, {"css": "amc-1px-line", "tag": "div"}, {"css": "section-info", "children": [{"tag": "text", "text": "推荐好友"}], "tag": "label"}, {"css": "amc-1px-line margin-lr", "tag": "div"}], "tag": "div"}, {}, {"css": "pd-lr items-box", "tag": "div", "id": "friends"}, {"css": "amc-1px-line", "tag": "div"}], "tag": "div", "id": "friendsTip"}, {}, {"css": "amc-bg-white amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "gap-bg", "tag": "div"}, {"css": "amc-1px-line", "tag": "div"}], "tag": "div"}, {"css": "amc-v-box", "children": [{"css": "amc-ellipsis section-info", "children": [{"tag": "text", "text": "你还可以通过以下方式找朋友代付"}], "tag": "label"}, {"css": "amc-1px-line margin-lr", "tag": "div"}], "tag": "div"}, {}, {"css": "pd-lr items-box", "tag": "div", "id": "channels"}, {"css": "amc-1px-line", "tag": "div"}], "tag": "div", "id": "channelsTip"}], "tag": "div", "id": "mainBody"}, {}, {"css": "amc-dlg-box android-dlg-box amc-v-box", "children": [{"css": "dlg-content-width amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "android-title dlg-title-font", "children": [{"tag": "text", "text": "确认发送给"}], "tag": "label"}, {"css": "android-msg dlg-msg-font", "children": [{"tag": "text", "text": "待朋友完成付款后，即可完成交易"}], "tag": "label", "id": "androidSubMsg"}], "tag": "div"}, {"css": "amc-justify-end", "children": [{"css": "android-btn-box", "children": [{"css": "android-cfm-btn-div", "children": [{"css": "android-btn-text android-cfm-lbtn-text amc-flex-1 amc-text-right amc-ellipsis", "children": [{"tag": "text", "text": "取消"}], "tag": "label"}], "onclick": "onAlipayDlgCancel();", "tag": "div"}, {"css": "android-cfm-btn-div", "children": [{"css": "android-btn-text android-cfm-rbtn-text amc-flex-1 amc-text-right amc-ellipsis", "children": [{"tag": "text", "text": "确定"}], "tag": "label"}], "onclick": "onAlipayDlgConfirm();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "androidAlipayDlg", "onkeydown": "onKeyDown()"}, {}, {"css": "ios-dlg-box amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "ios-title dlg-title-font", "children": [{"tag": "text", "text": "确认发送给"}], "tag": "label"}, {"css": "ios-msg dlg-msg-font", "children": [{"tag": "text", "text": "待朋友完成付款后，即可完成交易"}], "tag": "label", "id": "iosSubMsg"}], "tag": "div"}, {"css": "amc-v-box amc-justify-center", "children": [{"css": "amc-adapt-line", "tag": "div"}, {"css": "amc-align-center", "children": [{"css": "amc-flex-1 ios-cfm-btn-div", "children": [{"css": "ios-cfm-lbtn-text amc-flex-1 amc-text-center amc-ellipsis", "children": [{"tag": "text", "text": "取消"}], "tag": "label"}], "onclick": "onAlipayDlgCancel();", "tag": "div"}, {"css": "btn-vertical-line", "tag": "div"}, {"css": "amc-flex-1 ios-cfm-btn-div", "children": [{"css": "ios-cfm-rbtn-text amc-flex-1 amc-text-center amc-ellipsis", "children": [{"tag": "text", "text": "确定"}], "tag": "label"}], "onclick": "onAlipayDlgConfirm();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "iosAlipayDlg", "onkeydown": "onKeyDown()"}, {}, {"css": "amc-dlg-box android-dlg-box amc-v-box", "children": [{"css": "dlg-content-width amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "android-title dlg-title-font", "children": [{"tag": "text", "text": "发送结果"}], "tag": "label"}, {"css": "android-msg dlg-msg-font", "children": [{"tag": "text", "text": "待朋友完成付款后，即可完成交易"}], "tag": "label"}], "tag": "div"}, {"css": "amc-justify-end", "children": [{"css": "android-btn-box", "children": [{"css": "android-btn-div", "children": [{"css": "android-btn-text  btn-text amc-flex-1 amc-text-right amc-ellipsis", "children": [{"tag": "text", "text": "继续发送"}], "tag": "label"}], "onclick": "getTag('androidDlg').close();onClearInput();", "tag": "div"}, {"css": "android-btn-div", "children": [{"css": "android-btn-text  btn-text amc-flex-1 amc-text-right amc-ellipsis", "children": [{"tag": "text", "text": "完成发送"}], "tag": "label"}], "onclick": "amc.fn.exit();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "androidDlg", "onkeydown": "onKeyDown()"}, {}, {"css": "ios-dlg-box amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "ios-title dlg-title-font", "children": [{"tag": "text", "text": "发送结果"}], "tag": "label"}, {"css": "ios-msg dlg-msg-font", "children": [{"tag": "text", "text": "待朋友完成付款后，即可完成交易"}], "tag": "label"}], "tag": "div"}, {"css": "amc-v-box amc-justify-center", "children": [{"css": "amc-adapt-line", "tag": "div"}, {"css": "amc-align-center", "children": [{"css": "amc-flex-1 ios-btn-div", "children": [{"css": "btn-text amc-flex-1 amc-text-center amc-ellipsis", "children": [{"tag": "text", "text": "继续发送"}], "tag": "label"}], "onclick": "getTag('iosDlg').close();onClearInput();", "tag": "div"}, {"css": "btn-vertical-line", "tag": "div"}, {"css": "amc-flex-1 ios-btn-div", "children": [{"css": "btn-text amc-flex-1 amc-text-center amc-ellipsis", "children": [{"tag": "text", "text": "完成发送"}], "tag": "label"}], "onclick": "amc.fn.exit();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "iosDlg", "onkeydown": "onKeyDown()"}, {}, {"css": "amc-dlg-box android-dlg-box amc-v-box", "children": [{"css": "dlg-content-width amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "android-msg dlg-msg-font", "children": [{"tag": "text", "text": "发送失败"}], "tag": "label", "id": "subMsg"}], "tag": "div"}, {"css": "amc-justify-end", "children": [{"css": "android-btn-box", "children": [{"css": "android-btn-div", "children": [{"css": "android-btn-text  btn-text amc-flex-1 amc-text-right amc-ellipsis", "children": [{"tag": "text", "text": "确定"}], "tag": "label"}], "onclick": "getTag('androidAlert').close();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "android<PERSON><PERSON><PERSON>", "onkeydown": "onKeyDown()"}, {}, {"css": "ios-dlg-box amc-v-box", "children": [{"css": "amc-v-box", "children": [{"css": "ios-msg dlg-msg-font", "children": [{"tag": "text", "text": "发送失败"}], "tag": "label", "id": "subMsg"}], "tag": "div"}, {"css": "amc-v-box amc-justify-center", "children": [{"css": "amc-adapt-line", "tag": "div"}, {"css": "amc-align-center", "children": [{"css": "amc-flex-1 ios-btn-div", "children": [{"css": "btn-text amc-flex-1 amc-text-center amc-ellipsis", "children": [{"tag": "text", "text": "确定"}], "tag": "label"}], "onclick": "getTag('iosAlert').close();", "tag": "div"}], "tag": "div"}], "tag": "div"}], "tag": "dialog", "id": "ios<PERSON>lert", "onkeydown": "onKeyDown()"}, {"css": "amc-hidden", "tag": "embed", "id": "notification", "type": "MQPBNNotification"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown();", "onload": "onload();"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-peerpay-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0006", "tplId": "QUICKPAY@cashier-peerpay-flex", "tplVersion": "5.3.0"}