{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var getTag = amc.fn.getById;\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var rpc = amc.rpcData;\n\n        var questionList = [];\n\n        //### 初始化\n        function init() {\n            var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', '安全提醒', null, null, onBack, null);\n            getTag('bodyContainer').insertBefore(nav, getTag('mainBody'));\n\n            var remindInfo = {};\n            try {\n                remindInfo = JSON.parse(rpc['remindInfo']);\n            } catch (error) {\n                print(\"fail to get remindInfo: \" + error.message);\n            }\n            remindInfo = remindInfo || {};\n\n            getTag('warningImg').src = mic.path + 'alipay_vi_warning';\n            if (remindInfo['icon']) {\n                getTag('warningImg').src = remindInfo['icon'];\n            }\n\n            getTag('warningLable').innerText = remindInfo['header'] || '';\n\n            // 创建列表\n            questionList = remindInfo['questionList'];\n            for (var i = 0; i < questionList.length; i++) {\n                var obj = questionList[i];\n                createRiskTable(obj, i);\n            }\n        }\n\n        //### 后退\n        function onBack() {\n            mic.fn.onBack();\n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                onBack();\n            }\n        }\n\n        function createRiskTable(riskInfo, index) {\n            var riskTable = document.getElementById('riskTable');\n            var tableItem = document.createElement('div');\n            tableItem.className = 'vi-cell';\n            tableItem.value = index;\n\n            var cellContentDiv = document.createElement('div');\n            cellContentDiv.className = 'cellContent amc-pd-lr';\n\n            var titleDiv = document.createElement('div');\n            titleDiv.className = 'title-div';\n            var titleHead = document.createElement('label');\n            titleHead.className = 'title-text';\n            titleHead.innerText = riskInfo['quest'] || 'xxxx';\n            titleDiv.appendChild(titleHead);\n\n            var selectImgDiv = document.createElement('div');\n            selectImgDiv.className = 'selected-div';\n            selectImgDiv.style.visibility = \"hidden\";\n            var selectedImg = document.createElement('img');\n            selectedImg.src = mic.path + 'selected_style';\n            selectedImg.style.width = '16px';\n            selectedImg.style.height = '16px';\n            \n            selectImgDiv.appendChild(selectedImg);\n\n            cellContentDiv.appendChild(titleDiv);\n            cellContentDiv.appendChild(selectImgDiv);\n\n            cellContentDiv.onclick = function() {\n                var subnode = cellContentDiv.querySelector('.selected-div')\n                subnode.style.visibility = \"visible\";\n                onCellClick(index)\n            };\n\n            tableItem.appendChild(cellContentDiv);\n\n            \n\n            var line = document.createElement('div');\n            line.className = 'amc-1px-line';\n            riskTable.appendChild(line);\n            riskTable.appendChild(tableItem);\n        }\n\n        function onCellClick(index) {\n\n\n            var questionClickItem = questionList[index]['next'];\n            document.confirm({\n                \"message\": questionClickItem['text'] || '',\n                \"okButton\": questionClickItem['confirm'] || '',\n                \"cancelButton\": questionClickItem['cancel'] || '',\n            }, function(result) {\n                var question = questionList[index];\n                doVerify(question, result);\n            });\n        }\n\n        function doVerify(question, result) {\n            obj = {};\n            obj['showLoading'] = 'true';\n            obj['eventName'] = 'vi_rpc_validate';\n            obj['params'] = {\n                'answer': question['questOrder'] || '',\n                'operation': result.ok ? 'Y' : 'N'\n            };\n            obj['moduleName'] = 'REMIND';\n            obj['actionName'] = 'VERIFY_REMIND';\n\n            document.asyncSubmit(obj, function(data) {\n                var isFinishWithSuccess = Boolean(data['finish']) && data['finishCode'] === '1000';\n                var isNotFinishWithSuccess = !Boolean(data['finish']) && Boolean(data['verifySuccess']);\n                var isCommonMode = Boolean(rpc['VISwitchConfig']['remindNext']);\n                if (isCommonMode && (isFinishWithSuccess || isNotFinishWithSuccess)) {                    \n                    mic.fn.onBackWithResponse(data);    \n                } else {\n                    var finalResult = {};\n                    if (result.ok) {\n                        finalResult['finish'] = true;\n                        finalResult['success'] = true;\n                        finalResult['verifySuccess'] = true;\n                        finalResult['finishCode'] = '1000';\n                        mic.fn.onBackWithResponse(finalResult);\n                    } else {\n                        finalResult['eventName'] = 'vi_quit_module';\n                        document.submit(finalResult);\n                    }\n                }\n            });\n        }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".warningDiv {\n            flex-direction: column;\n            align-items: center;\n            margin-top: 38px;\n            margin-bottom: 44px;\n        }\n\n        .warningImg {\n            width: 90px;\n            height: 90px;\n            margin-bottom: 29px;\n            align-items: center;\n        }\n\n        .riskLabel {\n            text-align: center;\n            font-size: 18px;\n        }\n\n        .title-div {\n            flex: 1;\n        }\n\n        .selected-div {\n            width:16px;\n            height:16px;\n        }\n\n        .title-text {\n            flex: 1.0;\n            font-size: 17px;\n            color: #000;\n        }\n\n        .cellContent {\n            flex-direction:row;\n            align-items: center;\n            width: 100%;\n        }\n\n        .vi-cell {\n            align-items: center;\n            background-color: #fff;\n            min-height: 43px;\n        }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "amc-main amc-scroll-flex", "children": [{"css": "amc-align-center amc-pd-lr warningDiv", "children": [{"css": "warningImg", "tag": "img", "id": "warningImg"}, {"css": "riskLabel", "children": [{"tag": "text", "text": "为了保障你的资金安全，请根据实际情况选择交易原因..."}], "tag": "label", "id": "warningLable"}], "tag": "div"}, {"css": "amc-v-box", "tag": "div", "id": "riskTable"}, {"css": "amc-1px-line", "tag": "div"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown();", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "remindAsk", "format": "JSON", "tag": "MOBILEIC", "time": "0016", "tplId": "MOBILEIC@remindAsk", "tplVersion": "5.3.5"}