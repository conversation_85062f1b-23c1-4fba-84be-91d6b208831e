{"default": {"sms_alert_message": "1，请确认当前手机号是否还在使用，若不再使用，请至【我的】-【右上角设置】-【手机号】-【更换手机号】。\n2，请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。\n3，由于运营商网络原因，可能存在短信延迟，请耐心等待或稍候再试。\n4，若您最近操作过携号转网，请等待1-2天后再试。", "got_it": "我知道了", "changeOtherMethod": "选择其他验证方式", "alipay": "支付宝", "deduct_dlg_order": "扣款顺序：请查看“支付宝-我的-设置-支付设置-扣款顺序”", "code": "校验码", "protocol": "协议", "help": "帮助", "confirm_open": "确定开通", "pay_ok": "付款成功", "user_name_title": "持卡人姓名", "sms_help_title": "收不到校验码", "last_name": "姓", "open_pcredit_service": "正在开通蚂蚁花呗服务", "deduct_dlg_title": "信用免密支付说明", "auth_login": "授权登陆", "pcredit_user_protocol": "蚂蚁花呗用户服务合同", "card_info_tip": "请填写银行卡信息", "opening_text": "正在开通...", "please_wait": "请稍等", "dotspot": "、", "phone_info": "银行预留的手机号码是办理该银行卡时所填写的手机号码。没有预留、手机号忘记或者已停用，请联系银行客服更新处理。大陆手机号为11位数字，非大陆手机号为“国家代码-手机号码”形式。", "repeat_confirm": "请再次填写以确认", "activity_share": "分享", "pcredit_serve_protocol": "《花呗服务协议》", "and_so_on": "等", "phone_validate": "验证手机号", "retry_after_full": "#val#秒后重试", "alipay_serve_protocol": "支付宝服务协议", "write_card_info": "填写银行卡信息", "phone_sms_full": "本次操作需要短信确认，请输入 <font color='#000'>#val#</font> 收到的短信校验码", "skip": "跳过", "deduct_dlg_inform": "扣款成功后，支付宝会以客户端消息或短信方式通知你", "mail": "邮编", "select_account": "选择账户", "save": "保存", "tb": "淘宝", "card_no": "卡号", "use_alipay_account": "使用支付宝账户", "paying": "正在付款...", "id_validate": "身份验证", "return": "返回", "pay_spwd": "本次支付成功\n设置支付密码，用于支付宝付款\n建议勿与银行卡密码相同", "use_new_card": "使用新卡付款", "order_info": "订单信息", "auth": "授权", "open_pcredit_ad": "开通花呗，抢5元红包", "name_tip_2": "若更改过姓名，可联系银行处理。", "personal_info": "访问个人信息", "email_f_error": "电子邮箱格式不正确", "store_card": "储蓄卡", "full_name_format_error": "姓名格式错误，需在名和姓之间添加空格", "state_province": "州/省", "alipay_loginpwd": "支付宝登录密码", "xx_currency_unit": "#val#元", "name": "姓名", "add_card": "添加银行卡", "no_pwd_full": "支付金额少于#val#，无需支付密码。", "sel_dis": "更换优惠", "forexprod_notice": "支付宝需要对此笔交易的用户进行实名验证，请填写你的真实姓名及身份证号码。", "name_tip_3": "获取更多帮助，请致电支付宝客服<font color='#1677ff'>95188</font>。", "read_agree": "我已阅读并同意", "card_type": "卡类型", "cashier_no_tip_5": "了解详情，请致电支付宝客服<font color='#1677ff'>95188</font>。", "phone_no": "手机号", "set_pwd": "设置支付密码", "write_phone_no": "填写手机号码", "cashier_no_tip_1": "安全保障：账号保护、实时监控、紧急冻结等多重金融安全保障。", "cashier_no_tip_2": "双重验证：每次付款均验证支付密码，大额支付还需短信验证。", "cashier_no_tip_3": "隐私保护：高强度数据加密，保护用户隐私信息。", "auth_sth": "使用支付宝账户#val#", "country_area": "国家/地区", "money_limit": "充值金额超限", "cashier_no_dlg_title": "安全保障", "spwd_for_pay": "该6位数字密码将用于支付", "payment_text": "资金安全由众安保险承担，极速全额赔付", "sms_tip_1": "请确认该预留手机号是否为当前使用手机号。", "confirm_agree": "确认即表示同意", "sel_state_province": "选择 州（省）", "card_three_num": "卡背面末三位数字", "input_id_no": "请输入身份证号码", "sms_tip_3": "由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。", "use_alipay_auth": "使用支付宝账户授权", "input_valid_cvv2": "请输入正确的安全码", "phone_tip_4": "获取更多帮助，请致电支付宝客服<font color='#1677ff'>95188</font>。", "address_info": "访问收货信息", "auth_fingering": "正在验证指纹...", "safepwd_verify_complete": "校验成功", "pay_tool": "付款方式", "choose_dis": "选择优惠", "auth_finger_or": "请验证指纹或者", "account_info_tip": "包含您的支付宝账户名等信息，但不包含密码信息。", "sel": "更换", "validity_info": "有效期是打印在信用卡正面卡号下方，标准格式为月份在前，年份在后的一串数字。", "last_three_num": "卡背面后三位数字", "charge_fail": "无法完成付款，此银行卡余额不足。", "confirm_auth": "确定授权", "first_name": "名", "cvv_text": "安全码", "input_pay_pwd": "输入支付密码", "system_error": "系统出错", "agree_protocol": "同意协议", "other_login_way": "其他登录方式", "wait_retry": "等待重试", "new_card_deposit": "使用新卡充值", "limit_info_local": "余额的支付限额由账户认证等级、收款卡是否为同名卡等因素共同确定，转账信息提交后会确定是否超限。\n\n银行卡的支付限额是由银行设定，购物支付、转账、还款缴费等场景限额共享，<font color=\"#ff8208\">无法提升</font>。若提示超限，建议更换其他付款方式。", "select_account_full": "手机号#val#已关联以下支付宝账户，请选择", "exit": "退出", "finger": "指纹", "bill_address": "账单地址", "deposit_info": "充值提示信息", "paypwd": "支付密码", "confirm_cancel_pay": "确定要放弃付款？", "email": "邮箱", "use_phone_login": "使用手机号登录", "bank_no": "银行卡号", "forget_pwd_text": "忘记密码", "confirm_again": "请再次填写以确认", "input_name_on_card": "卡片上的姓名", "card_max_deposit_full": "该卡本次最多可充值#val#元", "input_answer": "输入答案", "sms_tip_4": "若银行预留手机号已停用，请联系银行客服处理。", "open_pcredit": "开通蚂蚁花呗", "input_email": "请输入邮箱", "paypwd_nologin": "是支付密码，不是登录密码", "confirm_upgrade": "确定升级", "confirm_fqg": "确认分期", "email_format_error": "邮箱格式错误，请重新输入", "phone_tip_1": "银行预留手机号是你在办理该银行卡时所填写的手机号。", "insuranced": "付款金额小于200元，无需输入支付密码。", "confirm_exit": "确定要退出吗?", "pwd-notice": "密码不能为连续、重复数字或者你的生日", "open_pcredit_agree": "开通花呗即同意", "input_pwd_s": "输入密码", "address": "地址", "line_alipay_select": "已关联以下支付宝账户，请选择", "bank_info_tip": "请填写银行预留信息", "some_money": "一定数额", "alipay_finger": "支付宝指纹支付", "alipay_auth": "支付宝授权", "cashier_no_dlg_btn": "知道了", "tb_finger": "淘宝指纹支付", "sel_country_area": "选择国家和地区", "promotion_sorry": "耽误您的时间，我们深表歉意", "input_alipay_account": "请输入支付宝账户名", "input_bank_phone": "请输入银行预留手机号", "country": "国家", "pwd-error-repeat": "两次密码输入不一致，请重新输入", "account_deposit": "账户充值", "pay_agree": "付款即同意", "validity_text": "有效期", "pay_success": "支付成功", "pcredit_ad": "本订单支持花呗付款，先消费，后还款，0利息。", "month_year": "月份/年份", "login": "登录", "use_other_account": "使用其他账户", "login_alipay": "登录支付宝账号", "info": "提示", "no_method": "无可用付款方式", "pay_right_now": "立即付款", "id_info_tip": "请输入证件号", "open_pcredit_protocol": "《蚂蚁花呗用户服务合同》、《芝麻信用服务协议及授权》", "forget_pwd": "忘记密码?", "alipay_agree": "《支付宝账户互通协议》", "alipay_with_sth": "支付宝#val#", "webbank": "网商银行", "cancel": "取消", "base_info_tip": "包含您的姓名、性别等信息", "input_deposit_money": "请输入充值金额", "sel_id_no": "选择证件号", "discount_detail": "优惠明细", "city": "城市", "pcredit_pay_m": "，下月", "id_card_tail": "证件的最后6位", "id_card": "身份证", "switch_account": "切换账户", "next": "下一步", "auth_bracelet": "验证手环...", "pcredit_protocol": "芝麻信用服务协议", "phone_text": "电话", "sel_pay_tool": "选择付款方式", "input_valid": "输入有效期", "info_sec": "信息加密处理，仅用于银行验证", "finger_pay_notice": "你同时开通以下指纹支付功能：", "bind_webbank_account": "请关联与网商银行身份信息一致的账户", "open_pcredit_pay": "开通并使用花呗付款", "pay_n_loginpwd": "支付宝支付密码，不是登录密码", "spwd_pay": "设置支付密码，用于支付宝付款\n建议勿与银行卡密码相同", "complete": "完成", "pwd_add_card": "请输入支付密码，以添加新的银行卡", "please_input": "请输入", "finger_pay_text": "开通指纹支付，让付款更安全便捷", "validity": "有效期说明", "spot": "。", "promotion_crowd": "顾客太多，客官请稍候", "phone": "手机号说明", "pwd": "密码", "input_name": "请输入持卡人姓名", "resend_sms": "重发验证码", "alipay_account": "支付宝账号", "alipaypwd": "支付宝支付密码", "use_alipay_account_full": "使用支付宝账户#val#授权", "confirm_pay": "确认付款", "phone_tip_2": "没有预留、手机号码忘记或者已停用，可联系银行客服更新处理。", "account": "账户", "auth_qrcode_info": "付款码、交易信息", "input_pwd": "请输入支付密码", "look": "查看", "cvv2_format_error": "安全码格式错误，请重新输入", "account_bind": "账户绑定", "cvv": "安全码说明", "finger_process": "指纹支付处理中", "id_no": "证件号", "cvv_info": "安全码是打印在信用卡背面签名区域的一组数字，一般是后3位或者4位数字。", "wait_text": "稍等一下，马上轮到你", "phone_sms": "请输入短信校验码", "retry_finger": "指纹校验失败，请重试", "input_right_phone": "请输入正确的手机号码，大陆手机号为11位数字，非大陆手机号为“国家代码-手机号码”形式。", "finger_protocol": "《指纹支付相关协议》", "user_agreement": "用户协议", "safe_question": "回答安全保护问题，进行身份验证。", "agree": "同意", "please_input_full": "请输入#val#号码", "base_info": "访问基本信息", "authing": "正在授权...", "personal_info_tip": "包含手机号码、证件号码等信息。", "money": "金额", "ad": "广告", "app_auth": "该应用将获得以下权限", "bank_name": "银行名字", "input_right_id_card": "你的证件类型或证件号有误", "pwdupgrade_text": "升级6位支付密码，付款更便捷", "address_info_tip": "包含您的收货人姓名、地址、邮编等信息。", "limit_info": "限额说明", "money_unit": "元", "pcredit_text_full": "先消费，下月#val#用支付宝还款，0费用", "activity_activity": "活动", "safe_bank_no": "填写以下银行卡卡号，进行身份验证。", "can_insuranced": "免费获赠账户安全险，最高保额100万！付款金额小于200元，无需输入支付密码。", "sms_tip_2": "请查看短信是否被安全软件拦截。", "waika_cvv": "安全码  CVV", "confirm_btn": "确定", "confirming_upgrade": "正在确定...", "paypwd_bind": "请输入支付密码，以完成账户绑定", "input_expire_ccr": "请输入付款信用卡的有效期", "sms_tip_5": "获取更多帮助，请致电支付宝客服<font color='#1677ff'>95188</font>。", "no": "号码", "pay": "付款", "upgrading": "正在升级…", "new_card_placeholder": "无需网银/免手续费", "phone_tip_3": "若你是海外用户，请按照区号-手机号码的形式输入。", "papers_type": "证件类型", "payment_info": "付款信息", "setting": "设置", "auth_bic": "验证智能设备...", "serve_protocol": "服务协议", "serve_protocol_full": "《服务协议》", "credit_quota_full": "信用额度#val#元", "pwdupgrade": "升级支付密码", "account_info": "访问账户信息", "retry_again": "再试一次", "open_npwd": "开通小额免密", "resend_after_full": "#val#秒后重发", "use_pcredit_agree": "使用花呗即同意", "id_card_only": "提醒：该银行仅支持大陆身份证验证", "need_pay": "需付款", "open_finger_pay": "开通指纹支付", "baoxian_protocol": "保险条款", "sms_help": "收不到校验码？", "pay_detail": "确认付款", "other_charge_way": "其他方式付款", "credit_card": "信用卡", "waika_validity": "有效期  MM/YY", "sms_code": "填写校验码", "activity_bonus_points": "积分奖励", "pcredit_pay": "正在用蚂蚁花呗付款", "tail_no": "尾号#val#", "confirm_sth": "确定#val#", "safepwd_verifying": "正在校验...", "alipay_login_id": "支付宝账号#val#", "open_account_fingerpay": "开通以下支付宝账户的指纹支付", "bind_alipay_account": "关联以下支付宝账户", "i_know_it": "知道了", "email_address": "电子邮箱地址", "alipay_sec": "支付宝智能加密，保障你的用卡安全，<font color='#1677ff'>了解安全保障</font>", "open_pcredit_and_pay": "开通花呗并付款", "name_tip_1": "持卡人姓名是你在办理该银行卡时所填写的姓名。", "cashier_no_tip_4": "支付理赔：支付安全由蚂蚁保险承保。", "access_account_info": "#val#请求访问以下支付宝信息：", "phone_notice": "电话号码是6 - 20个字符", "invalid_word": "输入字符不符合规则。", "credit_amount_full": "本订单支持蚂蚁花呗付款，先消费，下月#dueDay#还款，0利息。因您信用良好，获得花呗透支额度#creditAmount#元。", "deposit": "充值成功", "id_card_tip": "仅第18位数值可以输入字母X", "activity_comment": "评价", "safe_id_card": "填写你实名认证时的证件信息，进行身份验证。", "pwd_id_validate": "请输入支付密码，完成身份验证", "phone_card_deposit": "使用话费卡充值", "finger_pay": "指纹支付", "dot": "，", "activity_score_full": "获得#acquire#积分，现有#total#分", "safe_remind": "安全提醒", "input_email_code": "输入邮件验证码", "email_code_has_sent": "已发送邮件验证码到你的邮箱", "email_resend_second": "秒后重发", "email_resend": "重发验证码", "change_other_way": "换个验证方式", "email_back_msg": "邮件验证码即将到达，建议耐心等待。若长时间未到达，请查看是否在垃圾邮件中", "give_up": "放弃", "input_bank_info": "请输入银行卡信息", "card_no_placeholder": "请输入完整卡号", "id_no_placeholder": "持卡人的证件号码", "mobile_no": "手机号", "mobile_no_placeholder": "银行预留手机号", "input_bank_tip": "以上内容仅用于验证身份，支付宝将严格保密", "verify_bank_info": "通过你本人的银行卡信息验证身份", "card_binded_already": "已绑定银行卡", "bind_new_card": "也可以绑定新卡", "bind_new_card_content": "添加你本人的银行卡", "bind_card_sms_code": "验证码", "bind_card_sms_code_placeholder": "请输入短信验证码", "bind_card_sms_resend": "重新发送", "bind_card_sms_tip": "短信验证码已发送至银行预留手机号，请查收", "sms_back_msg": "短信可能有延迟，请再等一会儿", "sms_back_wait": "等待", "sms_back_quit": "放弃", "sms_alert_bank_message": "1，请确认该银行预留手机号是否为当前使用手机号。\n2，请查看短信是否被安全软件拦截，若是双卡双待手机，请检查副卡短信情况。\n3，由于运营商网络原因，可能存在短信延迟，请耐心等待或稍后再试。\n4，若银行预留手机号已停用，请联系银行客服处理。\n5，若您最近操作过携号转网，请等待1-2天后再试。"}}