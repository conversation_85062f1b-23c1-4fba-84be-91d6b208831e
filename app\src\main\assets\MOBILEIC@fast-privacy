{"time": "0002", "tplId": "MOBILEIC@fast-privacy", "publishVersion": "150603", "tag": "MOBILEIC", "name": "fast-privacy", "data": {"tag": "html", "children": [{"tag": "head", "children": [{"tag": "meta", "src": "VIVerifyCore.bundle/mic.i18n", "type": "i18n"}, {"tag": "link", "rel": "stylesheet", "href": "VIVerifyCore.bundle/mic.css"}, {"tag": "script", "src": "VIVerifyCore.bundle/vi-amc.js"}, {"tag": "script", "src": "VIVerifyCore.bundle/mic.js"}, {"tag": "meta", "src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "type": "i18n"}, {"tag": "link", "rel": "stylesheet", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"tag": "script", "src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js"}, {"tag": "script", "src": "android-phone-securitycommon-verifyidentitybiz/mic.js"}, {"tag": "script", "children": [{"text": "//别名\n        var hide = amc.fn.hide;\n        var show = amc.fn.show;\n        var getTag = amc.fn.getById;\n        var rpc = amc.rpcData;\n\n        //### 是否允许提交的标记\n        var canSubmit = false;\n        //### 卡号是否为拍卡来的标记\n        var scanflag = false;\n        var cameraImgPath = amc.path + 'alipay_msp_icon_camera';\n        //### 设置输入框右边的图片\n        //### 设置提交按钮的样式(这里用div模拟了一个button)\n        function resetButton() {\n            getTag('submit').disabled = !canSubmit;\n        }\n\n        //### 输入框信息发生改变时\n        function checkInput() {\n            var answerValue = getTag('answer').value;\n            if (answerValue.length > 0) {\n                canSubmit = true;\n                hide('iconImg');\n                formatBankNum();\n            } else {\n                canSubmit = false;\n            }\n            //### 默认置为false,拍卡回调中才会置为true.\n            scanflag = false;\n            resetButton();\n        }\n\n        //### 银行卡号格式化\n        function formatBankNum() {\n            var account = getTag('answer').value;\n            //### 每四位后加一个空格\n            account = account.replace(/(\\d{4})(?=\\d)/g, '$1' + ' ');\n            //### 去除末尾的空格(用户删除时的小优化)\n            account = account.trim();\n            getTag('answer').value = account || '';\n        }\n\n        //### 确认按钮,提交业务数据\n        function submitInfo() {\n            if (!canSubmit) {\n                return;\n            }\n\n            if (Boolean(rpc.isCredit)) {\n                var obj = {};\n                obj['eventName'] = 'vi_external_action';\n                var answerValue = ((getTag('answer').value).replace(/\\s+/g, '') + '') || '';\n                var salt = (rpc.salt + '') || '';\n                var finalAnswer = answerValue + salt;\n                var submitObj = {\n                    'raw_data': finalAnswer,\n                    'action_name': 'encrypt_sha256'\n                };\n                obj['params'] = submitObj;\n\n                document.asyncSubmit(obj, function(data) {\n                    var encrypt_data = data.encrypt_data;\n                    doValidate(encrypt_data);\n                });\n            } else {\n                var answerValue = (getTag('answer').value).replace(/\\s+/g, '');\n                doValidate(answerValue);\n            }\n        }\n\n        function doValidate(finalAnswer) {\n            //### 自动隐藏键盘\n            getTag('answer').blur();\n            //### 开始转菊花\n            resetButton();\n            var obj = {};\n            obj['eventName'] = 'vi_rpc_validate';\n\n            var submitObj = {\n                'fullBankCardNum': finalAnswer\n            };\n            obj['params'] = submitObj;\n\n            //安保问题模板\n            obj['moduleName'] = 'FAST_PAY_PRIVACY';\n            obj['actionName'] = 'VERIFY_FAST_PAY_PRIVACY';\n\n            document.asyncSubmit(obj, function(data) {\n                data['pageloading'] = '0';\n                amc.fn.shouldShowBtnIndicator(data, 'submit', 'loading');\n\n                if (true === Boolean(data['verifySuccess'])) {\n                    mic.fn.onBackWithResponse(data);\n                } else {\n                    //### 出错时，清空输入框\n                    getTag('answer').value = '';\n                    canSubmit = true;\n                    checkInput();\n                    var verifyMessage = data['verifyMessage'] || '人气太旺了，请稍后再试';\n                    if (data['verifyCode'] === 'RETRY') {\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                          getTag('answer').focus();\n                        });\n                    } else if (data['verifyCode'] === 'CLOSED') {\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    } else {\n                        amc.fn.viAlert({\n                            \"title\": '',\n                            \"message\": verifyMessage,\n                            \"button\": '{{confirm_btn}}'\n                        }, function() {\n                            mic.fn.onBackWithResponse(data);\n                        });\n                    }\n                }\n            });\n            amc.fn.shouldShowBtnIndicator({\n                'pageloading': '1'\n            }, 'submit', 'loading');\n        }\n\n        function init() {\n            try {\n                initUI();\n\n                if (amc.fn.logPageInit) {\n                    amc.fn.logPageInit(true);\n                }\n            } catch (e) {\n                if (amc.fn.logPageInit) {\n                    amc.fn.logPageInit();\n                }\n            }\n        }\n\n        function initUI() {\n            var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', rpc.page_title || '{{id_validate}}', null, null, onBack, null);\n            getTag('bodyContainer').insertBefore(nav, getTag('mainBody'));\n            // Android平台没有白色菊花，需要特殊处理\n            if (amc.isAndroid) {\n                getTag('loading').src = amc.path + 'alipay_msp_indicator_white_loading';\n            }\n            resetButton();\n\n            //### 加载提示信息\n            getTag('tips').innerText = rpc.bodyContent || '{{safe_bank_no}}';\n            getTag('answer').placeholder = rpc.placeholder || '{{input_answer}}';\n            getTag('question').innerText = rpc.question || '';\n            getTag('submit').innerText = rpc.submitButton || '{{confirm_btn}}';\n\n            if (true !== Boolean(rpc.HAS_OTHERS)) {\n                var parentNode = document.getElementById('mainBody');\n                var othersDiv = document.getElementById('others');\n                parentNode.removeChild(othersDiv);\n            }\n            getTag('answer').focus();\n        }\n\n        function onBack() {\n            mic.fn.onBack();\n        }\n\n        function onKeyDown() {\n            if (event.which == 4) {\n                mic.fn.onBack();\n            }\n        }", "tag": "text"}]}, {"tag": "style", "children": [{"text": ".btn-text {\n            color: #d2d2d2;\n            font-weight: bold;\n        }", "tag": "text"}]}]}, {"id": "body", "onload": "init()", "tag": "body", "children": [{"id": "bodyContainer", "tag": "div", "children": [{"id": "mainBody", "tag": "div", "children": [{"id": "tipsBox", "tag": "div", "children": [{"id": "tips", "tag": "label", "css": "amc-item-l-text amc-font-m amc-flex-1"}], "css": "amc-pd-lr"}, {"tag": "div", "css": "amc-abs-space-v-l"}, {"tag": "div", "children": [{"id": "question", "tag": "label", "css": "amc-text-center amc-font-xxl"}], "css": "amc-pd-lr amc-pd-b"}, {"id": "inputBox", "tag": "div", "children": [{"tag": "div", "children": [{"id": "answer", "autofocus": "true", "placeholder": "{{please_input}}", "tag": "input", "value": "", "type": "number", "css": "amc-input", "oninput": "checkInput()"}], "css": "amc-cell-m-box"}, {"id": "iconImg", "tag": "img", "onclick": "showCamera()"}], "css": "amc-cell amc-pd-lr"}, {"tag": "div", "css": "amc-abs-space-v-xs"}, {"id": "btnContainer", "tag": "div", "children": [{"id": "loading", "tag": "img", "src": "indicatior", "css": "amc-loading-img amc-text-white-clolor amc-hidden"}, {"id": "submit", "tag": "button", "children": [{"text": "{{confirm_btn}}", "tag": "text"}], "onclick": "submitInfo()", "css": "amc-btn-primary", "disabled": "true"}], "css": "amc-margin amc-align-center amc-justify-center amc-btn-primary"}, {}, {"id": "others", "tag": "div", "children": [{}, {"id": "othersBtn", "tag": "div", "children": [{"id": "btnText", "tag": "label", "children": [{"text": "{{changeOther<PERSON>ethod}}", "tag": "text"}], "css": "amc-text-color-blue amc-font-m"}], "onclick": "mic.fn.changeModule();"}], "css": "amc-pd-lr amc-justify-end mic-other-touch-area-height"}], "css": "amc-main amc-scroll-flex"}], "css": "mic-fullscreen"}], "onkeydown": "onKeyDown();", "css": "mic-body-opacity"}]}, "format": "JSON", "tplVersion": "5.1.9"}