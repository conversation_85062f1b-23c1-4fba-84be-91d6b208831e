{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"children": [{"tag": "text", "text": "var create=amc.fn.create;var getTag=amc.fn.getById;var hide=amc.fn.hide;var show=amc.fn.show;var rpc=amc.rpcData;var params=rpc.params||{};var checkedPng=amc.path+\"alipay_msp_checked\";var logoPng=params.logo_url||\"\";var partnerName=params.auth_title||\"\";var optionTitle=params.auth_option_title||\"\";var account=rpc.hidden_logon_id||\"\";var accountInfoTitle=\"支付宝账号：\"+account;var authInfoTitle=params.auth_custom_title||params.authInfoTitle;if(!authInfoTitle){authInfoTitle=\"您同意将以下信息授权给 \"+partnerName}var authOptionList=params.auth_option_list||\"\";var isSamsungAuth=(params.service===\"com.alipay.vendorpay.validate\");var isInsideAuth=(params.auth_scene===\"inside\");var isShowChangeAcc=params.show_change_acc&&!isSamsungAuth;var checkboxs=[];var protocols=[];var authTypeText=(params.auth_type===\"LOGIN\")?\"{{login}}\":\"{{auth}}\";var canAuth=true;var resPath=amc.isIOS?\"AlipaySDK.bundle/\":\"com.alipay.android.app/\";function init(){try{initUI();if(amc.fn.logPageInit){amc.fn.logPageInit(true)}}catch(a){if(amc.fn.logPageInit){amc.fn.logPageInit()}}}function initUI(){var l=\"\";if(params.auth_type===\"LOGIN\"){l=amc.fn.i18nPlaceholderReplace(\"{{alipay_with_sth}}\",authTypeText)}else{l=\"服务授权\"}var a=amc.fn.navBack(false,l,null,null,onBack,null);getTag(\"body\").insertBefore(a,getTag(\"mainBody\"));getTag(\"merchantLogo\").src=logoPng;getTag(\"merchantLogo\").defaultvalue=resPath+\"alipay_msp_auth_default_logo\";getTag(\"alipayLogo\").src=resPath+\"alipay_msp_auth_alipay_logo\";getTag(\"arrow\").src=resPath+\"alipay_msp_side_relate\";getTag(\"btnText\").innerText=amc.fn.i18nPlaceholderReplace(\"{{confirm_sth}}\",authTypeText);getTag(\"btnText\").style.maxWidth=window.innerWidth-70;getTag(\"authInfoTitle\").innerText=authInfoTitle;if(params.auth_option_sub_title){show(\"authInfoSubTitleBox\");getTag(\"authInfoSubTitle\").innerText=params.auth_option_sub_title}else{show(\"auth_info_line\");show(\"authItemsBoxLine\")}var k=getTag(\"accountInfo\");k.innerText=accountInfoTitle;k.style.maxWidth=window.innerWidth-30-(isShowChangeAcc?60:0);if(isShowChangeAcc){show(\"changeBtn\")}if(params.auth_text_list){show(\"authItemsBox\");var e=params.auth_text_list||[];if(isSamsungAuth){e.push(\"{{auth_qrcode_info}}\")}var c=getTag(\"authItems\");for(var d=0;d<e.length;d++){var f=create(\"div\",\"auth-item\",c);var b=create(\"div\",\"auth-dot\",f);var g=create(\"label\",\"auth-item-text amc-ellipsis\",f);g.innerText=e[d]}}checkboxs=getOptionList(authOptionList,\"TEXT\");if(checkboxs&&checkboxs.length>0){for(var h in checkboxs){initCheckbox(checkboxs[h])}}protocols=getOptionList(authOptionList,\"URL\");if(protocols.length===0){hide(\"protocolArea\")}if(isInsideAuth&&params.insideTip){show(\"insideTipBox\");getTag(\"insideTip\").innerText=params.insideTip}getTag(\"mainBody\").style.height=amc.specs.bodyHeight;if(params.auth_type!=\"LOGIN\"){if(protocols&&protocols.length){amc.fn.hide(\"agree\");var j='<font color=\"#333333\">同意</font> ';for(var d=0;d<protocols.length;d+=1){j=j+(protocols[d][\"optionName\"]||\"{{serve_protocol}}\");if((d+1)<protocols.length){j=j+\"、\"}}getTag(\"protocol\").innerText=j}show(\"authSceneTipBox\");show(\"skipAuthBox\")}if(isSamsungAuth||isInsideAuth){getTag(\"authSceneTip\").innerText=\"Auth_Inside\"}}function getOptionList(d,b){var c=[];for(var a in d){if(d[a].type===b){c.push(d[a])}}return c}function initCheckbox(b){var a=getTag(\"checkArea\");var c=create(\"div\");c.className=\"amc-align-center check-item\";var d;if(b.forced){d=create(\"img\");d.className=\"check-img\";d.src=checkedPng;c.appendChild(d)}else{d=create(\"input\");c.appendChild(d);d.type=\"checkbox\";d.className=amc.isIOS?\"checkbox-ios\":\"checkbox-android\";d.checked=b.checked}d.className=d.className+\" margin-r-xs\";checklabel=create(\"label\");checklabel.innerText=b.optionText;checklabel.className=\"check-text amc-flex-1 amc-ellipsis\";c.appendChild(checklabel);c.onclick=function(){d.checked=!d.checked};a.appendChild(c);b._checkbox=d}function showProtocol(){for(var a=0;a<protocols.length;a++){protocols[a][\"text\"]=protocols[a][\"optionName\"]||\"{{serve_protocol}}\";protocols[a][\"url\"]=protocols[a][\"optionText\"]}amc.fn.showProtocolList(protocols)}function onSubmit(){if(!canAuth){return}var b=[];for(var a=0;a<checkboxs.length;a++){if(checkboxs[a].forced){b.push(checkboxs[a].optionKey)}else{if(checkboxs[a][\"_checkbox\"].checked===true||checkboxs[a][\"_checkbox\"].checked===\"1\"){b.push(checkboxs[a].optionKey)}}}b.push(\"PROTOCOL\");var c;if(isSamsungAuth){c={param:{agree:b,service:\"com.alipay.vendorpay.validate\"},action:{name:\"/open/service\",loadtxt:\"\"}}}else{if(isInsideAuth){c={param:{agree:b,service:\"com.alipay.account.auth\"},action:{name:\"/open/service\",loadtxt:\"\"}}}else{c={param:{agree:b},action:{name:\"/account/auth\",loadtxt:\"\"}}}}amc.fn.playGif(getTag(\"gifImg\"),amc.res.loading);getTag(\"btnText\").innerText=\"{{confirming_upgrade}}\";document.submit(c);setTimeout(resetBtn,3000);canAuth=false}function onSwitchAccount(){var a={action:{name:\"/cashier/switchAccount\"}};document.submit(a)}function resetBtn(){amc.fn.playGif(getTag(\"gifImg\"),\"\");getTag(\"btnText\").innerText=amc.fn.i18nPlaceholderReplace(\"{{confirm_sth}}\",authTypeText);canAuth=true}function onKeyDown(){if(event.which==4){onBack()}}function onBack(){amc.fn.exitConfirm()};"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".bold-text {\n        font-weight: bold\n    }\n\n    .amc-nav-r-box {}\n\n    .amc-nav-l-text {\n        color: #108ee9;\n        font-size: 14px;\n        text-align: left;\n    }\n\n    .agree-label {\n        max-width: 150px;\n    }\n\n    .stretch-self {\n        align-self: stretch;\n    }\n\n    .inside-tip-box {\n        min-height: 63px;\n        padding: 5px 15px 24px 15px;\n        justify-content: flex-end;\n    }\n\n    .inside-tip {\n        font-size: 13px;\n        color: #888;\n    }\n\n    .top-bg {\n        background-color: #108EE9;\n        padding: 30px 15px;\n    }\n\n    .change-btn {\n        max-width: 100px;\n        margin-left: 6px;\n        font-size: 15px;\n        color: #fff;\n    }\n\n    .account-info {\n        font-size: 15px;\n        color: #fff;\n        opacity: 0.8;\n    }\n\n    .auth-info-title {\n        font-size: 15px;\n        color: #000;\n    }\n\n    .pd-b {\n        padding-bottom: 15px;\n    }\n\n    .pd-lr {\n        padding-left: 15px;\n        padding-right: 15px;\n    }\n\n    .auth-items-box {\n        padding: 0 15px 15px 15px;\n    }\n\n    .auth-items {\n        padding-top: 6px;\n    }\n\n    .auth-dot {\n        height: 4px;\n        width: 4px;\n        border-radius: 2px;\n        background-color: #888;\n        margin-right: 6px;\n    }\n\n    .auth-item {\n        margin-top: 9px;\n        align-items: center;\n    }\n\n    .auth-item-text {\n        font-size: 15px;\n        color: #888;\n    }\n\n    .check-text {\n        font-size: 15px;\n        color: #000;\n    }\n\n    .check-item {\n        padding-top: 18px;\n    }\n\n    .margin-t {\n        margin-top: 15px;\n    }\n\n    .btn-box {\n        padding-top: 30px;\n        padding-bottom: 15px;\n    }\n\n    .auth-info-title-box {\n        padding: 10px 15px 15px 15px;\n    }\n\n    .auth-info-sub-box {\n        padding: 0 15px;\n    }\n\n    .font-m {\n        font-size: 15px;\n    }\n\n    .font-l {\n        font-size: 18px;\n    }\n\n    .margin-r-xs {\n        margin-right: 6px;\n    }\n\n    .btn-primary {\n        background-color: #108ee9;\n        border: 0;\n        border-radius: 4px;\n        color: #fff;\n        font-size: 18px;\n        height: 43px;\n        max-height: 43px;\n        min-height: 43px;\n        flex: 1.0;\n    }\n\n    .btn-primary:active {\n        background-color: #1284D6;\n        color: #fff;\n    }\n\n    .btn-primary:disabled {\n        background-color: #ebebf0;\n        color: #d2d2d2;\n    }\n\n    .btn-img {\n        width: 24px;\n        height: 24px;\n        margin-right: 8px;\n    }\n\n    .check-img {\n        height: 15px;\n        width: 15px;\n    }\n\n    .checkbox-ios {\n        width: 15px;\n        height: 15px;\n        background-image: url(AlipaySDK.bundle/alipay_msp_check);\n        background-size: 15px 15px;\n    }\n\n    .checkbox-ios:checked {\n        background-image: url(AlipaySDK.bundle/alipay_msp_checked);\n    }\n\n    .checkbox-ios:disabled {\n        background-image: url(AlipaySDK.bundle/alipay_msp_check_disable);\n    }\n\n    .checkbox-android {\n        width: 15px;\n        height: 15px;\n        background-image: url(com.alipay.android.app/alipay_msp_check);\n        background-size: 15px 15px;\n    }\n\n    .checkbox-android:checked {\n        background-image: url(com.alipay.android.app/alipay_msp_checked);\n    }\n\n    .checkbox-android:disabled {\n        background-image: url(com.alipay.android.app/alipay_msp_check_disable);\n    }\n\n    .protocol-area {\n        padding-top: 18px;\n    }\n\n    .skip-auth-box {\n        padding: 0px 15px 0px 15px;\n        margin-bottom: 32px;\n        justify-content: center;\n        margin-top: -18px;\n        height: 18px;\n    }\n\n    .auth-scene-box {\n        padding: 0px 15px 0px 15px;\n        justify-content: center;\n        height: 18px;\n    }\n    .auth-scene-tip {\n        font-size: 10px;\n        text-align: right;\n        color: #CCC;\n    }\n    .logo-box {\n        width: 54px;\n        height: 54px;\n        border-radius: 10px;\n        background-color: #57AEEC;\n    }\n    .logo {\n        width: 50px;\n        border-radius: 10px;\n        height: 50px;\n    }\n    .arrow {\n        width: 29.83px;\n        height: 29.83px;\n        margin: 0px 16px;\n    }\n    .account-box {\n        margin-top: 9.5px;\n    }"}], "tag": "style"}], "tag": "head"}, {"css": "amc-body", "children": [{"css": "amc-v-box amc-scroll-flex", "children": [{}, {"css": "top-bg amc-v-box", "children": [{"css": "amc-flex-center", "children": [{"css": "logo-box amc-flex-center", "children": [{"css": "logo", "tag": "img", "id": "merchantLogo"}], "tag": "div"}, {"css": "arrow", "tag": "img", "id": "arrow"}, {"css": "logo-box amc-flex-center", "children": [{"css": "logo", "tag": "img", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "tag": "div"}], "tag": "div"}, {"css": "amc-justify-center account-box", "children": [{"css": "account-info amc-ellipsis", "tag": "label", "id": "accountInfo"}, {"css": "amc-align-center", "children": [{"css": "change-btn amc-hidden", "children": [{"tag": "text", "text": "{{sel}}"}], "onclick": "onSwitchAccount()", "tag": "label", "id": "changeBtn"}], "tag": "div"}], "tag": "div"}], "tag": "div", "id": "banner"}, {}, {"css": "amc-v-box amc-bg-white", "children": [{"css": "auth-info-title-box", "children": [{"css": "auth-info-title amc-flex-1 amc-ellipsis-2-line", "tag": "label", "id": "authInfoTitle"}], "tag": "div"}, {"css": "amc-v-box amc-hidden auth-info-sub-box", "children": [{"css": "auth-item-text amc-ellipsis-2-line", "tag": "label", "id": "authInfoSubTitle"}], "tag": "div", "id": "authInfoSubTitleBox"}, {"css": "amc-1px-line amc-hidden", "tag": "div", "id": "auth_info_line"}, {}, {"css": "amc-v-box auth-items-box amc-hidden", "children": [{"css": "amc-1px-line amc-hidden", "tag": "div", "id": "authItemsBoxLine"}, {"css": "amc-v-box auth-items", "tag": "div", "id": "authItems"}], "tag": "div", "id": "authItemsBox"}, {"css": "amc-1px-line", "tag": "div"}], "tag": "div"}, {"css": "amc-v-box", "children": [{}, {"css": "pd-lr amc-v-box", "tag": "div", "id": "checkArea"}, {}, {"css": "pd-lr protocol-area", "children": [{"css": "agree-label  amc-ellipsis-2-line font-m margin-r-xs", "children": [{"tag": "text", "text": "{{agree}}"}], "tag": "label", "id": "agree"}, {"css": "font-m amc-theme-color amc-flex-1 amc-ellipsis-2-line", "children": [{"tag": "text", "text": "{{serve_protocol}}"}], "onclick": "showProtocol()", "tag": "label", "id": "protocol"}], "tag": "div", "id": "protocolArea"}, {"css": "pd-lr btn-box", "children": [{"css": "btn-primary amc-justify-center amc-align-center", "children": [{"css": "btn-img amc-hidden", "tag": "embed", "id": "gifImg", "type": "MQPPayGifView"}, {"css": "amc-btn-text-color amc-font-super-l amc-btn-ellpsis", "children": [{"tag": "text", "text": "{{confirm_auth}}"}], "tag": "label", "id": "btnText"}], "onclick": "onSubmit()", "tag": "div", "id": "confirmBtn"}], "tag": "div", "id": "container"}], "tag": "div"}, {"css": "auth-scene-box amc-v-box amc-hidden", "children": [{"css": "auth-scene-tip", "tag": "label", "id": "authSceneTip"}], "tag": "div", "id": "authSceneTipBox"}, {"css": "skip-auth-box amc-hidden", "children": [{"css": "amc-theme-color amc-text-center amc-font-m", "children": [{"tag": "text", "text": "暂不授权"}], "onclick": "onBack()", "tag": "label"}], "tag": "div", "id": "skipAuthBox"}, {}, {"css": "amc-v-box amc-flex-1 inside-tip-box amc-hidden", "children": [{"css": "inside-tip amc-text-center amc-ellipsis-2-line", "tag": "label", "id": "insideTip"}], "tag": "div", "id": "insideTipBox"}, {"css": "amc-iphone-x-pd-b", "tag": "div"}], "tag": "div", "id": "mainBody"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150924", "name": "open-account-auth-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0011", "tplId": "QUICKPAY@open-account-auth-flex", "tplVersion": "5.2.9"}