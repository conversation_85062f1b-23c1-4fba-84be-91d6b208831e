{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"tag": "style"}, {"children": [{"tag": "text", "text": "._MdeductSignPage_1v77-t-textarea{font-family:\"PingFangSC-Regular\",\"Helvetica Neue\",Helvetica,STHeiTi,sans-serif;font-size:14px;color:#333333}._MdeductSignPage_1v77-t-body{background-color:#f5f5f5}._MdeductSignPage_1v77-c-main-body{padding-top:0;overflow:scroll}._MdeductSignPage_1v77-c-de-1px-line{height:1px;background-color:#eeeeee}._MdeductSignPage_1v77-c-no-top-padding{padding-top:0}._MdeductSignPage_1v77-c-cell-box{padding:10px 15px;min-height:60px}._MdeductSignPage_1v77-c-flex-row{display:flex;justify-content:center;align-items:center}._MdeductSignPage_1v77-c-algin-item-start{align-items:flex-start}._MdeductSignPage_1v77-c-flex-between{justify-content:space-between}._MdeductSignPage_1v77-c-flex-justify-start{justify-content:flex-start}._MdeductSignPage_1v77-c-checkbox-ios{width:15px;height:15px;background-image:url(AlipaySDK.bundle/alipay_msp_check);background-size:15px 15px}._MdeductSignPage_1v77-c-checkbox-ios:checked{background-image:url(AlipaySDK.bundle/alipay_msp_checked)}._MdeductSignPage_1v77-c-checkbox-ios:disabled{background-image:url(AlipaySDK.bundle/alipay_msp_check_disable)}._MdeductSignPage_1v77-c-checkbox-android{width:15px;height:15px;background-image:url(com.alipay.android.app/alipay_msp_check);background-size:17px 17px}._MdeductSignPage_1v77-c-checkbox-android:checked{background-image:url(com.alipay.android.app/alipay_msp_checked)}._MdeductSignPage_1v77-c-checkbox-android:disabled{background-image:url(com.alipay.android.app/alipay_msp_check_disable)}._MdeductSignPage_1v77-c-order-amount{color:#f96268}._MdeductSignPage_1v77-c-merchant-logo{margin:10px 0;height:60px;width:60px}._MdeductSignPage_1v77-c-product-name{flex:1;font-size:17px;font-weight:bold;margin-left:16px;margin-right:8px}._MdeductSignPage_1v77-c-dialog-info-icon{width:16px;height:16px}._MdeductSignPage_1v77-c-de-product-desc{color:#999999;margin-top:10px}._MdeductSignPage_1v77-c-deduct-label-list{padding:12px 16px}._MdeductSignPage_1v77-c-deduct-label-item{padding:4px 0}._MdeductSignPage_1v77-c-deduct-label{flex:1.0;color:#999999}._MdeductSignPage_1v77-c-deduct-label-content{flex:2.0;text-align:right}._MdeductSignPage_1v77-c-deduct-switch{min-height:44px}._MdeductSignPage_1v77-c-de-agreement-list{display:flex;align-items:flex-start;padding:16px 16px 0}._MdeductSignPage_1v77-c-de-agreement-checkbox{width:20px;margin-right:6px;align-items:flex-start}._MdeductSignPage_1v77-c-de-agreement-links{margin-left:0;margin-top:-5px;font-size:15px;flex:1;line-height:21px;word-break:break-all;word-wrap:break-word;flex-wrap:wrap}._MdeductSignPage_1v77-c-de-info-dialog-item{padding:5px 15px;display:flex;justify-content:flex-start;align-items:flex-start}._MdeductSignPage_1v77-c-de-dialog-prefix{flex:1;line-height:1;align-self:flex-start}._MdeductSignPage_1v77-c-de-dialog-content{flex:10;word-break:break-all;word-wrap:break-word}._MdeductSignPage_1v77-c-btn-primary-wrapper{margin:0 15px;border:0;border-radius:5px;color:#fff;font-size:18px;height:42px;max-height:42px;min-height:42px;flex:1.0}._MdeductSignPage_1v77-c-btn-primary{border:0;border-radius:5px;font-size:18px;height:42px;max-height:42px;min-height:42px;flex:1.0}._MdeductSignPage_1v77-c-de-dialog-prefix-logo{height:6px;width:6px;border-radius:3px;background-color:#333;align-self:flex-start;margin-right:5px;margin-top:5px}._MdeductSignPage_1v77-c-de-toast{position:absolute;top:300px;left:70px;right:70px;height:auto;display:flex;justify-content:center;align-items:center}._MdeductSignPage_1v77-c-deduct-pay-setting{margin-top:10px}._MdeductSignPage_1v77-c-de-toast-label{flex:1;text-align:center;color:#fff;padding:5px 15px;border-radius:4px;opacity:0.7;background-color:#333}._MdeductSignPage_1v77-c-slogan-box{padding:0px 0px 8px 0px}._MdeductSignPage_1v77-c-slogan-img{height:14px}._MdeductSignPage_1v77-c-item-box-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}._MdeductSignPage_1v77-c-propagate-arrow{position:absolute;width:16px;height:8px;top:0px;left:16px}._MdeductSignPage_1v77-c-propagate-icon{margin-right:8px;height:16px;width:16px}._MdeductSignPage_1v77-c-info-text-propagate{font-size:13px}._MdeductSignPage_1v77-c-font-color-primary{color:#333}._MdeductSignPage_1v77-c-item-btn{background-color:#f7fcff;margin-left:5px;border:0.5px solid #108ee9;border-radius:1px;padding:2px 8px;min-height:24px;min-width:48px}._MdeductSignPage_1v77-c-item-btn-text{font-size:13px;color:#108ee9;max-width:90px}._MdeductSignPage_1v77-c-double-line-for-propagate{height:34px}._MdeductSignPage_1v77-c-item-box-s-for-propagate{height:40px;max-height:40px;min-height:40px;padding:0 10px}._MdeductSignPage_1v77-c-bottom-container{flex:1.0}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from 5dce621e41295bc860fa4c53721a0b7e40d45d49:D */!function(n){var o={};function i(e){if(o[e])return o[e].exports;var t=o[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}i.m=n,i.c=o,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(e,'__esModule',{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&'object'==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,'default',{enumerable:!0,value:t}),2&e&&'string'!=typeof t)for(var o in t)i.d(n,o,function(e){return t[e]}.bind(null,o));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,'a',t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p='',i(i.s=7)}([function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.amc=window.amc},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(8);t.BNComponent=o.BNComponent;var i=n(3);t.ComponentRegistry=i.ComponentRegistry;var r=n(10);t.Logger=r.Logger,t.logger=r.logger},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},t.startsWith=function(e,t){return!!e&&0===e.indexOf(t)}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(1),i=function(){function n(){}return n.registerComponent=function(e,t){t?n.facts[e]?o.logger.e('CmpReg#regCmp','E0002 '+e):(o.logger.i('CmpReg#regCmp','I0003 '+e),n.facts[e]=t):o.logger.e('CmpReg#regCmp','E0001 '+e+', '+t)},n.getKnownComponents=function(){return n.facts},n.getComponentJson=function(e){return n.jsons[e]},n.putComponentJson=function(e,t){t||o.logger.e('CmpReg#putCmpJ','E0004 '+e+', '+t),n.getComponentJson(e)?o.logger.e('CmpReg#putCmpJ','E0005 '+e):(o.logger.i('CmpReg#putCmpJ','I0006 '+e),n.jsons[e]=t)},n.createComponent=function(e){o.logger.i('CmpReg#crtCmp','I0007 '+e);var t=n.facts[e];return t?new t:(o.logger.e('CmpReg#crtCmp','E0008 '+e),null)},n.facts={},n.jsons={},n}();t.ComponentRegistry=i},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var d=n(0),o=n(5),l=n(6),u=function(){function e(){}return e.nativeRenderSucceed=function(e,t){t&&(e.parentNode&&e.pluginNode&&(e.parentNode.style.height=t,e.pluginNode.style.height=t),e.pluginHeight=t),e.renderFinishCallback&&e.renderFinishCallback(e)},e.allPlugins={},e}(),i=function(){function s(e){this.mqpToken=e}return s.listAll=function(){var e=[];for(var t in u.allPlugins)u.allPlugins.hasOwnProperty(t)&&e.push({mqpToken:t,plugin:u.allPlugins[t]});return e},s.find=function(e){return u.allPlugins[e]},s.remove=function(e,t,n){var o=u.allPlugins[e];o&&o.remove(t,n)},s.create=function(e,t,n,o){if(e&&'PluginBN'===e.type&&e.tplInfo&&e.data){var i=new s(l.Utils.randomStr(6)),r=e.tplInfo,a=r.tplHash?r.tplId+'_'+r.tplHash:r.tplId,c=e.data;l.Utils.niceTry(function(){d.amc.fn.logAction('new|'+Date.now()+'|'+a+'|'+i.mqpToken,'BNPlugin')}),i.init({mqpToken:i.mqpToken,tpl:{tplid:a,tpl:JSON.stringify({time:'0001',tplId:a,tplVersion:'5.4.9',publishVersion:'150924',tplUrl:r.tplUrl,tplHash:r.tplHash}),data:{config:c.config,bizData:c.bizData,spm:c.spm,mqpToken:i.mqpToken}}},t,n,o)}},s.prototype.init=function(e,t,n,o){if(o){var i=e.tpl.data.config;'object'==typeof i.loc&&null!==i.loc||(i.loc={});var r=i.loc;o.width&&(r.width=o.width),o.height&&(r.height=o.height)}var a;if((u.allPlugins[this.mqpToken]=this).parentNode=t,this.renderFinishCallback=n,this.initData=e,'android'===l.Utils.getPlatform()?((a=document.createElement('embed',{type:'MQPBNFrame',src:JSON.stringify(e)},function(){})).type='MQPBNFrame',this.pluginNode=a):(a=document.createElement('embed'),(this.pluginNode=a).type='MQPBNFrame',a.src=JSON.stringify(e)),o&&o.mountAsFirstChild){var c=t.hasChildNodes()&&t.childNodes[0];c?t.insertBefore(a,c):t.appendChild(a)}else t.appendChild(a);o&&o.doNotClearParentHeight||(t.style.height=0)},s.prototype.remove=function(n,e){var o=this;delete u.allPlugins[this.mqpToken];var t=this.pluginNode;if(t&&(t.style.width=0,t.style.height=0),e&&e.resetParentHeight){var i=this.parentNode;i&&(i.style.height='auto')}var r=function(){var e=o.parentNode,t=o.pluginNode;e&&t&&e.removeChild(t),n&&n()};e&&e.delayMs?window.setTimeout(function(){r()},e.delayMs):r()},s}();t.BNFramePlugin=i,o.BNFrameChannelOuter.onInnerEvent('MQPBNFRAME_RENDER_SUCCESS',function(e,t){if(l.Utils.niceTry(function(){d.amc.fn.logAction('ack|'+Date.now()+'|'+(e?e.height:'-')+'|'+(t?t.mqpToken:'-'),'BNPlugin')}),!(t&&e&&e.mqpToken&&e.height))return{result:!1};var n=e.height;return u.nativeRenderSucceed(t,n),{result:!0}})},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var r=n(0),s=n(4),a=n(6),c=function(){function e(){}return e.onFramePluginEventLifetimeNotifier=function(e){var t='string'==typeof e?JSON.parse(e):e;if('onEvent'===t.type){var n=t.key;if(!n)return;var o=this.EventTypeInnerHandlers[n];o&&o.forEach(function(e){try{e(t.args)}catch(e){}});var i=this.EventTypeOuterHandlers[n];if(i&&t.mqpToken&&t.id)try{var r=i(t.args,s.BNFramePlugin.find(t.mqpToken));d.sendEventResultToPlugin(n,t.id,r||{},t.mqpToken)}catch(e){}}else if('onEventResult'===t.type){var a=t.id;if(!a)return;var c=this.EventResultCallbacks[a];if(c){try{c(!0,t.result)}catch(e){}delete this.EventResultCallbacks[a]}}},e.addEventHandler=function(e,t,n){e?(this.EventTypeInnerHandlers[t]||(this.EventTypeInnerHandlers[t]=[]),this.EventTypeInnerHandlers[t].push(n)):this.EventTypeOuterHandlers[t]=n},e.setEventResultCallback=function(e,t){t&&(this.EventResultCallbacks[e]=t)},e.listKnownEventTypes=function(){var e=[];for(var t in this.EventTypeOuterHandlers)this.EventTypeOuterHandlers.hasOwnProperty(t)&&e.push(t);return e},e.EventResultCallbacks={},e.EventTypeInnerHandlers={},e.EventTypeOuterHandlers={},e}(),d=function(){function i(){}return i.onInnerEvent=function(e,t){c.addEventHandler(!1,e,t)},i.sendEventToInner=function(t,n,e){if(e){var o=s.BNFramePlugin.find(e);o&&i.sendEventToOnePlugin(t,n||{},o)}else s.BNFramePlugin.listAll().forEach(function(e){i.sendEventToOnePlugin(t,n||{},e.plugin)})},i.sendEventResultToPlugin=function(e,t,n,o){if(o){var i=s.BNFramePlugin.find(o);i&&this.sendEventResultToOnePlugin(e,t,n,i)}},i.sendEventToOnePlugin=function(e,t,n){var o={type:'onEvent',id:this.generateEvId(e),mqpToken:n.mqpToken,key:e,args:t};this._setDomEvent(n,o)},i.sendEventResultToOnePlugin=function(e,t,n,o){var i={type:'onEventResult',id:t,mqpToken:o.mqpToken,key:e,result:n};this._setDomEvent(o,i)},i._setDomEvent=function(e,t){e.pluginNode&&window.setTimeout(function(){e.pluginNode.setAttribute('event',JSON.stringify(t))},1)},i.generateEvId=function(e){return'ev_o_'+(e||'')+'_'+a.Utils.randomStr(6)},i}();t.BNFrameChannelOuter=d;var l=function(){function e(){}return e.sendEventToOuter=function(e,t,n){var o={type:'onEvent',id:this.generateEvId(e),mqpToken:window.flybird&&window.flybird.rpcData&&window.flybird.rpcData.mqpToken,key:e,args:t||{}};c.setEventResultCallback(o.id,n),document.submit({action:{name:'onBnFrameEvent'},param:o})},e.onOuterEvent=function(e,t){c.addEventHandler(!0,e,t)},e.generateEvId=function(e){return'ev_i_'+(e||'')+'_'+a.Utils.randomStr(6)},e}();t.BNFrameChannelInner=l;var o=function(){function e(){}return e.listApis=function(n){l.sendEventToOuter(i,{},function(e,t){e&&t&&t.apis?n(t.apis):n([])})},e.renderFinished=function(o,i,e){void 0===e&&(e=20),window.setTimeout(function(){var e,t=window.flybird&&window.flybird.rpcData||{},n=t.mqpToken||'';e='number'==typeof o?o:o&&o.style&&'number'==typeof o.style.offsetHeight?a.Utils.convPX2px(o.style.offsetHeight):0,a.Utils.niceTry(function(){r.amc.fn.logAction('ok|'+Date.now()+'|'+e+'|'+(t?t.mqpToken:'-'),'BNPlugin')}),l.sendEventToOuter('MQPBNFRAME_RENDER_SUCCESS',{mqpToken:n,height:e},function(e,t){i&&i(e)})},e)},e._renderFinished=function(e,t){},e}();t.BNFrameCommonInnerEvents=o,window.onFramePluginEvent=function(e){c.onFramePluginEventLifetimeNotifier(e)};var i='LIST_APIS';d.onInnerEvent(i,function(e,t){return{apis:c.listKnownEventTypes()}})},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=function(){function e(){}return e.randomStr=function(e){for(var t='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',n=t.length,o=isNaN(e)?1:e,i='',r=0;r<o;r++)i+=t[Math.floor(Math.random()*n)];return i},e.alert=function(e,t){document.alert({title:e||'-',message:t||'-',button:'OK'},function(){})},e.niceTry=function(e){try{return e()}catch(e){}},e.getPlatform=function(){switch(document.platform){case'android':return'android';case'iOS':return'iOS';default:return''}},e.convPX2px=function(e){return e/window.devicePixelRatio},e}();t.Utils=o},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var r,_,a=n(1),y=n(0),s=n(11),c=n(12),b=y.amc.rpcData,d=function(e,t){try{y.amc.fn.logError(e,t)}catch(e){y.amc.fn.logError('log_error_failed','')}},C={outTradeNo:b.outTradeNo,personalProductCode:b.personalProductCode,externalAgreementNo:b.externalAgreementNo,salesProductCode:b.salesProductCode,userId:b.userId,partnerId:b.partnerId,requestToken:b.requestToken,signScene:b.signScene};y.amc.fn.spmPageCreate('a259.b13454',C);var l=function(e){function t(){var c=null!==e&&e.apply(this,arguments)||this;return c.vueModel={data:{settingBox:{tip:'',descr:'',tailImg:'',action:''},deductChannelFromZhuge:!1,switchStatus:!0,settingBoxClick:function(){if(c.vueModel.data.settingBox&&c.vueModel.data.settingBox.action){var e=x(c.vueModel.data.settingBox.action);e&&e.name&&('/channelPriority/query'===e.name&&(e.params||(e.params={}),e.params.sdkVersion=y.amc.sdkVersion,e.params.mspType=y.amc.mspType),document.submit({action:e}))}else c.vueModel.data.settingBox&&c.vueModel.data.settingBox.bnframe&&c.channelSelectBnframeToken&&s.BNFrameChannelOuter.sendEventToInner('SWITCH_CHANNEL',void 0,c.channelSelectBnframeToken)}},compute:{settingBoxExist:function(){return c.vueModel.data.switchStatus&&(!!c.vueModel.data.settingBox||!c.vueModel.data.deductChannelFromZhuge)},settingBoxTip:function(){return c.vueModel.data.settingBox&&c.vueModel.data.settingBox.tip||'扣款方式'},settingBoxDescr:function(){return c.vueModel.data.settingBox&&c.vueModel.data.settingBox.descr||'按设置的扣款顺序支付'},settingBoxTailImg:function(){return c.vueModel.data.settingBox&&'Y'===c.vueModel.data.settingBox.tailImg?y.amc.res.arrowRight:''}}},c.canUseALinkColor=y.amc.fn.sdkGreaterThanOrEqual('10.8.34'),c.onMounted=function(){var e=b;c.merchantLogDom=c.getViewInComponentById('merchant-logo'),c.deProductNameDom=c.getViewInComponentById('de-product-name'),c.deProductDescDom=c.getViewInComponentById('de-product-desc'),c.deLableListDom=c.getViewInComponentById('deduct-label-list'),c.agreementLinkListDom=c.getViewInComponentById('de-agreement-links'),c.confirmCheckbox=c.getViewInComponentById('de-agreement-confirm-checkbox'),c.submitBtn=c.getViewInComponentById('submit-btn'),c.agreeCheckbox=c.getViewInComponentById('de-agree-checkbox'),c.toastWrapper=c.getViewInComponentById('de-toast'),c.toastLabel=c.getViewInComponentById('de-toast-label'),c.btnLoading=c.getViewInComponentById('btn-loading'),c.deChannelLable=c.getViewInComponentById('deduct-channel-label'),c.renderDeductMerchant(e.merchantDetailInfo),c.renderDeductLabelsDom(e.deductPageLabelInfos),c.renderAgreementInfo(e.deductAgreementDetailInfos);var t=x(e.operationData);if(t&&c.createPropagate(t,!1),c.vueModel.data.deductChannelFromZhuge=!!b.deductChannelFromZhuge,c.vueModel.data.settingBox=b.deductChannelSettingBox,c.submitBtn.onclick=c.submitInfo,y.amc.isAndroid&&(c.btnLoading.src=y.amc.path+'alipay_msp_indicator_white_loading'),b.deductChannelSettingBox&&b.deductChannelSettingBox.bnframe){var n=b.deductChannelSettingBox.bnframe;n&&c.createDynamicFrame(n)}if(b.deductChannelSettingBox&&(b.deductChannelSettingBox.curChannelIndexList&&(c.currentChannelList=b.deductChannelSettingBox.curChannelIndexList),c.channelContextId=b.deductChannelSettingBox.channelContextId||''),b.alipayLogoUrl&&b.alipayLogoUrl.length){var o=function(e,t,n){e&&y.amc.fn.show(c.getViewInComponentById('sloganBox'))},i=c.getViewInComponentById('sloganImg'),r=y.amc.isSDK&&y.amc.isIOS;window.loadImageHelper(i,b.alipayLogoUrl,r?null:o),r&&o(!0,b.alipayLogoUrl)}var a=c.getViewInComponentById('mainBody');a.style.height=y.amc.specs.bodyHeight,y.amc.fn.show(a)},c.showToast=function(e){c.toastLabel.innerText=e,c.toastTimeoutRef&&clearTimeout(c.toastTimeoutRef),y.amc.fn.show(c.toastWrapper),y.amc.fn.show(c.toastLabel),c.toastTimeoutRef=setTimeout(function(){y.amc.fn.hide(r.toastWrapper),r.toastLabel.innerText=''},3e3)},c.submitInfo=function(){y.amc.fn.spmClick('a259.b13454.c32640.d64635',C),r.getViewInComponentById('deduct-switch');var e=r.getViewInComponentById('de-agree-checkbox');c.shouldShowAgreeCheckbox&&e&&!e.checked?c.showToast('请先阅读并同意协议'):(c.disableSubmitBtn(),c.applySign())},c.applySign=function(){g('alipay.mdeduct.sdk.sign.apply',{initPageToken:b.initPageToken},function(e){e.agreementNo?c.successCallback(e):(b.applyToken=e.applyToken,b.verifyId=e.verifyId,b.securityId=e.securityId,b.agreementNo=e.agreementNo,c.verifyIdendity())},function(e){'USER_AGREEMENT_PERIOD_CONFILICT'===e.errorCode?m(e):(e.errorMsg&&c.errorToast(e.errorMsg),c.enableSubmitBtn())})},c.verifyIdendity=function(){document.invoke('verifyIdentity',{verifyId:b.verifyId},function(e){'1000'===e.code?c.confirmSign():(d('IDENDITY_VERIFY_FAILED',e.code||''),c.enableSubmitBtn())})},c.confirmSign=function(){g('alipay.mdeduct.sdk.sign.confirm',{applyToken:b.applyToken,securityId:b.securityId},function(e){c.successCallback(e)},function(e){m(e)})},c}return i(t,e),t.prototype.renderDeductMerchant=function(e){var t=e.merchantLogoUrl||'';t=t.replace(/\\[pixelWidth\\]x\\[pixelHeight\\]/g,'60x60'),this.merchantLogDom.src=t,this.deProductNameDom.innerText=b.merchantDetailInfo.merchantProductName||'',this.deProductDescDom.innerText=b.merchantDetailInfo.merchantName||'';var n=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),o=this.createStyledElement('label','','deduct-label'),i=this.createStyledElement('label','','deduct-label-content');i.innerText=e.serviceDescription||'',o.innerText='服务详情';var r=this.getViewInComponentById('merchant-label');this.submitBtn.innerText=b.confirmBtnText||'同意协议并开通免密支付',r.appendChild(n),n.appendChild(o),n.appendChild(i)},t.prototype.renderDeductLabelsDom=function(e){if(e&&!(e.length<1)&&this.deLableListDom)for(var t=0;t<e.length;t++){var n=e[t],o=this.createStyledElement('div','','flex-row flex-between algin-item-start deduct-label-item'),i=this.createStyledElement('label','','deduct-label'),r=this.createStyledElement('label','','deduct-label-content');r.innerText=n.labelValue||'',i.innerText=n.labelName||'',this.deLableListDom.appendChild(o),o.appendChild(i),o.appendChild(r)}},t.prototype.renderAgreementInfo=function(i){var e='';if(!i||i.length<1)this.confirmCheckbox.innerText='';else{1<i.length?(y.amc.fn.show(this.confirmCheckbox),this.shouldShowAgreeCheckbox=!0,e+='<a color=\"#8b8b8b\" href=\"toggle_check\">我已阅读并同意</a>'):(this.confirmCheckbox.innerHTML='',this.shouldShowAgreeCheckbox=!1,e+='<font color=\"#000000\" >查看</font>');for(var t=0;t<i.length;t++){var n=i[t],o='<a color=\"'+c.getThemeColor()+'\" href=\"'+n.agreementUrl+'\">'+n.agreementName+'</a>';this.canUseALinkColor&&(o='<a color=\"'+c.getThemeColor()+'\" alinkcolor=\"'+c.getThemeColor()+'\" href=\"'+n.agreementUrl+'\">'+n.agreementName+'</a>'),e+=o}this.agreementLinkListDom.innerText=e,this.agreementLinkListDom.onlink=function(e){e&&('toggle_check'!==e?(y.amc.fn.spmClick('a259.b13454.c32640.d64637',C),function(e){for(var t='支付宝',n=0;n<i.length;n++){var o=i[n];if(o.agreementUrl===e){t=o.agreementName;break}}document.submit({action:{name:'loc:openweb(\\''+e+'\\',\\''+t+'\\')'}})}(e)):function(){y.amc.fn.spmClick('a259.b13454.c32640.d64636',C);var e=r.getViewInComponentById('de-agree-checkbox');e.checked=!e.checked}())}}},t.prototype.successCallback=function(e){document.invoke('setResult',e),y.amc.fn.exit()},t.prototype.disableSubmitBtn=function(){y.amc.fn.hide(this.submitBtn),y.amc.fn.show(this.btnLoading)},t.prototype.enableSubmitBtn=function(){y.amc.fn.hide(this.btnLoading),y.amc.fn.show(this.submitBtn)},t.prototype.errorToast=function(e){this.showToast(e||''),this.enableSubmitBtn()},t.prototype.createPropagate=function(e,t){var n=this.getViewInComponentById('anchoredBox'),o=this.propagateBox;if(t&&o&&n.removeChild(o),e&&e.title){o=this.createStyledElement('div','propagateBox','amc-v-box'),this.propagateBox=o,n.appendChild(o),y.amc.fn.show(n);var i=e.actTitle;this.createStyledElement('div','','amc-1px-line');var r=this.createStyledElement('div','','item-box-for-propagate amc-align-center');o.appendChild(r);var a=this.createStyledElement('div','','amc-1px-line');if(o.appendChild(a),b.payTool){var c=this.createStyledElement('img','','propagate-arrow');o.appendChild(c),c.contentMode='center',c.src=y.amc.path+'alipay_msp_propagate_arrow'}var s=e.channelLogo,d=e.channelLogoH5;if('money_fund'===s||'ant_credit_pay'===s||e.showIcon){var l=this.createStyledElement('img','','propagate-icon');r.appendChild(l),l.src='money_fund'===s?y.amc.path+'alipay_msp_moneyfund_logo':'ant_credit_pay'===s?y.amc.path+'alipay_msp_pcredit_logo':d||y.amc.path+'alipay_msp_icon_notice'}var u=e.action?r:null,p=null;if(i){p=this.createStyledElement('label','','info-text-propagate font-color-primary amc-flex-1 amc-ellipsis'),r.appendChild(p);var m=this.createStyledElement('div','','item-btn amc-flex-center');r.appendChild(m);var g=this.createStyledElement('label','','item-btn-text amc-ellipsis');m.appendChild(g),g.innerText=i,m.onmousedown=function(){m.style.backgroundColor='#108ee9',g.style.color='#fff'},m.onmouseup=function(){m.style.backgroundColor='#fff',g.style.color='#108ee9'},u=m}else p=this.createStyledElement('label','','double-line-for-propagate info-text-propagate font-color-primary amc-flex-1 amc-ellipsis'),r.appendChild(p),r.className='item-box-s-for-propagate amc-align-center amc-flex-1';p&&(p.innerText=e.title);var f=C,h=x(e.spmObj);for(var v in h)f[v]=h[v];f.spm&&y.amc.fn.spmExposure(f.spm,f,!1),u&&(u.onclick=function(){e.param&&e.param.channelIndex&&_.onChannelChanage([e.param.channelIndex])})}},t.prototype.createDynamicFrame=function(e){var r=this;if(!y.amc.fn.sdkGreaterThanOrEqual('10.8.38'))return!0;y.amc.fn.logAction('start|deduct-channal','snippet'),s.BNFramePlugin.create(e,this.getViewInComponentById('bnframeBox'),function(e){y.amc.fn.logAction('done|deduct-channal','snippet'),e&&e.mqpToken&&(r.channelSelectBnframeToken=e.mqpToken)}),s.BNFrameChannelOuter.onInnerEvent('ON_SELECT_CHANNEL',function(e,t){y.amc.fn.logAction('onEvent|deduct-channal','snippet');var n=t&&t.mqpToken;if(b.deductChannelSettingBox&&n===r.channelSelectBnframeToken){var o=!1,i=new Array;e.selectedChannels.forEach(function(e,t){i[t]=e.channelIndex,(!_.currentChannelList||_.currentChannelList.indexOf(e.channelIndex)<0)&&(o=!0)}),(o||_.currentChannelList.length!==i.length)&&_.onChannelChanage(i)}})},t.prototype.onChannelChanage=function(e){var t={initPageToken:b.initPageToken,curChannelIndexList:e,channelContextId:_.channelContextId};setTimeout(function(){document.invoke('showLoading')},20),document.invoke('rpc',{operationType:'alipay.mdeduct.sdk.sign.adjust',requestData:t},function(o){setTimeout(function(){if(document.invoke('hideLoading'),o.success){if(b.deductChannelSettingBox&&'PAGE'===b.deductChannelSettingBox.dataRefreshType){var e={action:{}};e.action.name='loc:bnvb',e.action.params={tplid:'QUICKPAY@mdeduct-sign-flex',tpl:'',data:o},document.submit(e)}else if(b.deductChannelSettingBox&&'CHANNEL'===b.deductChannelSettingBox.dataRefreshType&&_.vueModel.data.settingBox&&o.deductChannelSettingBox){var t=o.deductChannelSettingBox.descr;_.vueModel.data.settingBox.descr=t;var n=x(o.operationData);_.createPropagate(n,!0),o.deductChannelSettingBox&&o.deductChannelSettingBox.curChannelIndexList&&(_.currentChannelList=o.deductChannelSettingBox.curChannelIndexList,_.channelContextId=o.deductChannelSettingBox.channelContextId)}}else document.toast({text:'网络开小差，请稍后重试',type:'none'},function(){})},10)})},t.getComponentCSSRules=function(){return{textarea:'_MdeductSignPage_1v77-t-textarea',body:'_MdeductSignPage_1v77-t-body','.main-body':'_MdeductSignPage_1v77-c-main-body','.de-1px-line':'_MdeductSignPage_1v77-c-de-1px-line','.no-top-padding':'_MdeductSignPage_1v77-c-no-top-padding','.cell-box':'_MdeductSignPage_1v77-c-cell-box','.flex-row':'_MdeductSignPage_1v77-c-flex-row','.algin-item-start':'_MdeductSignPage_1v77-c-algin-item-start','.flex-between':'_MdeductSignPage_1v77-c-flex-between','.flex-justify-start':'_MdeductSignPage_1v77-c-flex-justify-start','.checkbox-ios':'_MdeductSignPage_1v77-c-checkbox-ios','.checkbox-android':'_MdeductSignPage_1v77-c-checkbox-android','.order-amount':'_MdeductSignPage_1v77-c-order-amount','.merchant-logo':'_MdeductSignPage_1v77-c-merchant-logo','.product-name':'_MdeductSignPage_1v77-c-product-name','.dialog-info-icon':'_MdeductSignPage_1v77-c-dialog-info-icon','.de-product-desc':'_MdeductSignPage_1v77-c-de-product-desc','.deduct-label-list':'_MdeductSignPage_1v77-c-deduct-label-list','.deduct-label-item':'_MdeductSignPage_1v77-c-deduct-label-item','.deduct-label':'_MdeductSignPage_1v77-c-deduct-label','.deduct-label-content':'_MdeductSignPage_1v77-c-deduct-label-content','.deduct-switch':'_MdeductSignPage_1v77-c-deduct-switch','.de-agreement-list':'_MdeductSignPage_1v77-c-de-agreement-list','.de-agreement-checkbox':'_MdeductSignPage_1v77-c-de-agreement-checkbox','.de-agreement-links':'_MdeductSignPage_1v77-c-de-agreement-links','.de-info-dialog-item':'_MdeductSignPage_1v77-c-de-info-dialog-item','.de-dialog-prefix':'_MdeductSignPage_1v77-c-de-dialog-prefix','.de-dialog-content':'_MdeductSignPage_1v77-c-de-dialog-content','.btn-primary-wrapper':'_MdeductSignPage_1v77-c-btn-primary-wrapper','.btn-primary':'_MdeductSignPage_1v77-c-btn-primary','.de-dialog-prefix-logo':'_MdeductSignPage_1v77-c-de-dialog-prefix-logo','.de-toast':'_MdeductSignPage_1v77-c-de-toast','.deduct-pay-setting':'_MdeductSignPage_1v77-c-deduct-pay-setting','.de-toast-label':'_MdeductSignPage_1v77-c-de-toast-label','.slogan-box':'_MdeductSignPage_1v77-c-slogan-box','.slogan-img':'_MdeductSignPage_1v77-c-slogan-img','.item-box-for-propagate':'_MdeductSignPage_1v77-c-item-box-for-propagate','.propagate-arrow':'_MdeductSignPage_1v77-c-propagate-arrow','.propagate-icon':'_MdeductSignPage_1v77-c-propagate-icon','.info-text-propagate':'_MdeductSignPage_1v77-c-info-text-propagate','.font-color-primary':'_MdeductSignPage_1v77-c-font-color-primary','.item-btn':'_MdeductSignPage_1v77-c-item-btn','.item-btn-text':'_MdeductSignPage_1v77-c-item-btn-text','.double-line-for-propagate':'_MdeductSignPage_1v77-c-double-line-for-propagate','.item-box-s-for-propagate':'_MdeductSignPage_1v77-c-item-box-s-for-propagate','.bottom-container':'_MdeductSignPage_1v77-c-bottom-container'}},t.getComponentJson=function(){return{_c:'amc-body',_t:'div',_cd:[{'sp-view-id':'mainBody',_c:'amc-main _MdeductSignPage_1v77-c-main-body amc-hidden',_t:'div',_cd:[{_c:'amc-v-box amc-bg-white amc-pd amc-align-center',_t:'div',_cd:[{'sp-view-id':'merchant-logo',_c:'_MdeductSignPage_1v77-c-merchant-logo',_t:'img'},{'sp-view-id':'de-product-name',_c:'_MdeductSignPage_1v77-c-product-name',_t:'label'},{'sp-view-id':'de-product-desc',_c:'_MdeductSignPage_1v77-c-de-product-desc',_t:'label'}]},{'sp-view-id':'merchant-label',_c:'_MdeductSignPage_1v77-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{_c:'amc-1px-line',_t:'div'},{'sp-view-id':'deduct-label-list',_c:'_MdeductSignPage_1v77-c-deduct-label-list amc-v-box amc-bg-white',_t:'div'},{_c:'_MdeductSignPage_1v77-c-de-1px-line',_t:'div'},{'sp-view-id':'deduct-pay-setting',_c:'amc-v-box amc-bg-white _MdeductSignPage_1v77-c-deduct-label-list _MdeductSignPage_1v77-c-deduct-pay-setting','v-if-cal':'@{settingBoxExist}',_t:'div',_cd:[{_c:'_MdeductSignPage_1v77-c-flex-row _MdeductSignPage_1v77-c-flex-between _MdeductSignPage_1v77-c-algin-item-start','v-click':'@{settingBoxClick}',_t:'div',_cd:[{_c:'deduct-pay-setting-label','v-text-cal':'@{settingBoxTip}',_t:'label'},{_c:'_MdeductSignPage_1v77-c-flex-row flex-end algin-item-center',_t:'div',_cd:[{_c:'deduct-label-content-light','v-text-cal':'@{settingBoxDescr}',_t:'label'},{_c:'amc-margin-l-xs','v-src-cal':'@{settingBoxTailImg}',_t:'img'}]}]}]},{'sp-view-id':'deduct-pay-setting-line',_c:'_MdeductSignPage_1v77-c-de-1px-line','v-if-cal':'@{settingBoxExist}',_t:'div'},{'sp-view-id':'anchoredBox',_c:'amc-v-box amc-hidden',_t:'div'},{'sp-view-id':'bnframeBox',_c:'amc-v-box amc-hidden',_t:'div'},{'sp-view-id':'de-agreement-list',_c:'_MdeductSignPage_1v77-c-de-agreement-list',_t:'div',_cd:[{'sp-view-id':'de-agreement-confirm-checkbox',_c:'_MdeductSignPage_1v77-c-de-agreement-checkbox amc-hidden',_t:'div',_cd:[{_y:'checkbox',_c:'amc-checkbox amc-margin-r-xs','sp-view-id':'de-agree-checkbox',onclick:'toggleAgreeCheckbox();',_t:'input'}]},{'sp-view-id':'de-agreement-links',_c:'_MdeductSignPage_1v77-c-de-agreement-links',_t:'label'}]},{_c:'amc-abs-space-v-m',_t:'div'},{_c:'amc-abs-space-v-m',_t:'div'},{_c:'_MdeductSignPage_1v77-c-btn-primary-wrapper amc-align-center amc-justify-center amc-bg-blue',_t:'div',_cd:[{'sp-view-id':'btn-loading',src:'indicatior',_c:'amc-loading-img amc-text-white-clolor amc-hidden',_t:'img'},{'sp-view-id':'submit-btn',_c:'amc-btn-primary _MdeductSignPage_1v77-c-btn-primary',_t:'button',_x:'确认开通'}]},{_c:'_MdeductSignPage_1v77-c-bottom-container',_t:'div'},{_c:'_MdeductSignPage_1v77-c-slogan-box amc-flex-center amc-hidden','sp-view-id':'sloganBox',_t:'div',_cd:[{_c:'_MdeductSignPage_1v77-c-slogan-img','sp-view-id':'sloganImg',_t:'img'}]},{_c:'amc-iphone-x-pd-b',_t:'div'}]},{'sp-view-id':'de-toast',_c:'_MdeductSignPage_1v77-c-de-toast amc-hidden',_t:'div',_cd:[{'sp-view-id':'de-toast-label',_c:'_MdeductSignPage_1v77-c-de-toast-label amc-hidden',_t:'label'}]}]}},t.componentName='MdeductSignPage',t.componentHashName='MdeductSignPage_1v77',t}(a.BNComponent);t.MdeductSignPage=l;var u=null;function p(){y.amc.fn.spmClick('a259.b13454.c32478.d64255',C),y.amc.fn.spmExposure('a259.b13454.c32629',C,!1),document.confirm({title:'',message:'{{confirm_exit}}',okButton:'{{confirm_btn}}',cancelButton:'{{cancel}}'},function(e){e.ok?(y.amc.fn.spmClick('a259.b13454.c32629.d64618',C),y.amc.fn.spmPageDestroy('a259.b13454',C),m({cancel:!0})):y.amc.fn.spmClick('a259.b13454.c32629.d64619',C)})}function m(e){document.invoke('setResult',e,function(e){}),y.amc.fn.exit()}function g(o,e,i,r){if(o){var t={operationType:o,requestData:e};setTimeout(function(){document.invoke('rpc',t,function(e){var t,n;!(n=e=e||{})||!n.success&&'SUCCESS'!==n.resultCode&&'success'!==n.resultCode?(d(o,e),(t=(t=e)||{}).errorMsg||('10'==t.error||'11'==t.error?h('网络不给力','fail'):t.error&&h('网络开小差，请稍后重试','none')),r&&r(e)):i&&i(e)})},10)}}window.loadImageHelper=function(e,t,n){if(!u){u={};var o=document.onImgLoaded;document.onImgLoaded=function(e,t){var n=u[t];n&&n.callback?n.callback(e,t,n.img):o&&o(e,t),delete u[t]}}u[t]={callback:n,img:e},e.src=t};var f=!(window.onload=function(){var e=b;_=new l,r=_;var t=y.amc.fn.getNav(y.amc.res.navBack,y.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'':'{{return}}',e.pageTitle,'','',p);document.body.style.height=y.amc.isAndroid?window.innerHeight:y.amc.specs.bodyHeight,document.body.appendChild(t),window.onKeyDown=function(){4==event.which&&p()},_.mountTo(document.body)});function x(e){if(y.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}}function h(e,t){if(e){if(y.amc.isAndroid){if(f)return;setTimeout(function(){f=!1},2e3),f=!0}document.toast({text:e,type:t||'none'},function(){})}}},function(e,t,n){'use strict';var a=this&&this.__assign||function(){return(a=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,'__esModule',{value:!0});var C=n(1),x=n(2),w=n(3),S=n(9),o=function(){function e(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=x.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return e.getComponentCSSRules=function(){throw new Error('E0100')},e.getComponentJson=function(){throw new Error('E0101')},e.prototype.mountTo=function(e,t){if(e){var n=this._acquireRootView();n?(t?e.insertBefore(n,t):e.appendChild(n),this._triggerOnMounted()):C.logger.e('Cmp#mT','E0103 '+n)}else C.logger.e('Cmp#mT','E0102 '+e)},Object.defineProperty(e.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),e.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},e.prototype.getMountedParentView=function(){var e=this.getMountedRootView();return e?e.parentNode:null},e.prototype.getSubComponentById=function(e,t){var n=this.debugName+'#SCById',o=this._subComponentsMap[e];if(!o)return null;var i='',r='';try{i=o.constructor.componentName,r=t.componentName}catch(e){C.logger.e(n,'E0104 '+e)}return i&&i===r?o:(C.logger.e(n,'E0105 '+i+', '+r),null)},e.prototype.getViewInComponentById=function(e){return this._viewsIdMap[e]},e.prototype.getComponentId=function(){return this._componentId},e.prototype.createStyledElement=function(e,t,n){var o=document.createElement(e);if(o)return e&&(o.className+=' '+this._css(e,2)),n&&(o.className+=' '+this._csses(n,1)),t&&(o.className+=' '+this._css('#'+t,2)),o},e.prototype.applyStyleTo=function(e,t){e&&(e.className+=' '+this._csses(t,1))},e.prototype.css=function(e){return this._css(e,0)},e.prototype.csses=function(e){return this._csses(e,0)},e.prototype._csses=function(e,t){var n=this;return e.split(' ').map(function(e){return n._css(e,t)}).join(' ')},e.prototype._css=function(e,t){if(!e)return'';var n=this._componentCSSRules;if(!n)return e;switch(e.charAt(0)){case'#':case'.':return n[e]||e;default:switch(t){case 0:return n['.'+e]||n[e]||e;case 1:return n['.'+e]||e;case 2:default:return e}}},e.prototype._triggerOnMounted=function(){new S.Observer(this.vueModel.data),C.logger.i('','I0106 '+this.debugName);for(var e=0,t=this._subComponents;e<t.length;e++){var n=t[e];n&&n._triggerOnMounted()}this.onMounted&&this.onMounted()},e.prototype._getMethod=function(e){var t=this[e];return t instanceof Function?t:null},e.prototype._acquireComponentJson=function(){var e=this.debugName+'#acCJ',t=w.ComponentRegistry.getComponentJson(this._componentName);return t?(C.logger.i(e,'I0107'),t):void 0!==this._componentJson?(C.logger.i(e,'I0108'),w.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(C.logger.e(e,'E0109'),null)},e.prototype._acquireRootView=function(){var e=this.debugName+'#acRV';if(this._hasRootViewBuilt)return C.logger.i(e,'I0110'),this._rootView;var t=this._acquireComponentJson();return t?(this._rootView=this._convertJsonToBNNode(t,this.vueModel.data||{}),this._hasRootViewBuilt=!0,C.logger.i(e,'I0112'),this._rootView):(C.logger.e(e,'E0111'),null)},e.prototype._genArrayChildNode=function(e,t,n,o,i){var r=this._convertJsonToBNNode(e,a({},t,{item:n,index:o,arrayName:i}));return r?(r.setAttribute('index',o),r.setAttribute('for_name',i),r):null},e.prototype._convertJsonToBNNode=function(e,d){var l=this,t=this.debugName+'#cJTB';if(void 0===e._t)return null;var u=document.createElement(e._t),p=[];if(void 0!==e._cd)for(var n=function(a){if(a['v-for']||a['v-for-cal']){var e=!a['v-for']&&!!a['v-for-cal'],t=(e?m.vueModel.compute:d)||{},n=S.vueUtils.getObject(e?a['v-for-cal']:a['v-for'],t,e),c=e?S.vueUtils.rmSymbol(a['v-for-cal']):S.vueUtils.rmSymbol(a['v-for']);if(!c||!n)return'continue';for(var o in a['v-for']='',a['v-for-cal']='',n)if(n.hasOwnProperty(o)){var i=m._genArrayChildNode(a,d,n[o],o,c);i&&p.push(i)}var s=document.createElement('div');s&&(s.style.display='none',s.setAttribute('for_end',c),p.push(s),new S.Watcher(c,t,function(e){if(u){S.rmWatchers(c);for(var t=0,n=u.childNodes;t<n.length;t++){var o=n[t];o.getAttribute('for_name')===c&&u.removeChild(o)}if(e)for(var i in e)if(e.hasOwnProperty(i)){var r=l._genArrayChildNode(a,d,e[i],i,c);r&&u.insertBefore(r,s)}}},e).id=d.arrayName)}else{var r=m._convertJsonToBNNode(a,d);if(!r)return'continue';p.push(r)}},m=this,o=0,i=e._cd;o<i.length;o++)n(i[o]);if(!u)return null;d&&d.index&&u.setAttribute('index',d.index);var r=e['bn-component']||e['sp-component'];if(r){C.logger.i(t,'I0113 '+r);var a=w.ComponentRegistry.createComponent(r);if(!a)return C.logger.e(t,'E0114 '+r+', '+a),null;var c=e['bn-component-id']||e['sp-component-id'];return c&&(a._componentId=c),a.onCreated&&a.onCreated(),C.logger.i(t,'I0115 '+a.debugName+', '+c),this._subComponents.push(a),c&&!this._subComponentsMap[c]&&(this._subComponentsMap[c]=a),a._acquireRootView()}var s=e['bn-view-id']||e['sp-view-id'];for(var g in s&&(C.logger.i(t,'I0116 '+s),this._viewsIdMap[s]||(this._viewsIdMap[s]=u)),e._i&&(u.id=e._i),e._c&&(u.className=e._c),e._s&&(u.style.cssText=e._s),e._x&&(u.innerText=e._x),e._y&&(u.type=e._y),e)if(e.hasOwnProperty(g))if(0===g.indexOf('on')){var f=this._getMethod(e[g]);f&&(u[g]=f.bind(this,u))}else if(0===g.indexOf('_'));else if(0===g.indexOf('bn-')||0===g.indexOf('sp-'));else if(x.startsWith(g,'v-')){var h=g.split('-');if(2===h.length||3===h.length){var v=h[1];2===h.length?new S.NodeCompile(d).compile(v,u,e[g],e._t):'cal'===h[2]&&new S.NodeCompile(this.vueModel.compute,!0).compile(v,u,e[g],e._t)}else u[g]=e[g]}else u[g]=e[g];for(var _=0,y=p;_<y.length;_++){var b=y[_];u.appendChild(b)}return u},e.componentName='',e.componentHTML='',e.componentCSS='',e.componentHashName='',e}();t.BNComponent=o},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var r,a=n(2),c=n(0);t.rmWatchers=function(t){r=r.filter(function(e){return e.id!==t})};var s=function(){function e(e,t,n,o){if(this.id='',this.lazy=!1,t&&'object'==typeof t){if(this.lazy=o,this.callback=n,a.startsWith(e,'item')&&t.arrayName&&t.index){var i=e.replace('item','');this.expression=t.arrayName+'.'+t.index,i&&(this.expression+=i)}else this.expression=e;this.data=t,this.value=l.getVal(e,t,this.lazy),r||(r=[]),r.push(this)}}return e.prototype.update=function(){if(this.data&&this.expression&&this.callback){var e=l.getVal(this.expression,this.data,this.lazy),t=this.value;l.equals(e,t)||(this.value=e,this.callback(e))}},e}();t.Watcher=s;var o=function(){function e(e){this.observe(e)}return e.prototype.observe=function(t){var n=this;t&&'object'==typeof t&&Object.keys(t).forEach(function(e){try{n.defineReactive(t,e,t[e]),n.observe(t[e])}catch(e){}})},e.prototype.defineReactive=function(e,t,n){var o=this;Object.defineProperty(e,t,{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){l.equals(e,n)||(n=e,o.observe(e),r&&r.forEach(function(e){e.update()}))}})},e}();t.Observer=o;var i=function(){function e(e,t){void 0===t&&(t=!1),this.data=e||{},this.lazy=t}return e.prototype.compile=function(n,e,t,o){var i=this;if(e)switch(n){case'text':this.labelProcess(e,t,function(e,t){e.innerText=void 0===t?'':t});break;case'html':this.labelProcess(e,t,function(e,t){e.innerHtml=void 0===t?'':t});break;case'class':this.labelProcess(e,t,function(e,t){var n=e.className,o=(n=n.replace(t,'').replace(/\\s$/,''))&&String(t)?' ':'';e.className=n+o+t});break;case'model':this.eventProcess(e,t,function(e,t){e.value=t}),'input'===o?e.oninput=function(){l.setTextVal(t,e.value,i.data)}:'switch'===o&&(e.onchange=function(e){l.setTextVal(t,e||'off',i.data)});break;case'if':this.eventProcess(e,t,function(e,t){!0===t?(e.style.display='flex',d.process(e,function(e){c.amc.fn.spmExposure(e.spmId,e.param4Map,e.doNotResume)})):e.style.display='none'});break;case'spm':this.labelProcess(e,t,function(e,t){e.setAttribute('spm',void 0===t?'':t)});break;case'click':this.eventProcess(e,t,function(e,t){l.isFunction(t)?e.onclick=function(){t(e),d.process(e,function(e){c.amc.fn.spmClick(e.spmId,e.param4Map)})}:e.onclick=function(){}});break;default:this.labelProcess(e,t,function(e,t){e[n]=void 0===t?'':t})}},e.prototype.labelProcess=function(n,o,i){var r=this,e=o.match(/@\\{([^}]+)\\}/g),t=l.getTextVal(o,this.data,this.lazy);e&&e.forEach(function(e){var t=/@\\{([^}]+)\\}/g.exec(e);t&&1<t.length&&(new s(t[1],r.data,function(e){i(n,l.getTextVal(o,r.data,r.lazy))},r.lazy).id=r.data.arrayName)}),i(n,t)},e.prototype.eventProcess=function(t,e,n){var o=/@\\{([^}]+)\\}/g.exec(e),i=l.getObject(e,this.data,this.lazy);o&&1<o.length&&(new s(o[1],this.data,function(e){n(t,e)},this.lazy).id=this.data.arrayName),n(t,i)},e}();t.NodeCompile=i;var d=function(){function e(){}return e.process=function(e,t){var n=e.getAttribute('spm');if(n)try{var o=JSON.parse(n);o&&o.spmId&&t(o)}catch(e){}},e}(),l=function(){function c(){}return c.item2ArrayIndex=function(e,t){var n=e;if(a.startsWith(e,'item')&&t.arrayName&&t.index){var o=e.replace('item','');n=t.arrayName+'.'+t.index,o&&(n+=o)}return n},c.getVal=function(e,t,n){if(e){var o=e.split('.').reduce(function(e,t){return e[t]},t);return n?c.isFunction(o)?o():void 0:o}},c.getTextVal=function(e,i,r){var a=this;return e.replace(/@\\{([^}]+)\\}/g,function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(r)e=a.getVal(t[1],i,r);else{var o=c.item2ArrayIndex(t[1],i);e=a.getVal(o,i,!1)}return void 0===e?'':e})},c.getObject=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);if(o&&1<o.length)return this.getVal(o[1],t,n)},c.rmSymbol=function(e){var t=/@\\{([^}]+)\\}/g.exec(e);return t&&1<t.length?t[1]:''},c.setVal=function(e,o,t){var i=e.split('.');return i.reduce(function(e,t,n){return n===i.length-1?e[t]=o:e[t]},t)},c.setTextVal=function(e,t,n){var o=/@\\{([^}]+)\\}/g.exec(e);o&&1<o.length&&this.setVal(o[1],t,n)},c.equals=function(e,t){return this.eq(e,t,void 0,void 0)},c.eq=function(e,t,n,o){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;var i=toString.call(e);if(i!==toString.call(t))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+e==''+t;case'[object Number]':return+e!=+e?+t!=+t:0==+e?1/+e==1/t:+e==+t;case'[object Date]':case'[object Boolean]':return+e==+t}var r='[object Array]'===i;if(!r){if('object'!=typeof e||'object'!=typeof t)return!1;var a=e.constructor,c=t.constructor;if(a!==c&&!(this.isFunction(a)&&a instanceof a&&this.isFunction(c)&&c instanceof c)&&'constructor'in e&&'constructor'in t)return!1}o=o||[];for(var s=(n=n||[]).length;s--;)if(n[s]===e)return o[s]===t;if(n.push(e),o.push(t),r){if((s=e.length)!==t.length)return!1;for(;s--;)if(!this.eq(e[s],t[s],n,o))return!1}else{var d=Object.keys(e),l=void 0;if(s=d.length,Object.keys(t).length!==s)return!1;for(;s--;)if(l=d[s],!t.hasOwnProperty(l)||!this.eq(e[l],t[l],n,o))return!1}return n.pop(),o.pop(),!0},c.isFunction=function(e){return'function'==typeof e||!1},c}();t.vueUtils=l},function(e,t,n){'use strict';var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,'__esModule',{value:!0});var r=function(){function r(){this.enabled=!0}return r.fmtLine=function(e,t,n,o){var i='';return o&&(i=o instanceof Error?'- '+o.name+': '+o.message+' - '+o.stack:'- '+o),'['+e+']['+r.fmtTime()+']['+t+']'+n+' '+i},r.fmtTime=function(){var e=new Date;return e.getHours()+':'+e.getMinutes()+':'+e.getSeconds()+'.'+e.getMilliseconds()},r.prototype.enable=function(){this.enabled=!0},r.prototype.disable=function(){this.enabled=!1},r}();t.Logger=r,t.logger=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.e=function(e,t,n){},t.prototype.i=function(e,t,n){},t}(r))},function(e,n,t){'use strict';function o(e){for(var t in e)n.hasOwnProperty(t)||(n[t]=e[t])}Object.defineProperty(n,'__esModule',{value:!0}),o(t(4)),o(t(5))},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o,a=n(0),i=n(13);function r(e,t,n){var o=t;a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&i.copyObj(n,o.style)}function c(e,t,n){if(t&&n){var o=t;if(a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o)for(var i in n)n.hasOwnProperty(i)&&(o[i]=n[i])}}t.modifyElementStyle=r,t.modifyElementAttribute=c,t.modifyElementClass=function(e,t,n,o){var i=t;a.amc.fn.isString(t)&&(i=e.getViewInComponentById(t)),i&&(o?i.className+=n:i.className=n)},t.visibleElement=function(e,t,n){var o;void 0===n&&(n=!0),t&&(o=a.amc.fn.isString(t)?e.getViewInComponentById(t):t)&&(n?a.amc.fn.show(o):a.amc.fn.hide(o))},t.modifyElementCSS=function(e,t,n){if(t){var o=t;a.amc.fn.isString(t)&&(o=e.getViewInComponentById(t)),o&&n&&(o.style.cssText=n)}},t.createEmbedViPlugin=function(e,t,n,o){var i;if(a.amc.isAndroid)i=document.createElement('embed',t,function(){});else for(var r in i=document.createElement('embed'),t)t.hasOwnProperty(r)&&(i[r]=t[r]);return n&&(i.className=n),o?e.insertBefore(i,o):e.appendChild(i),i},t.getThemeColor=(o='',function(){return o||(o=a.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'#1677FF':'#108EE9'),o}),window.DomUtils={modifyElementStyle:r,modifyElementAttribute:c,getThemeColor:t.getThemeColor}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var a=n(0);t.mergeObject=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n={};if(e&&e.length)for(var o=0;o<e.length;o++){var i=e[o];if(a.amc.fn.isObject(i))for(var r in i)i.hasOwnProperty(r)&&(n[r]=i[r])}return n},t.isPreRender=function(e){return e&&(e.local&&e.local.isPrerender||e.rpcData&&e.rpcData.isPrerender)},t.copyObj=function(e,t){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},t.doNothing=function(){},t.tryJSONParse=function(e){if(a.amc.fn.isObject(e))return e;try{return JSON.parse(e)}catch(e){return{}}},t.checkEmptyObj=function(e){return a.amc.fn.isString(e)?0===e.length:!(e&&0!==Object.keys(e).length)},t.substrWithFontWidth=function(e,t,n){if(!e)return e;for(var o='',i=0,r=e.length,a=0;a<r;a++){var c=n?e[r-a-1]:e[a];if(/^[A-Za-z0-9\\(\\)]*$/.test(c)?i+=.45:i++,o+=c,t-1<i)break}return o},t.calculateFontWidth=function(e){if(!e)return 0;for(var t=0,n=/^[A-Za-z0-9\\.\\(\\)]*$/,o=0;o<e.length;o++)n.test(e[o])?t+=.45:t++;return Math.round(t)},t.deepCopy=function e(t){if(null==t||'object'!=typeof t)return t;var n;if(t instanceof Date)return(n=new Date).setTime(t.getTime()),n;if(t instanceof Array){n=[];for(var o=0,i=t.length;o<i;o++)n[o]=e(t[o]);return n}if(t instanceof Object){for(var r in n={},t)t.hasOwnProperty(r)&&(n[r]=e(t[r]));return n}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},t.getConfig=function(e,t){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:e},function(e){t(e.available)})},20)},t.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},t.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)}}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-body", "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onload()"}], "tag": "html"}, "publishVersion": "150924", "name": "mdeduct-sign-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0137", "tplId": "QUICKPAY@mdeduct-sign-flex", "tplVersion": "5.3.7"}