{"time": "0002", "tplId": "QUICK<PERSON><PERSON>@open-pwd-check-flex", "publishVersion": "150924", "tag": "QUICKPAY", "name": "open-pwd-check-flex", "data": {"tag": "html", "children": [{"tag": "head", "children": [{"tag": "meta", "src": "AlipaySDK.bundle/amc.i18n", "type": "i18n"}, {"tag": "link", "rel": "stylesheet", "href": "AlipaySDK.bundle/amc.css"}, {"tag": "script", "src": "AlipaySDK.bundle/amc.js"}, {"tag": "script", "children": [{"text": "amc.fn.spmPageCreate(\"a283.b4038\");var rpc=amc.rpcData;var getTag=amc.fn.getById;var hide=amc.fn.hide;var show=amc.fn.show;var isIOS83=amc.isIOS&&(amc.fn.versionCompare&&amc.fn.versionCompare(amc.osVersion,\"8.1.3\")>=0);var fingerPay=rpc.fingerPay||false;if(rpc.AndroidFingerStatus===\"1\"){fingerPay=false}if(fingerPay&&isIOS83){var fpCancelAction=rpc.pageSwitch&&rpc.pageSwitch.fpCancelAction}var loadingBlueGif=amc.path+\"alipay_msp_loading_blue.gif\";var successBlueNorepeatGif=amc.path+\"alipay_msp_success_blue.gif\";var nextAction=rpc.nextAction||\"/pwd/validate\";var thirdPay=fingerPay||rpc.braceletPay||rpc.wearablePay;var wearablePayNewMode=!!(rpc.nWmode);var spasswordPay=rpc.spasswordPay;var successTxt=rpc.successText||\"{{pay_ok}}\";var payTxt=rpc.doingText||\"{{paying}}\";var btnText=rpc.btnText||\"{{pay}}\";var showQuickMode=!!(rpc.oneDayOnePwdShow&&rpc.oneDayOnePwdTips);var quickModeTip=rpc.oneDayOnePwdTips;var quickModeChecked=!!rpc.oneDayOnePwdChecked;var gifTag;var backAction=\"\";var braceletPayTip=null;function init(){try{initUI();if(amc.fn.logPageInit){amc.fn.logPageInit(true)}}catch(a){if(amc.fn.logPageInit){amc.fn.logPageInit()}}}function initUI(){var b=amc.res.close;if(rpc.backAction){backAction=rpc.backAction}else{if(rpc.bizType===\"deposit\"||rpc.payOrder){backAction=\"loc:back\";b=amc.res.arrowLeft}else{backAction=\"loc:exit\"}}var d=amc.fn.iNav(b,\"{{input_pwd}}\",null,null,onBack,null);getTag(\"body\").insertBefore(d,getTag(\"iLine\"));amc.fn.spmExposure(\"a283.b4038.c9698.d17397\");gifTag=getTag(\"pwdLoadGif\");getTag(\"loadTxt\").innerText=payTxt;getTag(\"payBtn\").innerText=btnText;if(showQuickMode){show(\"quickMode\");hide(\"leftMargin\");getTag(\"quickModeTip\").innerText=quickModeTip;getTag(\"quickModeCheckbox\").checked=quickModeChecked}if(thirdPay){if(amc.isIOS){amc.fn.showLoading(true,true)}getTag(\"body\").style.opacity=\"0\";var c={action:{name:rpc.authAction}};var a={nativeValidate:true};if(wearablePayNewMode){c.action.loadtxt=\"\";a.newMode=true}c.param=a;document.asyncSubmit(c,function(f){var e=f.status&&f.status.substr(-2);if(e===\"04\"||e===\"05\"){document.submit({action:{name:\"loc:continue;loc:swload('time=0', 'status=fail')\"}});braceletPayTip=f.tip;thirdPay=false;getTag(\"body\").style.opacity=\"0.95\";checkPwd()}else{if(f.status===\"0009\"){if(fpCancelAction===\"1\"){getTag(\"body\").style.opacity=\"1\";checkPwd()}else{if(fpCancelAction===\"2\"){alert()}else{amc.fn.exit()}}}else{if(f.status===\"0003\"){document.submit({action:{name:\"loc:continue;loc:swload('loadtxt=\"+payTxt+\"', 'status=loading')\"}})}else{if(f.status===\"0001\"){document.submit({action:{name:\"loc:continue;loc:swload('time=2500', 'loadtxt=\"+successTxt+\"', 'status=success')\"}})}else{if(f.status===\"0002\"){document.submit({action:{name:\"loc:continue;loc:swload('time=0')\"}})}}}}}})}else{checkPwd(amc.isIOS)}amc.fn.spmExposure(\"a283.b4038.c9698.d17395\")}document.viewDidAppear=function(){if(!thirdPay&&amc.isIOS){showKeyboard()}};function showKeyboard(){if(spasswordPay){getTag(\"spwd\").focus()}else{getTag(\"pwd\").focus()}}function alert(){var b=b||\"{{confirm_exit}}\";var a={title:\"\",message:b,okButton:\"{{confirm_btn}}\",cancelButton:\"{{input_pwd_s}}\"};document.confirm(a,function(c){if(c.ok){amc.fn.exit();amc.fn.logCount(\"fp\",\"confirmSwitch\")}else{getTag(\"body\").style.opacity=\"1\";checkPwd();amc.fn.logCount(\"fp\",\"confirmCancel\")}})}function checkPwd(a){show(\"tips\");if(braceletPayTip){getTag(\"tips\").innerText=braceletPayTip;braceletPayTip=null}else{if(rpc.pwdTip){getTag(\"tips\").innerText=rpc.pwdTip}else{hide(\"tips\")}}var c=getTag(\"password\");var b=getTag(\"spassword\");if(spasswordPay){hide(c);show(b)}else{hide(b);show(c)}if(!a){showKeyboard()}}function submitPay(b,a){hide(\"pwdBox\");hide(\"tips\");show(\"pwdLoad\");gifTag.src=loadingBlueGif;b.blur();a.action={};a.action[\"name\"]=nextAction;a.action[\"loadtxt\"]=\"\";if(rpc.biztype!=\"openservice\"&&(nextAction==\"/pwd/validate\"||nextAction==\"/cashier/pay\"||nextAction==\"/cashier/payment\")){a.action[\"neec\"]=\"6004\"}if(showQuickMode){if(getTag(\"quickModeCheckbox\").checked){a.param[\"one_day_one_pwd_checked\"]=\"true\"}else{a.param[\"one_day_one_pwd_checked\"]=\"false\"}}a.action[\"params\"]=rpc.params;document.asyncSubmit(a,function(c){if(amc.fn.sdkGreaterThan&&amc.fn.sdkGreaterThan(\"10.6.1\")){if(c.pageloading===\"0\"&&gifTag.src===loadingBlueGif){hide(\"pwdLoad\")}else{if(c.pageloading===\"1\"){show(\"pwdLoad\")}}}if(c.status===\"0001\"){getTag(\"loadTxt\").innerText=c.successText||successTxt;gifTag.src=successBlueNorepeatGif;show(\"pwdLoad\")}else{if(c.status===\"0002\"){resetPwd(false)}else{if(c.status===\"0000\"){resetPwd(true)}}}})}function resetPwd(a){gifTag.src=\"\";hide(\"pwdLoad\");show(\"pwdBox\");getTag(\"payBtn\").disabled=true;var b=spasswordPay?getTag(\"spwd\"):getTag(\"pwd\");b.value=\"\";if(!spasswordPay){onPwdInput()}if(a||amc.isAndroid){b.focus()}}function onPwdInput(){var a=getTag(\"pwd\");if(a.value.length>0){getTag(\"payBtn\").disabled=false;getTag(\"payBtn\").className=\"enabled-btn-text\";getTag(\"payBtnBox\").className=\"amc-flex-center amc-pwd-check-btn-w enabled-btn btn-height\"}else{getTag(\"payBtn\").disabled=true;getTag(\"payBtn\").className=\"disabled-btn-text\";getTag(\"payBtnBox\").className=\"amc-flex-center amc-pwd-check-btn-w disabled-btn btn-height\"}}function onSpwdInput(){var c=getTag(\"spwd\");if(c.value.length>=6){var b=c.value||\"\";var a={spwd:b};a.encryptType=\"RSA\";var d={};d.param=a;submitPay(c,d)}}function onPasswordPay(){var c=getTag(\"pwd\");if(c.value.length>0){var a={};a.pwd=c.value||\"\";a.encryptType=\"RSA\";var b={};b.param=a;submitPay(c,b)}}function onQuickModeCheck(){getTag(\"quickModeCheckbox\").checked=!getTag(\"quickModeCheckbox\").checked}function onBack(){amc.fn.spmClick(\"a283.b4038.c9698.d17397\");amc.fn.spmPageDestroy(\"a283.b4038\");document.submit({action:{name:backAction}})}function forgetPwd(){amc.fn.spmClick(\"a283.b4038.c9698.d17395\");var b=\"\";if(rpc.logon_id){b=\"&loginId=\"+rpc.logon_id}var a=\"alipays://platformapi/startApp?appId=20000013&pwdType=phonePassword\"+b+\"&bizScene=mobilecashier_sdk_pay\";document.submit({action:{name:\"loc:openurl('\"+a+\"','0')\"}})}function onKeyDown(){if(event.which==4){onBack()}};", "tag": "text"}]}, {"tag": "style", "children": [{"text": ".stretch-self {\n    align-self: stretch;\n}\n.flex-3 {\n    flex: 3.0;\n}\n.pd-lr {\n    padding-left: 15px;\n    padding-right: 15px;\n}\n\n.pd-t {\n    padding-top: 12px;\n}\n.pd-t-l {\n    padding-top: 20px;\n}\n.pd-t-s {\n    padding-top: 10px;\n}\n.margin-t {\n    margin-top:15px;\n}\n.margin-r-xs {\n    margin-right: 5px;\n}\n\n.font-m {\n    font-size: 15px;\n}\n\n.font-xxl {\n    font-size:18px;\n}\n\n.font-l-white {\n    font-size: 17px;\n    color:#000;\n}\n\n.input {\n    flex:1.0;\n    border: 0;\n    color: #000;\n    font-size: 16px;\n    padding:0px;\n    white-space: nowrap;\n}\n\n.amc-i-nav-m-box {\n    align-items: center;\n    justify-content: center;\n    flex:3.0;\n}\n\n.disabled-btn-text {\n    color: #bbb;\n}\n\n.enabled-btn-text {\n    color: #fff;\n}\n\n.enabled-btn {\n    margin-left: 15px;\n    background-color: #108ee9;\n}\n\n.disabled-btn {\n    margin-left: 15px;\n    background-color: #ddd;\n}\n\n.btn-height {\n    height: 46px;\n}\n\n.pwd-box {\n    border: 1PX #979797 solid;\n    border-radius: 4px;\n    height: 47px;\n    min-height: 47px;\n    max-height: 47px;\n    background-color: #fff;\n    overflow: hidden;\n    flex: 1.0;\n    align-items: center;\n}", "tag": "text"}]}]}, {"id": "body", "onload": "init()", "tag": "body", "children": [{"id": "iLine", "tag": "div", "css": "amc-1px-title-line"}, {"id": "mainBody", "tag": "div", "children": [{}, {"id": "tipsBox", "tag": "div", "children": [{"id": "tips", "tag": "label", "css": "amc-flex-1 font-m amc-ellipsis-2-line"}], "css": "amc-flex-center pd-lr pd-t"}, {"id": "pwdBox", "tag": "div", "children": [{}, {"id": "password", "tag": "div", "children": [{"tag": "div", "children": [{}, {"tag": "div", "children": [{"tag": "div", "css": "amc-abs-space-h-m"}, {"id": "pwd", "placeholder": "{{alipaypwd}}", "tag": "embed", "value": "", "src": "paypwd", "type": "MQPPayPwdView", "css": "input amc-pwd-height", "oninput": "onPwdInput()"}], "css": "amc-cell-m-box"}, {}, {"id": "payBtnBox", "tag": "div", "children": [{"id": "payBtn", "tag": "label", "children": [{"text": "{{pay}}", "tag": "text"}], "css": "amc-ellipsis amc-text-center amc-flex-1 disabled-btn-text font-xxl", "disabled": "disabled"}], "onclick": "onPasswordPay()", "css": "amc-flex-center amc-pwd-check-btn-w disabled-btn btn-height"}], "css": "pwd-box"}], "css": "pd-t"}, {}, {"id": "spassword", "tag": "div", "children": [{"id": "spwd", "tag": "embed", "value": "", "src": "payspwd", "type": "MQPPayPwdView", "css": "amc-pwd-input amc-spwd-height", "oninput": "onSpwdInput()"}], "css": "amc-spwd-box margin-t amc-hidden"}, {"tag": "div", "children": [{}, {"id": "quickMode", "tag": "div", "children": [{"id": "quickModeCheckbox", "tag": "input", "type": "checkbox", "css": "amc-checkbox margin-r-xs amc-align-self-start"}, {"id": "quickModeTip", "tag": "label", "css": "amc-flex-1 font-m amc-ellipsis-2-line amc-align-self-start"}], "onclick": "onQuickModeCheck()", "css": "amc-align-center flex-3 margin-r-xs pd-t-s amc-hidden"}, {}, {"id": "leftMargin", "tag": "div", "css": "amc-flex-1"}, {}, {"tag": "div", "children": [{"tag": "label", "children": [{"text": "{{forget_pwd}}", "tag": "text"}], "onclick": "forgetPwd()", "css": "amc-text-right amc-theme-color amc-flex-1 amc-ellipsis-2-line font-m"}], "css": "pd-t-s amc-flex-1 amc-pd-l"}]}], "css": "pd-lr amc-v-box"}, {}, {"id": "pwdLoad", "tag": "div", "children": [{"tag": "div", "css": "amc-flex-space-1"}, {"id": "pwdLoadGif", "tag": "embed", "type": "MQPPayGifView", "css": "amc-load-img"}, {"tag": "div", "children": [{"id": "loadTxt", "tag": "label", "children": [{"text": "{{paying}}", "tag": "text"}], "css": "font-l-white amc-flex-1 amc-text-center amc-ellipsis-2-line"}], "css": "pd-t-l stretch-self pd-lr"}, {"tag": "div", "css": "amc-flex-space-2"}], "css": "amc-loading-box"}], "css": "amc-v-box amc-flex-1"}], "onkeydown": "onKeyDown()", "css": "amc-i-body"}]}, "format": "JSON", "tplVersion": "5.3.5"}