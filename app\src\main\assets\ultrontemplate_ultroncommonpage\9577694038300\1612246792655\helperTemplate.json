{"blocks": [{"blockTypeEnum": "COMBINE", "components": [{"bizCode": "com.alibaba.ultron3.wallet.platform", "bizType": 1, "data": {"title": "选择要充值给谁", "relationFriendList": "${sku_script.relationFriendList}", "searchPhoneNumber": "${sku_script.searchPhoneNumber}", "inputCloseShow": "${sku_script.inputCloseShow}", "searchIcon": "${sku_script.searchIcon}"}, "description": "用户列表组件", "event": {"itemClick": [{"type": "adjustState", "fields": {"subType": "userSelect"}}, {"type": "userTrack", "option": 1, "fields": {"page": "Taobao_Wallet_Help", "eventId": "2101", "arg1": "<PERSON><PERSON>_Wallet_friend_click", "args": {"spm": "a2103.b66973795.c1615969262791.d1615969262791", "fromSearch": "${storeState.initialState.fromSearch}"}}}], "onPhoneInputChange": [{"type": "textInputChanged", "fields": {"componentTextInputKey": "wallet_phone_number"}}], "clearInputClick": [{"type": "hideKeyBoard"}, {"type": "adjustState", "fields": {"subType": "clearSearchUser"}}], "searchClick": [{"type": "checkEmpty", "option": 1, "fields": {"value": "${storeState.textInputChanged.wallet_phone_number}", "next": {"empty": [{"type": "toast", "fields": {"message": "请输入手机号"}}, {"type": "adjustState", "fields": {"subType": "clearSearchUser"}}], "notEmpty": [{"type": "request", "option": 1, "fields": {"loadingConfig": {"message": "加载中..."}, "mtopConfig": {"apiMethod": "mtop.com.taobao.wireless.grandet.fetch.friend", "apiVersion": "1.0", "isNeedWua": true, "usePost": false, "unitStrategy": "UNIT_TRADE", "data": {"phoneNum": "${checkEmpty.value}"}}, "next": {"success": [{"type": "checkEmpty", "option": 1, "fields": {"value": "${request.responseData.relationFriendList}", "next": {"empty": [{"type": "toast", "option": 1, "fields": {"message": "${request.responseData.errorMsg}"}}, {"type": "adjustState", "fields": {"subType": "searchUserListWithEmptyResponse"}}], "notEmpty": [{"type": "adjustState", "option": 1, "fields": {"subType": "searchUserList", "payload": "${request.responseData}"}}]}}}], "fail": [{"type": "toast", "option": 1, "fields": {"message": "${request.message}"}}]}}}]}}}, {"type": "userTrack", "fields": {"page": "Taobao_Wallet_Help", "eventId": "2101", "arg1": "Taobao_Wallet_find_click", "args": {"spm": "a2103.b66973795.c1615969262791.d1615969262791"}}}]}, "frontComponentAddress": {"resource": "https://ossgw.alicdn.com/rapid-oss-bucket/1619438954051/wallet_user_list.zip", "h5": "https://ossgw.alicdn.com/rapid-oss-bucket/1618402270656/wallet_user_list.js"}, "frontComponentId": "7036", "frontComponentMD5": {}, "frontComponentName": "wallet_user_list", "frontComponentVersion": "23", "id": 2762, "key": "dinamicx", "name": "walletUserListDefault", "tag": "dinamicx$walletUserListDefault", "templateId": 9577694038300, "type": "dinamicx"}], "description": "一卡", "id": 2236, "name": "walletUserListSection", "position": "walletCardFirst", "window": "wallet"}, {"blockTypeEnum": "COMBINE", "components": [{"bizCode": "com.alibaba.ultron3.wallet.platform", "bizType": 1, "data": {"toShipItems": "${sku_script.commoditiesVO.toShipItems}", "selectedId": "${sku_script.chargeEventVO.selectedId}"}, "description": "选择金额组件", "event": {"itemClick": [{"type": "adjustState", "fields": {"subType": "chargeSelect"}}]}, "frontComponentAddress": {"resource": "https://ossgw.alicdn.com/rapid-oss-bucket/1615194534549/tb_wallet_grid.zip", "h5": "https://ossgw.alicdn.com/rapid-oss-bucket/1614240876357/tb_wallet_grid.js"}, "frontComponentId": "7036", "frontComponentMD5": {}, "frontComponentName": "tb_wallet_grid", "frontComponentVersion": "10", "id": 2763, "key": "dinamicx", "name": "walletGridDefault", "tag": "dinamic$walletGridDefault", "templateId": 9577694038300, "type": "dinamicx"}], "description": "二卡", "id": 2237, "name": "walletGridSection", "position": "walletCardSecond", "window": "wallet"}, {"blockTypeEnum": "COMBINE", "components": [{"bizCode": "com.alibaba.ultron3.wallet.platform", "bizType": 1, "data": {"buttonText": "${sku_script.chargeToOtherOperationVO.buttonText}", "bgGradientStartColor": "${sku_script.chargeToOtherOperationVO.bgGradientStartColor}", "bgGradientEndColor": "${sku_script.chargeToOtherOperationVO.bgGradientEndColor}", "selctedShipItem": "${sku_script.selctedShipItem}", "selctedFriend": "${sku_script.selctedFriend}", "toShipItems": "${sku_script.toShipItemsStr}", "selctedShipItemId": "${sku_script.selctedShipItemId}", "selctedFriendId": "${sku_script.selctedFriendId}", "fromSearch": "${sku_script.fromSearch}", "sign": "${sku_script.commoditiesVO.sign}"}, "description": "提交组件", "event": {"itemClick": [{"type": "dispatchV3", "fields": {"conditions": "${sku_script.chargeEventVO.conditions}", "mtopWithAlipayV3": "${sku_script.mtopWithAlipayV3}", "toastVO1": "${sku_script.toastVO1}", "toastVO2": "${sku_script.toastVO2}"}}, {"type": "userTrack", "fields": {"page": "Taobao_Wallet_Help", "eventId": "2101", "arg1": "Taobao_Wallet_help_click", "args": {"spm": "a2103.b66973795.c1615969262791.d1615969262791"}}}]}, "frontComponentAddress": {"resource": "https://ossgw.alicdn.com/rapid-oss-bucket/1615366589330/tb_wallet_recharge_button.zip", "h5": "https://ossgw.alicdn.com/rapid-oss-bucket/1614241249637/tb_wallet_recharge_button.zip"}, "frontComponentId": "7037", "frontComponentMD5": {}, "frontComponentName": "tb_wallet_recharge_button", "frontComponentVersion": "8", "id": 2764, "key": "dinamicx", "name": "walletRechargeDefault", "tag": "dinamic$walletRechargeDefaultt", "templateId": 9577694038300, "type": "dinamicx"}], "description": "底部卡", "id": 2238, "name": "footerSection", "position": "footer", "window": "wallet"}], "globalStyle": {}, "hierarchy": {"nodes": {"footer": {"position": "footer", "tag": "footer"}, "walletHome": {"data": {"locatorId": "walletHome", "name": "walletHome", "type": "walletHome"}, "position": "字段名", "tag": "walletHome"}, "walletCardFirst": {"position": "walletCardFirst", "style": {"cardGroup": "true"}, "tag": "walletInfo"}, "walletCardSecond": {"position": "walletCardSecond", "style": {"cardGroup": "true"}, "tag": "walletInfo"}, "walletInfoUltron": {"data": {"locatorId": "walletInfoUltron", "name": "walletInfoUltron", "type": "walletInfoUltron"}, "position": "walletInfoUltron", "tag": "walletInfo"}, "wallet": {"position": "", "tag": ""}}, "root": "wallet", "structure": {"walletInfoUltron": ["walletCardFirst", "walletCardSecond"], "walletHome": ["walletInfoUltron"], "wallet": ["walletHome", "footer"]}}, "name": "钱包帮人充模版", "scenario": "wallet", "templateId": 9577694038300, "version": 1612246792655}