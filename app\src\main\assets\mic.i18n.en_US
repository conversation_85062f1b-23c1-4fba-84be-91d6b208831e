{"en_US": {"got_it": "Got it", "changeOtherMethod": "Use a different verification method", "alipay": "Alipay", "deduct_dlg_order": "Deduction priority: Please check \"Alipay-Me-Settings-Payment Settings-Priority\"", "code": "Code", "protocol": "Protocol", "help": "Help", "confirm_open": "Confirm", "pay_ok": "Completed", "user_name_title": "Card holder's name", "sms_help_title": "Code not received", "last_name": "Last Name", "open_pcredit_service": "Opening Huabei Service", "deduct_dlg_title": "Credit password free instruction", "auth_login": "Authorize to Login", "pcredit_user_protocol": "Huabei User Service Agreement", "card_info_tip": "Enter card information", "opening_text": "Opening …", "please_wait": "Please wait", "dotspot": ",", "phone_info": "Your account phone number is the phone number registered with your bank for the card.Any problem please consult your bank. In mainland China, a valid phone number include 11 digits, for other regions, please follow the format“Country code - mobile phone number”.", "repeat_confirm": "Please Reenter to Confirm", "activity_share": "Share", "pcredit_serve_protocol": "\"Huabei Service Agreement\"", "and_so_on": " So on", "phone_validate": "Verify mobile no.", "retry_after_full": "Retry after #val# sec.", "alipay_serve_protocol": "Agreement", "write_card_info": "Card Info.", "phone_sms_full": "This transaction requires SMS code confirmation. Please enter the code received by  <font color='#000'>#val#</font>.", "skip": "<PERSON><PERSON>", "deduct_dlg_inform": "Alipay will remind by client message or SMS message if deducted.", "mail": "Zip Code", "select_account": "Select Account", "save": "Save", "tb": "Taobao", "card_no": "Card No.", "use_alipay_account": "Use Alipay Account ", "paying": "Verifying Payment …", "id_validate": "ID Verification", "return": "Back", "pay_spwd": "Your payment is completed. \nSet up Payment Password\nPlease don’t use your bank card password", "use_new_card": "Pay with new card", "order_info": "<PERSON><PERSON>", "auth": " Authorization", "open_pcredit_ad": "<PERSON> <PERSON><PERSON><PERSON> to Win Coupon", "name_tip_2": "If you don't remember the format of your name used to register this card, please contact your bank. ", "personal_info": "Access Profile / Access Personal Info. ", "email_f_error": "Incorrect Email Address Format", "store_card": "Debit Card", "full_name_format_error": "Please add a space between your first name and last name", "state_province": "State/Province", "alipay_loginpwd": "Login Password", "xx_currency_unit": "RMB #val#", "name": "Name", "add_card": "Add Bank Card", "no_pwd_full": "Payment password free when payment is less than #val#.", "sel_dis": "Change Discount", "forexprod_notice": "Please enter your Real Name and ID Number for Real-Name Authentication  for this trade.", "name_tip_3": "For further help, please call us at <font color='#1677ff'>95188</font>.", "read_agree": "Agree to", "card_type": "Card Type", "cashier_no_tip_5": "For more details, please call us at <font color='#1677ff'>95188</font>. ", "phone_no": "Phone", "set_pwd": "Set Payment Password", "write_phone_no": "Enter Phone Number", "cashier_no_tip_1": "Account security: Account protection, real-time monitoring and emergency blocks provide multiple layers of security. ", "cashier_no_tip_2": "Two-step authentication: A password is needed for each payment, and SMS code verification is required for large amounts. ", "cashier_no_tip_3": "Privacy protection: Strong data encryption keeps user information secure. ", "auth_sth": "Use Alipay Account #val#", "country_area": "Country/Region", "money_limit": "Top-Up Limit Exceeded", "cashier_no_dlg_title": "Security measures", "spwd_for_pay": "6-digit password for payment", "payment_text": "ZhongAn Online P&C Insurance Co Ltd, underwrites your funds with fast and full reimbursement. ", "sms_tip_1": "Check if the mobile number you are using is the same as the one registered in your bank. If not, please contact your bank to update your information.", "confirm_agree": "Agree to", "sel_state_province": "Select State", "card_three_num": "3 last Bank card No.", "input_id_no": "Please Enter ID Number", "sms_tip_3": "Problems with your carrier's network may cause a delay. Please wait a while or try again later.", "use_alipay_auth": "Use Alipay Account Authorization", "input_valid_cvv2": "Please enter the correct Security Code", "phone_tip_4": "For further help, please call us at <font color='#1677ff'>95188</font>.", "address_info": "Access Shipping Info. ", "auth_fingering": "Verifying Touch ID …", "safepwd_verify_complete": "Verified", "pay_tool": "Method", "choose_dis": "Select Discount", "auth_finger_or": "Use fingerprint or", "account_info_tip": "Including account name, etc. except password. ", "sel": "Change", "validity_info": "Expire Date is a string of numbers under the credit card number, with month in front and year in back. ", "last_three_num": "Last 3-digits on Back of Card", "charge_fail": "Unable to complete payment due to insufficient bank card balance. ", "confirm_auth": "Confirm Authorization", "first_name": "First Name", "cvv_text": "CVV2", "input_pay_pwd": "Enter Payment Password", "system_error": "System Error", "agree_protocol": "Agree Protocol", "other_login_way": "Other Login Methods", "wait_retry": "Awaiting <PERSON><PERSON>", "new_card_deposit": "Use New Card for Top-Up", "limit_info_local": "Limits on payment by balance depends on your account level and whether your bank account name matches your Alipay account. When a transaction request is filed, you will be notified if the upper limit is exceeded. Your card payment limit is set by the bank and shared by all shopping, transfer and repayment activities, <font color=\"#ff8208\">unable to increase</font>. If the desired payment is above the limit, please consider other payment methods.", "select_account_full": "Please select below accounts linked to Phone Number #val#", "exit": "Quit", "finger": "Fingerprint", "bill_address": "Billing Address", "deposit_info": "Top-Up Notices", "paypwd": "Payment Password", "confirm_cancel_pay": "Are you sure to cancel payment? ", "email": "Email", "use_phone_login": "Login with Phone Number", "bank_no": "Bank Card Number", "forget_pwd_text": "Forgot Password", "confirm_again": "Reenter to confirm", "input_name_on_card": "Name on Card", "card_max_deposit_full": "You can top up max #val# yuan from this card.", "input_answer": "Enter Your Answer", "sms_tip_4": "If you do not use the mobile number registered in your bank any more, please give your bank a call. ", "open_pcredit": "Open <PERSON><PERSON><PERSON>", "input_email": "Please Enter Email Address", "paypwd_nologin": "Payment Password", "confirm_upgrade": "Confirm", "confirm_fqg": "Confirm Installment", "email_format_error": "Your email address has an invalid format. Please correct and try again", "phone_tip_1": "The mobile number registered in your bank is the one you provided for card application. ", "insuranced": "You do not need to enter Payment Password when amount is less than 200 CNY. ", "confirm_exit": "Are you sure to quit? ", "pwd-notice": "Password cannot be consecutive or repeat numbers, nor your date of birth.  ", "open_pcredit_agree": "Agree to", "input_pwd_s": "password", "address": "Address", "line_alipay_select": "Please select below linked accounts", "bank_info_tip": "Enter the information on file with the bank", "some_money": "Certain Amount", "alipay_finger": "Alipay Touch Pay", "alipay_auth": "Alipay Authorization", "cashier_no_dlg_btn": "Got It", "tb_finger": "Taobao Touch Pay", "sel_country_area": " Country and Region", "promotion_sorry": "Thank you for your time", "input_alipay_account": "Please enter account name of <PERSON><PERSON><PERSON>. ", "input_bank_phone": "Registered with Bank", "country": "Code", "pwd-error-repeat": "Password does not match. Please enter again. ", "account_deposit": "Balance Top Up", "pay_agree": "Agree to", "validity_text": "Expire Date", "pay_success": "Completed", "pcredit_ad": "<PERSON><PERSON>i is supported, with which you can consume first and pay off later with 0 interest. ", "month_year": "MM/YY", "login": " Log In", "use_other_account": "Other account", "login_alipay": "Log in", "info": "Notice", "no_method": "No payment method available", "pay_right_now": "Pay now", "id_info_tip": "Enter your ID no.", "open_pcredit_protocol": "“Huabei Service Agreement” and “Zhima Credit Service Agreement”", "forget_pwd": "Forgot Password?", "alipay_agree": "\"Ali<PERSON>y Account Interworking Protocol\"", "alipay_with_sth": "Alipay #val#", "webbank": "Mybank", "cancel": "Cancel", "base_info_tip": "Including name, gender, etc. ", "input_deposit_money": "Please Enter Top-Up Amount", "sel_id_no": "Select ID Number", "discount_detail": "Discount", "city": "City", "pcredit_pay_m": ", which can be paid on ", "id_card_tail": "Last 6 Numbers of ID", "id_card": "ID/Passport", "switch_account": "Switch Account", "next": "Next", "auth_bracelet": "Verifying Bracelet …", "pcredit_protocol": "Sesame Credit Service Agreement", "phone_text": "Phone", "sel_pay_tool": "Payment Method", "input_valid": "Enter Expire Date", "info_sec": "For bank verification only", "finger_pay_notice": "You will open below Touch Pay functions at the same time. ", "bind_webbank_account": "Please link to account of Mybank with the same ID information. ", "open_pcredit_pay": "Open and Use Huabei", "pay_n_loginpwd": "Please Enter Payment Password", "spwd_pay": "Set up Payment Password\nPlease don’t use your bank card password", "complete": "Done", "pwd_add_card": "Please enter Payment Password to add new bank card. ", "please_input": "Please enter ", "finger_pay_text": "Touch Pay makes your payment safer and easier", "validity": "Expire Date Introduction", "spot": ".", "promotion_crowd": "It is crowded. Please try again", "phone": "Phone Number Introduction", "pwd": "Password", "input_name": "Enter card holder's name", "resend_sms": "Resend", "alipay_account": "Account", "alipaypwd": "Alipay Payment Password", "use_alipay_account_full": "Use Alipay Account #val# Authorization", "confirm_pay": "Confirm", "phone_tip_2": "If the mobile number is not registered in your bank, forgotten or out of use, please contact your bank to update the information.", "account": "Account", "auth_qrcode_info": "Payment code, transaction information", "input_pwd": "Confirm password", "look": "View", "cvv2_format_error": "Your CVV has an invalid format. Please correct and try again", "account_bind": "Link Account", "cvv": "Security Code Introduction", "finger_process": "Processing", "id_no": "ID No.", "cvv_info": "Security Code is the last 3 or 4 numbers in signature area on the back of your card. ", "wait_text": "Next is your turn. ", "phone_sms": "Please enter SMS Code", "retry_finger": "Verification failed, please try again", "input_right_phone": "Please enter correct Phone Number. ", "finger_protocol": "\"Touch Pay Related Agreements\"", "user_agreement": "User Agreement", "safe_question": "Answer security questions for ID verification. ", "agree": "Agree to", "please_input_full": "Please enter #val# number", "base_info": "Access Basic Info. ", "authing": "Authorizing …", "personal_info_tip": "Including phone number, ID number, etc. ", "money": "Amount", "ad": "Advertisement", "app_auth": "This application will acquire below permissions. ", "bank_name": "Bank Name", "input_right_id_card": "Please enter correct ID Type and ID No.", "pwdupgrade_text": "Upgrade to 6-digit password for more convenient payment. ", "address_info_tip": "Including consignee's name, shipping address, post/zip code, etc. ", "limit_info": "Limit", "money_unit": "CNY", "pcredit_text_full": "No extra charge if you pay off the credit limit used on or before #val# of the next month", "activity_activity": "Activity", "safe_bank_no": "Enter following bank card number for ID verification. ", "can_insuranced": "Get Alipay Account Security Insurance for Free\nEnjoy Max. 1 Million Compensation\nYou do not need to enter Payment Password when amount is less than 200 CNY. ", "sms_tip_2": "Check if the SMS message has been blocked by any security software used", "waika_cvv": "CVV", "confirm_btn": "Sure", "confirming_upgrade": "Confirming …", "paypwd_bind": "Please enter payment password to add account. ", "input_expire_ccr": "Please enter Expire Date of your credit card", "sms_tip_5": "For further help, please call us at <font color='#1677ff'>95188</font>.", "no": " number", "pay": "Pay", "upgrading": "Upgrading…", "new_card_placeholder": "No online banking/no charges", "phone_tip_3": "If you are not a Chinese Mainland user, please enter it using the \"country code-mobile number\" format.", "papers_type": "ID Type", "payment_info": "Payment", "setting": "Settings", "auth_bic": "Verifying Smart Device …", "serve_protocol": "Service Agreement", "serve_protocol_full": "\"Service Agreement\"", "credit_quota_full": "Credit Limit #val# yuan", "pwdupgrade": "Upgrade Password", "account_info": "Access Account Info. ", "retry_again": "Try Again", "open_npwd": "Open 1-Step Payment", "resend_after_full": "#val#s", "use_pcredit_agree": "Agree to", "id_card_only": "Tip： only mainland ID verification is supported", "need_pay": "Need to Pay", "open_finger_pay": "Open Touch Pay", "baoxian_protocol": "Terms and Conditions of Insurance", "sms_help": "Code not received?", "pay_detail": "Payment details", "other_charge_way": "Use Other Payment Methods", "credit_card": "Credit Card", "waika_validity": "Expire Date  MM/YY", "sms_code": "Enter code", "activity_bonus_points": "Points rewards", "pcredit_pay": "Pay with <PERSON><PERSON><PERSON> ", "tail_no": "**** #val#", "confirm_sth": "Confirm #val#", "safepwd_verifying": "Verifying…", "alipay_login_id": "Account #val#", "open_account_fingerpay": "Open Touch Pay for below Alipay Accounts", "bind_alipay_account": "Please link below Alipay account. ", "i_know_it": "Got It", "email_address": "Email Address", "alipay_sec": "Your card is secure with Alipay smart encryption. <font color='#1677ff'>Learn more</font>", "open_pcredit_and_pay": "Open and Pay", "name_tip_1": "Make sure your name is exactly as the one used to register your bank card.", "cashier_no_tip_4": "Payment insurance: Payments are insured by Ant Insurance. ", "access_account_info": "#val# to Access Below Account Info. ", "phone_notice": "Enter between 6-20 characters for your Phone Number. ", "invalid_word": "Invalid Characters", "credit_amount_full": "You can use Huabeiand pay back Alipay later before #val# of the next month without interest. You can have a #val# CNY Credit Limit on <PERSON><PERSON><PERSON> right now.", "deposit": "completed", "id_card_tip": "Only the 18th digit can be X ", "activity_comment": "Evaluation", "safe_id_card": "Enter ID information of Real-Name Authentication for ID verification. ", "pwd_id_validate": "Please enter Payment Password to complete ID verification. ", "phone_card_deposit": "Use Prepaid Top-up Card", "finger_pay": "Touch Pay", "dot": ",", "activity_score_full": "Acquire #acquire# points and totally #total# points", "sms_alert_message": "1. Check to see if this is your current phone number. If not, go to Me > Settings > Phone No. > Change Mobile Number.\n2. Check to see if the message was blocked by security software. If you're using a dual-SIM phone, check the secondary SIM card's text messages.\n3. There may be a delay in message reception due to carrier network issues. Please wait or try again later.\n4. If you changed your carrier recently without changing your number, please wait for 1-2 days and try again.", "safe_remind": "Safety Reminders", "input_email_code": "Enter Email Verification Code", "email_code_has_sent": "The verification code has been sent to your mailbox", "email_resend_second": "s till resend", "email_resend": "Resend Verification Code", "change_other_way": "Other methods", "email_back_msg": "The email verification code will arrive soon. Please wait a moment, or check the junk mail folder after a while", "give_up": "Cancel", "input_bank_info": "Please enter bank card information", "card_no_placeholder": "Enter bank card number", "id_no_placeholder": "ID No. of cardholder", "mobile_no": "Mobile No.", "mobile_no_placeholder": "Mobile No. on file with bank", "input_bank_tip": "The above information is for ID verification only and will be kept confidential by Alipay.", "verify_bank_info": "Verify identity with your bank card information", "card_binded_already": "Bank card already linked", "bind_new_card": "You may link a new one", "bind_new_card_content": "Add your bank card", "bind_card_sms_code": "Code", "bind_card_sms_code_placeholder": "Enter SMS code", "bind_card_sms_resend": "Resend", "bind_card_sms_tip": "The SMS verification code has been sent to the mobile number registered with the bank.", "sms_back_msg": "The text may be delayed,please wait a little longer", "sms_back_wait": "Wait", "sms_back_quit": "Quit", "sms_alert_bank_message": "1. Please confirm whether the current mobile number is the number you registered with the bank.\n2. Please check whether the SMS is blocked by the security software. If it is a dual card dual standby mobile phone, please check the SMS on the secondary card.\n3. There may be SMS delay due to the network. Please wait patiently or try again later.\n4. If the phone number is not registered in your bank，forgotten or out of use, please contact the bank for help.\n5. If you have recently operated the network transfer with number, please wait for 1-2 days and try again."}}