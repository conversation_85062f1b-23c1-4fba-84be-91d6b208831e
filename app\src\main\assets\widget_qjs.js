// Declare global variables
var AlipayJSBridge;

(function(){

  var self = {}

  var globalContext = new Function('return this')();

  var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

  function InvalidCharacterError(message) {
    this.message = message;
  }
  InvalidCharacterError.prototype = new Error;
  InvalidCharacterError.prototype.name = 'InvalidCharacterError';

  globalContext.btoa = function(input) {
        var str = String(input);
        for (
            // Initialize result and counter.
            var block, charCode, idx = 0, map = chars, output = '';
            // If the next str index does not exist:
            //   change the mapping table to "="
            //   check if d has no fractional digits
            str.charAt(idx | 0) || (map = '=', idx % 1);
            // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8.
            output += map.charAt(63 & block >> 8 - idx % 1 * 8)) {
          charCode = str.charCodeAt(idx += 3 / 4);
          if (charCode > 0xFF) {
            throw new InvalidCharacterError(
                '\'btoa\' failed: The string to be encoded contains characters ' +
                'outside of the Latin1 range.');
          }
          block = block << 8 | charCode;
        }
        return output;
      };

  globalContext.atob = function(input) {
        var str = String(input).replace(/=+$/, '');
        if (str.length % 4 == 1) {
          throw new InvalidCharacterError(
              '\'atob\' failed: The string to be decoded is not ' +
              'correctly encoded.');
        }
        for (
            // Initialize result and counters.
            var bc = 0, bs, buffer, idx = 0, output = '';
            // Get next character.
            buffer = str.charAt(idx++);
            // Character found in table? initialize bit storage and add its
            // ascii value;
            ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,
                // And if not first of each 4 characters,
                // convert the first 8 bits to one ascii character.
                bc++ % 4) ? output += String.fromCharCode(
                      255 & bs >> (-2 * bc & 6)) : 0) {
          // Try to find character in table (0-63, not found => -1).
          buffer = chars.indexOf(buffer);
        }
        return output;
      };
})();