{"props": {"settingItemsSource": {"snapshot": {"bundleName": "TBSMySettingMenuV4.json", "filePath": "TBSMySettingMenuV4.json"}, "mtopParams": {"api": "mtop.taobao.mclaren.menue.get", "version": "4.0"}}}, "source": {"initTimeout": 3000, "sourceList": [{"name": "settingItemsSource", "type": "source.mtb.common.mtop.snapshot", "defaultLoad": 0}, {"name": "settingResourceSource", "type": "source.mtb.settings.resource", "defaultLoad": 0}]}, "transformer": {"nativeTransformerList": [], "jsTransformerList": []}, "event": {}, "layout": {"renderTemplate": {"name": "taobao_mytaobao_setting", "renderType": "dinamicX", "renderData": {"name": "taobao_mytaobao_setting", "version": "25", "url": "https://dinamicx.alibabausercontent.com/pub/taobao_mytaobao_setting/1744600454974/taobao_mytaobao_setting.zip", "heightMode": "matchParent", "immersiveStatusBar": "1"}}, "data": {"settingItems": "${originalData.settingItemsSource.settingItems}", "userProtocol": "${originalData.settingItemsSource.userProtocol}", "resource": "${originalData.settingResourceSource.resource}", "showProgress": "${originalData.settingItemsSource.isRequestingWhenInvalidSetting}"}, "userTrack": {}, "children": {}}}