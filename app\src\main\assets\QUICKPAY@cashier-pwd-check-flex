{"data": {"children": [{"children": [{"src": "AlipaySDK.bundle/amc.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "AlipaySDK.bundle/amc.css"}, {"src": "AlipaySDK.bundle/amc.js", "tag": "script"}, {}, {}, {"children": [{"tag": "text", "text": ".vi-pwd-plus{flex:1.0;height:61px;max-height:61px}.vi-pwd-box{margin-top:6px;height:49px;min-height:49px;max-height:49px}.vi-pwd-box-plus{margin-top:6px;height:63px;min-height:63px;max-height:63px}.vi-pwd{flex:1.0;height:47px;min-height:47px;max-height:47px}.vi-pwd-plugin-box{}"}], "tag": "style"}, {"children": [{"tag": "text", "text": "._PwdCheckPage_r3p9-c-stretch-self{align-self:stretch}._PwdCheckPage_r3p9-c-pd-lr{padding-left:16px;padding-right:16px}._PwdCheckPage_r3p9-c-pd-t{padding-top:12px}._PwdCheckPage_r3p9-c-pd-t-l{padding-top:20px}._PwdCheckPage_r3p9-c-font-m{font-size:15px}._PwdCheckPage_r3p9-c-font-l-white{font-size:17px;color:#000}._PwdCheckPage_r3p9-c-amc-i-nav-m-box{align-items:center;justify-content:center;flex:3.0}._PwdCheckPage_r3p9-c-pwd-tips{color:#F4333C;font-size:13px}._PwdCheckPage_r3p9-c-forget-pwd-box{margin-top:16px}._VIPlugin_orto-c-vi-finger{height:72px;max-height:72px;min-height:72px}._VIPlugin_orto-c-vi-finger-box{justify-content:center}._VIPlugin_orto-c-bottom-box{justify-content:flex-end;flex:1}._ButtonGroup_1afd-c-pay-btn-box{flex-direction:column;overflow:hidden;padding:12px 16px;min-height:68px;border-radius:2px;justify-content:flex-end}._Button_4g3v-c-text-primary{font-size:18px;color:#fff;height:43px}._Button_4g3v-c-button-primary{height:43px}._Button_4g3v-c-lottie-loading{width:320px;height:45px}._Button_4g3v-c-lottie-margin{margin-top:-45px}"}], "tag": "style", "type": "text/css"}, {"children": [{"tag": "text", "text": "/*! Built from b428c3664150e6336102ea5642da0bd5fb593cfc:C */!function(o){var n={};function i(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return o[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}i.m=o,i.c=n,i.d=function(t,e,o){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},i.r=function(t){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(t,'__esModule',{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&'object'==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(i.r(o),Object.defineProperty(o,'default',{enumerable:!0,value:e}),2&t&&'string'!=typeof e)for(var n in e)i.d(o,n,function(t){return e[t]}.bind(null,n));return o},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,'a',e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p='',i(i.s=9)}([function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.amc=window.amc},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var n=o(10);e.BNComponent=n.BNComponent;var i=o(6);e.ComponentRegistry=i.ComponentRegistry;var r=o(12);e.Logger=r.Logger,e.logger=r.logger},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var n,s=o(0),i=o(3);e.modifyElementStyle=function(t,e,o){var n=e;s.amc.fn.isString(e)&&(n=t.getViewInComponentById(e)),n&&i.copyObj(o,n.style)},e.modifyElementAttribute=function(t,e,o){if(e&&o){var n=e;if(s.amc.fn.isString(e)&&(n=t.getViewInComponentById(e)),n)for(var i in o)o.hasOwnProperty(i)&&(n[i]=o[i])}},e.modifyElementClass=function(t,e,o,n){var i=e;s.amc.fn.isString(e)&&(i=t.getViewInComponentById(e)),i&&(n?i.className+=o:i.className=o)},e.visibleElement=function(t,e,o){var n;void 0===o&&(o=!0),e&&(n=s.amc.fn.isString(e)?t.getViewInComponentById(e):e)&&(o?s.amc.fn.show(n):s.amc.fn.hide(n))},e.modifyElementCSS=function(t,e,o){if(e){var n=e;s.amc.fn.isString(e)&&(n=t.getViewInComponentById(e)),n&&o&&(n.style.cssText=o)}},e.createEmbedViPlugin=function(t,e,o,n){var i;if(s.amc.isAndroid)i=document.createElement('embed',e,function(){});else for(var r in i=document.createElement('embed'),e)e.hasOwnProperty(r)&&(i[r]=e[r]);return o&&(i.className=o),n?t.insertBefore(i,n):t.appendChild(i),i},e.getThemeColor=(n='',function(){return n||(n=s.amc.fn.sdkGreaterThanOrEqual('10.8.39')?'#1677FF':'#108EE9'),n})},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var s=o(0);e.mergeObject=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var o={};if(t&&t.length)for(var n=0;n<t.length;n++){var i=t[n];if(s.amc.fn.isObject(i))for(var r in i)i.hasOwnProperty(r)&&(o[r]=i[r])}return o},e.isFunction=function(t){return'[object Function]'===Object.prototype.toString.call(t)},e.isPreRender=function(t){return t&&(t.local&&t.local.isPrerender||t.rpcData&&t.rpcData.isPrerender)},e.copyObj=function(t,e){for(var o in e||(e={}),t)t.hasOwnProperty(o)&&(e[o]=t[o]);return e},e.doNothing=function(){},e.tryJSONParse=function(t){if(s.amc.fn.isObject(t))return t;try{return JSON.parse(t)}catch(t){return{}}},e.checkEmptyObj=function(t){return s.amc.fn.isString(t)?0===t.length:!(t&&0!==Object.keys(t).length)},e.substrWithFontWidth=function(t,e,o){if(!t)return t;for(var n='',i=0,r=t.length,s=0;s<r;s++){var a=o?t[r-s-1]:t[s];if(/^[A-Za-z0-9\\(\\)]*$/.test(a)?i+=.45:i++,n+=a,e-1<i)break}return n},e.calculateFontWidth=function(t){if(!t)return 0;for(var e=0,o=/^[A-Za-z0-9\\.\\(\\)]*$/,n=0;n<t.length;n++)o.test(t[n])?e+=.45:e++;return Math.round(e)},e.deepCopy=function t(e){if(null==e||'object'!=typeof e)return e;var o;if(e instanceof Date)return(o=new Date).setTime(e.getTime()),o;if(e instanceof Array){o=[];for(var n=0,i=e.length;n<i;n++)o[n]=t(e[n]);return o}if(e instanceof Object){for(var r in o={},e)e.hasOwnProperty(r)&&(o[r]=t(e[r]));return o}throw new Error('Unable to copy obj! Its type isn\\'t supported.')},e.getConfig=function(t,e){setTimeout(function(){document.invoke('queryInfo',{queryKey:'configInfo',configKey:t},function(t){e(t.available)})},20)},e.showLoading=function(){setTimeout(function(){document.invoke('showLoading')},20)},e.hideLoading=function(){setTimeout(function(){document.invoke('hideLoading')},20)}},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var n=o(0);e.logAction=function(t,e){window.pageId||(window.pageId='|'+Math.random().toString(36).substr(2,3)),e=e?e+window.pageId:window.pageId,n.amc.fn.logAction(t,e)}},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0}),e.randomStr=function(){return Math.floor(61439*Math.random()+4096).toString(16)},e.startsWith=function(t,e){return!!t&&0===t.indexOf(e)}},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var n=o(1),i=function(){function o(){}return o.registerComponent=function(t,e){e?o.facts[t]?n.logger.e('CmpReg#regCmp','E0002 '+t):(n.logger.i('CmpReg#regCmp','I0003 '+t),o.facts[t]=e):n.logger.e('CmpReg#regCmp','E0001 '+t+', '+e)},o.getKnownComponents=function(){return o.facts},o.getComponentJson=function(t){return o.jsons[t]},o.putComponentJson=function(t,e){e||n.logger.e('CmpReg#putCmpJ','E0004 '+t+', '+e),o.getComponentJson(t)?n.logger.e('CmpReg#putCmpJ','E0005 '+t):(n.logger.i('CmpReg#putCmpJ','I0006 '+t),o.jsons[t]=e)},o.createComponent=function(t){n.logger.i('CmpReg#crtCmp','I0007 '+t);var e=o.facts[t];return e?new e:(n.logger.e('CmpReg#crtCmp','E0008 '+t),null)},o.facts={},o.jsons={},o}();e.ComponentRegistry=i},function(t,a,e){'use strict';var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(a,'__esModule',{value:!0});var i,p,r,c,s,u,l,d,h=e(1),f=e(4),m=e(3),g=e(0),y=e(2),v=e(8),_=e(13);h.ComponentRegistry.registerComponent(v.Button.componentName,v.Button),h.ComponentRegistry.registerComponent(_.ButtonGroup.componentName,_.ButtonGroup),(d=i=a.VI_STATUS||(a.VI_STATUS={})).NORMAL='normal',d.VERIFYING='verifying',d.PAYING='paying',d.SUCCESS='success',(l=p||(p={})).viStart='viStart',l.viStatus='viStatus',l.viRpcRequest='viRpcRequest',l.viToPWD='viToPWD',l.viResult='viResult',a.EXIT_VI_SCENE={PPW_LOCK:'PPW_LOCK',forgetPwd:'forgetPwd'},a.RETAIN_VI_SCENE={USER_CANCEL:'USER_CANCEL'},a.VI_LOG_ACTION_TYPE='VI',(u=r=a.VI_ACTION||(a.VI_ACTION={})).PRE_LOAD='viPreload',u.START='viStart',u.START_HUAWEI='viPreStart',u.RPC_RESPONSE='viRpcResponse',u.PAY_STATUS='payStatus',u.CLOSE_PAGE='viClosePage',u.FORGET_PWD_HANDLE_OTHER_PAY_WAY='viHandleOtherPayWay',a.VI_TYPE={PASSWORD:'pwd',SHORT_PASSWORD:'spwd',FINGERPRINT:'fp',BRACELET:'wl',FACE_IDENTIFICATION_CAPS:'FACEID',FACE_IDENTIFICATION_LOWER:'faceid',ZOLO_FACE_IDENTIFICATION:'ZFACE'},(s=c||(c={})).NEW='2.0',s.OLD='1.0',s.UNKNOWN='unknown';var P=function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={buttonGroup:new _.ButtonGroup,isPayBtnShown:!0,isPaySuccessShown:!1,processVIRpcRequest:function(){},processVIStart:function(){},processVIStatus:function(){},processVIToPWD:function(){},processVIResult:function(){},pwdTip:'',pwdType:'',rpcVIData:'',buttons:[],buttonIndex:0,supportVersion:c.UNKNOWN},t.onCreated=function(){},t.onMounted=function(){window.viCallback=t.viCallback.bind(t),t.props.viPlugin=y.createEmbedViPlugin(t.getViewInComponentById('viPwdBox'),{type:'VIPayPluginView',src:JSON.stringify({}),id:'realViPlugin'},'vi-finger amc-flex-1'),t.props.buttonGroup=t.getSubComponentById('buttonGroup',_.ButtonGroup),t.props.buttonGroup.setOnClick(t.onButtonClick.bind(t))},t}return o(n,e),n.prototype.addButton=function(t,e,o){this.props.buttons.push({dom:t,text:e}),this.props.buttonGroup.addButton(t,o),t.setText(n.VIButtonText2ButtonText(e)).changeLoadingStatus(v.BUTTON_STATUS.NORMAL,v.LOADING_TYPE.CIRCLE)},n.prototype.onButtonClick=function(t){this.props.buttonIndex=t},n.VIButtonText2ButtonText=function(t){var e;return(e={})[v.BUTTON_STATUS.NORMAL]=t.normal,e[v.BUTTON_STATUS.SUCCESS]=t.success,e[v.BUTTON_STATUS.LOADING]=t.paying,e},n.prototype.getAboveButtonBox=function(){return this.getViewInComponentById('aboveButtonBox')},n.prototype.payButtonExposure=function(t,e){var o=this;this.props.isPayBtnShown=!0,setTimeout(function(){o.props.isPayBtnShown&&g.amc.fn.spmExposure(t,e,!0)},300)},n.prototype.setRpcVIData=function(t,e){return void 0===e&&(e=!0),this.props.rpcVIData=t||'',e&&this.preLoadVIPlugin(),this},n.prototype.showButton=function(t){return void 0===t&&(t=!0),(this.props.isPayBtnShown=t)?(this.props.buttonGroup.setVisible(!0),y.visibleElement(this,'viPwdBox',!1)):(this.props.buttonGroup.setVisible(!1),y.visibleElement(this,'viPwdBox',!0)),this},n.prototype.setPwdTip=function(t){return this.props.pwdTip=t||'',this},n.prototype.setProcessVIStart=function(t){return this.props.processVIStart=t,this},n.prototype.setProcessVIStatus=function(e){var o=this;return this.props.processVIStatus=function(t){t&&t.type&&(o.props.pwdType=t.type),t&&t.version&&(o.props.version=t.version),e(t)},this},n.prototype.setProcessVIRpcRequest=function(t){return this.props.processVIRpcRequest=t,this},n.prototype.setProcessVIToPWD=function(t){return this.props.processVIToPWD=t,this},n.prototype.setProcessVIResult=function(t){return this.props.processVIResult=t,this},n.prototype.getIsPaySuccessShown=function(){return this.props.isPaySuccessShown},n.prototype.getPwdType=function(){return this.props.pwdType||''},n.prototype.getActiveButton=function(){return this.props.buttons.length>this.props.buttonIndex?this.props.buttons[this.props.buttonIndex].dom:0<this.props.buttons.length?this.props.buttons[0].dom:void 0},n.prototype.getActiveButtonText=function(){return this.props.buttons.length>this.props.buttonIndex?this.props.buttons[this.props.buttonIndex].text:0<this.props.buttons.length?this.props.buttons[0].text:void 0},n.prototype.getButtonContainer=function(){return this.props.buttonGroup},n.prototype.toggleBioProcessLoading=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex].text;switch(t){case i.NORMAL:this.stopLoadingWithText();break;case i.VERIFYING:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.VERIFYING]||'');break;case i.PAYING:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.PAYING]||'');break;case i.SUCCESS:this.startLoadingWithText(v.LOADING_TYPE.CIRCLE,e[i.SUCCESS]||'',v.BUTTON_STATUS.SUCCESS);break;default:this.stopLoadingWithText()}}},n.prototype.startLoadingWithText=function(t,e,o){this.props.buttonIndex>=this.props.buttons.length||this.props.buttons[this.props.buttonIndex].dom.changeLoadingStatus(o||v.BUTTON_STATUS.LOADING,t,e||'')},n.prototype.stopLoadingWithText=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.NORMAL,void 0,t||e.text[i.NORMAL])}},n.prototype.onPaySuccess=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.SUCCESS,v.LOADING_TYPE.CIRCLE,e.text[i.PAYING]),y.modifyElementStyle(e.dom,'buttonText',{opacity:.2}),setTimeout(function(){y.modifyElementAttribute(e.dom,'buttonText',{innerText:t||e.text[i.SUCCESS]}),y.modifyElementStyle(e.dom,'buttonText',{opacity:1})},500),this.props.isPaySuccessShown=!0}},n.prototype.onPayFailure=function(t){if(!(this.props.buttonIndex>=this.props.buttons.length)){var e=this.props.buttons[this.props.buttonIndex];e.dom.changeLoadingStatus(v.BUTTON_STATUS.NORMAL,v.LOADING_TYPE.CIRCLE,t||e.text[i.NORMAL]),this.props.isPaySuccessShown=!0}},n.prototype.invoke=function(t,e){if(this.props.viPlugin)return this.props.viPlugin.invoke(t,e)},n.prototype.getVersion=function(){return this.props.version},n.prototype.getAuthType=function(t,e){var o=this;this.getVIConfig(function(){t(o.getAuthTypeIml(e))})},n.prototype.getAuthTypeIml=function(t){var e={};try{(e=this.invoke('getAuthType',{pwdTip:this.props.pwdTip,viData:t||this.props.rpcVIData,supportVersion:this.props.supportVersion}))&&e.version&&(this.props.version=e.version)}catch(t){g.amc.fn.logError('VI','getAuthType失败,'+t)}return f.logAction('authType-'+(this.props.supportVersion||'supportVersion')+'-'+(e&&e.version||'version'),a.VI_LOG_ACTION_TYPE),e},n.prototype.sendDataToVIPlugin=function(t){f.logAction(t&&t.action||'action',a.VI_LOG_ACTION_TYPE),y.modifyElementAttribute(this,this.props.viPlugin,{src:JSON.stringify(t)})},n.prototype.preLoadVIPlugin=function(){if(this.props.rpcVIData&&!m.checkEmptyObj(this.props.rpcVIData)){var t={action:r.PRE_LOAD,data:this.props.rpcVIData};this.sendDataToVIPlugin(t)}},n.prototype.getVIConfig=function(t){this.props.supportVersion===c.UNKNOWN&&(g.amc.isSDK?g.amc.isAndroid?g.amc.fn.sdkGreaterThanOrEqual('*********')?this.props.supportVersion=c.NEW:this.props.supportVersion=c.OLD:g.amc.fn.sdkGreaterThanOrEqual('*********')?this.props.supportVersion=c.NEW:this.props.supportVersion=c.OLD:g.amc.fn.sdkGreaterThanOrEqual('10.8.34')?this.props.supportVersion=c.NEW:this.props.supportVersion=c.OLD),t()},n.prototype.startVIPlugin=function(t,e,o){var n=this;this.getVIConfig(function(){n.startVIPluginIml(t,e,o)})},n.prototype.startVIPluginIml=function(t,e,o){var n={supportVersion:this.props.supportVersion,supportRetain:'Y'};this.props.pwdTip&&(n.pwdTip=this.props.pwdTip),o=m.copyObj(n,o),f.logAction('viStartConfig-'+(o.usePwd||'usePwd')+'-'+(o.costTip||'costTip'),a.VI_LOG_ACTION_TYPE),f.logAction('viLen|'+(this.props.rpcVIData||'').length+'-'+(o.supportVersion||'supportVersion'),a.VI_LOG_ACTION_TYPE);var i={action:t,data:e||this.props.rpcVIData,callbacks:{onViAction:'viCallback'},config:o};this.sendDataToVIPlugin(i)},n.prototype.sendResponseDataToVI=function(t){var e={action:r.RPC_RESPONSE,data:t};this.sendDataToVIPlugin(e)},n.prototype.showVILoadingWithPaymentResult=function(t){var e={action:r.PAY_STATUS,data:{status:t}};this.sendDataToVIPlugin(e)},n.prototype.sendForgetPwdPayWithOtherWayToVI=function(){var t={action:r.FORGET_PWD_HANDLE_OTHER_PAY_WAY,data:{from:'forgotPwd'}};this.sendDataToVIPlugin(t)},n.prototype.viCallback=function(t){var e,o;try{o=JSON.parse(t)||{action:'none',data:''}}catch(t){return void g.amc.fn.logError('VI','解析核身JSON失败'+t)}var n=o.action,i=o.data;if(g.amc.fn.isString(n)&&n===p.viToPWD){var r={};if(g.amc.fn.isString(i))try{r=JSON.parse(i)}catch(t){g.amc.fn.logError('pVI2PWD','json parse error')}else r=i;r&&(r.version?this.props.version=r.version:this.props.version=c.OLD)}var s=((e={})[p.viStart]=this.props.processVIStart,e[p.viStatus]=this.props.processVIStatus,e[p.viRpcRequest]=this.props.processVIRpcRequest,e[p.viToPWD]=this.props.processVIToPWD,e[p.viResult]=this.props.processVIResult,e.none=function(){},e);n!==p.viStatus&&n!==p.viToPWD&&n!==p.viResult&&n!==p.viStart&&f.logAction(n||'actType',a.VI_LOG_ACTION_TYPE),g.amc.fn.isString(n)&&s[n]&&s[n](i)},n.prototype.viPageCloseNotify=function(){var t={action:r.CLOSE_PAGE};this.sendDataToVIPlugin(t)},n.getComponentCSSRules=function(){return{'.vi-finger':'_VIPlugin_orto-c-vi-finger','.vi-finger-box':'_VIPlugin_orto-c-vi-finger-box','.bottom-box':'_VIPlugin_orto-c-bottom-box'}},n.getComponentJson=function(){return{'sp-view-id':'viPluginBox',_c:'_VIPlugin_orto-c-bottom-box amc-v-box',_t:'div',_cd:[{'sp-view-id':'aboveButtonBox',_t:'div'},{'sp-view-id':'buttonContainer',_c:'amc-v-box',_t:'div',_cd:[{'sp-component':'ButtonGroup','sp-component-id':'buttonGroup',_t:'div'}]},{'sp-view-id':'viPwdBox',_c:'_VIPlugin_orto-c-vi-finger _VIPlugin_orto-c-vi-finger-box amc-hidden',_t:'div'},{'sp-view-id':'bottomBlank',_c:'amc-iphone-x-pd-b',_t:'div'}]}},n.componentName='VIPlugin',n.componentHashName='VIPlugin_orto',n}(h.BNComponent);a.VIPlugin=P},function(t,e,o){'use strict';var n,i,r,s,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,'__esModule',{value:!0});var p,c,u,l,d,h,f=o(1),m=o(0),g=o(2),y=o(3);(h=p=e.BUTTON_TYPE||(e.BUTTON_TYPE={})).PRIMARY='PRIMARY',h.NORMAL='NORMAL',(d=c=e.LOADING_TYPE||(e.LOADING_TYPE={}))[d.JuHua=0]='JuHua',d[d.CIRCLE=1]='CIRCLE',(l=u=e.BUTTON_STATUS||(e.BUTTON_STATUS={}))[l.NORMAL=0]='NORMAL',l[l.LOADING=1]='LOADING',l[l.SUCCESS=2]='SUCCESS',l[l.DISABLE=3]='DISABLE';var v=((i={})[p.NORMAL]=m.amc.path+'alipay_msp_loading_blue.gif',i[p.PRIMARY]=m.amc.res.loading,i),_=((r={})[p.NORMAL]='amc-loading-img amc-text-color-blue',r[p.PRIMARY]='amc-loading-img amc-text-white-clolor',r),P=((s={})[p.NORMAL]=m.amc.path+'alipay_msp_success_blue.gif',s[p.PRIMARY]=m.amc.res.success,s),I='color: '+g.getThemeColor()+';',b='background-color: #FFF;border: 2px '+g.getThemeColor()+';font-size: 18px;',T=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={type:p.PRIMARY,onClick:function(){},circleLoading:{},juHuaLoading:{},lottieImg:null},t.onCreated=function(){},t.onMounted=function(){t.props.circleLoading=g.createEmbedViPlugin(t.getViewInComponentById('buttonInnerBox'),{type:'MQPPayGifView'},'amc-hidden',t.getViewInComponentById('buttonText')),g.modifyElementCSS(t,t.props.circleLoading,'width: 24px; height: 24px;margin-right: 8px;'),t.props.juHuaLoading=t.getViewInComponentById('loadingJuHua'),m.amc.isAndroid&&g.modifyElementAttribute(t,t.props.juHuaLoading,{src:m.amc.path+'alipay_msp_indicator_white_loading'}),g.modifyElementAttribute(t,'buttonText',{accessibilityTraits:'Button'})},t.lottieReady=!1,t.lottiePlay=!1,t}return a(t,e),t.prototype.setType=function(t){switch(this.props.type=t,g.modifyElementClass(this,this.props.juHuaLoading,_[t],!1),t){case p.NORMAL:g.modifyElementClass(this,'button','amc-btn-secondary amc-align-center amc-justify-center',!1),g.modifyElementCSS(this,'buttonText',I),g.modifyElementCSS(this,'button',b);break;case p.PRIMARY:g.modifyElementClass(this,'button','amc-btn-primary amc-align-center amc-justify-center',!1),g.modifyElementCSS(this,'buttonText','color: #fff;')}return this},t.prototype.setLottieImg=function(t){var e=this;if(!m.amc.isSDK&&t&&m.amc.fn.sdkGreaterThanOrEqual('10.8.29')){var o=this.getViewInComponentById('buttonBackground');this.props.lottieImg=m.amc.fn.create('lottie','',o),this.applyStyleTo(this.props.lottieImg,'amc-flex-center lottie-loading'),this.props.lottieImg.djangoId=t,this.props.lottieImg.repeatCount='-1',this.props.lottieImg.invoke('pause',{}),this.props.lottieImg.dataReady=function(){e.lottieReady=!0,e.lottiePlay&&e.props.lottieImg&&e.props.lottieImg.invoke('play',{})}}return this},t.prototype.startLottie=function(){this.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke('play',{}),this.lottiePlay=!0},t.prototype.stopLottie=function(){this.lottieReady&&this.props.lottieImg&&this.props.lottieImg.invoke('stop',{}),this.lottiePlay=!1},t.prototype.setVisible=function(t){return g.modifyElementStyle(this,'button',{display:t?'none':'flex'}),this},t.prototype.setOnClick=function(t){return this.props.onClick=t||y.doNothing,g.modifyElementAttribute(this,'button',{onclick:this.props.onClick}),this},t.prototype.setText=function(t){return this.props.text=t,this},t.prototype.changeTextAndOnClick=function(t,e){var o=void 0;switch(t){case u.SUCCESS:this.props.text&&(o=this.props.text[u.SUCCESS]),g.modifyElementAttribute(this,'button',{onclick:y.doNothing});break;case u.NORMAL:this.props.text&&(o=this.props.text[u.NORMAL]),g.modifyElementAttribute(this,'button',{onclick:this.props.onClick});break;case u.LOADING:this.props.text&&(o=this.props.text[u.LOADING]),g.modifyElementAttribute(this,'button',{onclick:y.doNothing});break;case u.DISABLE:g.modifyElementAttribute(this,'button',{onclick:y.doNothing});break;default:o=''}return void 0!==e?g.modifyElementAttribute(this,'buttonText',{innerText:e}):void 0!==o&&g.modifyElementAttribute(this,'buttonText',{innerText:o}),this},t.prototype.changeLoadingStatus=function(t,e,o){switch(e){case c.CIRCLE:g.visibleElement(this,this.props.juHuaLoading,!1),g.visibleElement(this,this.props.circleLoading,!1),this.changeTextAndOnClick(t,o),this.changeCircleLoading(t),g.visibleElement(this,this.props.circleLoading,t!==u.NORMAL);break;case c.JuHua:g.visibleElement(this,this.props.juHuaLoading,!1),g.visibleElement(this,this.props.circleLoading,!1),this.changeTextAndOnClick(t,o),g.visibleElement(this,this.props.juHuaLoading,t!==u.NORMAL);break;default:this.changeLoadingStatus(u.NORMAL,c.CIRCLE,o)}},t.prototype.changeCircleLoading=function(t){var e=this.props.circleLoading;switch(t){case u.LOADING:g.modifyElementAttribute(this,e,{src:v[this.props.type]});break;case u.NORMAL:g.modifyElementAttribute(this,e,{src:''});break;case u.SUCCESS:g.modifyElementAttribute(this,e,{src:P[this.props.type]});break;default:m.amc.fn.logError('Button','loading-'+(t||'status'))}},t.getComponentCSSRules=function(){return{'.text-primary':'_Button_4g3v-c-text-primary','.button-primary':'_Button_4g3v-c-button-primary','.lottie-loading':'_Button_4g3v-c-lottie-loading','.lottie-margin':'_Button_4g3v-c-lottie-margin'}},t.getComponentJson=function(){return{'sp-view-id':'button',_c:'amc-btn-primary amc-align-center amc-justify-center amc-v-box',_t:'div',_cd:[{'sp-view-id':'buttonBackground',_c:'_Button_4g3v-c-lottie-loading amc-align-center amc-justify-center',_t:'div'},{'sp-view-id':'buttonInnerBox',_c:'_Button_4g3v-c-lottie-margin amc-align-center amc-justify-center',_t:'div',_cd:[{'sp-view-id':'loadingJuHua',src:'indicatior',_c:'amc-loading-img amc-text-white-clolor',alt:'',_t:'img'},{'sp-view-id':'buttonText',_c:'_Button_4g3v-c-text-primary amc-ellipsis',_t:'label',_x:'{{pay_right_now}}'}]}]}},t.componentName='Button',t.componentHashName='Button_4g3v',t}(f.BNComponent);e.Button=T},function(t,e,o){'use strict';var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,'__esModule',{value:!0});var s=o(2),a=o(0),r=o(1),p=o(7),c=o(4),u=o(3),l=o(14);r.ComponentRegistry.registerComponent(p.VIPlugin.componentName,p.VIPlugin);var d=a.amc.path+'alipay_msp_loading_blue.gif',h=a.amc.path+'alipay_msp_success_blue.gif';function f(){a.amc.fn.showLoading(!1,!1)}function m(t){document.body.style.opacity=t?'0.96':'0'}var g=function(t){function e(){var e=t.call(this)||this;return e.props=new function(){},e.onCreated=function(){},e.onMounted=function(){try{e.props.rpcData=a.amc.rpcData,e.props.btnText=e.props.rpcData.btnText||'{{pay}}',e.props.successTxt=e.props.rpcData.successText||'{{pay_ok}}',e.props.payingText=e.props.rpcData.doingText||'{{paying}}',e.props.confirmAction=l.buildConfirmActionWithAct('/pwd/validate',e.props.rpcData.confirmAct),e.props.viPlugin=e.getSubComponentById('viPluginComponent',p.VIPlugin),e.props.viPlugin.showButton(!1).setRpcVIData(e.props.rpcData.VIData,!1).setProcessVIStart(e.processVIStart.bind(e)).setProcessVIStatus(e.processVIStatus.bind(e)).setProcessVIRpcRequest(e.processVIRpcRequest.bind(e)).setProcessVIToPWD(e.processVIToPWD.bind(e)).setPwdTip(e.props.rpcData.pwdTip),s.visibleElement(e.props.viPlugin,'bottomBlank',!1),s.modifyElementClass(e.props.viPlugin,'viPwdBox','vi-pwd-box',!1);var t=document.getElementById('realViPlugin');t&&(t.className='vi-pwd'),s.modifyElementAttribute(e,'forgetPwdText',{onclick:e.forgetPwd.bind(e)}),s.modifyElementAttribute(e,'loadTxt',{innerText:e.props.payingText}),e.initNav(),e.initUI()}catch(t){a.amc.fn.logPageInit&&a.amc.fn.logPageInit(!1)}},window.onDestroy=e.onDestroy.bind(e),window.onKeyDown=e.onKeyDown.bind(e),e}return i(e,t),e.prototype.onBack=function(){a.amc.fn.spmClick('a283.b4038.c9698.d17397',{}),a.amc.fn.spmPageDestroy('a283.b4038',{}),this.props.rpcData.pwdExitAction?document.submit({action:this.props.rpcData.pwdExitAction}):document.submit({action:{name:this.props.backAction}})},e.prototype.initNav=function(){var t=a.amc.res.close;this.props.rpcData.backAction?this.props.backAction=this.props.rpcData.backAction:'deposit'===this.props.rpcData.bizType||this.props.rpcData.payOrder||'back'===this.props.rpcData.backAct?(this.props.backAction='loc:back',t=a.amc.res.arrowLeft):this.props.backAction='loc:exit';var e=a.amc.fn.iNav(t,'{{input_pwd}}','','',this.onBack.bind(this),void 0);document.body.insertBefore(e,this.getViewInComponentById('container')),a.amc.fn.spmExposure('a283.b4038.c9698.d17397',{},!1)},e.prototype.isIOSBioAuth=function(t){var e=t||this.props.viPlugin.getPwdType();return'fp'===e||'faceid'===e||'ZFACE'===e},e.prototype.isAndroidBioAuth=function(t){var e=t||this.props.viPlugin.getPwdType();return'fp'===e||'ZFACE'===e||'FACEID'===e},e.prototype.initUIIml=function(t){var e=this,o=t&&t.type||p.VI_TYPE.PASSWORD,n=t&&t.version,i=(o===p.VI_TYPE.PASSWORD||o===p.VI_TYPE.SHORT_PASSWORD)&&'2.0'!==n;(!i&&this.isIOSBioAuth(o)||'2.0'===n)&&a.amc.fn.showLoading(!0,!0),m(i),a.amc.isIOS?setTimeout(function(){e.startVIPlugin(e.props.rpcData.VIData)},1):this.startVIPlugin(this.props.rpcData.VIData),a.amc.fn.spmExposure('a283.b4038.c9698.d17395',{},!1),a.amc.fn.logPageInit&&a.amc.fn.logPageInit(!0)},e.prototype.initUI=function(){a.amc.isIOS?this.props.viPlugin.getAuthType(this.initUIIml.bind(this)):(a.amc.fn.sdkGreaterThanOrEqual('10.8.34')&&!a.amc.isSDK&&m(!1),this.startVIPlugin(this.props.rpcData.VIData),a.amc.fn.spmExposure('a283.b4038.c9698.d17395',{},!1),a.amc.fn.logPageInit&&a.amc.fn.logPageInit(!0))},e.prototype.processVIStart=function(t){var e=(t=t||{}).otherPayWay;e&&('Y'===e.config&&'string'==typeof e.content?(s.modifyElementAttribute(this,'forgetPwdText',{innerText:e.content}),this.props.forgetPwdWithOtherVIWay=!0):this.props.forgetPwdWithOtherVIWay=!1)},e.prototype.showPwdInputAndTips=function(){var t=this.props.viPwdTip||this.props.rpcData.pwdTip;t?(this.props.viPwdTip='',this.props.rpcData.pwdTip='',s.modifyElementAttribute(this,'tips',{innerText:t}),a.amc.fn.show(this.getViewInComponentById('tips'))):a.amc.fn.hide(this.getViewInComponentById('tips')),s.modifyElementAttribute(this,'pwdLoadGif',{src:''}),a.amc.fn.hide(this.getViewInComponentById('pwdLoad')),a.amc.fn.show(this.getViewInComponentById('pwdBox'))},e.prototype.processVIStatus=function(t){if(t){var e=t.status,o=t.type,n=t.version,i=t.scene;if(c.logAction((o||'type')+'-'+(e||'status')+'-'+(n||'version')+'-'+(i||'scene'),p.VI_LOG_ACTION_TYPE),'start'===e){if(this.props.viPlugin.getPwdType()!==p.VI_TYPE.PASSWORD&&this.props.viPlugin.getPwdType()!==p.VI_TYPE.SHORT_PASSWORD||'2.0'===n)m(!1);else if(this.props.viPwdTip=t.tip,m(!0),this.showPwdInputAndTips(),this.props.viPlugin.getPwdType()===p.VI_TYPE.SHORT_PASSWORD&&a.amc.isPlus){var r=document.getElementById('realViPlugin');s.modifyElementClass(this.props.viPlugin,'viPwdBox','vi-pwd-box-plus',!1),r&&(r.className='vi-pwd-plus')}}else'abort'===e?p.RETAIN_VI_SCENE.hasOwnProperty(i)&&'true'===this.props.rpcData.payRetrieveOnce?(a.amc.isIOS&&f(),this.props.rpcData.pwdExitAction?document.submit({action:this.props.rpcData.pwdExitAction}):a.amc.fn.back()):p.EXIT_VI_SCENE.hasOwnProperty(i)?a.amc.fn.exit():o!==p.VI_TYPE.SHORT_PASSWORD&&o!==p.VI_TYPE.PASSWORD||'true'!==this.props.rpcData.payRetrieveOnce?'back'===this.props.rpcData.backAct?(a.amc.isIOS&&f(),a.amc.fn.back()):a.amc.fn.exit():(a.amc.isIOS&&f(),this.props.rpcData.pwdExitAction?document.submit({action:this.props.rpcData.pwdExitAction}):a.amc.fn.back()):'awaitUser'===e?(this.props.viPlugin.getPwdType()!==p.VI_TYPE.PASSWORD&&this.props.viPlugin.getPwdType()!==p.VI_TYPE.SHORT_PASSWORD||this.showPwdInputAndTips(),this.isIOSBioAuth()&&a.amc.isIOS&&'2.0'!=this.props.viPlugin.getVersion()&&f()):'findPwd'===e&&a.amc.isIOS&&a.amc.fn.setResult({ResultStatus:'6006'})}},e.prototype.beforeSendPwdPay=function(){a.amc.fn.hide(this.getViewInComponentById('tips')),a.amc.fn.hide(this.getViewInComponentById('pwdBox')),a.amc.fn.show(this.getViewInComponentById('pwdLoad')),s.modifyElementAttribute(this,'pwdLoadGif',{src:d})},e.prototype.showSuccessAnimation=function(t){s.modifyElementAttribute(this,'loadTxt',{innerText:t||this.props.successTxt}),s.modifyElementAttribute(this,'pwdLoadGif',{src:h}),a.amc.fn.show(this.getViewInComponentById('pwdLoad'))},e.prototype.onPaymentFail=function(){this.showPwdInputAndTips()},e.prototype.onReceivePwdPay=function(t){'0'===(t=t||{}).pageloading&&this.getViewInComponentById('pwdLoadGif').src===d?a.amc.fn.hide(this.getViewInComponentById('pwdLoad')):'1'===t.pageloading&&a.amc.fn.show(this.getViewInComponentById('pwdLoad')),'0001'===t.status?this.showSuccessAnimation(t.successText):'0002'===t.status&&this.onPaymentFail()},e.prototype.submitPay=function(t,o){var n=this;'2.0'===this.props.viPlugin.getVersion()||a.amc.isAndroid&&this.isAndroidBioAuth()?this.props.confirmAction.loadtxt=void 0:'true'===this.props.rpcData.payRetrieveOnce&&a.amc.isIOS&&this.isIOSBioAuth()?(f(),this.props.confirmAction.loadtxt=void 0):a.amc.isIOS&&'ZFACE'===this.props.viPlugin.getPwdType()?this.props.confirmAction.loadtxt=void 0:this.props.confirmAction.loadtxt='';var e={action:this.props.confirmAction};e.action.params=u.mergeObject(e.action.params||{},{VIData:t},this.props.rpcData.params||{}),'openservice'==this.props.rpcData.biztype||'/pwd/validate'!==this.props.confirmAction.name&&'/cashier/pay'!==this.props.confirmAction.name&&'/cashier/payment'!==this.props.confirmAction.name||(e.action.neec='6004'),document.asyncSubmit(e,function(t){if((t=t||{}).confirmAct&&t.confirmAct.name&&(n.props.confirmAction=l.buildConfirmActionWithAct('/pwd/validate',t.confirmAct)),o&&o(t),t.VIData){try{var e=JSON.parse(t.VIData).finishCode;c.logAction('viCode|'+e,p.VI_LOG_ACTION_TYPE)}catch(t){}n.props.viPlugin.sendResponseDataToVI(t.VIData)}})},e.prototype.beforeSendBioPay=function(){this.props.viPlugin.showVILoadingWithPaymentResult('paying')},e.prototype.onReceiveBioPay=function(t){'0001'===(t=t||{}).status?this.props.viPlugin.showVILoadingWithPaymentResult('success'):'0002'===t.status&&this.props.viPlugin.showVILoadingWithPaymentResult('fail')},e.prototype.processVIRpcRequest=function(t){this.props.viPlugin.getPwdType()===p.VI_TYPE.PASSWORD||this.props.viPlugin.getPwdType()===p.VI_TYPE.SHORT_PASSWORD?(this.beforeSendPwdPay(),this.submitPay(t,this.onReceivePwdPay.bind(this))):(this.beforeSendBioPay(),this.submitPay(t,this.onReceiveBioPay.bind(this)))},e.prototype.processVIResult=function(t){l.commonProcessVIResult(t,{viPlugin:this.props.viPlugin})},e.prototype.processVIToPWD=function(t){t=t||{},this.isIOSBioAuth()&&a.amc.isIOS&&'2.0'!=this.props.viPlugin.getVersion()&&f(),this.startVIPlugin(t)},e.prototype.startVIPlugin=function(t){var e={pwdInputBtn:this.props.btnText,pwdPlaceHolder:'{{alipaypwd}}',fpAlertMsg:'{{confirm_exit}}',fpAlertCancelBtn:'{{confirm_btn}}',fpAlertToPwdBtn:'{{input_pwd_s}}',pwdInputTip:this.props.rpcData.pwdTip,loadingPayingText:this.props.payingText,loadingPaySuccessText:this.props.successTxt,shouldDismissAfterAuth:'N',viSourcePage:'pwdCheck'};this.props.viPlugin.startVIPlugin(p.VI_ACTION.START,t,e)},e.prototype.onDestroy=function(){this.props.viPlugin.viPageCloseNotify()},e.prototype.onKeyDown=function(){event&&4===event.which&&this.onBack()},e.prototype.forgetPwd=function(){if(a.amc.fn.spmClick('a283.b4038.c9698.d17395',{}),this.props.forgetPwdWithOtherVIWay)a.amc.fn.postNotification('kMQPFindPwdNotification',{}),this.props.viPlugin.sendForgetPwdPayWithOtherWayToVI();else{var t='';this.props.rpcData.logon_id&&(t='&loginId='+this.props.rpcData.logon_id);var e='alipays://platformapi/startApp?appId=20000013&pwdType=phonePassword'+t+'&bizScene=mobilecashier_sdk_pay';a.amc.fn.postNotification('kMQPFindPwdNotification',{}),document.submit({action:{name:'loc:openurl(\\''+e+'\\',\\'0\\')'}})}},e.getComponentCSSRules=function(){return{'.stretch-self':'_PwdCheckPage_r3p9-c-stretch-self','.pd-lr':'_PwdCheckPage_r3p9-c-pd-lr','.pd-t':'_PwdCheckPage_r3p9-c-pd-t','.pd-t-l':'_PwdCheckPage_r3p9-c-pd-t-l','.font-m':'_PwdCheckPage_r3p9-c-font-m','.font-l-white':'_PwdCheckPage_r3p9-c-font-l-white','.amc-i-nav-m-box':'_PwdCheckPage_r3p9-c-amc-i-nav-m-box','.pwd-tips':'_PwdCheckPage_r3p9-c-pwd-tips','.forget-pwd-box':'_PwdCheckPage_r3p9-c-forget-pwd-box'}},e.getComponentJson=function(){return{'sp-view-id':'container',_c:'amc-v-box amc-flex-1',_t:'div',_cd:[{'sp-view-id':'iLine',_c:'amc-1px-title-line',_t:'div'},{'sp-view-id':'mainBody',_c:'amc-v-box amc-flex-1',_t:'div',_cd:[{'sp-view-id':'tipsBox',_c:'amc-flex-center _PwdCheckPage_r3p9-c-pd-lr _PwdCheckPage_r3p9-c-pd-t',_t:'div',_cd:[{'sp-view-id':'tips',_c:'amc-flex-1 _PwdCheckPage_r3p9-c-pwd-tips amc-text-center amc-ellipsis-2-line',_t:'label'}]},{'sp-view-id':'pwdBox',_c:'_PwdCheckPage_r3p9-c-pd-lr amc-v-box',_t:'div',_cd:[{'sp-component':'VIPlugin','sp-component-id':'viPluginComponent',_t:'div'},{_c:'_PwdCheckPage_r3p9-c-forget-pwd-box amc-flex-1 amc-flex-center',_t:'div',_cd:[{'sp-view-id':'forgetPwdText',_c:'amc-text-center amc-theme-color amc-flex-1 amc-ellipsis-2-line _PwdCheckPage_r3p9-c-font-m',_t:'label',_x:'{{forget_pwd}}'}]}]},{'sp-view-id':'pwdLoad',_c:'amc-loading-box',_t:'div',_cd:[{_c:'amc-flex-space-1',_t:'div'},{_y:'MQPPayGifView','sp-view-id':'pwdLoadGif',_c:'amc-load-img',_t:'embed'},{_c:'_PwdCheckPage_r3p9-c-pd-t-l _PwdCheckPage_r3p9-c-stretch-self _PwdCheckPage_r3p9-c-pd-lr',_t:'div',_cd:[{'sp-view-id':'loadTxt',_c:'_PwdCheckPage_r3p9-c-font-l-white amc-flex-1 amc-text-center amc-ellipsis-2-line',_t:'label',_x:'{{paying}}'}]},{_c:'amc-flex-space-2',_t:'div'}]}]}]}},e.componentName='PwdCheckPage',e.componentHashName='PwdCheckPage_r3p9',e}(r.BNComponent);window.onLoad=function(){(new g).mountTo(document.body)}},function(t,e,o){'use strict';var s=this&&this.__assign||function(){return(s=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var i in e=arguments[o])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,'__esModule',{value:!0});var I=o(1),b=o(5),T=o(6),w=o(11),n=function(){function t(){this.vueModel={data:{},compute:{}},this._componentName=this.constructor.componentName,this._htmlString=this.constructor.componentHTML,this._componentJson=this.constructor.getComponentJson(),this._componentCSSRules=this.constructor.getComponentCSSRules(),this._hash=b.randomStr(),this._hasRootViewBuilt=!1,this._rootView=null,this._componentId='',this._subComponents=[],this._subComponentsMap={},this._viewsIdMap={}}return t.getComponentCSSRules=function(){throw new Error('E0100')},t.getComponentJson=function(){throw new Error('E0101')},t.prototype.mountTo=function(t,e){if(t){var o=this._acquireRootView();o?(e?t.insertBefore(o,e):t.appendChild(o),this._triggerOnMounted()):I.logger.e('Cmp#mT','E0103 '+o)}else I.logger.e('Cmp#mT','E0102 '+t)},Object.defineProperty(t.prototype,'debugName',{get:function(){return'<'+this._componentName+' '+this._hash+' id:'+this._componentId+'>'},enumerable:!0,configurable:!0}),t.prototype.getMountedRootView=function(){return this._hasRootViewBuilt?this._rootView:null},t.prototype.getMountedParentView=function(){var t=this.getMountedRootView();return t?t.parentNode:null},t.prototype.getSubComponentById=function(t,e){var o=this.debugName+'#SCById',n=this._subComponentsMap[t];if(!n)return null;var i='',r='';try{i=n.constructor.componentName,r=e.componentName}catch(t){I.logger.e(o,'E0104 '+t)}return i&&i===r?n:(I.logger.e(o,'E0105 '+i+', '+r),null)},t.prototype.getViewInComponentById=function(t){return this._viewsIdMap[t]},t.prototype.getComponentId=function(){return this._componentId},t.prototype.createStyledElement=function(t,e,o){var n=document.createElement(t);if(n)return t&&(n.className+=' '+this._css(t,2)),o&&(n.className+=' '+this._csses(o,1)),e&&(n.className+=' '+this._css('#'+e,2)),n},t.prototype.applyStyleTo=function(t,e){t&&(t.className+=' '+this._csses(e,1))},t.prototype.css=function(t){return this._css(t,0)},t.prototype.csses=function(t){return this._csses(t,0)},t.prototype._csses=function(t,e){var o=this;return t.split(' ').map(function(t){return o._css(t,e)}).join(' ')},t.prototype._css=function(t,e){if(!t)return'';var o=this._componentCSSRules;if(!o)return t;switch(t.charAt(0)){case'#':case'.':return o[t]||t;default:switch(e){case 0:return o['.'+t]||o[t]||t;case 1:return o['.'+t]||t;case 2:default:return t}}},t.prototype._triggerOnMounted=function(){new w.Observer(this.vueModel.data),I.logger.i('','I0106 '+this.debugName);for(var t=0,e=this._subComponents;t<e.length;t++){var o=e[t];o&&o._triggerOnMounted()}this.onMounted&&this.onMounted()},t.prototype._getMethod=function(t){var e=this[t];return e instanceof Function?e:null},t.prototype._acquireComponentJson=function(){var t=this.debugName+'#acCJ',e=T.ComponentRegistry.getComponentJson(this._componentName);return e?(I.logger.i(t,'I0107'),e):void 0!==this._componentJson?(I.logger.i(t,'I0108'),T.ComponentRegistry.putComponentJson(this._componentName,this._componentJson),this._componentJson):(I.logger.e(t,'E0109'),null)},t.prototype._acquireRootView=function(){var t=this.debugName+'#acRV';if(this._hasRootViewBuilt)return I.logger.i(t,'I0110'),this._rootView;var e=this._acquireComponentJson();return e?(this._rootView=this._convertJsonToBNNode(e,this.vueModel.data||{}),this._hasRootViewBuilt=!0,I.logger.i(t,'I0112'),this._rootView):(I.logger.e(t,'E0111'),null)},t.prototype._genArrayChildNode=function(t,e,o,n,i){var r=this._convertJsonToBNNode(t,s({},e,{item:o,index:n,arrayName:i}));return r?(r.setAttribute('index',n),r.setAttribute('for_name',i),r):null},t.prototype._convertJsonToBNNode=function(t,c){var u=this,e=this.debugName+'#cJTB';if(void 0===t._t)return null;var l=document.createElement(t._t),d=[];if(void 0!==t._cd)for(var o=function(s){if(s['v-for']||s['v-for-cal']){var t=!s['v-for']&&!!s['v-for-cal'],e=(t?h.vueModel.compute:c)||{},o=w.vueUtils.getObject(t?s['v-for-cal']:s['v-for'],e,t),a=t?w.vueUtils.rmSymbol(s['v-for-cal']):w.vueUtils.rmSymbol(s['v-for']);if(!a||!o)return'continue';for(var n in s['v-for']='',s['v-for-cal']='',o)if(o.hasOwnProperty(n)){var i=h._genArrayChildNode(s,c,o[n],n,a);i&&d.push(i)}var p=document.createElement('div');p&&(p.style.display='none',p.setAttribute('for_end',a),d.push(p),new w.Watcher(a,e,function(t){if(l){w.rmWatchers(a);for(var e=0,o=l.childNodes;e<o.length;e++){var n=o[e];n.getAttribute('for_name')===a&&l.removeChild(n)}if(t)for(var i in t)if(t.hasOwnProperty(i)){var r=u._genArrayChildNode(s,c,t[i],i,a);r&&l.insertBefore(r,p)}}},t).id=c.arrayName)}else{var r=h._convertJsonToBNNode(s,c);if(!r)return'continue';d.push(r)}},h=this,n=0,i=t._cd;n<i.length;n++)o(i[n]);if(!l)return null;c&&c.index&&l.setAttribute('index',c.index);var r=t['bn-component']||t['sp-component'];if(r){I.logger.i(e,'I0113 '+r);var s=T.ComponentRegistry.createComponent(r);if(!s)return I.logger.e(e,'E0114 '+r+', '+s),null;var a=t['bn-component-id']||t['sp-component-id'];return a&&(s._componentId=a),s.onCreated&&s.onCreated(),I.logger.i(e,'I0115 '+s.debugName+', '+a),this._subComponents.push(s),a&&!this._subComponentsMap[a]&&(this._subComponentsMap[a]=s),s._acquireRootView()}var p=t['bn-view-id']||t['sp-view-id'];for(var f in p&&(I.logger.i(e,'I0116 '+p),this._viewsIdMap[p]||(this._viewsIdMap[p]=l)),t._i&&(l.id=t._i),t._c&&(l.className=t._c),t._s&&(l.style.cssText=t._s),t._x&&(l.innerText=t._x),t._y&&(l.type=t._y),t)if(t.hasOwnProperty(f))if(0===f.indexOf('on')){var m=this._getMethod(t[f]);m&&(l[f]=m.bind(this,l))}else if(0===f.indexOf('_'));else if(0===f.indexOf('bn-')||0===f.indexOf('sp-'));else if(b.startsWith(f,'v-')){var g=f.split('-');if(2===g.length||3===g.length){var y=g[1];2===g.length?new w.NodeCompile(c).compile(y,l,t[f],t._t):'cal'===g[2]&&new w.NodeCompile(this.vueModel.compute,!0).compile(y,l,t[f],t._t)}else l[f]=t[f]}else l[f]=t[f];for(var v=0,_=d;v<_.length;v++){var P=_[v];l.appendChild(P)}return l},t.componentName='',t.componentHTML='',t.componentCSS='',t.componentHashName='',t}();e.BNComponent=n},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var r,s=o(5),a=o(0);e.rmWatchers=function(e){r=r.filter(function(t){return t.id!==e})};var p=function(){function t(t,e,o,n){if(this.id='',this.lazy=!1,e&&'object'==typeof e){if(this.lazy=n,this.callback=o,s.startsWith(t,'item')&&e.arrayName&&e.index){var i=t.replace('item','');this.expression=e.arrayName+'.'+e.index,i&&(this.expression+=i)}else this.expression=t;this.data=e,this.value=u.getVal(t,e,this.lazy),r||(r=[]),r.push(this)}}return t.prototype.update=function(){if(this.data&&this.expression&&this.callback){var t=u.getVal(this.expression,this.data,this.lazy),e=this.value;u.equals(t,e)||(this.value=t,this.callback(t))}},t}();e.Watcher=p;var n=function(){function t(t){this.observe(t)}return t.prototype.observe=function(e){var o=this;e&&'object'==typeof e&&Object.keys(e).forEach(function(t){try{o.defineReactive(e,t,e[t]),o.observe(e[t])}catch(t){}})},t.prototype.defineReactive=function(t,e,o){var n=this;Object.defineProperty(t,e,{enumerable:!0,configurable:!1,get:function(){return o},set:function(t){u.equals(t,o)||(o=t,n.observe(t),r&&r.forEach(function(t){t.update()}))}})},t}();e.Observer=n;var i=function(){function t(t,e){void 0===e&&(e=!1),this.data=t||{},this.lazy=e}return t.prototype.compile=function(o,t,e,n){var i=this;if(t)switch(o){case'text':this.labelProcess(t,e,function(t,e){t.innerText=void 0===e?'':e});break;case'html':this.labelProcess(t,e,function(t,e){t.innerHtml=void 0===e?'':e});break;case'class':this.labelProcess(t,e,function(t,e){var o=t.className,n=(o=o.replace(e,'').replace(/\\s$/,''))&&String(e)?' ':'';t.className=o+n+e});break;case'model':this.eventProcess(t,e,function(t,e){t.value=e}),'input'===n?t.oninput=function(){u.setTextVal(e,t.value,i.data)}:'switch'===n&&(t.onchange=function(t){u.setTextVal(e,t||'off',i.data)});break;case'if':this.eventProcess(t,e,function(t,e){!0===e?(t.style.display='flex',c.process(t,function(t){a.amc.fn.spmExposure(t.spmId,t.param4Map,t.doNotResume)})):t.style.display='none'});break;case'spm':this.labelProcess(t,e,function(t,e){t.setAttribute('spm',void 0===e?'':e)});break;case'click':this.eventProcess(t,e,function(t,e){u.isFunction(e)?t.onclick=function(){e(t),c.process(t,function(t){a.amc.fn.spmClick(t.spmId,t.param4Map)})}:t.onclick=function(){}});break;default:this.labelProcess(t,e,function(t,e){t[o]=void 0===e?'':e})}},t.prototype.labelProcess=function(o,n,i){var r=this,t=n.match(/@\\{([^}]+)\\}/g),e=u.getTextVal(n,this.data,this.lazy);t&&t.forEach(function(t){var e=/@\\{([^}]+)\\}/g.exec(t);e&&1<e.length&&(new p(e[1],r.data,function(t){i(o,u.getTextVal(n,r.data,r.lazy))},r.lazy).id=r.data.arrayName)}),i(o,e)},t.prototype.eventProcess=function(e,t,o){var n=/@\\{([^}]+)\\}/g.exec(t),i=u.getObject(t,this.data,this.lazy);n&&1<n.length&&(new p(n[1],this.data,function(t){o(e,t)},this.lazy).id=this.data.arrayName),o(e,i)},t}();e.NodeCompile=i;var c=function(){function t(){}return t.process=function(t,e){var o=t.getAttribute('spm');if(o)try{var n=JSON.parse(o);n&&n.spmId&&e(n)}catch(t){}},t}(),u=function(){function a(){}return a.item2ArrayIndex=function(t,e){var o=t;if(s.startsWith(t,'item')&&e.arrayName&&e.index){var n=t.replace('item','');o=e.arrayName+'.'+e.index,n&&(o+=n)}return o},a.getVal=function(t,e,o){if(t){var n=t.split('.').reduce(function(t,e){return t[e]},e);return o?a.isFunction(n)?n():void 0:n}},a.getTextVal=function(t,i,r){var s=this;return t.replace(/@\\{([^}]+)\\}/g,function(){for(var t,e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];if(r)t=s.getVal(e[1],i,r);else{var n=a.item2ArrayIndex(e[1],i);t=s.getVal(n,i,!1)}return void 0===t?'':t})},a.getObject=function(t,e,o){var n=/@\\{([^}]+)\\}/g.exec(t);if(n&&1<n.length)return this.getVal(n[1],e,o)},a.rmSymbol=function(t){var e=/@\\{([^}]+)\\}/g.exec(t);return e&&1<e.length?e[1]:''},a.setVal=function(t,n,e){var i=t.split('.');return i.reduce(function(t,e,o){return o===i.length-1?t[e]=n:t[e]},e)},a.setTextVal=function(t,e,o){var n=/@\\{([^}]+)\\}/g.exec(t);n&&1<n.length&&this.setVal(n[1],e,o)},a.equals=function(t,e){return this.eq(t,e,void 0,void 0)},a.eq=function(t,e,o,n){if(t===e)return 0!==t||1/t==1/e;if(null==t||null==e)return t===e;var i=toString.call(t);if(i!==toString.call(e))return!1;switch(i){case'[object RegExp]':case'[object String]':return''+t==''+e;case'[object Number]':return+t!=+t?+e!=+e:0==+t?1/+t==1/e:+t==+e;case'[object Date]':case'[object Boolean]':return+t==+e}var r='[object Array]'===i;if(!r){if('object'!=typeof t||'object'!=typeof e)return!1;var s=t.constructor,a=e.constructor;if(s!==a&&!(this.isFunction(s)&&s instanceof s&&this.isFunction(a)&&a instanceof a)&&'constructor'in t&&'constructor'in e)return!1}n=n||[];for(var p=(o=o||[]).length;p--;)if(o[p]===t)return n[p]===e;if(o.push(t),n.push(e),r){if((p=t.length)!==e.length)return!1;for(;p--;)if(!this.eq(t[p],e[p],o,n))return!1}else{var c=Object.keys(t),u=void 0;if(p=c.length,Object.keys(e).length!==p)return!1;for(;p--;)if(u=c[p],!e.hasOwnProperty(u)||!this.eq(t[u],e[u],o,n))return!1}return o.pop(),n.pop(),!0},a.isFunction=function(t){return'function'==typeof t||!1},a}();e.vueUtils=u},function(t,e,o){'use strict';var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,'__esModule',{value:!0});var r=function(){function r(){this.enabled=!0}return r.fmtLine=function(t,e,o,n){var i='';return n&&(i=n instanceof Error?'- '+n.name+': '+n.message+' - '+n.stack:'- '+n),'['+t+']['+r.fmtTime()+']['+e+']'+o+' '+i},r.fmtTime=function(){var t=new Date;return t.getHours()+':'+t.getMinutes()+':'+t.getSeconds()+'.'+t.getMilliseconds()},r.prototype.enable=function(){this.enabled=!0},r.prototype.disable=function(){this.enabled=!1},r}();e.Logger=r,e.logger=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.e=function(t,e,o){},e.prototype.i=function(t,e,o){},e}(r))},function(t,e,o){'use strict';var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,'__esModule',{value:!0});var r=o(8),s=o(1),a=o(2);s.ComponentRegistry.registerComponent(r.Button.componentName,r.Button);var p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.props={buttonsNum:0,container:{},buttons:[],onClick:function(t){}},t.onCreated=function(){},t.onMounted=function(){t.props.container=t.getViewInComponentById('buttonContainer')},t}return i(t,e),t.prototype.addButton=function(t,e){var o=this;0===this.props.buttons.length&&this.initDom(),this.props.buttons.push(t),t.mountTo(this.props.container);var n=this.props.buttons.length-1;t.setOnClick(function(){o.props.onClick(n),e&&e()})},t.prototype.setOnClick=function(t){this.props.onClick=t},t.prototype.setVisible=function(t){return a.modifyElementStyle(this,'buttonContainer',{display:t?'flex':'none'}),this},t.prototype.affectAllButtons=function(t){for(var e=0;e<this.props.buttons.length;e++)this.props.buttons[e].changeLoadingStatus(t)},t.prototype.initDom=function(t){void 0===t&&(t=!1),t?a.modifyElementClass(this,this.props.container,'',!1):this.applyStyleTo(this.props.container,'pay-btn-box')},t.getComponentCSSRules=function(){return{'.pay-btn-box':'_ButtonGroup_1afd-c-pay-btn-box'}},t.getComponentJson=function(){return{'sp-view-id':'buttonContainer',_t:'div'}},t.componentName='ButtonGroup',t.componentHashName='ButtonGroup_1afd',t}(s.BNComponent);e.ButtonGroup=p},function(t,e,o){'use strict';Object.defineProperty(e,'__esModule',{value:!0});var n=o(15),s=o(7),a=o(4),p=o(0);function r(t,e,o,n,i){void 0===i&&(i=!1);var r={action:{name:'loc:bnvb'},param:{tplid:'QUICKPAY@cashier-pwd-check-flex',tpl:e.viPwdTpl,data:{payOrder:o,pwdTip:e.pwdTip,logon_id:e.logon_id,VIData:t,confirmAct:n}}};document.submit(r)}e.buildConfirmActionWithAct=function(t,e){var o={name:t};return e&&e.name&&(o=e),o.loadtxt='',o.viChannelMode=n.VI_CHANNEL_MODE_FROM_TEMPLATE,o},e.commonStartVIPlugin=function(t,e){var o=e.showAgreement?'{{agree_auth_fp}}':'{{auth_finger_or}}',n=e.isHuaWeiFinger?s.VI_ACTION.START_HUAWEI:s.VI_ACTION.START,i=e.isHuaWeiFinger?{hwPaySuccessText:'{{pay_success}}',hwAuthingText:'{{auth_fingering}}',hwPayingText:'{{paying}}',hwRetryText:'{{retry_finger}}',hwAuthTip:o,hwInputPwdTip:'{{input_pwd_s}}'}:{fpAlertMsg:'{{confirm_exit}}',fpAlertCancelBtn:'{{confirm_btn}}',fpAlertToPwdBtn:'{{input_pwd_s}}',shouldDismissAfterAuth:'Y',pwdInputBtn:'{{confirm_btn}}',pwdPlaceHolder:'{{alipaypwd}}',pwdInputTip:e.rpcData.pwdTip,loadingPayingText:e.rpcData.doingText||'{{paying}}',loadingPaySuccessText:e.rpcData.successText||'{{pay_ok}}',LoginId:e.rpcData.logon_id,costTip:e.rpcData.costTip||'',usePwd:e.usePwd?'Y':'N',pwdBtnShow:e.rpcData.rightBtn?'Y':'N',kVIDisableForgetPwdKey:e.kVIDisableForgetPwdKey?'Y':'N',viSourcePage:e.viSourcePage||''};e.setViCallBack&&(e.isHuaWeiFinger?e.setViCallBack.HuaWei&&e.setViCallBack.HuaWei():e.setViCallBack.standard&&e.setViCallBack.standard()),e.viPlugin.startVIPlugin(n,t,i)},e.commonProcessVIToPWD=function(t,e){var o={};if(p.amc.fn.isString(t))try{o=JSON.parse(t)}catch(t){p.amc.fn.logError('pVI2PWD','json parse error')}else o=t;var n=o.version,i=o.usePwd;a.logAction('VIToPwD-'+(n||'version-'+(i||'usePwd')),s.VI_LOG_ACTION_TYPE),'2.0'!==n?(e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL),r(t,e.rpcData,e.canGoBack,e.confirmAction,'Y'===i)):e.startVIPlugin(t,!1,'Y'===i)},e.gotoPwdPage=r,e.processVIRpcRequest=function(t,e){e.submitPay(t,function(t){'0001'===t.status&&e.viPlugin.onPaySuccess(),e.viPlugin.getIsPaySuccessShown()||('1'===t.pageloading?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.PAYING):'0'===t.pageloading&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL))})&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.PAYING)},e.commonProcessVIResult=function(t,e){var o={};if(p.amc.fn.isString(t))try{o=JSON.parse(t)}catch(t){p.amc.fn.logError('processVIResult','json parse error')}else o=t;a.logAction('viResult-'+(o.code||'code')+'-'+(e.viPlugin.getVersion()||'version')+'-'+(e.viPlugin.getPwdType()||'VIType'),s.VI_LOG_ACTION_TYPE),'2.0'!==e.viPlugin.getVersion()||'1000'!==o.code||e.viPlugin.getPwdType()!==s.VI_TYPE.PASSWORD&&e.viPlugin.getPwdType()!==s.VI_TYPE.SHORT_PASSWORD||(p.amc.isIOS?p.amc.fn.showLoading(!0,!0):setTimeout(function(){document.invoke('showLoading')},10))},e.processVIStatus=function(t,e){if(t){var o=t.status,n=t.type,i=t.version,r=t.scene;a.logAction((n||'type')+'-'+(o||'status')+'-'+(i||'version')+'-'+(r||'scene'),s.VI_LOG_ACTION_TYPE),'start'===o?(e.setCanGoBack('wl'!==n),n!==s.VI_TYPE.PASSWORD&&n!==s.VI_TYPE.SHORT_PASSWORD||'2.0'===i?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.VERIFYING):e.processVIToPWD()):'end'===o?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):'abort'===o?s.RETAIN_VI_SCENE.hasOwnProperty(r)?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):s.EXIT_VI_SCENE.hasOwnProperty(r)?document.submit({action:{name:'loc:exit'}}):n===s.VI_TYPE.SHORT_PASSWORD||n===s.VI_TYPE.PASSWORD?e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL):p.amc.fn.exit():'awaitUser'===o&&e.viPlugin.toggleBioProcessLoading(s.VI_STATUS.NORMAL)}},e.pwdFreePay=function(t,e){var o={action:t};o.action.neec='6004',e.toggleBioProcessLoading(s.VI_STATUS.VERIFYING),document.asyncSubmit(o,function(t){if(!e.getIsPaySuccessShown())switch(t.status){case'0001':e.onPaySuccess();break;case'0000':case'0002':case'0006':e.onPayFailure()}})}},function(t,e,o){'use strict';var n;Object.defineProperty(e,'__esModule',{value:!0}),e.VI_CHANNEL_MODE_FROM_TEMPLATE='1',(n=e.SCALE_FACTOR||(e.SCALE_FACTOR={})).LEVEL_0='0',n.LEVEL_1='1',n.LEVEL_2='2',n.LEVEL_3='3',n.LEVEL_4='4'}])"}], "tag": "script", "type": "text/javascript"}], "tag": "head"}, {"css": "amc-i-body", "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "onLoad()"}], "tag": "html"}, "publishVersion": "150924", "name": "cashier-pwd-check-flex", "format": "JSON", "tag": "QUICKPAY", "time": "0302", "tplId": "QUICKPAY@cashier-pwd-check-flex", "tplVersion": "5.3.7"}