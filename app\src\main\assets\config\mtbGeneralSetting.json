{"props": {"settingItemsSource": {"snapshot": {"bundleName": "TBGeneralSettingMenuV2.json", "filePath": "TBGeneralSettingMenuV2.json"}, "mtopParams": {"api": "mtop.taobao.reborn.mclaren.general.setting.get", "version": "2.0"}}}, "source": {"initTimeout": 3000, "sourceList": [{"name": "settingItemsSource", "type": "source.mtb.common.mtop.snapshot", "defaultLoad": 1}, {"name": "settingStatusSource", "type": "source.mtb.user.setting.status", "defaultLoad": 0}]}, "transformer": {"nativeTransformerList": [{"name": "mergeItemAndStatus", "type": "transformer.mtb.general.settings.mergeItemAndStatus"}], "jsTransformerList": []}, "event": {}, "layout": {"renderTemplate": {"name": "ta<PERSON><PERSON>_mytaobao_setting_general", "renderType": "dinamicX", "renderData": {"name": "ta<PERSON><PERSON>_mytaobao_setting_general", "version": "18", "url": "https://dinamicx.alibabausercontent.com/pub/taobao_mytaobao_setting_general/1744600401376/taobao_mytaobao_setting_general.zip", "heightMode": "matchParent", "immersiveStatusBar": "1"}}, "data": {"settingItems": "${runtimeData.settingItems}", "showProgress": "${originalData.settingItemsSource.isRequestingWhenInvalidSetting}"}, "userTrack": {}, "children": {}, "eventHandler": {"btnClick": [{"type": "eventhandler.jdy.common.dialog.dismiss", "data": {}}]}}}