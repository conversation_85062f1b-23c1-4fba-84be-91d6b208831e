{"container": {"data": [{"name": "%s", "containerType": "dinamicx", "version": "%s", "url": "%s", "md5": null, "type": ["dinamicx$ol3_orderlisttitle$0$boughtlist"]}, {"name": "%s", "containerType": "dinamicx", "version": "%s", "url": "%s", "md5": null, "type": ["dinamicx$new_babel_orderlistmaintabs$0$boughtlist4"]}, {"name": "%s", "containerType": "dinamicx", "version": "%s", "url": "%s", "md5": null, "type": ["dinamicx$ol3_orderlisttabs$0$25"]}, {"name": "babel_listRefund", "containerType": "weex2", "version": "0", "url": "https://meta.wapa.taobao.com/app/mtb/refund-list/home?wh_weex=true&weex_mode=dom", "md5": null, "type": ["weex2$babel_listRefund"]}]}, "data": {"orderList": {"tag": "orderList"}, "root": {"tag": "root"}, "query3": {"tag": "query3", "type": "dinamicx$ol3_orderlisttitle$0$boughtlist", "position": "header", "fields": {"actionUrl": "https://meta.m.taobao.com/app/dinamic/tb-logistics/home?wh_weex=true&weex_mode=dom", "isNewSearch": "true", "isSearchDowngrade": "false", "isSearchResult": "false", "navToCart": "false", "orderFilter": "true", "url": "%s", "%s": "我的包裹"}}, "mainTab": {"tag": "mainTab", "position": "header", "type": "dinamicx$new_babel_orderlistmaintabs$0$boughtlist4", "fields": {"curTabIndex": "0", "isHeaderTitle": "true", "categoryTabs": [{"code": "all", "index": "0", "title": "全部订单"}, {"code": "shopping", "index": "1", "title": "购物"}, {"code": "sg", "bigSaleTagImageUrl": "https://img.alicdn.com/imgextra/i2/O1CN016KuCYl1hA8nPSsRYL_!!6000000004236-2-tps-104-56.png", "tagImageUrl": "https://img.alicdn.com/imgextra/i4/O1CN0108scaf1TRdJGKcIcT_!!6000000002379-2-tps-104-56.png", "index": "2", "title": "闪购"}]}}, "listtab": {"tag": "listtab", "type": "dinamicx$ol3_orderlisttabs$0$25", "position": "header", "fields": {"isContainRefund": "true"}}, "listRefund": {"tag": "weex2", "type": "weex2$babel_listRefund", "position": "body"}}, "linkage": {"common": {"compress": "false"}}, "hierarchy": {"root": "orderList", "structure": {"orderList": ["root"], "root": ["query3", "mainTab", "listtab", "listRefund"]}}, "endpoint": {"ultronage": "true", "protocolVersion": "3.0"}, "reload": "true"}