{"data": {"children": [{"children": [{"src": "VIVerifyCore.bundle/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "VIVerifyCore.bundle/mic.css"}, {"src": "VIVerifyCore.bundle/vi-amc.js", "tag": "script"}, {"src": "VIVerifyCore.bundle/mic.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.i18n", "tag": "meta", "type": "i18n"}, {"rel": "stylesheet", "tag": "link", "href": "android-phone-securitycommon-verifyidentitybiz/mic.css"}, {"src": "android-phone-securitycommon-verifyidentitybiz/vi-amc.js", "tag": "script"}, {"src": "android-phone-securitycommon-verifyidentitybiz/mic.js", "tag": "script"}, {"children": [{"tag": "text", "text": "var hide = amc.fn.hide;\n            var show = amc.fn.show;\n            var getTag = amc.fn.getById;\n            var rpc = amc.rpcData;\n            var gQuestionJson = {};\n            try {\n                gQuestionJson = JSON.parse(rpc.questionJson);\n            } catch (error) {\n                gQuestionJson = {};\n            }\n            \n            var gSubmitBtn = getTag(\"submitBtn\");\n            var gInputArray = [];\n            \n            function init() {\n                try {\n                    initUI();\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit(true);\n                    }\n                } \n                catch(e) {\n                    if (amc.fn.logPageInit) {\n                        amc.fn.logPageInit();\n                    }\n                }\n            }\n\n            function initUI() {\n                alert(\"AAA\");\n                //head_title\n                var naviTitle = rpc.head_title || \"身份驗證\";\n                var nav = amc.fn.getNav(amc.res.navBack, '{{return}}', naviTitle, null, null, onBack, null);\n                getTag(\"bodyContainer\").insertBefore(nav, getTag(\"mainBody\"));\n\n                getTag(\"bodyTitle\").innerText = rpc.body_title || \"驗證身份證號\";\n                getTag(\"subTitle\").innerText = rpc.form_title || \"輸入身份證號，以驗證身份\";\n\n                //中间是组件，有多个问题，需要动态渲染(目前只有一个页面)\n                if (gQuestionJson && gQuestionJson.pages && gQuestionJson.pages.length > 0) {\n                    var firstPage = gQuestionJson.pages[0];\n                    if (firstPage.queryViews && firstPage.queryViews.length > 0) {\n                        for (var i = 0; i < firstPage.queryViews.length; i++) {\n                            var item = firstPage.queryViews[i];\n                            //多个问题\n                            var requestContainer = amc.fn.create(\"div\");\n                            requestContainer.className = \"request-container\";\n\n                            var title = amc.fn.create(\"label\");\n                            title.className = \"request-title-label\";\n                            title.innerText = item.text || \"身份證號\";\n                            requestContainer.appendChild(title);\n\n                            var input = amc.fn.create(\"input\");\n                            input.id = \"input_\" + i;\n                            input.className = \"input\";\n                            input.type = \"number\";\n                            input.placeholder = item.placeHolder || \"請輸入身份證號\";\n                            input.oninput = \"onInputChanged()\";\n                            requestContainer.appendChild(input);\n                            //加到数组中\n                            gInputArray.push(input);\n\n                            var line = amc.fn.create(\"div\");\n                            line.className = \"input-line\";\n                            requestContainer.appendChild(line);\n\n                            //如果有多个问题，每个问题之间需要加margin\n                            if (firstPage.queryViews.length > 1 && i != firstPage.queryViews.length - 1) {\n                                var marginDiv = amc.fn.create(\"div\");\n                                marginDiv.className = \"request-v-margin\";\n                                requestContainer.appendChild(marginDiv);\n                            }\n\n                            getTag(\"mainBody\").insertBefore(requestContainer, gSubmitBtn);\n                        }\n                    }\n                }\n\n                gSubmitBtn.innerText = rpc.form_button || \"提交\";\n                getTag(\"changeModule\").innerText = rpc.foot_tip || \"換個驗證方式\";\n                if (Boolean(rpc.HAS_OTHERS)) {\n                    getTag(\"changeModule\").style.visibility = \"visible\";\n                }\n\n                //第一个input高亮\n                if (gInputArray.length > 0) {\n                    var firstInput = gInputArray[0];\n                    var timeInterval = 100;\n                    if (amc.isAnroid) {\n                        timeInterval = 1000;\n                    }\n                    setTimeout(function() {\n                        firstInput.focus();\n                    }, timeInterval);\n                }\n            }\n\n            function onKeyDown() {\n                if (event.which == 4) {\n                    onBack();\n                }\n            }\n\n            function onBack() {\n                var obj = {\n                    \"eventName\" : \"vi_quit_module\"\n                };\n                document.submit(obj);\n            }\n\n            function onInputChanged() {\n                var disabled = false;\n                for (var i = 0; i < gInputArray.length; i++) {\n                    var input = gInputArray[i];\n                    if (!input.value || input.value.length == 0) {\n                        disabled = true;\n                    }\n                }\n                gSubmitBtn.disabled = disabled;\n            }\n\n            //获取最后上传给服务的数据\n            function getFinalInputData() {\n                var privacyQuestionAnswerViews = [];\n                if (gQuestionJson && gQuestionJson.pages && gQuestionJson.pages.length > 0) {\n                    var firstPage = gQuestionJson.pages[0];\n                    if (firstPage.queryViews && firstPage.queryViews.length > 0) {\n                        for (var i = 0; i < firstPage.queryViews.length; i++) {\n                            var question = firstPage.queryViews[i];\n                            var answer = {\n                                multipleAnswer: [],\n                                questionId: question.questionId\n                            }\n                            var input = gInputArray[i];\n                            answer.multipleAnswer.push(input.value || \"\");\n                            privacyQuestionAnswerViews.push(answer);\n                        }\n                    }\n                }\n                return privacyQuestionAnswerViews;\n            }\n\n            function submit() {\n                //隐藏键盘\n                for (var i = 0; i < gInputArray.length; i++) {\n                    var input = gInputArray[i];\n                    input.blur();\n                }\n\n                var privacyQuestionAnswerViews = getFinalInputData();\n                var obj = {\n                    \"eventName\": \"vi_rpc_validate\",\n                    \"moduleName\": \"REGISTER_CERTIFICATE\",\n                    \"actionName\": \"VERIFY\",\n                    \"showLoading\": \"true\",\n                    \"params\": {\n                        data: {\n                            privacyQuestionAnswerViews: privacyQuestionAnswerViews\n                        }\n                    }\n                };\n\n                document.asyncSubmit(obj, function(data) {\n                    if (true === Boolean(data[\"verifySuccess\"])) {\n                        mic.fn.onBackWithResponse(data);\n                    }\n                    else {\n                        //验证失败\n                        var verifyMessage = data[\"verifyMessage\"] || \"人氣太旺了，請稍後再試\";\n                        if(!Boolean(data[\"finish\"]) && data[\"nextStep\"] === \"REGISTER_CERTIFICATE\") {\n                            //重试\n                            document.toast({\n                                text: verifyMessage,\n                            }, function() {});\n                        }\n                        else {\n                            //验证次数限制\n                            var buttonText = Boolean(rpc.HAS_OTHERS) ? \"換個驗證方式\" : \"確定\";\n                            amc.fn.viAlert({\n                                \"title\": \"\",\n                                \"message\": verifyMessage,\n                                \"button\": buttonText\n                            }, function() {\n                                if (Boolean(rpc.HAS_OTHERS)) {\n                                    changeModule();\n                                }\n                                else {\n                                    mic.fn.onBackWithResponse(data);\n                                }\n                            });\n                        }\n                    }\n                });\n            }\n\n            function changeModule() {\n                //隐藏键盘\n                var input = gInputArray[0];\n                input.blurForceLostFocus();\n                mic.fn.changeModule();\n            }"}], "tag": "script"}, {"children": [{"tag": "text", "text": ".main-body {\n                background-color: #ffffff;\n            }\n\n            .main-title-label {\n                text-align: center;\n                font-size: 30px;\n                color: #000000;\n                line-height: 42px;\n                margin-top: 32px;\n            }\n\n            .sub-title-label {\n                text-align: center;\n                font-size: 18px;\n                color: #888888;\n                line-height: 24px;\n                margin-top: 16px;\n                margin-left: 30px;\n                margin-right: 30px;\n            }\n\n            .content-placeholder {\n                height: 38px;\n            }\n\n            .request-container {\n                display: flex;\n                flex-direction: column;\n            }\n\n            .request-title-label {\n                text-align: left;\n                font-size: 18px;\n                color: #333333;\n                line-height: 24px;\n                margin-left: 16px;\n            }\n\n            .input {\n                border: 0;\n                color:#000;\n                font-size: 18px;\n                white-space: nowrap;\n                margin-top: 10px;\n                margin-left: 16px;\n                margin-right: 16px;\n                height: 40px;\n                padding: 0px 0px 0px 0px;\n            }\n\n            .input-line {\n                margin-left: 16px;\n                margin-right: 16px;\n                height: 0.5px;\n                background-color: #d1d1d1;\n            }\n\n            .request-v-margin {\n                height: 25px;\n            }\n\n            .submit-btn {\n                margin-top: 24px;\n                margin-left: 16px;\n                margin-right: 16px;\n                border-radius: 2px;\n                border: 0;\n                color: #fff;\n                font-size: 18px;\n                height: 48px;\n                background-color: #1677ff;\n            }\n\n            .submit-btn:active {\n                background-color: rgba(16, 142, 233, 1);\n            }\n\n            .submit-btn:disabled {\n                background-color: rgba(16, 142, 233, 0.6);\n            }\n\n            .change-module-label {\n                text-align: center;\n                font-size: 18px;\n                color: #1677ff;\n                line-height: 22px;\n                margin-top: 24px;\n                width: 140px;\n                align-self: center;\n                visibility: hidden; \n            }"}], "tag": "style"}], "tag": "head"}, {"css": "mic-body-opacity", "children": [{"css": "mic-fullscreen", "children": [{"css": "mic-fullscreen main-body", "children": [{"css": "main-title-label", "tag": "label", "id": "bodyTitle"}, {"css": "sub-title-label", "tag": "label", "id": "subTitle"}, {"css": "content-placeholder", "tag": "div"}, {"css": "submit-btn", "onclick": "submit()", "disabled": "true", "tag": "button", "id": "submitBtn"}, {"css": "change-module-label", "onclick": "changeModule()", "tag": "label", "id": "changeModule"}], "tag": "div", "id": "mainBody"}], "tag": "div", "id": "bodyContainer"}], "tag": "body", "id": "body", "onkeydown": "onKeyDown()", "onload": "init()"}], "tag": "html"}, "publishVersion": "150603", "name": "idNoMacau", "format": "JSON", "tag": "MOBILEIC", "time": "0044", "tplId": "MOBILEIC@idNoMacau", "tplVersion": "5.4.4"}