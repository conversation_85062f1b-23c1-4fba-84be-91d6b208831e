{"api": "mtop.taobao.reborn.mclaren.stream", "data": {"pagebackContext": {"components": ["benefitModule2024V2", "orderModule", "logisticStatisticModule2024"], "params": ""}, "hierarchy": [[{"containerType": "dinamicx", "name": "mtb_2024_header_v2", "moduleName": "headerModule", "weight": "1", "version": "53", "url": "https://dinamicx.alibabausercontent.com/pub/mtb_2024_header_v2/1724226858689/mtb_2024_header_v2.zip"}], [{"containerType": "dinamicx", "moduleName": "benefitModule2024V2", "name": "mtb_2024_equity_vip_v4", "url": "https://dinamicx.alibabausercontent.com/pub/mtb_2024_equity_vip_v4/1739188967392/mtb_2024_equity_vip_v4.zip", "version": "49", "weight": "1"}], [{"containerType": "dinamicx", "name": "mtb_2024_order_v2", "moduleName": "orderModule", "weight": "1", "version": "22", "url": "https://dinamicx.alibabausercontent.com/pub/mtb_2024_order_v2/1726295930969/mtb_2024_order_v2.zip"}], [{"containerType": "dinamicx", "name": "mtb_2024_collection_v5", "moduleName": "logisticStatisticModule2024", "weight": "1", "version": "30", "url": "https://dinamicx.alibabausercontent.com/pub/mtb_2024_collection_v5/1724239996027/mtb_2024_collection_v5.zip"}], [{"containerType": "weex", "moduleName": "basementTip", "weight": "1"}]], "global": {"disablePageback": "false", "customSkin": {"actionBarBackgroundImage": "https://gw.alicdn.com/imgextra/i1/O1CN01ElW2fP1ebfbzCHoDB_!!6000000003890-0-tps-1125-192.jpg", "actionBarBackgroundImageForiPhoneX": "https://gw.alicdn.com/imgextra/i2/O1CN01kvLNA21IYzkfBCyIY_!!6000000000906-0-tps-1125-264.jpg", "skinPic": "https://gw.alicdn.com/imgextra/i1/O1CN012axjmR1REEKzaWl1c_!!6000000002079-0-tps-1125-1020.jpg", "skinPicForiPhoneX": "https://gw.alicdn.com/imgextra/i4/O1CN01LhlFyy1ZaggHRdy1W_!!6000000003211-0-tps-1125-880.jpg", "themeLevel": "0"}, "skinConfig": {"bgHeight": "350", "horizontalBgHeight": "110"}, "openNewService": true}, "hierarchyData": {"basementTip": {"fields": {"basementTip": {"bizParams": {"tab": "%5B%22feeds%22%2C%22contentSingleFlow%22%5D", "version": "1.0"}, "bizType": "commonSecScreen", "disableAutoCeiling": "true", "disableLowDeviceShield": "true", "disableMemoryRecycle": "false", "disablePrefetchInitData": "true", "disableQuickBackTop": "false", "fragmentWeexUrl": "https://web.m.taobao.com/app/message-social-front/wotao-basement/home?wh_weex=true&weex_mode=dom&wx_use_layoutng=true&container=recommend_my_taobao&contentSingleFlow=%E5%A5%BD%E5%8F%8B%E5%8A%A8%E6%80%81&feeds=%E7%8C%9C%E4%BD%A0%E5%96%9C%E6%AC%A2&feedsExpParam=shoucaiFeedExp2&source=mytaobao_basement&tab=%5B%22feeds%22%2C%22contentSingleFlow%22%5D", "newBasementContainer": "true", "pageType": "weex", "pageUTParams": {"spm": "a2141.20240304.0.0", "pageName": "Page_MYTBmixtab"}, "placeHolderType": "3", "useNewStyle": "false"}}, "events": {"clickEvents": {"openBasement": {"url": ""}}, "clickUt": {"openBasement": {"arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-basement"}}}}, "benefitModule2024V2": {"fields": {"moduleTitle": "我的权益", "vipCenterAndSavingCard": {"cardType": "2", "smallVipNewStyle": "false", "smallVipVersion": "true", "vipCenterAndSavingCardItems": [{"preText": "查看海量会员权益", "spm": "a2141.7631743.70.1", "traceInfo": [], "twoLinesTitleVersion": "false"}], "vipWhiteCard": "false"}, "benefit": {"88Vip": "false", "backgroundPicDarkModeV2": "https://gw.alicdn.com/imgextra/i2/O1CN01qGGR491h3GmAWhfcb_!!6000000004221-2-tps-1077-410.png", "backgroundPicV2": "https://gw.alicdn.com/imgextra/i1/O1CN01uyMSpT1nP9OEHfLAx_!!6000000005081-2-tps-1077-410.png", "barScrollWeight": "0", "benefitGuideBars": [{"buttonText": "去领取", "clickEventName": "openBenefitBar0", "iconType": "1", "iconUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01HjN5rt1niNzPvdlUO_!!6000000005123-2-tps-78-88.png", "preText": "点击领取今日红包", "subText": "限时发放，错过可惜"}], "benefitList": [{"hasCredit": "false", "bizCode": "coinGame", "integerAssets": "0", "preText": "¥", "title": "淘金币抵", "postText": "", "decimalAssets": ".00", "benefitAssets": "0.00"}, {"postText": "张", "bizCode": "coupon", "integerAssets": "0", "title": "优惠券", "benefitAssets": "0"}, {"decimalAssets": ".00", "bizCode": "redEnvelope", "integerAssets": "0", "preText": "¥", "title": "红包", "benefitAssets": "0.00"}, {"hasCredit": "false", "bizCode": "tmallPoint", "integerAssets": "0", "title": "天猫积分", "benefitAssets": "0"}], "crowdType": "0", "isBgColorLight": "false", "taoBaoTotalSaving": {"backgroundPic": "https://gw.alicdn.com/imgextra/i1/O1CN01SVXeuL1Gh7XxzXBAC_!!6000000000653-2-tps-693-300.png", "backgroundPicDarkMode": "https://gw.alicdn.com/imgextra/i1/O1CN01wpcBv31OGZW3p1n8p_!!6000000001678-2-tps-693-300.png", "bizCode": "defaultTotalSaving", "enableAssetsScroll": "false", "spm": "a2141.7631743.888.15", "title": "查收你的年度账单", "totalSavingBarPic": "https://gw.alicdn.com/imgextra/i4/O1CN01E6aDCf1ZSu0LPc2E5_!!6000000003194-2-tps-279-48.png"}}, "moduleSubTitle": "全部", "mtbSubVersion": "benefitSmallVip"}, "events": {"clickEvents": {"openBenefitBar0": {"url": "https://pages-fast.m.taobao.com/wow/bz/jingmi/1592?x-ssr=true&forceThemis=true&disableNav=YES&x-preload=true&disableProgress=true&skeleton=true&tabType=redEnvelope&dailyRedPacket=true&dailyRedPacketAnchor=true&spm=a2141.7631743.888.99&scm=********.1.10.42"}, "openVip0": {"url": "https://pages-fast.m.taobao.com/wow/z/blackvip/v/home?x-ssr=true&disableNav=YES&status_bar_transparent=true&x-preload=true&disableProgress=true&memberV=1&from=mytao&carryType=BACKUP_MODULE&materialInfo=22577--0-0&spm=a2141.7631743.70.1&scm=********.1.2.102"}, "open0": {"url": "https://pages-fast.m.taobao.com/wow/z/tmtjb/town/home?spm=a2141.7631743.888.1&scm=********.1.2.42&x-ssr=true&disableNav=YES&x-sec=wua&asac=2A21B017LSI0KGN210C6D7&pha_h5=true&pha_nav=true&uniapp_id=1011525&uniapp_page=home&hd_from=newMetao_zc&miniappSourceChannel=mytaobao"}, "open1": {"url": "https://pages-fast.m.taobao.com/wow/bz/jingmi/1592?spm=a2141.7631743.888.2&scm=********.1.3.778&x-ssr=true&forceThemis=true&disableNav=YES&x-preload=true&disableProgress=true&skeleton=true&dailyRedPacket=true"}, "openMore": {"url": "https://pages-fast.m.taobao.com/wow/bz/jingmi/1592?x-ssr=true&forceThemis=true&disableNav=YES&x-preload=true&disableProgress=true&skeleton=true&scm=********.1.2.8&spm=a2141.7631743.888.88&dailyRedPacket=true"}, "open2": {"url": "https://pages-fast.m.taobao.com/wow/bz/jingmi/1592?spm=a2141.7631743.888.3&scm=********.1.3.777&x-ssr=true&forceThemis=true&disableNav=YES&x-preload=true&disableProgress=true&skeleton=true&tabType=redEnvelope&dailyRedPacket=true"}, "open3": {"url": "https://pages-fast.m.taobao.com/wow/z/blackvip/point/super?spm=a2141.7631743.888.4&scm=********.1.10.7&x-ssr=true&disableNav=YES&status_bar_transparent=true&x-preload=true&disableProgress=true&from=st_my_jifen_jifen&point_source=mytao"}, "openTotalSaving": {"url": "https://pages.tmall.com/wow/z/tblife/achievement/nfcFHNzbzbAcJ8CBm3Am?spm=a2141.7631743.888.15&scm=********.1.10.31&disableNav=YES&from=wtgerenzhongxin#/bill&spm=a2141.7631743.888.15&scm=********.1.10.31"}}}}, "headerModule": {"fields": {"nick": "昵称", "userInfo": {"is88Vip": "false", "isDynamicAvatar": "false", "logoScm": "********.1.7.5", "logoSpm": "a2141.7631743.1.20", "nick": "昵称", "nickDescHeightColor": "#cdcdcd", "nickDescLightColor": "#CCFFFFFF", "nickHeightColor": "#111111", "nickLightColor": "#FFFFFF", "nickScm": "********.1.7.6", "nickSpm": "a2141.7631743.1.25", "noNickHeightColor": "#666666", "noNickLightColor": "#CCFFFFFF", "snsNick": "昵称", "userLabel": "", "userLogo": "", "userName": "用户名"}, "accountTip": {"accountName": "用户名", "scm": "********.1.7.30", "spm": "a2141.7631743.1.35", "text": "账号名：", "textHeightColor": "#666666", "textLightColor": "#FFFFFF"}, "settingButton": {"hasRedDot": "false", "scm": "********.1.7.1", "spm": "a2141.7631743.1.100", "text": "设置", "textHeightColor": "#111111", "textLightColor": "#FFFFFF"}, "customerService": {"scm": "********.1.7.31", "spm": "a2141.7631743.1.301", "text": "官方客服", "textHeightColor": "#111111", "textLightColor": "#FFFFFF", "vipText": "专属客服", "vipUrl": "https://ai.alimebot.taobao.com/intl/index.htm?from=jwkaDKRCtd"}, "extra": {"vipWhiteCard": "false"}, "openAddressEnter": "true", "fontColor88vip": "false", "smallHeader": "false"}, "events": {"clickEvents": {"openAccountTip": {"url": "https://web.m.taobao.com/app/message-social-front/relation-base/my-qrcode?spm=a2141.7631743.1.35&scm=********.1.7.30&wh_weex=true&weex_mode=dom&wx_use_layoutng=true"}, "openSetting": {"url": "https://tb.cn/n/im/dynamic/tb_aura_page.html?spm=a2141.7631743.1.100&scm=********.1.7.1&bizConfigCode=mtbSetting&pageTitleTag=mt_mainSettingTitle"}, "openCustomerService": {"url": "https://ai.alimebot.taobao.com/intl/index.htm?spm=a2141.7631743.1.301&scm=********.1.7.31&from=WmOcN4SzYf"}, "openUserLogo": {"url": "https://meta.m.taobao.com/app/tmall-wireless/user-info-weex2/home?spm=a2141.7631743.1.20&scm=********.1.7.5&wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true"}, "openName": {"url": "https://meta.m.taobao.com/app/tmall-wireless/user-info-weex2/home?spm=a2141.7631743.1.25&scm=********.1.7.6&wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true"}, "openAddress": {"url": "https://my.m.taobao.com/deliver/wap_deliver_address_list.htm?spm=a2141.7631743.1.51"}}, "clickUt": {"openAccountTip": {"args": {"spm": "a2141.7631743.1.35", "mtbVersion": "mtb2024", "scm": "********.1.7.30"}, "umbrellaArgs": "", "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-accountTip", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openFriends": {"args": {"spm": "a2141.7631743.1.302", "mtbVersion": "mtb2024", "scm": "********.1.7.33"}, "umbrellaArgs": "", "arg1": "Page_My<PERSON><PERSON>bao_click-friends", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openFollow": {"args": {"spm": "a2141.7631743.1.36", "mtbVersion": "mtb2024", "scm": "********.1.7.15"}, "umbrellaArgs": "", "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-attention", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openSetting": {"args": {"spm": "a2141.7631743.1.100", "mtbVersion": "mtb2024", "scm": "********.1.7.1"}, "umbrellaArgs": "", "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_But<PERSON>-Setting", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openCustomerService": {"args": {"spm": "a2141.7631743.1.301", "mtbVersion": "mtb2024", "scm": "********.1.7.31"}, "umbrellaArgs": "", "arg1": "Page_MyTaobao_click-service", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openUserLogo": {"args": {"spm": "a2141.7631743.1.20", "mtbVersion": "mtb2024", "scm": "********.1.7.5"}, "umbrellaArgs": {}, "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ton-MyFace", "umbrellaUpload": "close", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openName": {"args": {"spm": "a2141.7631743.1.25", "mtbVersion": "mtb2024", "scm": "********.1.7.6"}, "umbrellaArgs": "", "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-MyNickName", "userTapRecorder": "", "page": "Page_MyTaobao"}, "openFans": {"args": {"spm": "a2141.7631743.1.37", "mtbVersion": "mtb2024", "scm": "********.1.7.17"}, "umbrellaArgs": "", "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Button-Fans", "userTapRecorder": "", "page": "Page_MyTaobao"}}}}, "orderModule": {"fields": {"deliveryList": [], "subTitle": "全部", "orderInfo": {"order2Deliver": {"count": "0", "name": "待发货"}, "order2Pay": {"count": "0", "name": "待付款"}, "order2Rate": {"count": "0", "name": "待评价"}, "order2Receive": {"count": "0", "name": "待收货"}, "order2Refund": {"count": "0", "name": "退款/售后"}}, "dynamic": {"screenWidthSmall": "false", "uniformStyle": "true"}, "title": "我的订单", "moreUrl": "http://tm.m.taobao.com/list.htm?OrderListType=total_orders", "disableOrderBackColor": "true"}, "events": {"clickEvents": {"open2Refund": {"url": "https://tm.m.taobao.com/list.htm?OrderListType=reFund&refundUrl=https%3A%2F%2Fmeta.m.taobao.com%2Fapp%2Fmtb%2Frefund-list%2Fhome%3Fwh_weex%3Dtrue%26weex_mode%3Ddom"}, "open2Receive": {"url": "http://tm.m.taobao.com/list.htm?OrderListType=wait_to_confirm"}, "openMore": {"url": "http://tm.m.taobao.com/list.htm?OrderListType=total_orders"}, "open2Deliver": {"url": "http://tm.m.taobao.com/list.htm?OrderListType=wait_to_shipments"}, "open2Rate": {"url": "https://web.m.taobao.com/app/mtb-guang/note-center/Home?pha=true&disableNav=YES&tab=comment&btnMode=light"}, "open2Pay": {"url": "http://tm.m.taobao.com/list.htm?OrderListType=wait_to_pay"}}, "clickUt": {"open2Refund": {"args": {"spm": "a2141.7631743.3.25", "mtbVersion": "mtb2024"}, "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ton-RefundOrderList", "nextPage": {"spm-url": "a2141.7631743.3.25"}}, "open2Receive": {"args": {"spm": "a2141.7631743.3.23", "mtbVersion": "mtb2024"}, "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-WaitToConfirm", "nextPage": {"spm-url": "a2141.7631743.3.23"}}, "openMore": {"args": {"spm": "a2141.7631743.3.20", "mtbVersion": "mtb2024"}, "arg1": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Button-AllOrder", "nextPage": {"spm-url": "a2141.7631743.3.20"}}, "open2Deliver": {"args": {"spm": "a2141.7631743.3.22", "mtbVersion": "mtb2024"}, "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-WaitToShiping", "nextPage": {"spm-url": "a2141.7631743.3.22"}}, "open2Rate": {"args": {"spm": "a2141.7631743.3.24", "mtbVersion": "mtb2024"}, "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-WaitToRate", "nextPage": {"spm-url": "a2141.7631743.3.24"}}, "open2Pay": {"args": {"spm": "a2141.7631743.3.21", "mtbVersion": "mtb2024"}, "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>-WaitToPay", "nextPage": {"spm-url": "a2141.7631743.3.21"}}}}}, "logisticStatisticModule2024": {"fields": {"footprint": {"arg1": "Page_MyTaobao_Show-Footprints", "bizCode": "foot", "cardType": "channel", "channelList": [{"arg1": "Page_MyTaobao_Show-Footprints", "channelId": "1818181818", "channelName": "我的足迹", "clickArg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints", "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017AdM5M1xR7CVAaZS8_!!6000000006439-2-tps-120-120.png", "scm": "********.1.2.2", "spm": "a2141.7631743.2.22", "type": "default"}, {"arg1": "Page_MyTaobao_show-Footprints1", "channelId": "8536719", "channelName": "淘票票", "clickArg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints1", "icon": "https://img.alicdn.com/imgextra/i4/O1CN01mz4Qrl1nNlu8HjhTj_!!6000000005078-2-tps-120-120.png", "scm": "********.1.2.107", "spm": "a2141.7631743.2.25", "type": "mtbRecommend"}, {"arg1": "Page_MyTaobao_show-Footprints2", "channelId": "1000004", "channelName": "极有家", "clickArg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints2", "icon": "https://img.alicdn.com/imgextra/i1/O1CN01TSturD1euRchuvKeG_!!6000000003931-2-tps-120-120.png", "scm": "********.1.2.107", "spm": "a2141.7631743.2.26", "type": "default"}], "clickArg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints", "defaultCard": "false", "firstTitle": "看过的商品和频道", "footTag": "1", "hideText": "false", "icon": "https://gw.alicdn.com/imgextra/i3/O1CN01R37hCs1NCcW2wSfdq_!!6000000001534-2-tps-180-180.png", "interestText": "暂无推荐", "logisticsScrollWeight": "1", "name": "足迹", "scm": "********.1.2.2", "spm": "a2141.7631743.2.22", "title": "去看看最近浏览"}, "logistics": {"bizCode": "logistics", "defaultCard": "true", "firstTitle": "查看快递信息", "hideText": "false", "logisticsScrollWeight": "1", "name": "快递", "receiveExpressText": "收快递", "sendExpressText": "寄快递", "spm": "a2141.7631743.2.41"}, "favorite": {"bizCode": "fav", "defaultCard": "true", "favTag": "1", "firstTitle": "查看最近收藏宝贝", "hideText": "false", "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01wy1xWX1kitaWvab6u_!!6000000004718-2-tps-180-180.png", "interestText": "暂无推荐", "labelType": "default", "logisticsScrollWeight": "1", "name": "收藏", "openFavPic": "openFavPic", "scm": "********.1.2.1", "spm": "a2141.7631743.2.20", "title": "去逛逛更多宝贝"}, "disableBottomPadding": "true"}, "events": {"clickEvents": {"openFavPic": {"url": "https://web.m.taobao.com/app/message-social-front/itao-favor-weex2/index?wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true&spm=a2141.7631743.2.20&scm=********.1.2.1"}, "openLogisticsSend": {"url": "https://tb.cn/n/im/dynamic/msg_aura_page.html?bizConfigCode=myExpress&enableImmersive=true&anchorPage=sendPage&spm=a2141.7631743.2.41"}, "openFav": {"url": "https://web.m.taobao.com/app/message-social-front/itao-favor-weex2/index?wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true&spm=a2141.7631743.2.20&scm=********.1.2.1"}, "openLogistics": {"url": "https://tb.cn/n/im/dynamic/msg_aura_page.html?bizConfigCode=myExpress&enableImmersive=true&spm=a2141.7631743.2.41"}, "openApp2": {"url": "https://m.duanqu.com?miniappSourceChannel=mytaobao&_ariver_appid=1000004&_mp_code=tb&customSource=secondFloor&miniappSourceChannel=mytaobao&spm=a2141.7631743.2.26&scm=********.1.2.107"}, "openApp1": {"url": "https://m.taopiaopiao.com/tickets/moviemain/pages/home/<USER>"}, "openLogisticsReceive": {"url": "https://tb.cn/n/im/dynamic/msg_aura_page.html?bizConfigCode=myExpress&enableImmersive=true&anchorPage=receivePage&spm=a2141.7631743.2.41"}, "openApp0": {"url": "https://web.m.taobao.com/app/message-social-front/footprint/home?wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true&spm=a2141.7631743.2.22&scm=********.1.2.2&passParam=%7B%22pageVersion%22%3A%223.0%22%2C%22wysiwygStr%22%3A%22%5B1818181818%2C8536719%2C1000004%5D%22%7D"}, "openFoot": {"url": "https://web.m.taobao.com/app/message-social-front/footprint/home?wh_weex=true&weex_mode=dom&wx_navbar_hidden=true&wx_statusbar_hidden=true&spm=a2141.7631743.2.22&scm=********.1.2.2&passParam=%7B%22pageVersion%22%3A%223.0%22%2C%22wysiwygStr%22%3A%22%5B1818181818%2C8536719%2C1000004%5D%22%7D"}}, "clickUt": {"openFavPic": {"args": {"spm": "a2141.7631743.2.20", "mainpoint": "default", "mtbVersion": "mtb2024", "description": "去逛逛更多宝贝", "scm": "********.1.2.1"}, "umbrellaArgs": "", "removeArgs": "", "userTrackInfoArgs": "", "arg1": "Page_MyTaobao_Button-ItemFavorite", "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "page": "Page_MyTaobao", "addFatigueArgs": ""}, "openLogisticsSend": {"args": {"spm": "a2141.7631743.2.41", "mtbVersion": "mtb2024"}, "umbrellaArgs": "", "removeArgs": "", "userTrackInfoArgs": "", "arg1": "Page_MyTaobao_click-express", "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "page": "Page_MyTaobao", "addFatigueArgs": ""}, "openFav": {"args": {"spm": "a2141.7631743.2.20", "mainpoint": "default", "mtbVersion": "mtb2024", "description": "去逛逛更多宝贝", "scm": "********.1.2.1"}, "umbrellaArgs": "", "removeArgs": "", "userTrackInfoArgs": "", "arg1": "Page_MyTaobao_Button-ItemFavorite", "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "page": "Page_MyTaobao", "addFatigueArgs": ""}, "openLogistics": {"args": {"spm": "a2141.7631743.2.41", "mainpoint": "header", "mtbVersion": "mtb2024"}, "umbrellaArgs": "", "removeArgs": "", "userTrackInfoArgs": "", "arg1": "Page_MyTaobao_click-express", "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "page": "Page_MyTaobao", "addFatigueArgs": ""}, "openApp2": {"umbrellaArgs": "", "userTrackInfoArgs": "", "nextPage": {"spm-url": "a2141.7631743.2.26"}, "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "addFatigueArgs": "", "args": {"spm": "a2141.7631743.2.26", "mtbVersion": "mtb2024", "miniapp_id": "1000004", "channelType": "2", "scm": "********.1.2.107"}, "removeArgs": "", "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints3", "page": "Page_MyTaobao"}, "openApp1": {"umbrellaArgs": "", "userTrackInfoArgs": "", "nextPage": {"spm-url": "a2141.7631743.2.25"}, "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "addFatigueArgs": "", "args": {"spm": "a2141.7631743.2.25", "mtbVersion": "mtb2024", "miniapp_id": "8536719", "channelType": "2", "scm": "********.1.2.107"}, "removeArgs": "", "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints2", "page": "Page_MyTaobao"}, "openLogisticsReceive": {"args": {"spm": "a2141.7631743.2.41", "mtbVersion": "mtb2024"}, "umbrellaArgs": "", "removeArgs": "", "userTrackInfoArgs": "", "arg1": "Page_MyTaobao_click-express", "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "page": "Page_MyTaobao", "addFatigueArgs": ""}, "openApp0": {"umbrellaArgs": "", "userTrackInfoArgs": "", "nextPage": {"spm-url": "a2141.7631743.2.22"}, "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "addFatigueArgs": "", "args": {"spm": "a2141.7631743.2.22", "mtbVersion": "mtb2024", "miniapp_id": "1818181818", "channelType": "2", "scm": "********.1.2.2"}, "removeArgs": "", "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints1", "page": "Page_MyTaobao"}, "openFoot": {"umbrellaArgs": "", "userTrackInfoArgs": "", "nextPage": {"spm-url": "a2141.7631743.2.22"}, "fatigueCondition": "", "userTapRecorder": "", "userTrackInfoCondition": "", "addFatigueArgs": "", "args": {"spm": "a2141.7631743.2.22", "mtbVersion": "mtb2024", "scm": "********.1.2.2"}, "removeArgs": "", "arg1": "Page_<PERSON><PERSON><PERSON><PERSON>_Button-Footprints", "page": "Page_MyTaobao"}}}}}, "sourceFrom": 1}, "ret": ["SUCCESS::调用成功"], "v": "1.0"}