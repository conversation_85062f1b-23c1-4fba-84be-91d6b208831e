package mtopsdk.common.util;

import p277tb.t2o;

/* compiled from: Taobao */
/* loaded from: classes11.dex */
public class HttpHeaderConstant {
    public static final String BX_ACTION = "Bx-action";
    public static final String CACHE_CONTROL = "cache-control";
    public static final String CLIENT_FALCO_ID = "x-falco-id";
    public static final String CLIENT_TRACE_ID = "x-c-traceid";
    public static final String CONTENT_ENCODING = "content-encoding";
    public static final String CONTENT_LENGTH = "content-length";
    public static final String CONTENT_TYPE = "content-type";
    public static final String COOKIE = "cookie";
    public static final String DATE = "Date";
    public static final String EAGLE_TRACE_ID = "eagleeye-traceid";
    public static final String ETAG = "etag";
    public static final String FORM_CONTENT_TYPE = "application/x-www-form-urlencoded;charset=UTF-8";
    public static final String F_REFER = "f-refer";
    public static final String F_REFER_MTOP = "mtop";
    public static final String GZIP = "gzip";
    public static final String IF_MODIFIED_SINCE = "if-modified-since";
    public static final String IF_NONE_MATCH = "if-none-match";
    public static final String KEY_EXTDATA = "extdata";
    public static final String KEY_EXTDATA_ACCESSTOKEN = "accesstoken";
    public static final String KEY_EXTDATA_OPENAPPKEY = "openappkey";
    public static final String KEY_EXTTYPE = "exttype";
    public static final String LAST_MODIFIED = "last-modified";
    public static final String LAUNCH_INFO_KEY = "c-launch-info";
    public static final String MTOPSDK_PROPERTY_PREFIX = "mtopsdk.";
    public static final String MTOP_X_ETAG = "MTOP-XETag";
    public static final String M_SDKVER_VALUE = "*******";
    public static final String NO_CACHE = "no-cache";
    public static final String OFFLINE_FLAG_ON = "of=on";
    public static final String PRIVATE_FLAG_FALSE = "private=false";
    public static final String REDIRECT_LOCATION = "location";
    public static final int SC_FLOW_LIMITED = 420;
    public static final int SC_INSUFFICIENT_SPACE_ON_RESOURCE = 419;
    public static final int SC_NOT_MODIFIED = 304;
    public static final int SC_OK = 200;
    public static final String SERVER_BRT = "s-brt";
    public static final String SERVER_CUNIT = "s-cunit";
    public static final String SERVER_RT = "s-rt";
    public static final String SERVER_TRACE_ID = "x-s-traceid";
    public static final String USED_STREAMING = "x-used-stream";
    public static final String USER_AGENT = "user-agent";
    public static final String X_AB = "MTOP-x-ali-ab";
    public static final String X_ACCEPT_STREAM = "x-accept-stream";
    public static final String X_ACT = "x-act";
    public static final String X_ACT_HINT = "x-act-hint";
    public static final String X_APPKEY = "x-appkey";
    public static final String X_APP_CONF_V = "x-app-conf-v";
    public static final String X_APP_VER = "x-app-ver";
    public static final String X_BIN_LENGTH = "x-bin-length";
    public static final String X_COMMAND_ORANGE = "x-orange-p-i";
    public static final String X_DEVICE_ENV = "x-device-env";
    public static final String X_DEVICE_LEVEL = "x-device-level";
    public static final String X_DEVID = "x-devid";
    public static final String X_EXTDATA = "x-extdata";
    public static final String X_EXTTYPE = "x-exttype";
    public static final String X_FEATURES = "x-features";
    public static final String X_LOCATION = "x-location";
    public static final String X_LOCATION_EXT = "x-location-ext";
    public static final String X_LOW_BUYER = "x-lowbuyer";
    public static final String X_MAPPING_CODE = "x-mapping-code";
    public static final String X_MINI_APPKEY = "x-mini-appkey";
    public static final String X_MINI_WUA = "x-mini-wua";
    public static final String X_M_SLIM = "x-m-slim";
    public static final String X_M_SLIM_CFG = "x-m-slim-cfg";
    public static final String X_NETINFO = "x-netinfo";
    public static final String X_NETTYPE = "x-nettype";
    public static final String X_NQ = "x-nq";
    public static final String X_OPEN_BIZ = "x-open-biz";
    public static final String X_OPEN_BIZ_DATA = "x-open-biz-data";
    public static final String X_ORANGE_Q = "x-orange-q";
    public static final String X_PAGE_MAB = "x-page-mab";
    public static final String X_PAGE_NAME = "x-page-name";
    public static final String X_PAGE_URL = "x-page-url";
    public static final String X_PLACE_ID = "x-place-id";
    public static final String X_PRIORITY_DATA = "x-priority-data";
    public static final String X_PV = "x-pv";
    public static final String X_REQBIZ_EXT = "x-reqbiz-ext";
    public static final String X_REQ_APPKEY = "x-req-appkey";
    public static final String X_RETCODE = "x-retcode";
    public static final String X_ROUTER_ID = "x-router-id";
    public static final String X_SERVICE_DOMAIN = "x-service-domain";
    public static final String X_SERVICE_IPS = "x-service-ips";
    public static final String X_SERVICE_TOKEN = "x-service-token";
    public static final String X_SERVICE_UNIT = "x-service-unit";
    public static final String X_SESSION_RET = "x-session-ret";
    public static final String X_SG_COOKIE = "x-sg-cookie";
    public static final String X_SID = "x-sid";
    public static final String X_SIGN = "x-sign";
    public static final String X_SIGN_CONTROL = "x-s-c";
    public static final String X_SSR_PV = "x-ssr-pv";
    public static final String X_SSR_TOKEN_VALIDATION = "x-token-validation";
    public static final String X_SYSTIME = "x-systime";
    public static final String X_T = "x-t";
    public static final String X_TTID = "x-ttid";
    public static final String X_UA = "x-ua";
    public static final String X_UID = "x-uid";
    public static final String X_UMID_TOKEN = "x-umt";
    public static final String X_UNIT = "x-unit";
    public static final String X_UTDID = "x-utdid";
    public static final String X_WUAT = "x-wuat";

    static {
        t2o.m151972a(623902803);
    }
}
